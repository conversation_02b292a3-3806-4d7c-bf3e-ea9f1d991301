import { useMemo } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { useDevicesData } from '@/hooks/api/use-devices'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormControl, FormMessage, Input, Button, Checkbox } from '@/components/ui'

import { type StoreFormValues } from '../../../data'

interface DeviceSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  storeUid?: string
}

export function DeviceSection({ form, isLoading = false, storeUid }: DeviceSectionProps) {
  const { data: devices = [], isLoading: loadingDevices } = useDevicesData({
    storeUid,
    params: { skip_limit: true },
    enabled: true
  })

  const deviceOptions = useMemo(
    () =>
      devices
        .filter(d => d.isActive)
        .map(d => ({
          value: d.device_code || '',
          label: `${d.name}`
        })),
    [devices]
  )

  const handleAddDevice = () => {
    // TODO: Implement add device functionality
    console.log('Adding device...')
  }

  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Thiết bị</h2>
      <p className='mb-4 text-sm text-gray-600'>Một điểm bán hàng có thể có 1 hoặc nhiều thiết bị bán hàng.</p>

      <div className='space-y-4'>
        {/* Device Management Code */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <div className='flex items-center gap-2'>
              <label className='text-sm font-medium text-gray-700'>Mã quản lý thiết bị</label>
              <div className='flex h-4 w-4 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600'>
                ?
              </div>
            </div>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='store_id'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder='XP5446MG969X' disabled={true} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Prevent Device Removal */}
        <div className='grid grid-cols-12 items-center gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>
              Không cho phép kích thiết bị ra khỏi bàn trừ máy chủ
            </label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='no_kick_pda'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id='no_kick_pda'
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Online Order Device */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <div className='flex items-center gap-2'>
              <label className='text-sm font-medium text-gray-700'>Thiết bị nhận đơn online</label>
              <div className='flex h-4 w-4 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600'>
                ?
              </div>
            </div>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='device_receive_online'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Combobox
                      options={deviceOptions}
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder='Chọn thiết bị nhận đơn online tại cửa hàng'
                      searchPlaceholder='Tìm kiếm thiết bị...'
                      emptyText='Không tìm thấy thiết bị nào.'
                      disabled={isLoading || loadingDevices}
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Active Devices */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Thiết bị đang hoạt động</label>
          </div>
          <div className='col-span-9'>
            <Button
              type='button'
              variant='link'
              className='h-auto p-0 text-blue-600 hover:text-blue-800'
              onClick={handleAddDevice}
              disabled={isLoading}
            >
              Thêm thiết bị
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
