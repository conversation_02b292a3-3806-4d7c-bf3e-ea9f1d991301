import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { RefreshCw } from 'lucide-react'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Input, Button } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface PinCodeSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function PinCodeSection({ form, isLoading = false }: PinCodeSectionProps) {
  const [pinLength] = useState(6)

  const generateRandomPin = () => {
    const min = Math.pow(10, pinLength - 1)
    const max = Math.pow(10, pinLength) - 1
    const randomPin = Math.floor(Math.random() * (max - min + 1)) + min
    return randomPin.toString()
  }

  const autoChangePinOptions = [
    {
      value: '0',
      label: '<PERSON>hông tự động thay đổi mã Pin khi sử dụng'
    },
    {
      value: '0.02',
      label: '<PERSON><PERSON> Pin chỉ sử dụng được một lần'
    },
    {
      value: '1',
      label: 'Sau 1 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '2',
      label: 'Sau 2 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '3',
      label: 'Sau 3 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '4',
      label: 'Sau 4 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '5',
      label: 'Sau 5 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '6',
      label: 'Sau 6 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '7',
      label: 'Sau 7 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '8',
      label: 'Sau 8 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '9',
      label: 'Sau 9 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    },
    {
      value: '10',
      label: 'Sau 10 phút sử dụng, hệ thống sẽ tự động đổi mã PIN'
    }
  ]

  const handleGeneratePin = () => {
    const newPin = generateRandomPin()
    form.setValue('pin_code', newPin)
  }

  const handlePinChange = (value: string) => {
    const numericValue = value.replace(/\D/g, '').slice(0, pinLength)
    form.setValue('pin_code', numericValue)
  }

  const currentPin = form.watch('pin_code') || ''

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Mã PIN</h2>
        <p className='text-sm text-gray-600'>
          Mã được tạo tự động và được sử dụng để quản lý xác nhận cho phép thực hiện các chức năng của quản lý mà không
          cần phải thay đổi tài khoản!
        </p>
      </div>

      <div className='space-y-4'>
        {/* Mã PIN */}
        <FormField
          control={form.control}
          name='pin_code'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>
                    Mã PIN <span className='text-red-500'>*</span>
                  </FormLabel>
                </div>
                <div className='flex flex-1 items-center gap-2'>
                  <FormControl>
                    <div className='relative flex-1'>
                      <Input
                        {...field}
                        value={currentPin}
                        onChange={e => handlePinChange(e.target.value)}
                        placeholder='Nhập mã PIN'
                        disabled={isLoading}
                        className='pr-16'
                        maxLength={pinLength}
                      />
                      <div className='absolute top-1/2 right-3 -translate-y-1/2 text-xs text-gray-400'>
                        {currentPin.length}/{pinLength}
                      </div>
                    </div>
                  </FormControl>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleGeneratePin}
                    disabled={isLoading}
                    className='shrink-0'
                  >
                    <RefreshCw className='h-4 w-4' />
                  </Button>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tự động thay đổi mã PIN */}
        <FormField
          control={form.control}
          name='time_out_use_pin'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Tự động thay đổi mã PIN</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value?.toString() || '0'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn cấu hình tự động thay đổi'
                    className='flex-1'
                    options={autoChangePinOptions}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
