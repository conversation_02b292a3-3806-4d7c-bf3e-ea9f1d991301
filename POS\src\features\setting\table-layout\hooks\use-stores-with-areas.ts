import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { areasApi, type AreasListParams } from '@/lib/api'

import { QUERY_KEYS } from '@/constants/query-keys'

import { useStoresData } from '@/hooks/api/use-stores'

export interface StoreWithAreas {
  id: string
  name: string
  hasAreas: boolean
  areasCount: number
}

/**
 * Hook to fetch stores that have areas
 */
export const useStoresWithAreas = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0] 

  const { data: allStores, isLoading: isLoadingStores } = useStoresData()

  const storesWithAreasQuery = useQuery({
    queryKey: [QUERY_KEYS.STORES_WITH_AREAS, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<StoreWithAreas[]> => {
      if (!allStores || !company?.id || !selectedBrand?.id) {
        return []
      }

      const storesWithAreas: StoreWithAreas[] = []

      for (const store of allStores) {
        try {
          const params: AreasListParams = {
            company_uid: company.id,
            brand_uid: selectedBrand.id,
            store_uid: store.id,
            skip_limit: true,
            page: 1,
            results_per_page: 1
          }

          const areasResponse = await areasApi.getAreasList(params)
          const hasAreas = areasResponse.data && areasResponse.data.length > 0

          storesWithAreas.push({
            id: store.id,
            name: store.name,
            hasAreas,
            areasCount: areasResponse.data?.length || 0
          })
        } catch (error) {
          storesWithAreas.push({
            id: store.id,
            name: store.name,
            hasAreas: false,
            areasCount: 0
          })
        }
      }

      return storesWithAreas.filter(store => store.hasAreas)
    },
    enabled: !!(allStores && company?.id && selectedBrand?.id && !isLoadingStores),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000 // 15 minutes
  })

  return {
    data: storesWithAreasQuery.data || [],
    isLoading: isLoadingStores || storesWithAreasQuery.isLoading,
    error: storesWithAreasQuery.error
  }
}
