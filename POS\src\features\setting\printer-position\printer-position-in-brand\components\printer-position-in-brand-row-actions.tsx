'use client'

import { Row } from '@tanstack/react-table'
import { Trash2 } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { usePrinterPositionInBrand } from '../context'
import { PrinterPositionInBrand } from '../data'

interface PrinterPositionInBrandRowActionsProps {
  row: Row<PrinterPositionInBrand>
}

export function PrinterPositionInBrandRowActions({ row }: PrinterPositionInBrandRowActionsProps) {
  const printerPosition = row.original
  const { setOpen, setCurrentRow } = usePrinterPositionInBrand()

  const handleDelete = () => {
    setCurrentRow(printerPosition)
    setOpen('delete')
  }

  return (
    <Button
      variant='ghost'
      size='sm'
      onClick={handleDelete}
      className='h-8 w-8 p-0 text-destructive hover:text-destructive'
    >
      <span className='sr-only'>Xóa</span>
      <Trash2 className='h-4 w-4' />
    </Button>
  )
}
