import { crmApi } from './crm-api'

export const authenticateCrmByToken = async (token: string, onSuccess?: () => void) => {
  try {
    const response = await crmApi.post('/api/auth-by-token', { token })

    // Nếu authentication thành công và có token
    if (response.data && response.data.token) {
      // Set crm_token vào localStorage
      localStorage.setItem('crm_token', response.data.token)

      // Call callback nếu có
      if (onSuccess) {
        onSuccess()
      }
    }

    return response.data
  } catch (error) {
    console.error('CRM authentication failed:', error)
    throw error
  }
}
