import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { QUERY_KEYS } from '@/constants/query-keys'

export {
  usePrinterPositionsData,
  type UsePrinterPositionsDataOptions
} from '@/features/setting/printer-position/printer-position-in-brand/hooks'

export type { PrinterPosition } from '@/lib/printer-position-api'

export interface UpdatePrinterPositionCategoryParams {
  categoryId: string
  printerPosition: any
  action: 'add' | 'remove'
}

export const useUpdatePrinterPositionCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (_params: UpdatePrinterPositionCategoryParams) => {
      return { success: true }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PRINTER_POSITIONS] })
      toast.success('Cập nhật vị trí máy in thành công')
    },
    onError: (_error: Error) => {
      toast.error('Có lỗi xảy ra khi cập nhật vị trí máy in')
    }
  })
}
