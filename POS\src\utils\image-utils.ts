/**
 * Validate image file size and type
 */
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return {
      isValid: false,
      error: '<PERSON>ui lòng chọn file ảnh'
    }
  }

  // Check file size (2.5MB = 2.5 * 1024 * 1024 bytes)
  const maxSize = 2.5 * 1024 * 1024
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: '<PERSON><PERSON><PERSON> thước ảnh phải nhỏ hơn 2.5MB'
    }
  }

  return { isValid: true }
}

/**
 * Create image preview URL from file
 */
export const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      resolve(e.target?.result as string)
    }
    reader.onerror = () => {
      reject(new Error('Không thể tạo preview ảnh'))
    }
    reader.readAsDataURL(file)
  })
}

/**
 * Format file size to human readable string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
