export interface RegisterPageFormField {
  field_id: string
  field_name: string
  active: number
  data_type: string
  require: boolean
}

export interface RegisterPageConfigData {
  id: string
  pos_parent: string
  banner: string
  active: number
  send_otp: number
  logo: string
  default_lang: string
  form_data: RegisterPageFormField[]
  update_at: string
}

export interface GetRegisterPageConfigResponse {
  data: RegisterPageConfigData
  trackid?: string
  ip?: string
}

export interface SaveRegisterPageConfigRequest {
  pos_parent: string
  banner: string
  active: number
  logo: string
  send_otp: number
  default_lang: string
  form_data: RegisterPageFormField[]
}

export interface SaveRegisterPageConfigResponse {
  data?: RegisterPageConfigData
  message?: string
  trackid?: string
}
