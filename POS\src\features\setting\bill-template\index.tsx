import { useState, useEffect } from 'react'

import { Check } from 'lucide-react'
import { toast } from 'sonner'

import { usePosStores } from '@/stores/posStore'

import { useStoreData } from '@/hooks/api'

import { Main } from '@/components/layout/main'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui'

import { BillTemplateActionBar, BillConfigPanel, BillTemplate1 } from './components'
import { useSaveBillTemplateWithImage, useGetBillTemplate, useUseBillTemplate } from './hooks'
import { BillData } from './types'
import { createUseBillTemplatePayload, getTemplateNumberFromTab, createMinimalUseBillTemplatePayload } from './utils'

function TabTriggerWithIcon({
  value,
  activeTab,
  children
}: {
  value: string
  activeTab: string
  children: React.ReactNode
}) {
  const isActive = activeTab === value

  return (
    <TabsTrigger value={value} className='flex items-center gap-2'>
      {children}
      {isActive && <Check className='h-4 w-4' />}
    </TabsTrigger>
  )
}

const placeholder: BillData = {
  billNumber: '000001',
  billId: '#FU8KQ',
  orderSource: 'TA',
  table: 'TA',
  checkInTime: '10:45',
  checkOutTime: '10:45',
  cashier: '<EMAIL>',
  date: '03 thg 12, 2019',
  items: [
    {
      id: '1',
      name: 'Món 1',
      quantity: 1,
      unitPrice: 40000,
      subtotal: 40000,
      unit: 'MON',
      itemType: 'Đồ ăn',
      notes: 'Ghi chú món ăn hiển thị ở đây',
      discountPercentage: 10,
      discountAmount: 4000,
      vatPercentage: 10,
      vatAmount: 10,
      toppings: [
        {
          name: 'topping',
          quantity: 1,
          unitPrice: 5000,
          subtotal: 5000,
          unit: 'MON',
          notes: 'Ghi chú topping hiển thị ở đây',
          discountPercentage: 10,
          discountAmount: 500,
          vatPercentage: 10,
          vatAmount: 0
        }
      ]
    },
    {
      id: '2',
      name: 'Món 2',
      quantity: 2,
      unitPrice: 50000,
      subtotal: 100000,
      unit: 'BAT',
      itemType: 'Đồ uống',
      notes: 'Ghi chú món ăn hiển thị ở đây',
      discountPercentage: 10,
      discountAmount: 10000,
      vatPercentage: 10,
      vatAmount: 10
    },
    {
      id: '3',
      name: 'Món 3',
      quantity: 1,
      unitPrice: 30000,
      subtotal: 30000,
      unit: 'DIA',
      itemType: 'Đồ uống',
      notes: 'Ghi chú món ăn hiển thị ở đây',
      discountPercentage: 10,
      discountAmount: 3000,
      vatPercentage: 10,
      vatAmount: 10
    },
    {
      id: '4',
      name: 'Món 4',
      quantity: 1,
      unitPrice: 50000,
      subtotal: 50000,
      unit: 'CHAI',
      itemType: 'Không có loại',
      notes: 'Ghi chú món ăn hiển thị ở đây',
      discountPercentage: 10,
      discountAmount: 5000,
      vatPercentage: 10,
      vatAmount: 10
    },
    {
      id: '5',
      name: 'Món 5',
      quantity: 1,
      unitPrice: 70000,
      subtotal: 70000,
      unit: 'MON',
      itemType: 'Không có loại',
      notes: 'Ghi chú món ăn hiển thị ở đây',
      discountPercentage: 10,
      discountAmount: 7000,
      vatPercentage: 10,
      vatAmount: 10
    }
  ],
  subtotal: 295000,
  discountAmount: 0,
  itemDiscountAmount: 0,
  vatAmount: 25542,
  totalWithVat: 295000,
  shippingFee: 20000,
  voucherDiscount: 30000,
  grandTotal: 285000,
  paymentMethod: 'Tiền mặt',
  paymentAmount: 285000,
  amountReceived: 285000,
  changeAmount: 0,
  restaurantName: 'Tên nhà hàng',
  restaurantAddress: 'Địa chỉ nhà hàng',
  customerName: 'Charles',
  customerPhone: '84123456789',
  customerTaxId: '0101234567',
  customerAddress: '106 Hoàng Quốc Việt, Cầu Giấy, Hà Nội',
  accumulatedPoints: 12345,
  voucherName: 'Tên voucher',
  cardFeeAmount: 28500,
  customText1: 'Nhập văn bản tuỳ chỉnh 1',
  customText2: 'Cảm ơn Quý Khách',
  hotline: ''
}

export default function BillTemplatePage() {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [activeTab, setActiveTab] = useState<string>('template-1')
  const [billData, _setBillData] = useState<BillData>(placeholder)
  const [configTemplate1, setConfigTemplate1] = useState<any>({})
  const [currentLogo, setCurrentLogo] = useState<string | null>(null)

  const apiParams = {
    companyUid: '595e8cb4-674c-49f7-adec-826b211a7ce3',
    brandUid: 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e',
    storeUid: selectedStoreId || 'ba6e0a44-080d-4ae4-aba0-c29b79e95ab3'
  }

  const { currentBrandApiStores } = usePosStores()
  const { data: storeData } = useStoreData(selectedStoreId)
  const { data: billTemplateData, isLoading: isLoadingTemplate } = useGetBillTemplate(
    {
      company_uid: apiParams.companyUid,
      brand_uid: apiParams.brandUid,
      store_uid: apiParams.storeUid
    },
    {
      enabled: Boolean(selectedStoreId)
    }
  )

  const saveBillTemplateWithImage = useSaveBillTemplateWithImage({
    onSuccess: () => {
      toast.success('Lưu cấu hình thành công!')
    },
    onError: error => {
      toast.error(error.message || 'Có lỗi xảy ra khi lưu cấu hình')
    }
  })
  const useBillTemplate = useUseBillTemplate()

  useEffect(() => {
    if (currentBrandApiStores.length > 0) {
      const firstStoreId = currentBrandApiStores[0].id
      setSelectedStoreId(firstStoreId)
    }
  }, [currentBrandApiStores])

  useEffect(() => {
    if (billTemplateData?.data) {
      setConfigTemplate1(billTemplateData.data.extra_data || {})
    }
  }, [billTemplateData?.data])

  const handleUseBillTemplate = () => {
    let currentStoreData: any = storeData

    if (!currentStoreData && selectedStoreId) {
      currentStoreData = currentBrandApiStores.find(store => store.id === selectedStoreId)
    }

    if (!currentStoreData) {
      return
    }

    const templateNumber = getTemplateNumberFromTab(activeTab)
    try {
      const payload = createMinimalUseBillTemplatePayload(currentStoreData, templateNumber)
      useBillTemplate.mutate(payload as any)
    } catch (error) {
      try {
        const payload = createUseBillTemplatePayload(currentStoreData, templateNumber)
        useBillTemplate.mutate(payload)
      } catch (fallbackError) {}
    }
  }

  const handleCopyTemplates = () => {}

  if (isLoadingTemplate && selectedStoreId) {
    return (
      <Main>
        <BillTemplateActionBar
          selectedStoreId={selectedStoreId}
          onStoreChange={setSelectedStoreId}
          onUseBillTemplate={handleUseBillTemplate}
          onCopyTemplates={handleCopyTemplates}
          isUsingTemplate={useBillTemplate.isPending}
        />
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900'></div>
            <p className='text-gray-600'>Đang tải cấu hình bill template...</p>
          </div>
        </div>
      </Main>
    )
  }

  if (!selectedStoreId) {
    return (
      <Main>
        <BillTemplateActionBar
          selectedStoreId={selectedStoreId}
          onStoreChange={setSelectedStoreId}
          onUseBillTemplate={handleUseBillTemplate}
          onCopyTemplates={handleCopyTemplates}
          isUsingTemplate={useBillTemplate.isPending}
        />
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <p className='text-gray-600'>Vui lòng chọn cửa hàng để xem cấu hình bill template</p>
          </div>
        </div>
      </Main>
    )
  }

  return (
    <Main>
      <BillTemplateActionBar
        selectedStoreId={selectedStoreId}
        onStoreChange={setSelectedStoreId}
        onUseBillTemplate={handleUseBillTemplate}
        onCopyTemplates={handleCopyTemplates}
        isUsingTemplate={useBillTemplate.isPending}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className='mt-6'>
        <TabsList className='grid w-full grid-cols-5 lg:grid-cols-10'>
          <TabTriggerWithIcon value='template-1' activeTab={activeTab}>
            Mẫu hoá đơn 1
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='template-2' activeTab={activeTab}>
            Mẫu hoá đơn 2
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='template-3' activeTab={activeTab}>
            Mẫu hoá đơn 3
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='template-4' activeTab={activeTab}>
            Mẫu hoá đơn 4
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='custom-template' activeTab={activeTab}>
            Mẫu tuỳ chỉnh
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='shift-report' activeTab={activeTab}>
            Phiếu báo cáo chốt ca
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='order-slip' activeTab={activeTab}>
            Phiếu đặt đồ
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='label-template' activeTab={activeTab}>
            Mẫu in tem
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='temp-receipt' activeTab={activeTab}>
            Phiếu tạm tính
          </TabTriggerWithIcon>
          <TabTriggerWithIcon value='table-booking' activeTab={activeTab}>
            Mẫu đặt bàn
          </TabTriggerWithIcon>
        </TabsList>

        <TabsContent value='template-1' className='mt-6'>
          <div className='grid grid-cols-1 gap-8 lg:grid-cols-2 lg:items-stretch'>
            <div className='flex justify-center self-start'>
              {billTemplateData?.data ? (
                <BillTemplate1
                  billData={billData}
                  billTemplateData={
                    {
                      ...billTemplateData.data,
                      extra_data: configTemplate1,
                      logo: currentLogo
                    } as any
                  }
                />
              ) : (
                <BillTemplate1 billData={billData} />
              )}
            </div>

            <div>
              <BillConfigPanel
                config={configTemplate1}
                onConfigChange={setConfigTemplate1}
                serverLogo={billTemplateData?.data?.logo || null}
                onLogoChange={setCurrentLogo}
                onSave={(logoFile, isLogoRemoved) => {
                  if (billTemplateData?.data) {
                    const updatedPayload = {
                      ...billTemplateData.data,
                      revision: billTemplateData.data.revision + 1,
                      updated_at: new Date().toISOString(),
                      extra_data: configTemplate1
                    }

                    saveBillTemplateWithImage.mutate({
                      payload: updatedPayload as any,
                      logoFile,
                      isLogoRemoved
                    })
                  }
                }}
                isSaving={saveBillTemplateWithImage.isPending}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value='template-2' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu hoá đơn 2 sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='template-3' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu hoá đơn 3 sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='template-4' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu hoá đơn 4 sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='custom-template' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu tuỳ chỉnh sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='shift-report' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Phiếu báo cáo chốt ca sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='order-slip' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Phiếu đặt đồ sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='label-template' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu in tem sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='temp-receipt' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Phiếu tạm tính sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>

        <TabsContent value='table-booking' className='mt-6'>
          <div className='rounded-lg border p-8 text-center'>
            <p className='text-muted-foreground'>Nội dung Mẫu đặt bàn sẽ được hiển thị ở đây</p>
          </div>
        </TabsContent>
      </Tabs>
    </Main>
  )
}
