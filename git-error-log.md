> git -c user.useConfigOnly=true commit --quiet --allow-empty-message --file -

> shadcn-admin@1.4.0 lint:fix D:\StudyWork\TTMI\POS-iPOS\POS
> eslint . --fix


D:\StudyWork\TTMI\POS-iPOS\POS\src\components\layout\brand-compat.tsx
  13:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\components\pagination.tsx
  185:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\components\pos\discount-toggle-button.tsx
  31:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\components\pos\modal\index.tsx
  1:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  2:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\components\table-pagination.tsx
  101:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\areas-list.tsx
  40:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\components\area-form.tsx
   42:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars
  155:16  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\components\areas-columns.tsx
  24:37  error    Unexpected any. Specify a different type                                                                @typescript-eslint/no-explicit-any
  29:5   warning  Unexpected console statement                                                                            no-console
  34:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  38:5   warning  Unexpected console statement                                                                            no-console
  60:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  64:5   warning  Unexpected console statement                                                                            no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\components\areas-table-header.tsx
  25:30  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\components\areas-table-row.tsx
  35:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\components\import-areas-modal.tsx
  71:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\areas\hooks\use-areas-excel-parser.ts
  54:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\crm\general-setups\store-menu\index.tsx
  19:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\devices\detail\components\cancel-device-modal.tsx
  30:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\devices\detail\components\device-form.tsx
   85:58  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   99:53  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  105:48  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  113:48  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  123:46  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  156:40  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  157:7   warning  Unexpected console statement                           no-console
  193:31  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  218:43  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  220:5   warning  Unexpected console statement                           no-console
  224:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  295:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  306:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  342:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  354:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  366:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  378:41  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  456:1   error    File has too many lines (750). Maximum allowed is 389  max-lines
  596:27  warning  Unexpected console statement                           no-console
  661:33  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  678:36  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\devices\detail\components\order-log-modal.tsx
   46:54  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   73:36  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   81:49  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  169:53  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\devices\detail\components\printer-modal.tsx
  137:9  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\devices\list\components\index.tsx
  1:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  2:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  3:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  4:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\employee\detail\components\index.tsx
  2:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  5:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  8:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\employee\detail\hooks\use-create-employee-form.ts
  142:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\employee\list\components\index.tsx
  2:10  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\employee\list\index.tsx
  101:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\employee\role\components\index.tsx
  1:10  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-brand\detail\components\category-form\index.tsx
  55:9   warning  The 'printerPositions' conditional could make the dependencies of useEffect Hook (at line 103) change on every render. To fix this, wrap the initialization of 'printerPositions' in its own useMemo() Hook  react-hooks/exhaustive-deps
  58:3   warning  Unexpected console statement                                                                                                                                                                                 no-console
  84:27  error    Unexpected any. Specify a different type                                                                                                                                                                     @typescript-eslint/no-explicit-any
  91:24  error    Unexpected any. Specify a different type                                                                                                                                                                     @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-brand\detail\components\category-form\item-selection-modal.tsx
  34:3  warning  Unexpected console statement  no-console
  35:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-brand\detail\components\category-form\printer-selection-modal.tsx
   95:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  137:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-brand\hooks\use-categories-import.ts
   55:27  error  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
   56:24  error  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  100:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars
  124:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-store\detail\components\category-form\index.tsx
  64:9   warning  The 'printerPositions' conditional could make the dependencies of useEffect Hook (at line 105) change on every render. To fix this, wrap the initialization of 'printerPositions' in its own useMemo() Hook  react-hooks/exhaustive-deps
  85:27  error    Unexpected any. Specify a different type                                                                                                                                                                     @typescript-eslint/no-explicit-any
  92:24  error    Unexpected any. Specify a different type                                                                                                                                                                     @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-store\detail\components\category-form\item-selection-modal.tsx
  34:3  warning  Unexpected console statement  no-console
  35:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-store\detail\components\category-form\printer-selection-modal.tsx
   95:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  137:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\categories\categories-in-store\hooks\use-categories-import.ts
   55:27  error  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
   56:24  error  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  100:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars
  124:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\customization\customization-in-city\detail\edit\index.tsx
   90:6  warning  React Hook useEffect has missing dependencies: 'customizationForm' and 'groupManagement'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  123:6  warning  React Hook useEffect has missing dependencies: 'dishSelection' and 'groupManagement'. Either include them or remove the dependency array      react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\customization\customization-in-city\detail\index.tsx
   92:6  warning  React Hook useEffect has missing dependencies: 'customizationForm' and 'groupManagement'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  125:6  warning  React Hook useEffect has missing dependencies: 'dishSelection' and 'groupManagement'. Either include them or remove the dependency array      react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\customization\customization-in-store\detail\edit\index.tsx
  105:6  warning  React Hook useEffect has missing dependencies: 'customizationForm' and 'groupManagement'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  142:6  warning  React Hook useEffect has missing dependencies: 'dishSelection' and 'groupManagement'. Either include them or remove the dependency array      react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\customization\customization-in-store\hooks\use-customization-copy.ts
  37:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\customization\customization-in-store\hooks\use-customization-delete.ts
  14:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\item-class\components\index.tsx
  1:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  2:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  3:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  4:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  5:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\item-class\components\item-selection-modal.tsx
  34:3  warning  Unexpected console statement  no-console
  35:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\item-removed\item-removed-in-store\index.tsx
  332:11  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-city\components\customization-dialog.tsx
  116:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-city\components\item-configuration.tsx
  415:1  error  File has too many lines (396). Maximum allowed is 389  max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-city\components\items-in-city-mutate.tsx
  240:9   warning  Unexpected console statement                           no-console
  385:9   warning  Unexpected console statement                           no-console
  388:13  warning  Unexpected console statement                           no-console
  393:13  warning  Unexpected console statement                           no-console
  397:9   warning  Unexpected console statement                           no-console
  400:13  warning  Unexpected console statement                           no-console
  405:13  warning  Unexpected console statement                           no-console
  410:7   warning  Unexpected console statement                           no-console
  417:1   error    File has too many lines (471). Maximum allowed is 389  max-lines
  440:17  warning  Unexpected console statement                           no-console
  441:17  warning  Unexpected console statement                           no-console
  442:17  warning  Unexpected console statement                           no-console
  443:17  warning  Unexpected console statement                           no-console
  447:17  warning  Unexpected console statement                           no-console
  448:17  warning  Unexpected console statement                           no-console
  452:19  warning  Unexpected console statement                           no-console
  458:19  warning  Unexpected console statement                           no-console
  459:19  warning  Unexpected console statement                           no-console
  460:19  warning  Unexpected console statement                           no-console
  461:19  warning  Unexpected console statement                           no-console
  462:19  warning  Unexpected console statement                           no-console
  463:19  warning  Unexpected console statement                           no-console
  464:19  warning  Unexpected console statement                           no-console
  465:19  warning  Unexpected console statement                           no-console
  466:19  warning  Unexpected console statement                           no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-city\hooks\items-in-city-api.ts
  395:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-city\index.tsx
  27:50  error    Unexpected any. Specify a different type                                                                           @typescript-eslint/no-explicit-any
  57:6   warning  React Hook useMemo has an unnecessary dependency: 'currentPage'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\items\items-in-store\context\index.tsx
  40:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\schedule\components\menu-schedule-data-table.tsx
  77:38  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\menu\schedule\context\index.tsx
  36:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\order-sources\components\data-tables\data-table.tsx
  14:10  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\order-sources\components\store-selection-modal.tsx
  45:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\payment-methods\components\payment-method-detail-form.tsx
  41:3   warning  Unexpected console statement              no-console
  47:3   warning  Unexpected console statement              no-console
  74:50  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\payment-methods\components\payment-method-stores-modal.tsx
  38:5  error  The following dependencies are missing in your queryKey: storeUids  @tanstack/query/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\payment-methods\components\payment-methods-table-row.tsx
  54:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\payment-methods\components\store-selection-modal.tsx
  45:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\reports\invoice\bang-ke-chi-tiet-hoa-don-ban-hang\components\invoice-list.tsx
  52:6  warning  React Hook useMemo has unnecessary dependencies: 'forceRender' and 'selectedBrand.id'. Either exclude them or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\components\copy-channel-modal.tsx
  131:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\components\data-tables\delete-channel-button.tsx
  55:7  warning  Unexpected console statement  no-console
  57:9  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\components\index.tsx
  2:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  5:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components
  8:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\components\modals\import-sale-channel-modal.tsx
  83:7  warning  Unexpected console statement  no-console
  98:9  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\detail\hooks\use-channel-save.ts
  200:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\detail\index.tsx
  13:1   warning  This rule can't verify that `export *` only exports components                    react-refresh/only-export-components
  25:12  error    'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars
  45:12  error    Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\hooks\use-import-sale-channel.ts
  140:43  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  242:9   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\channel\utils\excel-utils.ts
   19:63  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   76:29  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  105:29  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  165:29  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  258:38  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:35  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  428:29  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\discount\components\copy-discount-modal.tsx
  98:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\discount\components\discount-data-table.tsx
  59:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\discount\components\discount-form\components\add-modal.tsx
  193:41  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\discount\components\discount-form\hooks\use-discount-form.ts
  131:6  warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale-channel\discount\components\discount-form\stores\discount-form-context.tsx
  26:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  34:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  39:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  44:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-columns.tsx
  54:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  73:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-form\components\add-modal.tsx
  193:41  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-form\hooks\use-combo-form.ts
  126:6  warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-form\stores\combo-form-context.tsx
  26:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  34:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  39:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  44:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-import-preview-dialog.tsx
  198:16  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\combo-list.tsx
  74:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\copy-combo-modal.tsx
   97:49  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  103:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\export-combo-dialog.tsx
   41:50  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   70:48  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   93:9   warning  Unexpected console statement                           no-console
  113:42  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  117:54  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  145:62  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  217:7   warning  Unexpected console statement                           no-console
  233:42  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  247:38  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  254:20  error    Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  261:13  warning  Unexpected console statement                           no-console
  375:7   warning  Unexpected console statement                           no-console
  462:1   error    File has too many lines (423). Maximum allowed is 389  max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\components\import-combo-dialog.tsx
  36:50  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  55:9   warning  Unexpected console statement              no-console
  58:46  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  78:9   warning  Unexpected console statement              no-console
  84:9   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\combo\index.tsx
  1:1  warning  This rule can't verify that `export *` only exports components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\CopyDiscountPaymentModal.tsx
   84:7   warning  Unexpected console statement  no-console
   91:9   warning  Unexpected console statement  no-console
  100:11  warning  Unexpected console statement  no-console
  102:11  warning  Unexpected console statement  no-console
  104:11  warning  Unexpected console statement  no-console
  111:7   warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\components\add-modal.tsx
  193:41  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\components\customization-section.tsx
  27:43  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\components\header-form.tsx
  19:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  38:5   warning  Unexpected console statement              no-console
  41:67  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  45:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\components\promotion-selector.tsx
  23:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\hooks\use-discount-form.ts
   55:3   warning  Unexpected console statement                                                                                                   no-console
   96:3   warning  Unexpected console statement                                                                                                   no-console
  120:48  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  165:7   warning  Unexpected console statement                                                                                                   no-console
  167:7   warning  Unexpected console statement                                                                                                   no-console
  170:6   warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  262:5   warning  Unexpected console statement                                                                                                   no-console
  265:33  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  267:33  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount-payment\components\payment-discount-form\stores\discount-form-context.tsx
  33:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  41:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  46:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  51:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  56:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\copy-discount-modal.tsx
  85:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\components\add-modal.tsx
  193:41  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\components\customization-section.tsx
  27:43  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\components\header-form.tsx
  23:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  42:5   warning  Unexpected console statement              no-console
  45:67  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  49:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\components\promotion-selector.tsx
  23:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\hooks\use-discount-form.ts
   57:3   warning  Unexpected console statement                                                                                                   no-console
  102:3   warning  Unexpected console statement                                                                                                   no-console
  174:7   warning  Unexpected console statement                                                                                                   no-console
  176:7   warning  Unexpected console statement                                                                                                   no-console
  179:6   warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  285:5   warning  Unexpected console statement                                                                                                   no-console
  288:33  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  290:33  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\membership\components\membership-discount-form\stores\discount-form-context.tsx
  33:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  41:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  46:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  51:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  56:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\regular\components\copy-discount-modal.tsx
  99:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\regular\components\regular-discount-form\components\add-modal.tsx
  193:41  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:33  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\regular\components\regular-discount-form\components\promotion-selector.tsx
  24:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\regular\components\regular-discount-form\hooks\use-discount-form.ts
  136:6  warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  184:5  warning  Unexpected console statement                                                                                                   no-console
  185:5  warning  Unexpected console statement                                                                                                   no-console
  186:5  warning  Unexpected console statement                                                                                                   no-console
  189:7  warning  Unexpected console statement                                                                                                   no-console
  201:7  warning  Unexpected console statement                                                                                                   no-console
  215:3  warning  Unexpected console statement                                                                                                   no-console
  216:3  warning  Unexpected console statement                                                                                                   no-console
  217:3  warning  Unexpected console statement                                                                                                   no-console
  218:3  warning  Unexpected console statement                                                                                                   no-console
  219:3  warning  Unexpected console statement                                                                                                   no-console
  220:3  warning  Unexpected console statement                                                                                                   no-console
  221:3  warning  Unexpected console statement                                                                                                   no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\discount\regular\components\regular-discount-form\stores\discount-form-context.tsx
  33:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  41:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  46:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  51:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  56:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\promotion\components\promotion-data-table.tsx
  75:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\CopyServiceChargeModal.tsx
  95:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\components\add-modal.tsx
  178:37  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  186:31  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  195:35  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\components\customization-section.tsx
  27:43  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\components\header-form.tsx
  47:77  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  50:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\components\service-charge-information-section.tsx
  57:43  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\hooks\use-service-charge-form.ts
   94:48  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  151:6   warning  React Hook useEffect has a missing dependency: 'transformApiDataToFormData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  258:25  error    Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\sale\service-charge\components\service-charge-form\stores\service-charge-form-context.tsx
  26:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  34:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  39:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  44:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\printer-position\printer-position-in-brand\hooks\use-printer-position-detail.ts
  33:5  error  The following dependencies are missing in your queryKey: params  @tanstack/query/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\printer-position\printer-position-in-brand\hooks\use-update-printer-position.ts
  20:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\components\data-tables\stores-columns.tsx
   27:31  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   43:37  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  103:37  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  133:37  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\components\sections\basic-info\location-section.tsx
  34:3  warning  Unexpected console statement  no-console
  42:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\components\sections\order-source\order-source-selection-modal.tsx
  118:51  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  153:53  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\components\ui\address-autocomplete.tsx
  52:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\components\ui\openstreet-map-picker.tsx
    9:37  error    Unexpected any. Specify a different type                                                                               @typescript-eslint/no-explicit-any
   52:7   warning  Unexpected console statement                                                                                           no-console
   73:7   warning  Unexpected console statement                                                                                           no-console
   90:11  warning  Unexpected console statement                                                                                           no-console
   98:7   warning  Unexpected console statement                                                                                           no-console
  112:6   warning  React Hook useEffect has a missing dependency: 'getCurrentLocation'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\hooks\use-order-source-selection.ts
   14:12  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   77:26  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   78:23  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  100:30  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  115:59  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\hooks\use-separated-sources.ts
   2:12  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   7:20  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   8:22  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  16:51  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  20:53  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\hooks\use-store-data.ts
  279:5  warning  Object rest destructuring on a query will observe all changes to the query, leading to excessive re-renders  @tanstack/query/no-rest-destructuring

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\index.tsx
  36:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\detail\utils\store-mapper.ts
  428:1  error  File has too many lines (416). Maximum allowed is 389  max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\setting\store\hooks\use-store-list.ts
  12:11  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  13:19  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  21:10  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\create-table-form.tsx
   84:14  error    'error' is defined but never used. Allowed unused caught errors must match /^_/u                                                            @typescript-eslint/no-unused-vars
  143:40  error    Unexpected any. Specify a different type                                                                                                    @typescript-eslint/no-explicit-any
  144:43  error    Unexpected any. Specify a different type                                                                                                    @typescript-eslint/no-explicit-any
  145:75  error    Unexpected any. Specify a different type                                                                                                    @typescript-eslint/no-explicit-any
  160:59  error    Unexpected any. Specify a different type                                                                                                    @typescript-eslint/no-explicit-any
  181:6   warning  React Hook useEffect has missing dependencies: 'areaData' and 'formData.selectedItems'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  197:35  error    Unexpected any. Specify a different type                                                                                                    @typescript-eslint/no-explicit-any
  456:1   error    File has too many lines (536). Maximum allowed is 389                                                                                       max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\import-tables-modal.tsx
  71:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\tables-columns.tsx
  22:37  error    Unexpected any. Specify a different type                                                                @typescript-eslint/no-explicit-any
  27:5   warning  Unexpected console statement                                                                            no-console
  32:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  36:5   warning  Unexpected console statement                                                                            no-console
  58:10  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  72:7   warning  Unexpected console statement                                                                            no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\tables-data-table.tsx
  92:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\tables-table-header.tsx
  25:30  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\components\tables-table-row.tsx
  41:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\hooks\use-tables-excel-parser.ts
  54:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\features\tables\tables-list.tsx
   44:7  warning  Unexpected console statement  no-console
  107:5  warning  Unexpected console statement  no-console
  112:5  warning  Unexpected console statement  no-console
  117:5  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-areas.ts
   34:5   error    The following dependencies are missing in your queryKey: company.id, selectedBrand.id                        @tanstack/query/exhaustive-deps
   66:5   warning  Object rest destructuring on a query will observe all changes to the query, leading to excessive re-renders  @tanstack/query/no-rest-destructuring
   90:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  131:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  175:7   warning  Unexpected console statement                                                                                 no-console
  176:7   warning  Unexpected console statement                                                                                 no-console
  179:7   warning  Unexpected console statement                                                                                 no-console
  190:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  223:7   warning  Unexpected console statement                                                                                 no-console
  234:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  251:5   error    The following dependencies are missing in your queryKey: company.id, selectedBrand.id                        @tanstack/query/exhaustive-deps
  334:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  384:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-channels.ts
  48:52  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-cities.ts
  24:58  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  35:63  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-combos.ts
  142:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  186:38  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  199:32  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  208:7   warning  Unexpected console statement              no-console
  216:60  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  220:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  236:57  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  240:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  258:31  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-devices.ts
  143:3  warning  Unexpected console statement  no-console
  144:3  warning  Unexpected console statement  no-console
  145:3  warning  Unexpected console statement  no-console
  160:7  warning  Unexpected console statement  no-console
  162:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-discount-payment.ts
   34:39  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   41:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   42:7   warning  Unexpected console statement              no-console
   58:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   59:7   warning  Unexpected console statement              no-console
  102:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  103:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-discount.ts
  118:87  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  151:5   error  The following dependencies are missing in your queryKey: company.id, selectedBrand.id  @tanstack/query/exhaustive-deps
  209:14  error  'error' is defined but never used. Allowed unused caught errors must match /^_/u       @typescript-eslint/no-unused-vars

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-item-types.ts
  60:9  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-order-sources.ts
   23:5   error  The following dependencies are missing in your queryKey: company.id, selectedBrand.id  @tanstack/query/exhaustive-deps
   52:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
   70:5   error  The following dependencies are missing in your queryKey: company.id, selectedBrand.id  @tanstack/query/exhaustive-deps
  114:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  155:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  193:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  243:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-payment-discounts.ts
   70:30  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   71:7   warning  Unexpected console statement              no-console
   84:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   85:7   warning  Unexpected console statement              no-console
   97:30  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   98:7   warning  Unexpected console statement              no-console
  111:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  112:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-payment-methods.ts
  148:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  193:32  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  229:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  268:22  error  Unexpected any. Specify a different type                                               @typescript-eslint/no-explicit-any
  286:5   error  The following dependencies are missing in your queryKey: company.id, selectedBrand.id  @tanstack/query/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-printer-positions.ts
  16:20  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-printers.ts
  16:5  error  The following dependencies are missing in your queryKey: params.results_per_page  @tanstack/query/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-regular-discounts.ts
  171:5  error  The following dependencies are missing in your queryKey: company.id, selectedBrand.id  @tanstack/query/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-service-charge.ts
   65:37  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   72:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   73:7   warning  Unexpected console statement              no-console
   83:37  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   90:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   91:7   warning  Unexpected console statement              no-console
  108:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  109:7   warning  Unexpected console statement              no-console
  230:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  231:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-stores.ts
  53:24  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-tables.ts
   40:5   error    The following dependencies are missing in your queryKey: company.id, selectedBrand.id                        @tanstack/query/exhaustive-deps
   65:5   warning  Object rest destructuring on a query will observe all changes to the query, leading to excessive re-renders  @tanstack/query/no-rest-destructuring
   89:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  116:39  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  117:43  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  154:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  188:7   warning  Unexpected console statement                                                                                 no-console
  199:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  216:5   error    The following dependencies are missing in your queryKey: company.id, selectedBrand.id                        @tanstack/query/exhaustive-deps
  309:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  359:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  399:22  error    Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-update-customization.ts
  33:7  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\api\use-vietqr.ts
  37:22  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  38:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\use-deleted-orders.ts
  243:5  warning  React Hook useCallback has an unnecessary dependency: 'currentBrandStores'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\use-revenue-data.ts
   65:6  warning  React Hook useMemo has a missing dependency: 'customDateRange'. Either include it or remove the dependency array                                                                              react-hooks/exhaustive-deps
   71:6  warning  React Hook useMemo has a missing dependency: 'storeIds'. Either include it or remove the dependency array                                                                                     react-hooks/exhaustive-deps
   71:7  warning  React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked                                                        react-hooks/exhaustive-deps
  143:6  warning  React Hook useCallback has missing dependencies: 'activeStores', 'company', 'memoizedDateRange', 'memoizedStoreIds', and 'selectedBrand'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  149:5  warning  React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked                                                    react-hooks/exhaustive-deps
  159:6  warning  React Hook useEffect has missing dependencies: 'company', 'fetchRevenue', and 'selectedBrand'. Either include them or remove the dependency array                                             react-hooks/exhaustive-deps
  166:5  warning  React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked                                                      react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\hooks\use-top-deleted-stores.ts
  303:5  warning  React Hook useCallback has a missing dependency: 'storesToFetch'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\areas-api.ts
    8:16  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  117:5   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\channels-api.ts
  171:23  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  178:7   warning  Unexpected console statement              no-console
  183:7   warning  Unexpected console statement              no-console
  188:5   warning  Unexpected console statement              no-console
  189:5   warning  Unexpected console statement              no-console
  195:5   warning  Unexpected console statement              no-console
  197:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  198:5   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\combos-api.ts
   71:22  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  195:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  195:48  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  203:42  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  203:58  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  232:60  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  232:80  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  232:86  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\devices-api.ts
  370:3  warning  Unexpected console statement                           no-console
  383:3  warning  Unexpected console statement                           no-console
  480:1  error    File has too many lines (399). Maximum allowed is 389  max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\discount-payment-api.ts
   68:23   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   69:9    warning  Unexpected console statement              no-console
   96:54   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  102:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  103:7    warning  Unexpected console statement              no-console
  122:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  123:7    warning  Unexpected console statement              no-console
  146:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  147:7    warning  Unexpected console statement              no-console
  174:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  175:7    warning  Unexpected console statement              no-console
  200:7    warning  Unexpected console statement              no-console
  201:7    warning  Unexpected console statement              no-console
  205:7    warning  Unexpected console statement              no-console
  209:62   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  211:40   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  218:7    warning  Unexpected console statement              no-console
  220:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  221:7    warning  Unexpected console statement              no-console
  229:107  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  239:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  240:7    warning  Unexpected console statement              no-console
  248:54   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  254:21   error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  255:7    warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\discounts-api.ts
  117:3   warning  Unexpected console statement              no-console
  159:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  227:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  247:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\item-categories-api.ts
   50:30  error  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  552:1   error  File has too many lines (400). Maximum allowed is 389  max-lines

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\membership-discounts-api.ts
   49:5   warning  Unexpected console statement              no-console
   61:5   warning  Unexpected console statement              no-console
   70:5   warning  Unexpected console statement              no-console
   79:5   warning  Unexpected console statement              no-console
  105:5   warning  Unexpected console statement              no-console
  127:5   warning  Unexpected console statement              no-console
  128:5   warning  Unexpected console statement              no-console
  132:5   warning  Unexpected console statement              no-console
  142:5   warning  Unexpected console statement              no-console
  146:5   warning  Unexpected console statement              no-console
  170:5   warning  Unexpected console statement              no-console
  171:5   warning  Unexpected console statement              no-console
  175:5   warning  Unexpected console statement              no-console
  185:5   warning  Unexpected console statement              no-console
  189:5   warning  Unexpected console statement              no-console
  214:5   warning  Unexpected console statement              no-console
  215:5   warning  Unexpected console statement              no-console
  219:5   warning  Unexpected console statement              no-console
  223:60  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  225:38  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  232:5   warning  Unexpected console statement              no-console
  235:5   warning  Unexpected console statement              no-console
  265:5   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\order-logs-api.ts
  44:17  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\order-sources-api.ts
  74:53  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\payment-methods-api.ts
  113:5   warning  Unexpected console statement              no-console
  118:5   warning  Unexpected console statement              no-console
  127:5   warning  Unexpected console statement              no-console
  142:37  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  181:68  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  198:14  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\printers-api.ts
  41:23  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  82:22  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\regular-discounts-api.ts
  152:3   warning  Unexpected console statement              no-console
  189:5   warning  Unexpected console statement              no-console
  190:5   warning  Unexpected console statement              no-console
  191:5   warning  Unexpected console statement              no-console
  217:5   warning  Unexpected console statement              no-console
  221:5   warning  Unexpected console statement              no-console
  226:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  227:5   warning  Unexpected console statement              no-console
  287:19  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  314:3   warning  Unexpected console statement              no-console
  315:3   warning  Unexpected console statement              no-console
  319:3   warning  Unexpected console statement              no-console
  330:3   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\service-charge-api.ts
   72:23  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   73:9   warning  Unexpected console statement              no-console
  114:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  115:7   warning  Unexpected console statement              no-console
  123:50  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  129:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  130:7   warning  Unexpected console statement              no-console
  138:50  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  144:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  145:7   warning  Unexpected console statement              no-console
  164:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  165:7   warning  Unexpected console statement              no-console
  190:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  191:7   warning  Unexpected console statement              no-console
  216:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  217:7   warning  Unexpected console statement              no-console
  240:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  241:7   warning  Unexpected console statement              no-console
  266:21  error    Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  267:7   warning  Unexpected console statement              no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\lib\tables-api.ts
   9:18  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  34:18  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  56:16  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\routes\_authenticated\sale\discount-payment\detail\$id.tsx
  20:3  warning  Unexpected console statement  no-console
  21:3  warning  Unexpected console statement  no-console
  22:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\routes\_authenticated\sale\discount\membership\detail\$id.tsx
  20:3  warning  Unexpected console statement  no-console
  21:3  warning  Unexpected console statement  no-console
  22:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\routes\_authenticated\sale\service-charge\detail\$id.tsx
  12:3  warning  Unexpected console statement  no-console
  13:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\routes\_authenticated\setting\area\detail\$areaId.tsx
  20:3  warning  Unexpected console statement  no-console
  21:3  warning  Unexpected console statement  no-console
  22:3  warning  Unexpected console statement  no-console
  23:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\routes\_authenticated\setting\table\detail\$areaId.tsx
  20:3  warning  Unexpected console statement  no-console
  21:3  warning  Unexpected console statement  no-console
  22:3  warning  Unexpected console statement  no-console
  23:3  warning  Unexpected console statement  no-console

D:\StudyWork\TTMI\POS-iPOS\POS\src\types\api\combo-types.ts
   29:10  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   85:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  110:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  116:18  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\types\api\discount-types.ts
   29:10  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   85:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  110:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  116:18  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\types\combos.ts
  50:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  76:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  82:18  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

D:\StudyWork\TTMI\POS-iPOS\POS\src\types\discounts.ts
   50:32  error  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   76:32  error  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   82:18  error  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  402:32  error  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  412:1   error  File has too many lines (392). Maximum allowed is 389  max-lines

✖ 602 problems (273 errors, 329 warnings)

 ELIFECYCLE  Command failed with exit code 1.
husky - pre-commit script failed (code 1)
