import { apiClient } from './api/pos/pos-api'

// Types
export interface PrinterPosition {
  id: string
  created_at: string
  created_by: string
  updated_at: string
  updated_by: string
  deleted: boolean
  deleted_by: string | null
  deleted_at: string | null
  printer_position_name: string
  printer_position_id: string
  list_item_type_id: string
  revision: number
  brand_uid: string
  company_uid: string
  sort: number
  list_item_id: string
  store_uid: string | null
  area_ids: string
  sources: string
  apply_with_store: number
}

export interface PrinterPositionsApiParams {
  company_uid: string
  brand_uid: string
  results_per_page?: number
}

export interface PrinterPositionsApiResponse {
  data: PrinterPosition[]
  track_id: string
}

// Helper functions for managing comma-separated category lists
export const categoryListHelpers = {
  /**
   * Add a category ID to a comma-separated list if it doesn't already exist
   */
  addCategoryToList: (currentList: string, categoryId: string): string => {
    if (!categoryId.trim()) return currentList

    const categories = currentList
      ? currentList
          .split(',')
          .map(id => id.trim())
          .filter(Boolean)
      : []

    if (!categories.includes(categoryId)) {
      categories.push(categoryId)
    }

    return categories.join(',')
  },

  /**
   * Remove a category ID from a comma-separated list
   */
  removeCategoryFromList: (currentList: string, categoryId: string): string => {
    if (!categoryId.trim() || !currentList) return currentList

    const categories = currentList
      .split(',')
      .map(id => id.trim())
      .filter(Boolean)
    const filteredCategories = categories.filter(id => id !== categoryId)

    return filteredCategories.join(',')
  }
}

// API Functions
export const printerPositionsApi = {
  /**
   * Fetch printer positions from POS API
   */
  fetchPrinterPositions: async (params: PrinterPositionsApiParams): Promise<PrinterPositionsApiResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      results_per_page: (params.results_per_page || 15000).toString()
    })

    const response = await apiClient.get<PrinterPositionsApiResponse>(
      `/v3/pos-cms/printer-position?${queryParams.toString()}`
    )

    return response.data
  },

  /**
   * Update a printer position via POST request
   */
  updatePrinterPosition: async (printerPosition: PrinterPosition): Promise<PrinterPosition> => {
    const response = await apiClient.post<{ data: PrinterPosition }>(`/v3/pos-cms/printer-position`, printerPosition, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      }
    })

    return response.data.data
  }
}
