import { createFileRoute } from '@tanstack/react-router'

import { ServiceChargeForm } from '@/features/sale/service-charge/components/service-charge-form'

export const Route = createFileRoute('/_authenticated/sale/service-charge/detail/$id')({
  component: EditServiceChargePage
})

function EditServiceChargePage() {
  const { id } = Route.useParams()

  console.log('🔥 Service Charge Detail Page - URL Params:')
  console.log('🔥 serviceChargeId:', id)

  return <ServiceChargeForm serviceChargeId={id} />
}
