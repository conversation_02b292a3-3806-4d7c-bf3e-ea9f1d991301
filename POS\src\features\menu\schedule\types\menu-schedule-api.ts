export interface ItemScheduleData {
  id: string
  sort: number
  is_fc: number
  point: number
  active: number
  is_kit: number
  is_sub: number
  ta_tax: number
  deleted: boolean
  is_fabi: number
  is_gift: number
  item_id: string
  ots_tax: number
  user_id: string
  city_uid: string
  revision: number
  ta_price: number
  unit_uid: string
  brand_uid: string
  is_parent: number
  item_name: string
  ots_price: number
  store_uid: string
  cost_price: number
  created_at: number
  created_by: string
  deleted_at: number | null
  deleted_by: string | null
  extra_data: {
    is_buffet_item: number
    up_size_buffet: unknown[]
    is_item_service: number
    is_virtual_item: number
    price_by_source: unknown[]
    enable_edit_price: number
    exclude_items_buffet: unknown[]
    no_update_quantity_toping: number
  }
  image_path: string
  is_foreign: number
  is_service: number
  item_color: string
  list_order: number
  source_uid: string | null
  updated_at: number
  updated_by: string
  company_uid: string
  description: string
  expire_date: number
  is_eat_with: number
  is_material: number
  show_on_web: number
  price_change: number
  time_cooking: number
  item_type_uid: string
  process_index: number
  effective_date: number
  is_print_label: number
  item_class_uid: string | null
  quantity_limit: number
  allow_take_away: number
  item_id_barcode: string
  item_id_mapping: string
  apply_with_store: number
  currency_type_id: string
  image_path_thumb: string | null
  item_id_eat_with: string
  quantity_default: number
  quantity_per_day: number
  customization_uid: string | null
  is_allow_discount: number
  show_price_on_web: number
  time_sale_hour_day: number
  unit_secondary_uid: string | null
  time_sale_date_week: number
}

export interface ItemScheduleResponse {
  id: string
  item_id: string
  item_old_uid: string | null
  item_new_uid: string | null
  type: string
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  partner: string
  time: number
  end_time: number | null
  user_info: Record<string, unknown>
  changed_data: ItemScheduleData
  data_old: Record<string, unknown>
  error: string
  status: string
  store_uid: string
  city_uid: string
  brand_uid: string
  company_uid: string
  created_by: string
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
}

export interface MenuScheduleApiResponse {
  data: {
    item_schedules: ItemScheduleResponse[]
    company_uid: string
    brand_uid: string
    store_uid: string
    city_uid: string
    time: number
    end_time: number | null
    deleted: boolean
    status: string
  }
  track_id: string
}

export interface MenuItem {
  id: string
  code: string
  name: string
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  originalItem?: ItemScheduleData
  scheduleId?: string
  item_class_uid?: string
  item_type_uid?: string
  unit_uid?: string
  unit_secondary_uid?: string
  ots_price?: number
  customization_uid?: string
  vat_rate?: number
  cooking_time?: number
  allow_edit_price?: boolean
  require_quantity_input?: boolean
  allow_remove_without_permission?: boolean
  is_featured?: boolean
  is_buffet?: boolean
  is_service?: boolean
  display_order?: number
  time_sale_date_week?: number
  time_sale_hour_day?: number
}

export interface MenuScheduleWithItems {
  company_uid: string
  brand_uid: string
  store_uid: string
  city_uid: string
  time: number
  end_time: number | null
  status?: 'PENDING' | 'PROCESS' | 'DONE'
  menuItems: MenuItem[]
}

export interface UpdateItemSchedulePayload {
  company_uid: string
  brand_uid: string
  type: string
  id: string
  item_id: string
  item_old_uid: string | null
  item_new_uid: string | null
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  partner: string
  time: number
  end_time: number | null
  user_info: Record<string, unknown>
  changed_data: ItemScheduleData
  data_old: Record<string, unknown>
  error: string
  status: string
  store_uid: string
  city_uid: string
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  item_uid: string
}
