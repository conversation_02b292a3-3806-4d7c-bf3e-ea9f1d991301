import { useEffect, useState } from 'react'

import { Search, X } from 'lucide-react'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useCitiesData, useItemsData, useItemTypesData, useSourcesData, useStoresData } from '@/hooks/api'
import { useAreasData } from '@/hooks/api/use-areas'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { PrinterPositionInStore } from '../data'
import {
  useCreatePrinterPositionInStore,
  usePrinterPositionInStoreDetail,
  useUpdatePrinterPositionInStore
} from '../hooks'
import { AreaSelectionDialog } from './area-selection-dialog'
import { CategorySelectionSection } from './category-selection-section'
import { ItemSelectionSection } from './item-selection-section'
import { OrderSourceSelectionDialog } from './order-source-selection-dialog'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: PrinterPositionInStore
}

export function PrinterPositionInStoreMutate({ open, onOpenChange, currentRow }: Props) {
  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()
  const isUpdate = !!currentRow

  const createPrinterPositionMutation = useCreatePrinterPositionInStore()
  const updatePrinterPositionMutation = useUpdatePrinterPositionInStore()

  const { data: printerPositionDetail } = usePrinterPositionInStoreDetail(
    currentRow?.id || '',
    isUpdate && !!currentRow?.id
  )

  const [selectedCityUid, setSelectedCityUid] = useState<string>('')
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [itemScope, setItemScope] = useState<'city' | 'store'>('city')

  const { data: sourcesData } = useSourcesData()
  const { data: itemTypesData, isLoading: isLoadingItemTypes } = useItemTypesData({
    store_uid: printerPositionDetail?.store_uid || undefined,
    skip_limit: true,
    active: 1
  })

  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData()

  const getItemsParams = () => {
    if (itemScope === 'city') {
      return {
        city_uid: selectedCityUid || undefined,
        skip_limit: true,
        active: 1
      }
    } else {
      return {
        list_store_uid: selectedStoreUid || undefined,
        skip_limit: true,
        active: 1
      }
    }
  }

  const { data: itemsData, isLoading: isLoadingItems } = useItemsData({
    params: getItemsParams(),
    enabled: itemScope === 'city' ? !!selectedCityUid : !!selectedStoreUid
  })

  const [showOrderSourceDialog, setShowOrderSourceDialog] = useState(false)
  const [showAreaDialog, setShowAreaDialog] = useState(false)
  const [itemTypeSearchTerm, setItemTypeSearchTerm] = useState('')

  const filteredItemTypes = (itemTypesData || []).filter(itemType =>
    itemType.item_type_name.toLowerCase().includes(itemTypeSearchTerm.toLowerCase())
  )

  const [formData, setFormData] = useState({
    printerPositionName: '',
    applyToAllCategories: false,
    selectedCategories: [] as string[],
    selectedItems: [] as string[],
    applicationType: 'category' as 'category' | 'item',
    selectedOrderSources: [] as string[],
    selectedStoreUids: [] as string[],
    selectedAreaIds: [] as string[]
  })

  useEffect(() => {
    if (open && !currentRow) {
      setFormData({
        printerPositionName: '',
        applyToAllCategories: false,
        selectedCategories: [],
        selectedItems: [],
        applicationType: 'category',
        selectedOrderSources: [],
        selectedStoreUids: [],
        selectedAreaIds: []
      })
    }
  }, [open, currentRow])

  useEffect(() => {
    if (open && isUpdate && printerPositionDetail && itemTypesData && !isLoadingItemTypes) {
      const categoryIdsFromApi = printerPositionDetail.list_item_type_id
        ? printerPositionDetail.list_item_type_id.split(',').filter(Boolean)
        : []

      const mappedCategoryIds = categoryIdsFromApi
        .map(categoryIdOrName => {
          const foundById = itemTypesData.find(itemType => itemType.id === categoryIdOrName)
          if (foundById) return foundById.id

          const foundByItemTypeId = itemTypesData.find(itemType => itemType.item_type_id === categoryIdOrName)
          if (foundByItemTypeId) return foundByItemTypeId.id

          const foundByName = itemTypesData.find(
            itemType => itemType.item_type_name.toLowerCase() === categoryIdOrName.toLowerCase()
          )
          if (foundByName) return foundByName.id

          return categoryIdOrName
        })
        .filter(Boolean)

      const sources = printerPositionDetail.sources ? printerPositionDetail.sources.split(',').filter(Boolean) : []

      const allCategoriesExceptUncategory = (itemTypesData || [])
        .filter(itemType => itemType.item_type_id !== 'ITEM_TYPE_OTHER')
        .map(itemType => itemType.id)

      const isAllSelected =
        allCategoriesExceptUncategory.length > 0 &&
        allCategoriesExceptUncategory.every(id => mappedCategoryIds.includes(id))

      const itemIdsFromApi = printerPositionDetail.list_item_id
        ? printerPositionDetail.list_item_id.split(',').filter(Boolean)
        : []

      const mappedItemIds = itemIdsFromApi
        .map(itemIdOrName => {
          if (!itemsData) return itemIdOrName

          const foundById = itemsData.find(item => item.id === itemIdOrName)
          if (foundById) return foundById.id

          const foundByItemId = itemsData.find(item => item.item_id === itemIdOrName)
          if (foundByItemId) return foundByItemId.id

          const foundByName = itemsData.find(item => item.item_name.toLowerCase() === itemIdOrName.toLowerCase())
          if (foundByName) return foundByName.id

          return itemIdOrName
        })
        .filter(Boolean)

      const applicationType = mappedItemIds.length > 0 ? 'item' : 'category'

      setFormData(prevData => {
        const shouldPreserveItemMode = prevData.applicationType === 'item' && mappedItemIds.length === 0

        return {
          printerPositionName: printerPositionDetail.printer_position_name || '',
          applyToAllCategories: isAllSelected,
          selectedCategories: mappedCategoryIds,
          selectedItems: mappedItemIds,
          applicationType: shouldPreserveItemMode ? 'item' : applicationType,
          selectedOrderSources: sources,
          selectedStoreUids: printerPositionDetail.store_uid ? [printerPositionDetail.store_uid] : [],
          selectedAreaIds: printerPositionDetail.area_ids
            ? printerPositionDetail.area_ids.split(',').filter(Boolean)
            : []
        }
      })
    }
  }, [open, isUpdate, printerPositionDetail, itemTypesData, isLoadingItemTypes, itemsData])

  const singleSelectedStoreUid = formData.selectedStoreUids.length === 1 ? formData.selectedStoreUids[0] : ''
  const { data: areasData = [] } = useAreasData({ storeUid: singleSelectedStoreUid })

  const handleClose = () => {
    onOpenChange(false)
    setFormData({
      printerPositionName: '',
      applyToAllCategories: false,
      selectedCategories: [],
      selectedItems: [],
      applicationType: 'category',
      selectedOrderSources: [],
      selectedStoreUids: [],
      selectedAreaIds: []
    })
    setShowOrderSourceDialog(false)
    setShowAreaDialog(false)
  }

  useEffect(() => {
    if (!selectedCityUid && citiesData.length > 0) {
      setSelectedCityUid(citiesData[0].id)
    }
  }, [citiesData, selectedCityUid])

  const isFormValid = formData.printerPositionName.trim() !== ''

  const handleSave = async () => {
    if (!isFormValid) return
    if (!companyUid || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    try {
      const printerPositionData = {
        printer_position_name: formData.printerPositionName,
        company_uid: companyUid,
        brand_uid: selectedBrand.id,
        store_uid: formData.selectedStoreUids.length === 1 ? formData.selectedStoreUids[0] : undefined,
        printer_position_id:
          isUpdate && printerPositionDetail?.printer_position_id
            ? printerPositionDetail.printer_position_id
            : `POSITION_PRINTER-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
        list_item_type_id: formData.applicationType === 'category' ? formData.selectedCategories.join(',') : '',
        list_item_id: formData.applicationType === 'item' ? formData.selectedItems.join(',') : '',
        sources: formData.selectedOrderSources.join(','),
        apply_with_store: formData.selectedStoreUids.length > 0 ? 1 : 0,
        area_ids: formData.selectedAreaIds.join(',')
      }

      if (isUpdate && currentRow) {
        await updatePrinterPositionMutation.mutateAsync({
          id: currentRow.id,
          ...printerPositionData
        })
      } else {
        await createPrinterPositionMutation.mutateAsync(printerPositionData)
      }

      handleClose()
    } catch (_error) {
      // Error handling is done in the mutation hooks
    }
  }

  const handleApplyToAllChange = (checked: boolean) => {
    if (checked) {
      const allCategoriesExceptUncategory = (itemTypesData || [])
        .filter(itemType => itemType.item_type_id !== 'ITEM_TYPE_OTHER')
        .map(itemType => itemType.id)

      setFormData({
        ...formData,
        applyToAllCategories: checked,
        selectedCategories: allCategoriesExceptUncategory
      })
    } else {
      setFormData({
        ...formData,
        applyToAllCategories: checked,
        selectedCategories: []
      })
    }
  }

  const handleOpenOrderSourceDialog = () => {
    setShowOrderSourceDialog(true)
  }

  const handleOrderSourceDialogConfirm = (selectedIds: string[]) => {
    setFormData({ ...formData, selectedOrderSources: selectedIds })
    setShowOrderSourceDialog(false)
  }

  const handleOrderSourceDialogCancel = () => {
    setShowOrderSourceDialog(false)
  }

  const handleCategorySelectionChange = (categoryId: string, checked: boolean) => {
    const updatedCategories = checked
      ? [...formData.selectedCategories, categoryId]
      : formData.selectedCategories.filter(id => id !== categoryId)

    const allCategoriesExceptUncategory = (itemTypesData || [])
      .filter(itemType => itemType.item_type_id !== 'ITEM_TYPE_OTHER')
      .map(itemType => itemType.id)

    const isAllSelected =
      allCategoriesExceptUncategory.length > 0 &&
      allCategoriesExceptUncategory.every(id => updatedCategories.includes(id))

    setFormData({
      ...formData,
      selectedCategories: updatedCategories,
      applyToAllCategories: isAllSelected
    })
  }

  const handleOpenAreaDialog = () => {
    setShowAreaDialog(true)
  }

  const handleAreaDialogConfirm = (selectedIds: string[]) => {
    setFormData({ ...formData, selectedAreaIds: selectedIds })
    setShowAreaDialog(false)
  }

  const handleAreaDialogCancel = () => {
    setShowAreaDialog(false)
  }

  if (!open) return null

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={
              !isFormValid || createPrinterPositionMutation.isPending || updatePrinterPositionMutation.isPending
            }
            onClick={handleSave}
          >
            {createPrinterPositionMutation.isPending || updatePrinterPositionMutation.isPending ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>{isUpdate ? 'Chỉnh sửa vị trí máy in' : 'Tạo vị trí máy in'}</h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <div className='rounded-lg border bg-white p-6 shadow-sm'>
          <div className='space-y-6'>
            <div className='space-y-6'>
              <div className='flex items-center gap-4'>
                <Label htmlFor='printer-name' className='min-w-[200px] text-sm font-medium'>
                  Nhập tên vị trí máy in <span className='text-red-500'>*</span>
                </Label>
                <Input
                  id='printer-name'
                  value={formData.printerPositionName}
                  onChange={e => setFormData({ ...formData, printerPositionName: e.target.value })}
                  placeholder='Tên vị trí máy in'
                  className='flex-1'
                />
              </div>

              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Chọn khu vực áp dụng</Label>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleOpenAreaDialog}
                  className='border-blue-200 text-sm text-blue-600 hover:bg-blue-50'
                  disabled={formData.selectedStoreUids.length !== 1}
                >
                  {formData.selectedAreaIds.length > 0
                    ? `${formData.selectedAreaIds.length} khu vực được áp dụng`
                    : 'Chưa chọn khu vực'}
                </Button>
              </div>

              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Áp dụng ngành đơn</Label>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleOpenOrderSourceDialog}
                  className='border-blue-200 text-sm text-blue-600 hover:bg-blue-50'
                >
                  {formData.selectedOrderSources.length > 0
                    ? `${formData.selectedOrderSources.length} nguồn đơn được áp dụng`
                    : '0 nguồn đơn được áp dụng'}
                </Button>
              </div>

              <div className='space-y-4'>
                <Label className='text-sm font-medium'>Các nhóm món được dùng tại vị trí</Label>

                {formData.applicationType === 'category' && (
                  <div className='flex items-center justify-between gap-4'>
                    <div className='flex flex-shrink-0 items-center space-x-2'>
                      <Checkbox
                        id='apply-all'
                        checked={formData.applyToAllCategories}
                        onCheckedChange={handleApplyToAllChange}
                      />
                      <Label htmlFor='apply-all' className='text-sm whitespace-nowrap text-blue-600'>
                        Áp dụng cho tất cả nhóm món
                      </Label>
                    </div>

                    <div className='max-w-full flex-1'>
                      <div className='relative'>
                        <Search className='absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400' />
                        <Input
                          placeholder='Tìm kiếm nhóm món...'
                          value={itemTypeSearchTerm}
                          onChange={e => setItemTypeSearchTerm(e.target.value)}
                          className='h-8 w-full pl-7 text-sm'
                        />
                      </div>
                    </div>
                  </div>
                )}
                {formData.applicationType === 'item' && (
                  <div className='flex items-center justify-between gap-4'>
                    <div className='max-w-full flex-1'>
                      <div className='relative'>
                        <Search className='absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400' />
                        <Input
                          placeholder='Tìm kiếm'
                          value={itemTypeSearchTerm}
                          onChange={e => setItemTypeSearchTerm(e.target.value)}
                          className='h-8 w-full pl-7 text-sm'
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div className='flex items-center gap-4 border-y py-4'>
                  <Label className='min-w-[200px] text-sm font-medium'>Áp dụng cho</Label>
                  <div className='flex gap-2'>
                    <Button
                      type='button'
                      variant={formData.applicationType === 'category' ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => {
                        setFormData({ ...formData, applicationType: 'category' })
                      }}
                    >
                      Nhóm
                    </Button>
                    <Button
                      type='button'
                      variant={formData.applicationType === 'item' ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => {
                        setFormData({ ...formData, applicationType: 'item' })
                      }}
                    >
                      Món ăn
                    </Button>
                  </div>
                </div>

                {formData.applicationType === 'category' ? (
                  <CategorySelectionSection
                    categories={filteredItemTypes}
                    selectedCategories={formData.selectedCategories}
                    onToggleCategory={handleCategorySelectionChange}
                  />
                ) : (
                  <ItemSelectionSection
                    items={itemsData || []}
                    isLoading={isLoadingItems}
                    cities={citiesData}
                    selectedCityUid={selectedCityUid}
                    onChangeCity={setSelectedCityUid}
                    stores={storesData}
                    selectedStoreUid={selectedStoreUid}
                    onChangeStore={setSelectedStoreUid}
                    mode={itemScope}
                    onChangeMode={setItemScope}
                    selectedItems={formData.selectedItems}
                    onItemsChange={items => setFormData({ ...formData, selectedItems: items })}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <OrderSourceSelectionDialog
        open={showOrderSourceDialog}
        onOpenChange={setShowOrderSourceDialog}
        orderSources={sourcesData || []}
        selectedOrderSources={formData.selectedOrderSources}
        onConfirm={handleOrderSourceDialogConfirm}
        onCancel={handleOrderSourceDialogCancel}
      />

      <AreaSelectionDialog
        open={showAreaDialog}
        onOpenChange={setShowAreaDialog}
        areas={areasData}
        selectedAreaIds={formData.selectedAreaIds}
        onConfirm={handleAreaDialogConfirm}
        onCancel={handleAreaDialogCancel}
      />
    </div>
  )
}
