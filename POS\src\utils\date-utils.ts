/**
 * Chuyển đổi array ngày thành bit flags
 * @param days Array các ngày (0=T2, 1=T3, ..., 5=T7, 6=CN)
 * @returns Bit flags number
 * Mapping: CN=bit1, T2=bit2, T3=bit3, T4=bit4, T5=bit5, T6=bit6, T7=bit7
 */
export const convertDaysToBitFlags = (days: number[]): number => {
  const bitMapping = [2, 3, 4, 5, 6, 7, 1] // [T2, T3, T4, T5, T6, T7, CN] -> [bit2, bit3, bit4, bit5, bit6, bit7, bit1]
  return days.reduce((flags, day) => {
    if (day >= 0 && day < bitMapping.length) {
      return flags | (1 << bitMapping[day])
    }
    return flags
  }, 0)
}

/**
 * Chuyển đổi array giờ thành bit flags
 * @param hours Array các giờ (0-23)
 * @returns Bit flags number
 */
export const convertHoursToBitFlags = (hours: number[]): number => {
  return hours.reduce((flags, hour) => flags | (1 << hour), 0)
}

/**
 * Chuyển đổi bit flags thành array ngày
 * @param flags Bit flags number
 * @returns Array các ngày (0=T2, 1=T3, ..., 5=T7, 6=CN)
 * Mapping: bit1=CN, bit2=T2, bit3=T3, bit4=T4, bit5=T5, bit6=T6, bit7=T7
 */
export const convertBitFlagsToDays = (flags: number): number[] => {
  const days: number[] = []

  // Check each bit position and map to day index
  // bit 1 = CN (index 6), bit 2 = T2 (index 0), bit 3 = T3 (index 1), etc.
  if (flags & (1 << 1)) days.push(6) // CN = bit 1 -> index 6
  if (flags & (1 << 2)) days.push(0) // T2 = bit 2 -> index 0
  if (flags & (1 << 3)) days.push(1) // T3 = bit 3 -> index 1
  if (flags & (1 << 4)) days.push(2) // T4 = bit 4 -> index 2
  if (flags & (1 << 5)) days.push(3) // T5 = bit 5 -> index 3
  if (flags & (1 << 6)) days.push(4) // T6 = bit 6 -> index 4
  if (flags & (1 << 7)) days.push(5) // T7 = bit 7 -> index 5

  return days.sort((a, b) => a - b) // Sort to maintain order: T2, T3, T4, T5, T6, T7, CN
}

/**
 * Chuyển đổi bit flags thành array giờ
 * @param flags Bit flags number
 * @returns Array các giờ (0-23)
 */
export const convertBitFlagsToHours = (flags: number): number[] => {
  const hours: number[] = []
  for (let i = 0; i < 24; i++) {
    if (flags & (1 << i)) {
      hours.push(i)
    }
  }
  return hours
}

/**
 * Day name mappings
 */
export const DAY_NAMES = {
  VI: ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'],
  EN: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
  SHORT_VI: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
  SHORT_EN: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
} as const

/**
 * Day string to number mapping
 * Mapping: 0=T2(Monday), 1=T3(Tuesday), 2=T4(Wednesday), 3=T5(Thursday), 4=T6(Friday), 5=T7(Saturday), 6=CN(Sunday)
 * Bit mapping: T2=bit2, T3=bit3, T4=bit4, T5=bit5, T6=bit6, T7=bit7, CN=bit1
 */
export const DAY_STRING_TO_NUMBER: { [key: string]: number } = {
  monday: 0, // T2 -> index 0 -> bit 2
  tuesday: 1, // T3 -> index 1 -> bit 3
  wednesday: 2, // T4 -> index 2 -> bit 4
  thursday: 3, // T5 -> index 3 -> bit 5
  friday: 4, // T6 -> index 4 -> bit 6
  saturday: 5, // T7 -> index 5 -> bit 7
  sunday: 6, // CN -> index 6 -> bit 1
  t2: 0, // Monday
  t3: 1, // Tuesday
  t4: 2, // Wednesday
  t5: 3, // Thursday
  t6: 4, // Friday
  t7: 5, // Saturday
  cn: 6, // Sunday
  '0': 0, // T2
  '1': 1, // T3
  '2': 2, // T4
  '3': 3, // T5
  '4': 4, // T6
  '5': 5, // T7
  '6': 6 // CN
}

/**
 * Convert day strings to numbers
 * @param dayStrings Array of day strings ('monday', 'tuesday', '0', '1', etc.)
 * @returns Array of day numbers (0=T2, 1=T3, ..., 6=CN)
 */
export const convertDayStringsToNumbers = (dayStrings: string[]): number[] => {
  return dayStrings
    .map(day => {
      const key = day.toLowerCase()
      return DAY_STRING_TO_NUMBER[key] !== undefined ? DAY_STRING_TO_NUMBER[key] : parseInt(day, 10)
    })
    .filter(num => !isNaN(num) && num >= 0 && num <= 6)
}

/**
 * Convert hour strings to numbers
 * @param hourStrings Array of hour strings ('0', '1', '23', etc.)
 * @returns Array of hour numbers (0-23)
 */
export const convertHourStringsToNumbers = (hourStrings: string[]): number[] => {
  return hourStrings.map(hour => parseInt(hour, 10)).filter(hour => !isNaN(hour) && hour >= 0 && hour <= 23)
}

/**
 * Convert numbers to day strings
 * @param dayNumbers Array of day numbers (0=T2, 1=T3, ..., 6=CN)
 * @returns Array of day strings ('0', '1', '2', etc.) for UI compatibility
 */
export const convertNumbersToDayStrings = (dayNumbers: number[]): string[] => {
  return dayNumbers.map(num => num.toString()).filter(num => num >= '0' && num <= '6')
}

/**
 * Convert numbers to hour strings
 * @param hourNumbers Array of hour numbers (0-23)
 * @returns Array of hour strings ('0', '1', '23', etc.)
 */
export const convertNumbersToHourStrings = (hourNumbers: number[]): string[] => {
  return hourNumbers.map(hour => hour.toString())
}

/**
 * Convert bitmask to numbers (for time_sale_date_week and time_sale_hour_day)
 * @param bitmask Bitmask number
 * @returns Array of numbers representing set bits
 */
export const convertBitmaskToNumbers = (bitmask: number): number[] => {
  const numbers: number[] = []
  for (let i = 0; i < 32; i++) {
    if (bitmask & (1 << i)) {
      numbers.push(i)
    }
  }
  return numbers
}

/**
 * Format selected days for display
 * @param days Array of day numbers (0=T2, 1=T3, ..., 6=CN)
 * @param language Language for day names ('VI' | 'EN')
 * @param short Use short names
 * @returns Formatted string
 */
export const formatSelectedDays = (days: number[], language: 'VI' | 'EN' = 'VI', short: boolean = false): string => {
  const dayNames = short
    ? language === 'VI'
      ? DAY_NAMES.SHORT_VI
      : DAY_NAMES.SHORT_EN
    : language === 'VI'
      ? DAY_NAMES.VI
      : DAY_NAMES.EN

  return days.map(day => dayNames[day]).join(', ')
}

/**
 * Format selected hours for display
 * @param hours Array of hour numbers (0-23)
 * @param format24 Use 24-hour format (default: true)
 * @returns Formatted string
 */
export const formatSelectedHours = (hours: number[], format24: boolean = true): string => {
  if (format24) {
    return hours.map(hour => `${hour.toString().padStart(2, '0')}:00`).join(', ')
  }

  return hours
    .map(hour => {
      if (hour === 0) return '12:00 AM'
      if (hour === 12) return '12:00 PM'
      if (hour < 12) return `${hour}:00 AM`
      return `${hour - 12}:00 PM`
    })
    .join(', ')
}

/**
 * Check if all days are selected
 * @param days Array of day numbers
 * @returns True if all 7 days are selected
 */
export const isAllDaysSelected = (days: number[]): boolean => {
  return days.length === 7
}

/**
 * Check if all hours are selected
 * @param hours Array of hour numbers
 * @returns True if all 24 hours are selected
 */
export const isAllHoursSelected = (hours: number[]): boolean => {
  return hours.length === 24
}

/**
 * Get all days array (0-6)
 * @returns Array [0, 1, 2, 3, 4, 5, 6]
 */
export const getAllDays = (): number[] => {
  return Array.from({ length: 7 }, (_, i) => i)
}

/**
 * Get all hours array (0-23)
 * @returns Array [0, 1, 2, ..., 23]
 */
export const getAllHours = (): number[] => {
  return Array.from({ length: 24 }, (_, i) => i)
}

/**
 * Get business hours (9 AM to 9 PM)
 * @returns Array [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]
 */
export const getBusinessHours = (): number[] => {
  return Array.from({ length: 13 }, (_, i) => i + 9)
}

/**
 * Get weekdays only (Monday to Friday)
 * @returns Array [0, 1, 2, 3, 4] (T2-T6)
 */
export const getWeekdays = (): number[] => {
  return [0, 1, 2, 3, 4]
}

/**
 * Get weekend days (Saturday and Sunday)
 * @returns Array [5, 6] (T7, CN)
 */
export const getWeekendDays = (): number[] => {
  return [5, 6]
}

/**
 * Format date string to Vietnamese locale
 * @param dateString Date string in ISO format (YYYY-MM-DD)
 * @returns Formatted date string (DD/MM/YYYY)
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

/**
 * Convert date string to timestamp (milliseconds)
 * @param dateString Date string in ISO format (YYYY-MM-DD)
 * @param isEndOfDay If true, set time to 23:59:59.999, otherwise 00:00:00.000
 * @returns Timestamp in milliseconds
 */
export const convertDateToTimestamp = (dateString: string, isEndOfDay: boolean = false): number => {
  const date = new Date(dateString)
  if (isEndOfDay) {
    date.setHours(23, 59, 59, 999)
  } else {
    date.setHours(0, 0, 0, 0)
  }
  return date.getTime()
}

/**
 * Convert timestamp to date string (YYYY-MM-DD)
 * @param timestamp Timestamp in milliseconds
 * @returns Date string in ISO format (YYYY-MM-DD)
 */
export const convertTimestampToDate = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toISOString().split('T')[0]
}
/**
 * Convert date range to timestamp range
 * @param dateRange Array with [fromDate, toDate]
 * @returns Array with [startDate, endDate] timestamps
 */
export const getDateRangeTimestamps = (dateRange: [Date, Date]): [number, number] => {
  const [fromDate, toDate] = dateRange

  const start = new Date(fromDate)
  start.setHours(0, 0, 0, 0)
  const startDate = start.getTime()

  const end = new Date(toDate)
  end.setHours(23, 59, 59, 999)
  const endDate = end.getTime()

  return [startDate, endDate]
}

/**
 * Date utilities object for easy import
 */
export const dateUtils = {
  convertDaysToBitFlags,
  convertHoursToBitFlags,
  convertBitFlagsToDays,
  convertBitFlagsToHours,
  convertDayStringsToNumbers,
  convertHourStringsToNumbers,
  convertNumbersToDayStrings,
  convertNumbersToHourStrings,
  convertBitmaskToNumbers,
  formatSelectedDays,
  formatSelectedHours,
  formatDate,
  convertDateToTimestamp,
  convertTimestampToDate,
  isAllDaysSelected,
  isAllHoursSelected,
  getAllDays,
  getAllHours,
  getBusinessHours,
  getWeekdays,
  getWeekendDays,
  getDateRangeTimestamps,
  DAY_NAMES,
  DAY_STRING_TO_NUMBER
}
