// Source API data structure
export interface SourceApiData {
  source_id: string
  source_name: string
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  source_type: string[]
  description: string | null
  extra_data: {
    commission: number
    exclude_ship: number
    payment_type: string
    deduct_tax_rate: number
    require_tran_no: number
    use_order_online: number
    payment_method_id: string
    payment_method_name: string
    voucher_run_partner: string | null
    not_show_partner_bill: number
    marketing_partner_cost: number
    marketing_partner_cost_type: string
    marketing_partner_cost_to_date: number
    marketing_partner_cost_hour_day: number
    marketing_partner_cost_date_week: number
    marketing_partner_cost_from_date: number
  }
  is_fb: number
  active: number
  revision: number | null
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  store_uid: string
  partner_config: number
  row: number
  stores: number
}

// Converted source data structure
export interface Source {
  id: string
  sourceId: string
  sourceName: string
  storeUid: string
  active: number
  sourceType: string[]
  description: string | null
  extraData: SourceApiData['extra_data']
  createdAt: number
  updatedAt: number
  partnerConfig: number
  stores: number
}

// API response structure
export interface SourcesApiResponse {
  data: SourceApiData[]
  track_id: string
}

// Parameters for fetching sources
export interface GetSourcesParams {
  companyUid?: string
  brandUid?: string
  partnerConfig?: number
  storeUid?: string
  skipLimit?: boolean
  page?: number
  results_per_page?: number
}

// Convert API data to internal format
export function convertApiSourceToSource(apiSource: SourceApiData): Source {
  return {
    id: apiSource.id,
    sourceId: apiSource.source_id,
    sourceName: apiSource.source_name,
    storeUid: apiSource.store_uid,
    active: apiSource.active,
    sourceType: apiSource.source_type,
    description: apiSource.description,
    extraData: apiSource.extra_data,
    createdAt: apiSource.created_at,
    updatedAt: apiSource.updated_at,
    partnerConfig: apiSource.partner_config,
    stores: apiSource.stores
  }
}
