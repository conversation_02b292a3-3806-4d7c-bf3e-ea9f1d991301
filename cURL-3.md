curl 'https://posapi.ipos.vn/api/mdata/v1/item-types?skip_limit=true&company_uid=595e8cb4-674c-49f7-adec-826b211a7ce3&brand_uid=d43a01ec-2f38-4430-a7ca-9b3324f7d39e&store_uid=e20d55dd-6dcc-4238-a32e-42f8ae6abaeb' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************.PBwQgJukDUpRmLx_Bvld-dy0atBN3okaUSJuNhBdtfo' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Origin: https://fabi.ipos.vn' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://fabi.ipos.vn/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'accept-language: vi' \
  -H 'access_token: 5c885b2ef8c34fb7b1d1fad11eef7bec' \
  -H 'fabi_type: pos-cms' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'x-client-timezone: 25200000'