import { useParams } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { useCurrentCompany, useCurrentBrand } from '@/stores/posStore'

import { useUpdateServiceCharge, useServiceChargeDetail } from '@/hooks/api/use-service-charge'

import { DiscountStatusButton } from '@/components/pos/discount-toggle-button'
import { Button } from '@/components/ui'

import { useServiceChargeFormActions, useServiceChargeFormStatus, useServiceChargeFormData } from '../stores'

export function HeaderForm() {
  const { handleBack, handleSave } = useServiceChargeFormActions()
  const { isEditMode, isLoading, isFormValid } = useServiceChargeFormStatus()
  const { updateFormData } = useServiceChargeFormData()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const params = useParams({ strict: false })
  const serviceChargeId = params.id as string

  const shouldFetchServiceCharge = !!serviceChargeId
  const { data: originalServiceChargeData } = useServiceChargeDetail({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    id: serviceChargeId,
    enabled: shouldFetchServiceCharge
  })

  const updateServiceChargeMutation = useUpdateServiceCharge()

  const canToggleStatus = isEditMode && originalServiceChargeData
  const isToggleDisabled = isLoading || updateServiceChargeMutation.isPending

  const handleToggleStatus = async () => {
    if (!canToggleStatus) return

    const newActiveStatus = originalServiceChargeData.active === 1 ? 0 : 1
    const serviceChargeApiData = {
      ...originalServiceChargeData,
      active: newActiveStatus as 0 | 1,
      is_update_same_service_charges: false
    }

    try {
      await updateServiceChargeMutation.mutateAsync(serviceChargeApiData as any)
      updateFormData({ active: newActiveStatus })
    } catch (error) {
      console.error('Failed to toggle service charge status:', error)
    }
  }

  const getPageTitle = () => {
    return isEditMode ? 'Chi tiết phí dịch vụ' : 'Tạo phí dịch vụ'
  }

  const getSaveButtonText = () => {
    if (isLoading) {
      return isEditMode ? 'Đang cập nhật...' : 'Đang tạo...'
    }
    return isEditMode ? 'Cập nhật' : 'Lưu'
  }

  const isSaveDisabled = isLoading || !isFormValid
  const isActiveStatus = originalServiceChargeData?.active === 1

  return (
    <div className='mb-8 flex items-center justify-between'>
      <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
        <X className='h-4 w-4' />
      </Button>

      <h1 className='text-3xl font-bold'>{getPageTitle()}</h1>

      <div className='flex items-center gap-2'>
        {canToggleStatus && (
          <DiscountStatusButton isActive={isActiveStatus} onToggle={handleToggleStatus} disabled={isToggleDisabled} />
        )}
        <Button type='button' disabled={isSaveDisabled} className='min-w-[100px]' onClick={handleSave}>
          {getSaveButtonText()}
        </Button>
      </div>
    </div>
  )
}
