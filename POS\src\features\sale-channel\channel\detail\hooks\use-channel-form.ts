import { useState } from 'react'

import { ChannelFormData, initialChannelFormData } from '../data'

export const useChannelForm = () => {
  const [formData, setFormData] = useState<ChannelFormData>(initialChannelFormData)

  const updateFormData = (updates: Partial<ChannelFormData>) => {
    setFormData(prev => {
      const newData = { ...prev, ...updates }

      // Reset payment-related fields when payment method changes
      if (updates.paymentMethodId !== undefined) {
        if (updates.paymentMethodId === '') {
          // Reset all payment fields when clearing payment method
          newData.requirePartnerInvoice = false
          newData.selectedPaymentMethodId = ''
        } else if (updates.paymentMethodId === 'prepaid') {
          // Reset payment method selection when switching to prepaid
          newData.selectedPaymentMethodId = ''
        }
      }

      // Reset payment method selection when store changes
      if (updates.storeId !== undefined && updates.storeId !== prev.storeId) {
        newData.selectedPaymentMethodId = ''
      }

      return newData
    })
  }

  const resetForm = () => {
    setFormData(initialChannelFormData)
  }

  // Basic validation
  const isFormValid =
    formData.sourceName.length > 0 &&
    formData.storeId !== '' &&
    formData.paymentMethodId !== '' &&
    // If postpaid is selected, payment method must be selected
    (formData.paymentMethodId !== 'postpaid' || formData.selectedPaymentMethodId !== '')

  return {
    formData,
    updateFormData,
    resetForm,
    isFormValid
  }
}
