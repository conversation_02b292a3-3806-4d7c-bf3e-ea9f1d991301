import React from 'react'

import type { Area } from '@/lib/api'
import type { Table } from '@/lib/tables-api'

import { DraggableTableItem } from './draggable-table-item'

interface TablesGridProps {
  areas: Area[]
  tables: Table[]
  isLoading: boolean
  selectedAreaId: string
  draggedTableIndex: number | null
  onTableDragStart: (e: React.DragEvent, index: number) => void
  onTableDragOver: (e: React.DragEvent) => void
  onTableDrop: (e: React.DragEvent, index: number) => void
}

export const TablesGrid: React.FC<TablesGridProps> = ({
  areas,
  tables,
  isLoading,
  selectedAreaId,
  draggedTableIndex,
  onTableDragStart,
  onTableDragOver,
  onTableDrop
}) => {
  return (
    <div className='flex h-full min-w-0 flex-1 flex-col'>
      <h3 className='mb-2 flex-shrink-0 truncate font-medium text-gray-900'>
        {selectedAreaId ? `${areas.find(a => a.id === selectedAreaId)?.area_name || ''}` : 'Chọn khu vực để xem bàn'}
      </h3>
      <div className='min-h-0 flex-1 overflow-x-hidden overflow-y-auto'>
        {isLoading ? (
          <div className='flex h-full items-center justify-center text-gray-500'>Đang tải bàn...</div>
        ) : !selectedAreaId ? (
          <div className='flex h-full items-center justify-center text-gray-500'>Chọn khu vực để xem danh sách bàn</div>
        ) : tables.length === 0 ? (
          <div className='flex h-full items-center justify-center text-gray-500'>
            Không có bàn nào trong khu vực này
          </div>
        ) : (
          <div className='grid grid-cols-2 gap-3 p-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5'>
            {tables.map((table, index) => (
              <DraggableTableItem
                key={table.id}
                table={table}
                index={index}
                onDragStart={onTableDragStart}
                onDragOver={onTableDragOver}
                onDrop={onTableDrop}
                isDragging={draggedTableIndex === index}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
