import { setTimeToStartOfDay, setTimeToEndOfDay } from '@/constants/crm/time'

export interface DateFilterable {
  request_at: string
}

export const createDateFilter = <T extends DateFilterable>(from: Date, to: Date) => {
  return (item: T): boolean => {
    const itemDate = new Date(item.request_at)
    const fromDate = setTimeToStartOfDay(from)
    const toDate = setTimeToEndOfDay(to)
    return itemDate >= fromDate && itemDate <= toDate
  }
}

export const createTextFilter = <T>(field: keyof T) => {
  return (searchText: string) =>
    (item: T): boolean => {
      if (!searchText || searchText === 'all') return true
      const fieldValue = item[field]
      if (typeof fieldValue !== 'string') return false
      return fieldValue.toLowerCase().includes(searchText.toLowerCase())
    }
}
