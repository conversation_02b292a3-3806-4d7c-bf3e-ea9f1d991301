import type {
  CreateDiscountRequest,
  CreateDiscountResponse,
  DiscountFormData,
  UpdateDiscountRequest
} from '@/types/api/discount-types'
import type { GetDiscountsParams, DiscountsApiResponse, Discount } from '@/types/discounts'
import type { DiscountApiData } from '@/types/discounts'

import { dateUtils } from '@/utils/date-utils'

import { apiClient } from './api/pos/pos-api'

/**
 * Fetch discounts from API
 */
export const getDiscounts = async (params: GetDiscountsParams = {}): Promise<Discount[]> => {
  const queryParams = new URLSearchParams()

  // Add company and brand UIDs
  if (params.companyUid) {
    queryParams.append('company_uid', params.companyUid)
  }
  if (params.brandUid) {
    queryParams.append('brand_uid', params.brandUid)
  }

  // Add pagination
  if (params.page) {
    queryParams.append('page', params.page.toString())
  }

  // Add store UIDs
  if (params.listStoreUid && params.listStoreUid.length > 0) {
    queryParams.append('list_store_uid', params.listStoreUid.join(','))
  }

  // Add promotion partner auto gen
  if (params.promotionPartnerAutoGen !== undefined) {
    queryParams.append('promotion_partner_auto_gen', params.promotionPartnerAutoGen.toString())
  }

  // Add status filter
  if (params.status) {
    queryParams.append('status', params.status)
  }

  // Add active filter
  if (params.active !== undefined) {
    queryParams.append('active', params.active.toString())
  }

  // Add search term
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  const response = await apiClient.get<DiscountsApiResponse>(`/mdata/v1/discounts?${queryParams.toString()}`)

  if (response.data?.data) {
    // Import the conversion function dynamically to avoid circular dependency
    const { convertApiDiscountToDiscount } = await import('@/types/discounts')
    return response.data.data.map(convertApiDiscountToDiscount)
  }

  return []
}

/**
 * Get discount by ID
 */
export const getDiscountById = async (params: {
  companyUid: string
  brandUid: string
  id: string
}): Promise<DiscountApiData> => {
  const queryParams = new URLSearchParams()
  queryParams.append('company_uid', params.companyUid)
  queryParams.append('brand_uid', params.brandUid)
  queryParams.append('id', params.id)

  const response = await apiClient.get<{ data: DiscountApiData; track_id: string }>(
    `/mdata/v1/discount?${queryParams.toString()}`
  )

  if (response.data?.data) {
    return response.data.data
  }

  throw new Error('Discount not found')
}

/**
 * Delete discount by ID
 */
export const deleteDiscount = async (params: { companyUid: string; brandUid: string; id: string }): Promise<void> => {
  const queryParams = new URLSearchParams()
  queryParams.append('company_uid', params.companyUid)
  queryParams.append('brand_uid', params.brandUid)
  queryParams.append('id', params.id)

  await apiClient.delete(`/mdata/v1/discount?${queryParams.toString()}`)
}

/**
 * Chuyển đổi form data thành API request (cho sale-channel discount)
 */
export const transformFormDataToRequest = (
  formData: DiscountFormData,
  storeUid: string,
  companyUid: string,
  brandUid: string
): CreateDiscountRequest => {
  const timeSaleDateWeek = dateUtils.convertDaysToBitFlags(formData.selectedDays)
  const timeSaleHourDay = dateUtils.convertHoursToBitFlags(formData.selectedHours)

  console.log('Transform - formData.filterState:', formData.filterState)

  // Use filterState directly - no need for complex logic
  const { is_all, is_item, is_type, type_id, item_id, is_combo, combo_id } = formData.filterState

  return {
    store_uid: storeUid,
    promotion_uid: formData.promotionUid,
    discount_type: formData.discountType,
    type_id: type_id,
    item_id: item_id,
    from_date: formData.fromDate.getTime(),
    to_date: formData.toDate.getTime(),
    sale_channel_uid: formData.saleChannelUid,
    time_sale_date_week: timeSaleDateWeek,
    time_sale_hour_day: timeSaleHourDay,
    company_uid: companyUid,
    brand_uid: brandUid,
    ta_discount: formData.discountValue,
    ots_discount: formData.discountValue,
    is_all: is_all,
    is_type: is_type,
    is_item: is_item,
    extra_data: {
      is_combo: is_combo,
      combo_id: combo_id
    },
    promotion_partner_auto_gen: 1,
    is_update_same_discounts: false
  }
}

/**
 * Create new discount with enhanced API (sale-channel)
 */
export const createDiscountEnhanced = async (request: CreateDiscountRequest): Promise<CreateDiscountResponse> => {
  try {
    const response = await apiClient.post('/mdata/v1/discount', request)
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tạo discount'
    }
  }
}

/**
 * Create new discount (legacy)
 */
export const createDiscount = async (discountData: Partial<DiscountApiData>): Promise<void> => {
  await apiClient.post('/mdata/v1/discount', discountData)
}

/**
 * Transform form data to full discount object for update
 * Merges formData changes with originalDiscount, keeping unchanged fields intact
 */
export const transformFormDataToUpdateRequest = (
  formData: DiscountFormData,
  originalDiscount: DiscountApiData
): UpdateDiscountRequest => {
  const timeSaleDateWeek = dateUtils.convertDaysToBitFlags(formData.selectedDays)
  const timeSaleHourDay = dateUtils.convertHoursToBitFlags(formData.selectedHours)

  const { is_all, is_item, is_type, type_id, item_id, is_combo, combo_id } = formData.filterState

  // Merge formData with originalDiscount, only updating fields that exist in formData
  return {
    // Keep all original fields
    ...originalDiscount,

    // Update only the fields that can be modified in the form
    ta_discount: formData.discountValue,
    ots_discount: formData.discountValue,
    is_all: is_all,
    is_type: is_type,
    is_item: is_item,
    type_id: type_id,
    item_id: item_id,
    discount_type: formData.discountType,
    from_date: formData.fromDate.getTime(),
    to_date: formData.toDate.getTime(),
    time_sale_hour_day: timeSaleHourDay,
    time_sale_date_week: timeSaleDateWeek,
    extra_data: {
      is_combo: is_combo,
      combo_id: combo_id
    },

    // Add required fields for update API
    sale_channel_uid: originalDiscount.source_uid,
    promotion_partner_auto_gen: 1 as 0 | 1,
    is_update_same_discounts: false
  }
}

/**
 * Update discount with enhanced API
 */
export const updateDiscountEnhanced = async (request: CreateDiscountRequest): Promise<CreateDiscountResponse> => {
  try {
    const response = await apiClient.put('/mdata/v1/discount', request)
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật discount'
    }
  }
}

/**
 * Update discount with full object (for edit mode)
 */
export const updateDiscountWithFullObject = async (
  discountData: UpdateDiscountRequest
): Promise<CreateDiscountResponse> => {
  try {
    const response = await apiClient.put('/mdata/v1/discount', discountData)
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật discount'
    }
  }
}

/**
 * Update discount (toggle active status)
 */
export const updateDiscount = async (discountData: DiscountApiData): Promise<void> => {
  await apiClient.put('/mdata/v1/discount', discountData)
}

/**
 * Discount utilities
 */
export const discountUtils = {
  // Format discount value for display
  formatDiscountValue: (value: number, type: 'PERCENT' | 'AMOUNT'): string => {
    if (type === 'PERCENT') {
      return `${value}%`
    }
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value)
  },

  // Validate form data
  validateFormData: (formData: DiscountFormData): string[] => {
    const errors: string[] = []

    // Validate discount value
    if (!formData.discountValue || formData.discountValue <= 0) {
      errors.push('Giá trị giảm giá phải lớn hơn 0')
    }

    // Remove percentage validation - handle at input level
    // if (formData.discountType === 'PERCENT' && formData.discountValue > 100) {
    //   errors.push('Giá trị giảm giá phần trăm không được vượt quá 100%')
    // }

    // Validate dates
    if (!formData.fromDate || !formData.toDate) {
      errors.push('Vui lòng chọn ngày bắt đầu và kết thúc')
    }

    if (formData.fromDate && formData.toDate && formData.fromDate > formData.toDate) {
      errors.push('Ngày bắt đầu không được lớn hơn ngày kết thúc')
    }

    // Validate time selection
    if (!formData.selectedDays.length) {
      errors.push('Vui lòng chọn ít nhất một ngày trong tuần')
    }

    if (!formData.selectedHours.length) {
      errors.push('Vui lòng chọn ít nhất một giờ trong ngày')
    }

    // Validate apply to selection using filterState
    if (formData.filterState.is_all !== 1) {
      const hasSelection =
        (formData.filterState.is_item === 1 && formData.filterState.item_id.trim()) ||
        (formData.filterState.is_type === 1 && formData.filterState.type_id.trim()) ||
        (formData.filterState.is_combo === 1 && formData.filterState.combo_id.trim())

      if (!hasSelection) {
        errors.push('Vui lòng chọn ít nhất một món, nhóm hoặc combo để áp dụng')
      }
    }

    // Validate required UIDs
    if (!formData.saleChannelUid) {
      errors.push('Thiếu thông tin kênh bán hàng')
    }

    if (!formData.promotionUid) {
      errors.push('Thiếu thông tin chương trình khuyến mãi')
    }

    return errors
  }
}
