import React from 'react'

import { useStoresData } from '@/hooks/api/use-stores'

import { FilterDropdown } from '@/components/filter-dropdown'

interface StoreSelectorProps {
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  className?: string
}

export const StoreSelector: React.FC<StoreSelectorProps> = ({
  value,
  onValueChange,
  placeholder = 'Chọn cửa hàng',
  className = 'w-48'
}) => {
  const { data: stores, isLoading } = useStoresData()

  const storeOptions =
    stores?.map(store => ({
      value: store.id,
      label: store.name
    })) || []

  return (
    <FilterDropdown
      value={value}
      onValueChange={onValueChange}
      options={storeOptions}
      isLoading={isLoading}
      placeholder={placeholder}
      className={className}
      showAllOption={false}
      loadingText='Đang tải...'
      emptyText='Không có cửa hàng có khu vực'
    />
  )
}
