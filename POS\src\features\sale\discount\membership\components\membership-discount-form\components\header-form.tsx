import { useParams, useSearch } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { useUpdateMembershipDiscount, useMembershipDiscountDetail } from '@/hooks/api/use-membership-discounts'

import { DiscountStatusButton } from '@/components/pos/discount-toggle-button'
import { Button } from '@/components/ui'

import {
  useMembershipDiscountFormActions,
  useMembershipDiscountFormStatus,
  useMembershipDiscountFormData
} from '../stores'

export function HeaderForm() {
  const { handleBack, handleSave } = useMembershipDiscountFormActions()
  const { isEditMode, isLoading, isFormValid } = useMembershipDiscountFormStatus()
  const { updateFormData } = useMembershipDiscountFormData()
  const params = useParams({ strict: false })
  const search = useSearch({ strict: false })
  const discountId = params.id as string
  const storeUid = (search as any)?.store_uid as string

  // Get the original discount data from API for toggle functionality
  const { data: originalDiscountData } = useMembershipDiscountDetail(discountId || '', storeUid || '')
  const updateDiscountMutation = useUpdateMembershipDiscount()

  const handleToggleStatus = async () => {
    if (!isEditMode || !originalDiscountData) return

    // Toggle the active status: 1 -> 0 or 0 -> 1
    const newActiveStatus = originalDiscountData.active === 1 ? 0 : 1

    // Use the complete original discount data and only change the active field
    const discountApiData = {
      ...originalDiscountData,
      active: newActiveStatus as 0 | 1,
      is_update_same_discounts: false
    }

    console.log('🔥 Membership Discount Toggle - API Data:', discountApiData)

    try {
      await updateDiscountMutation.mutateAsync(discountApiData as any)
      // Update the form data to reflect the new active status
      updateFormData({ active: newActiveStatus })
    } catch (error) {
      console.error('Failed to toggle discount status:', error)
    }
  }

  return (
    <div className='mb-8 flex items-center justify-between'>
      <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
        <X className='h-4 w-4' />
      </Button>

      <h1 className='text-3xl font-bold'>
        {isEditMode && 'Chỉnh sửa CTKM'}
        {!isEditMode && 'Tạo CTKM'}
      </h1>

      <div className='flex items-center gap-2'>
        {isEditMode && originalDiscountData && (
          <DiscountStatusButton
            isActive={originalDiscountData.active === 1}
            onToggle={handleToggleStatus}
            disabled={isLoading || updateDiscountMutation.isPending}
          />
        )}
        <Button type='button' disabled={isLoading || !isFormValid} className='min-w-[100px]' onClick={handleSave}>
          {isLoading && isEditMode && 'Đang cập nhật...'}
          {isLoading && !isEditMode && 'Đang tạo...'}
          {!isLoading && isEditMode && 'Cập nhật'}
          {!isLoading && !isEditMode && 'Lưu'}
        </Button>
      </div>
    </div>
  )
}
