import { STORE_CONSTANTS } from '../../../../data'

interface ExpiryDateCellProps {
  expiryTimestamp?: number
}

export function ExpiryDateCell({ expiryTimestamp }: ExpiryDateCellProps) {
  if (!expiryTimestamp) {
    return <span className='text-muted-foreground'>-</span>
  }

  const expiryDate = new Date(expiryTimestamp * 1000)
  const isExpired = expiryDate < new Date()

  if (isExpired) {
    return (
      <span className='rounded bg-red-100 px-2 py-1 text-xs text-red-600'>{STORE_CONSTANTS.EXPIRED_LICENSE_BADGE}</span>
    )
  }

  return <span className='font-medium'>{expiryDate.toLocaleDateString('vi-VN')}</span>
}
