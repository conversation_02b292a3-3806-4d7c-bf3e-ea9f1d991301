import { useState } from 'react'

import type { Discount } from '@/types/discounts'

import { usePosStores, useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useDiscounts, useDeleteDiscountSimple, useUpdateDiscountSimple, useSalesChannels } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { CopyDiscountModal, DiscountDataTable, DiscountActionBar } from './components'

export function DiscountPage() {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('all')
  const [selectedChannelId, setSelectedChannelId] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedExpiry, setSelectedExpiry] = useState<string>('all')
  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false)

  const { currentBrandStores } = usePosStores()
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const deleteDiscountMutation = useDeleteDiscountSimple()

  const updateDiscountMutation = useUpdateDiscountSimple()

  const storeUids = selectedStoreId === 'all' ? currentBrandStores.map(store => store.id) : [selectedStoreId]

  // For sales channels, use single store (first store if "all" is selected)
  const singleStoreUid = selectedStoreId === 'all' ? currentBrandStores[0]?.id : selectedStoreId

  const { data: salesChannels = [], isLoading: isLoadingChannels } = useSalesChannels({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    storeUid: singleStoreUid,
    partnerConfig: 1,
    skipLimit: true
  })

  const { data: allDiscounts = [], isLoading: isLoadingDiscounts } = useDiscounts({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    page: 1,
    listStoreUid: storeUids,
    promotionPartnerAutoGen: 1,
    status:
      selectedStatus === '0'
        ? 'unexpired'
        : selectedExpiry === 'all'
          ? undefined
          : (selectedExpiry as 'expired' | 'unexpired'),
    active: selectedStatus === 'all' ? undefined : parseInt(selectedStatus)
  })

  const discounts =
    selectedChannelId === 'all'
      ? allDiscounts
      : allDiscounts.filter(discount => discount.sourceUid === selectedChannelId)

  const isLoading = isLoadingChannels || isLoadingDiscounts

  const handleDeleteDiscount = (discountId: string) => {
    if (!company?.id || !selectedBrand?.id) return

    deleteDiscountMutation.mutate({
      companyUid: company.id,
      brandUid: selectedBrand.id,
      id: discountId
    })
  }

  const handleToggleActive = (discount: Discount) => {
    const discountApiData = {
      id: discount.id,
      created_at: discount.createdAt,
      created_by: discount.createdBy,
      updated_at: discount.updatedAt,
      updated_by: discount.updatedBy,
      deleted: discount.deleted || false,
      deleted_at: discount.deletedAt || null,
      deleted_by: discount.deletedBy || null,
      ta_discount: discount.taDiscount,
      ots_discount: discount.otsDiscount,
      is_all: discount.isAll as 0 | 1,
      is_type: discount.isType as 0 | 1,
      is_item: discount.isItem as 0 | 1,
      type_id: discount.typeId,
      item_id: discount.itemId,
      discount_type: discount.discountType as 'PERCENT' | 'AMOUNT',
      from_date: discount.fromDate,
      to_date: discount.toDate,
      time_sale_hour_day: 0,
      time_sale_date_week: 0,
      description: null,
      extra_data: { combo_id: '', is_combo: 0 as 0 | 1 },
      active: (discount.active === 1 ? 0 : 1) as 0 | 1, // Toggle active status
      revision: null,
      promotion_uid: discount.promotionUid,
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      sort: 1000,
      store_uid: discount.storeUid,
      discount_clone_id: null,
      source_uid: discount.sourceUid,
      promotion: {
        id: discount.promotionUid,
        sort: 1000,
        active: 1 as 0 | 1,
        deleted: false,
        is_fabi: 1 as 0 | 1,
        revision: 0,
        brand_uid: selectedBrand?.id || '',
        store_uid: discount.storeUid,
        created_at: discount.createdAt || Date.now(),
        created_by: 'system',
        deleted_at: null,
        deleted_by: null,
        extra_data: {},
        source_uid: discount.sourceUid,
        updated_at: discount.updatedAt || Date.now(),
        updated_by: 'system',
        company_uid: company?.id || '',
        description: null,
        promotion_id: discount.promotionId,
        promotion_name: discount.promotionName,
        partner_auto_gen: discount.partnerAutoGen as 0 | 1
      },
      promotion_id: discount.promotionId,
      partner_auto_gen: discount.partnerAutoGen,
      store_name: discount.storeName,
      source: {
        id: discount.source.id,
        sort: 1000,
        is_fb: 0 as 0 | 1,
        active: discount.source.active as 0 | 1,
        deleted: false,
        is_fabi: 1 as 0 | 1,
        revision: null,
        brand_uid: selectedBrand?.id || '',
        source_id: discount.source.sourceId,
        store_uid: discount.storeUid,
        created_at: discount.createdAt || Date.now(),
        created_by: 'system',
        deleted_at: null,
        deleted_by: null,
        extra_data: {},
        updated_at: discount.updatedAt || Date.now(),
        updated_by: 'system',
        company_uid: company?.id || '',
        description: null,
        source_name: discount.source.sourceName,
        source_type: discount.source.sourceType,
        partner_config: 1 as 0 | 1
      }
    }

    updateDiscountMutation.mutate(discountApiData)
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='space-y-6'>
          <div className='space-y-4'>
            <DiscountActionBar
              title='Chương trình giảm giá theo kênh'
              selectedStoreId={selectedStoreId}
              selectedChannelId={selectedChannelId}
              selectedStatus={selectedStatus}
              selectedExpiry={selectedExpiry}
              stores={currentBrandStores}
              salesChannels={salesChannels}
              onStoreChange={setSelectedStoreId}
              onChannelChange={setSelectedChannelId}
              onStatusChange={setSelectedStatus}
              onExpiryChange={setSelectedExpiry}
              onCopyDiscount={() => setIsCopyModalOpen(true)}
            />

            <DiscountDataTable
              discounts={discounts}
              isLoading={isLoading}
              onToggleActive={handleToggleActive}
              onDeleteDiscount={handleDeleteDiscount}
              isDeleting={deleteDiscountMutation.isPending}
            />
          </div>

          <CopyDiscountModal isOpen={isCopyModalOpen} onClose={() => setIsCopyModalOpen(false)} />
        </div>
      </Main>
    </>
  )
}
