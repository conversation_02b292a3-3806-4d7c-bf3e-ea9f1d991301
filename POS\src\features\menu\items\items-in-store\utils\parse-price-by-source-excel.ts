import * as XLSX from 'xlsx'

import type { PriceBySourcePreviewItem } from '../list/components/modals/price-by-source-preview-dialog'

const EXPECTED_HEADERS = [
  'item_uid',
  'item_id',
  'item_name',
  'Nhóm món',
  '<PERSON><PERSON><PERSON> gốc',
  'Vat (%)',
  'ZAL<PERSON> [10000045]',
  'FACEBOOK [10000049]',
  'SO [10000134]',
  'CRM [10000162]',
  'VNPAY [10000165]',
  'GOJEK (GOVIET) [10000168]',
  'ShopeeFood [10000169]',
  'MANG VỀ [10000171]',
  'TẠI CHỖ [10000172]',
  'CALL CENTER [10000176]',
  'O2O [10000216]',
  'BEFOOD [10000253]'
]

export const parsePriceBySourceExcel = async (file: File): Promise<PriceBySourcePreviewItem[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = e.target?.result
        if (!data) {
          reject(new Error('Không thể đọc file'))
          return
        }

        // Parse Excel file
        const workbook = XLSX.read(data, { type: 'array' })

        // Get first worksheet
        const sheetName = workbook.SheetNames[0]
        if (!sheetName) {
          reject(new Error('File Excel không có sheet nào'))
          return
        }

        const worksheet = workbook.Sheets[sheetName]

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

        if (jsonData.length < 2) {
          reject(new Error('File Excel không có dữ liệu'))
          return
        }

        // Get headers from first row
        const headers = jsonData[0] as string[]

        // Validate headers
        const missingHeaders = EXPECTED_HEADERS.filter(expectedHeader => !headers.includes(expectedHeader))

        if (missingHeaders.length > 0) {
          console.warn('Missing headers:', missingHeaders)
          // Don't reject, just warn - some headers might be optional
        }

        // Parse data rows
        const parsedData: PriceBySourcePreviewItem[] = []

        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (!row || row.length === 0) continue // Skip empty rows

          const item: PriceBySourcePreviewItem = {
            item_uid: '',
            item_id: '',
            item_name: '',
            item_type_name: '',
            ots_price: 0,
            ots_tax: 0
          }

          // Map each cell to corresponding field
          headers.forEach((header, index) => {
            const cellValue = row[index]

            switch (header) {
              case 'item_uid':
                item.item_uid = String(cellValue || '')
                break
              case 'item_id':
                item.item_id = String(cellValue || '')
                break
              case 'item_name':
                item.item_name = String(cellValue || '')
                break
              case 'Nhóm món':
                item.item_type_name = String(cellValue || '')
                break
              case 'Giá gốc':
                item.ots_price = parseFloat(String(cellValue || '0')) || 0
                break
              case 'Vat (%)':
                item.ots_tax = parseFloat(String(cellValue || '0')) || 0
                break
              // Source price columns
              case 'ZALO [10000045]':
              case 'FACEBOOK [10000049]':
              case 'SO [10000134]':
              case 'CRM [10000162]':
              case 'VNPAY [10000165]':
              case 'GOJEK (GOVIET) [10000168]':
              case 'ShopeeFood [10000169]':
              case 'MANG VỀ [10000171]':
              case 'TẠI CHỖ [10000172]':
              case 'CALL CENTER [10000176]':
              case 'O2O [10000216]':
              case 'BEFOOD [10000253]':
                if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                  const price = parseFloat(String(cellValue))
                  if (!isNaN(price)) {
                    item[header] = price
                  }
                }
                break
              default:
                // Handle any additional columns
                if (cellValue !== undefined && cellValue !== null) {
                  item[header] = cellValue
                }
                break
            }
          })

          // Only add items that have required fields
          if (item.item_uid && item.item_id && item.item_name) {
            parsedData.push(item)
          }
        }

        console.log('📊 Parsed Excel data:', {
          totalRows: jsonData.length - 1,
          validItems: parsedData.length,
          headers,
          sampleItem: parsedData[0]
        })

        resolve(parsedData)
      } catch (error) {
        console.error('Error parsing Excel file:', error)
        reject(new Error('Có lỗi xảy ra khi đọc file Excel'))
      }
    }

    reader.onerror = () => {
      reject(new Error('Có lỗi xảy ra khi đọc file'))
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * Validate Excel file before parsing
 */
export const validatePriceBySourceExcelFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'application/octet-stream' // Sometimes Excel files are detected as this
  ]

  if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
    return {
      isValid: false,
      error: 'File phải có định dạng Excel (.xlsx hoặc .xls)'
    }
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File không được vượt quá 10MB'
    }
  }

  return { isValid: true }
}
