import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { type DeletePrinterPositionParams, printerPositionApi } from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const useDeletePrinterPosition = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: (id: string | string[]) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Missing company or brand information')
      }

      const params: DeletePrinterPositionParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: Array.isArray(id) ? id : [id]
      }

      return printerPositionApi.deletePrinterPosition(params)
    },
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PRINTER_POSITIONS] })
      toast.success(response.message || 'Xóa vị trí máy in thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa vị trí máy in')
    }
  })
}
