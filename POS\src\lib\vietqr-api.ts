import axios from 'axios'

import type { VietQRBanksResponse, VietQRQuickLinkRequest, VietQRQuickLinkResponse } from '@/types/api/vietqr-types'

// Create separate axios instance for VietQR banks API
const vietqrClient = axios.create({
  baseURL: 'https://api.vietqr.io',
  timeout: 30000,
  headers: {
    accept: 'application/json, text/plain, */*',
    'accept-language': 'vi',
    access_token: '5c885b2ef8c34fb7b1d1fad11eef7bec',
    authorization: '',
    'cache-control': 'no-cache',
    fabi_type: 'pos-cms',
    pragma: 'no-cache',
    'x-client-timezone': '********'
  }
})

/**
 * VietQR API client
 */
export const vietqrApi = {
  /**
   * Get list of banks from VietQR API
   */
  getBanks: async (): Promise<VietQRBanksResponse> => {
    const response = await vietqrClient.get<VietQRBanksResponse>('/v2/banks')
    return response.data
  },

  /**
   * Generate QR code quick link
   */
  generateQuickLink: async (data: VietQRQuickLinkRequest): Promise<VietQRQuickLinkResponse> => {
    // Mock response for testing UI
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay

    return {
      data: {
        imageURL: `https://img.vietqr.io/image/${data.bank_id}-${data.bank_acc}-compact.png?accountName=${encodeURIComponent(data.bank_acc_name)}`
      },
      track_id: `2025-0804-${Date.now()}-mock-test-id`
    }

    // Real API call (commented out for now due to CORS/auth issues)
    // const response = await api.post<VietQRQuickLinkResponse>('/v1/vietqr/quick-link', data, {
    //   headers: {
    //     Accept: 'application/json, text/plain, */*',
    //     'Cache-Control': 'no-cache',
    //     Connection: 'keep-alive',
    //     'Content-Type': 'application/json;charset=UTF-8',
    //     Pragma: 'no-cache',
    //     'accept-language': 'vi',
    //     access_token: '5c885b2ef8c34fb7b1d1fad11eef7bec',
    //     fabi_type: 'pos-cms',
    //     'x-client-timezone': '********'
    //   }
    // })
    // return response.data.data || response.data
  }
}
