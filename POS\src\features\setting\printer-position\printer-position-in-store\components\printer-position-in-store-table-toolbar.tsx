'use client'

import { useState, useEffect } from 'react'

import { Table } from '@tanstack/react-table'

import { X, Trash2, Store } from 'lucide-react'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { DataTableViewOptions } from '@/components/data-table'

import { usePrinterPositionInStore } from '../context'
import { PrinterPositionInStore } from '../data'

interface PrinterPositionInStoreTableToolbarProps<TData> {
  table: Table<TData>
  onStoreFilter?: (storeId: string | null) => void
  onScopeTypeChange?: (scopeType: string) => void
  currentScopeType?: string
  currentStoreId?: string | null
}

export function PrinterPositionInStoreTableToolbar<TData>({
  table,
  onStoreFilter,
  onScopeTypeChange,
  currentScopeType = '0',
  currentStoreId = null
}: PrinterPositionInStoreTableToolbarProps<TData>) {
  const { setOpen, setSelectedRows } = usePrinterPositionInStore()
  const [selectedStoreId, setSelectedStoreId] = useState<string | null>(null)
  const [scopeType, setScopeType] = useState<string>(currentScopeType)

  useEffect(() => {
    setScopeType(currentScopeType)
  }, [currentScopeType])

  useEffect(() => {
    setSelectedStoreId(currentStoreId ?? null)
  }, [currentStoreId])

  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()

  const { data: stores = [], isLoading: isLoadingStores } = useStoresData({
    params: {
      company_uid: companyUid || '',
      brand_uid: selectedBrand?.id || ''
    },
    enabled: !!(companyUid && selectedBrand?.id)
  })

  const isFiltered = table.getState().columnFilters.length > 0 || selectedStoreId !== null || scopeType !== '0'
  const selectedRows = table.getFilteredSelectedRowModel().rows
  const hasSelectedRows = selectedRows.length > 0

  const handleBulkDelete = () => {
    const selectedData = selectedRows.map(row => row.original as PrinterPositionInStore)
    setSelectedRows(selectedData)
    setOpen('bulk-delete')
  }

  const handleStoreFilter = (value: string) => {
    const storeId = value === '' ? null : value
    setSelectedStoreId(storeId)
    if (storeId === null) {
      table.getColumn('printerPositionName')?.setFilterValue('')
      setScopeType('0')
    }
    if (onStoreFilter) {
      onStoreFilter(storeId)
    }
  }

  const handleScopeTypeChange = (value: string) => {
    table.getColumn('printerPositionName')?.setFilterValue('')
    if (onScopeTypeChange) {
      onScopeTypeChange(value)
    }
  }

  const handleResetFilters = () => {
    table.resetColumnFilters()
    setSelectedStoreId(null)
    setScopeType('0')
    if (onStoreFilter) {
      onStoreFilter(null)
    }
  }

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Tìm kiếm vị trí máy in...'
          value={(table.getColumn('printerPositionName')?.getFilterValue() as string) ?? ''}
          onChange={event => table.getColumn('printerPositionName')?.setFilterValue(event.target.value)}
          className='h-8 w-[150px] lg:w-[250px]'
          disabled={selectedStoreId === null}
        />

        <Select value={scopeType} onValueChange={handleScopeTypeChange} disabled={selectedStoreId === null}>
          <SelectTrigger className='h-8 w-[200px]'>
            <Store className='mr-2 h-4 w-4' />
            <SelectValue placeholder='Phạm vi' />
          </SelectTrigger>
          <SelectContent className='z-50'>
            <SelectItem value='0'>Cửa hàng và thương hiệu</SelectItem>
            <SelectItem value='1'>Cửa hàng</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedStoreId || undefined} onValueChange={handleStoreFilter}>
          <SelectTrigger className='h-8 w-[200px]'>
            <Store className='mr-2 h-4 w-4' />
            <SelectValue placeholder='Chọn cửa hàng *' />
          </SelectTrigger>
          <SelectContent className='z-50'>
            {isLoadingStores && (
              <SelectItem value='loading' disabled>
                Đang tải...
              </SelectItem>
            )}
            {!isLoadingStores && stores.length === 0 && (
              <SelectItem value='no-stores' disabled>
                Không có cửa hàng nào
              </SelectItem>
            )}
            {!isLoadingStores &&
              stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.name}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        {isFiltered && (
          <Button variant='ghost' onClick={handleResetFilters} className='h-8 px-2 lg:px-3'>
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}

        {hasSelectedRows && (
          <div className='ml-4 flex items-center space-x-2'>
            <span className='text-muted-foreground text-sm'>{selectedRows.length} hàng được chọn</span>
            <Button variant='ghost' size='sm' onClick={() => table.resetRowSelection()} className='h-8'>
              Hủy chọn
            </Button>
          </div>
        )}
      </div>

      <div className='flex items-center gap-2'>
        {hasSelectedRows && (
          <Button variant='destructive' size='sm' onClick={handleBulkDelete} className='h-8'>
            <Trash2 className='mr-2 h-4 w-4' />
            Xóa ({selectedRows.length})
          </Button>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}
