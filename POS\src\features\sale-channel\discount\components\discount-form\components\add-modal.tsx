import { useState, useEffect } from 'react'

import { useQuery } from '@tanstack/react-query'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import { PosModal } from '@/components/pos'
import { Button, Checkbox, Input, Label } from '@/components/ui'

import { GroupsSection } from './groups-section'
import { ItemsSection } from './items-section'
import { PackagesSection } from './packages-section'

interface AddModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  storeUid: string
  onSave: (selectedItems: string[], applyToAll: boolean, activeFilter: FilterType | null) => void
  initialApplyToAll?: boolean
  initialActiveFilter?: FilterType | null
  initialSelectedItems?: string[]
}

type FilterType = 'groups' | 'items' | 'packages'

export function AddModal({
  open,
  onOpenChange,
  storeUid,
  onSave,
  initialApplyToAll = false,
  initialActiveFilter = null,
  initialSelectedItems = []
}: AddModalProps) {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const [searchTerm, setSearchTerm] = useState('')
  const [applyToAll, setApplyToAll] = useState(false)
  const [activeFilter, setActiveFilter] = useState<FilterType | null>(null)
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const { data: itemTypes = [], isLoading: isLoadingItemTypes } = useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/mdata/v1/item-types?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const { data: items = [], isLoading: isLoadingItems } = useQuery({
    queryKey: [QUERY_KEYS.ITEMS, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/mdata/v1/items?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&list_store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const { data: packages = [], isLoading: isLoadingPackages } = useQuery({
    queryKey: [QUERY_KEYS.PACKAGES, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/mdata/v1/packages?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const isLoading = isLoadingItemTypes || isLoadingItems || isLoadingPackages

  useEffect(() => {
    if (open) {
      setSearchTerm('')
      setApplyToAll(initialApplyToAll)
      setActiveFilter(initialActiveFilter)
      setSelectedItems(initialSelectedItems)
    }
  }, [open, initialApplyToAll, initialActiveFilter, initialSelectedItems])

  const handleApplyToAllChange = (checked: boolean) => {
    setApplyToAll(checked)
    if (checked) {
      setActiveFilter(null)
    }
  }

  const handleFilterToggle = (filter: FilterType) => {
    setApplyToAll(false)
    setActiveFilter(prev => {
      const newFilter = prev === filter ? null : filter
      // Clear selectedItems when switching to a different filter
      if (newFilter !== prev) {
        setSelectedItems([])
      }
      return newFilter
    })
  }

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => (prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]))
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const handleConfirm = () => {
    if (applyToAll) {
      onSave(['all'], true, null)
    } else {
      onSave(selectedItems, false, activeFilter)
    }
    onOpenChange(false)
  }

  return (
    <PosModal
      title='Áp dụng cho'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      cancelText='Hủy'
      confirmText='Lưu'
      maxWidth='sm:max-w-2xl'
      isLoading={isLoading}
      confirmDisabled={!applyToAll && selectedItems.length === 0}
    >
      <div className='space-y-4'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center space-x-2'>
            <Checkbox id='apply-all' checked={applyToAll} onCheckedChange={handleApplyToAllChange} />
            <Label htmlFor='apply-all' className='text-sm font-medium'>
              Áp dụng cho tất cả món và nhóm
            </Label>
          </div>
          <Input
            placeholder='Tìm kiếm'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='flex-1'
          />
        </div>

        <div className='flex items-center gap-4'>
          <Label className='text-sm font-medium'>Áp dụng cho</Label>
          <div className='flex gap-2'>
            <Button
              type='button'
              variant={activeFilter === 'groups' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('groups')}
            >
              Nhóm
            </Button>
            <Button
              type='button'
              variant={activeFilter === 'items' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('items')}
            >
              Món ăn
            </Button>
            <Button
              type='button'
              variant={activeFilter === 'packages' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('packages')}
            >
              Combo
            </Button>
          </div>
        </div>

        {!applyToAll && (
          <div className='space-y-2'>
            {activeFilter === 'groups' && (
              <GroupsSection
                itemTypes={itemTypes as any[]}
                selectedItems={selectedItems}
                searchTerm={searchTerm}
                onItemToggle={handleItemToggle}
              />
            )}
            {activeFilter === 'items' && (
              <ItemsSection
                items={items as any[]}
                selectedItems={selectedItems}
                searchTerm={searchTerm}
                isLoading={isLoading}
                onItemToggle={handleItemToggle}
              />
            )}
            {activeFilter === 'packages' && (
              <PackagesSection
                packages={packages as any[]}
                selectedItems={selectedItems}
                searchTerm={searchTerm}
                isLoading={isLoading}
                onItemToggle={handleItemToggle}
              />
            )}
          </div>
        )}
      </div>
    </PosModal>
  )
}
