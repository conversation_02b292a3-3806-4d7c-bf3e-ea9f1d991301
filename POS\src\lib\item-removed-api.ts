import type { RemovedItem, RemovedItemApiData, CityData } from '@/types/item-removed'
import { convertApiRemovedItemToRemovedItem } from '@/types/item-removed'

import { apiClient } from './api/pos/pos-api'

// API response interface
interface RemovedItemsApiResponse {
  data: RemovedItemApiData[]
  track_id: string
}

// Parameters for fetching removed items
export interface GetRemovedItemsParams {
  searchTerm?: string
  page?: number
  listCityUid?: string[] // Array of city UIDs
  listStoreUid?: string[] // Array of store UIDs
}

/**
 * Get all cities from localStorage
 */
const getAllCityUids = (): string[] => {
  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      return cities.map(city => city.id)
    }
  } catch {
    // Error parsing cities data
  }
  return []
}

/**
 * Fetch removed items from the API
 */
export const getRemovedItems = async (params: GetRemovedItemsParams = {}): Promise<RemovedItem[]> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    page: (params.page || 1).toString()
  })

  // Add search term if provided
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  if (params.listStoreUid && params.listStoreUid.length > 0) {
    // If specific stores are selected, use list_store_uid
    queryParams.append('list_store_uid', params.listStoreUid.join(','))
  } else if (params.listCityUid && params.listCityUid.length > 0) {
    const allCityUids = getAllCityUids()
    const isAllCitiesSelected =
      params.listCityUid.length === allCityUids.length &&
      params.listCityUid.every(cityUid => allCityUids.includes(cityUid))

    if (isAllCitiesSelected) {
      // User explicitly selected "All cities" - send all city UIDs
      queryParams.append('list_city_uid', allCityUids.join(','))
    } else {
      // User selected specific cities (e.g., just "Hà Nội") - send only selected ones
      queryParams.append('list_city_uid', params.listCityUid.join(','))
    }
  } else {
    // No specific selection provided - default to all cities (for "Tất cả thành phố" initial state)
    const allCityUids = getAllCityUids()

    if (allCityUids.length > 0) {
      queryParams.append('list_city_uid', allCityUids.join(','))
    } else {
      throw new Error('No cities or stores available for filtering')
    }
  }

  const apiUrl = `/mdata/v1/item-removed?${queryParams.toString()}`

  const response = await apiClient.get<RemovedItemsApiResponse>(apiUrl)

  if (response.data?.data) {
    // Convert API data to RemovedItem objects
    const convertedItems = response.data.data.map((apiRemovedItem: RemovedItemApiData) => {
      return convertApiRemovedItemToRemovedItem(apiRemovedItem)
    })

    return convertedItems
  }

  return []
}

/**
 * Restore a removed item
 */
export const restoreRemovedItem = async (itemId: string): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')

  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // For restore functionality, we'll need to call an API endpoint
  // This is a placeholder - the actual endpoint might be different
  const payload = {
    company_uid: companyUid,
    brand_uid: brandUid,
    item_id: itemId
  }

  await apiClient.post('/mdata/v1/item-restore', payload)
}

/**
 * Bulk restore removed items
 */
export const bulkRestoreRemovedItems = async (itemUids: string[]): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')

  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  if (!itemUids || itemUids.length === 0) {
    throw new Error('No items selected for restore')
  }

  // Create the list_item_uid parameter as comma-separated string
  const listItemUid = itemUids.join(',')

  const url = `/mdata/v1/item-removed?company_uid=${companyUid}&brand_uid=${brandUid}&apply_with_store=true&list_item_uid=${listItemUid}`

  await apiClient.patch(url)
}

/**
 * Export removed items report by cities
 */
export const exportRemovedItemsReport = async (cityUids: string[]): Promise<Blob> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')

  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  if (!cityUids || cityUids.length === 0) {
    throw new Error('City UIDs are required for export')
  }

  // Build URL with parameters
  const listCityUid = cityUids.join(',')
  const url = `/mdata/v1/item-removed?company_uid=${companyUid}&brand_uid=${brandUid}&list_city_uid=${listCityUid}&results_per_page=15000`

  const response = await apiClient.get(url, {
    responseType: 'blob'
  })

  return response.data
}

/**
 * Export removed items report by stores
 */
export const exportRemovedItemsReportByStores = async (storeUids: string[]): Promise<Blob> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')

  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  if (!storeUids || storeUids.length === 0) {
    throw new Error('Store UIDs are required for export')
  }

  // Build URL with parameters
  const listStoreUid = storeUids.join(',')
  const url = `/mdata/v1/item-removed?company_uid=${companyUid}&brand_uid=${brandUid}&list_store_uid=${listStoreUid}&results_per_page=15000`

  const response = await apiClient.get(url, {
    responseType: 'blob'
  })

  return response.data
}

/**
 * Get cities from localStorage
 */
export const getCitiesFromLocalStorage = (): CityData[] => {
  const posCitiesData = localStorage.getItem('pos_cities_data')

  if (posCitiesData) {
    try {
      const citiesData = JSON.parse(posCitiesData)
      if (Array.isArray(citiesData)) {
        return citiesData
      }
    } catch {
      // Error parsing cities data
    }
  }

  return []
}

// Export all API functions
export const removedItemsApi = {
  getRemovedItems,
  restoreRemovedItem,
  bulkRestoreRemovedItems,
  exportRemovedItemsReport,
  exportRemovedItemsReportByStores,
  getCitiesFromLocalStorage
}
