import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetDiscountPaymentParams } from '@/types/discount-payment'
import { toast } from 'sonner'

import { discountPaymentApi } from '@/lib/discount-payment-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseDiscountPaymentDataOptions {
  params?: Partial<GetDiscountPaymentParams>
}

export const useDiscountPaymentData = (options: UseDiscountPaymentDataOptions = {}) => {
  const { params = {} } = options

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT, params],
    queryFn: async () => {
      if (!params.company_uid || !params.brand_uid) {
        throw new Error('Company UID and Brand UID are required')
      }

      const response = await discountPaymentApi.getDiscountPayments(params as GetDiscountPaymentParams)
      return response.data || []
    }
  })
}

export const useUpdateDiscountPayment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (discountPaymentData: any) => discountPaymentApi.updateDiscountPayment(discountPaymentData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT]
      })
      toast.success('Đã cập nhật trạng thái chiết khấu thành công!')
    },
    onError: (error: any) => {
      console.error('Error updating discount payment:', error)
      toast.error(error.message || 'Lỗi khi cập nhật chiết khấu thanh toán')
    }
  })
}

export const useDeleteDiscountPayment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: { id: string; company_uid: string; brand_uid: string }) =>
      discountPaymentApi.deleteDiscountPayment(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT] })
      toast.success('Đã xóa chiết khấu thanh toán thành công!')
    },
    onError: (error: any) => {
      console.error('Error deleting discount payment:', error)
      toast.error(error.message || 'Lỗi khi xóa chiết khấu thanh toán')
    }
  })
}

/**
 * Hook to get discount payment programs for a specific store
 */
export const useDiscountPaymentPrograms = (params: {
  company_uid?: string
  brand_uid?: string
  store_uid?: string
}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT, 'programs', params.company_uid, params.brand_uid, params.store_uid],
    queryFn: async () => {
      if (!params.company_uid || !params.brand_uid || !params.store_uid) {
        throw new Error('Missing required parameters')
      }
      return discountPaymentApi.getDiscountPaymentPrograms({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        store_uid: params.store_uid
      })
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to clone discount payment programs
 */
export const useCloneDiscountPayments = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: discountPaymentApi.cloneDiscountPayments,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT] })
      toast.success('Đã sao chép chiết khấu thanh toán thành công!')
    },
    onError: (error: any) => {
      console.error('Error cloning discount payments:', error)
      toast.error(error.message || 'Lỗi khi sao chép chiết khấu thanh toán')
    }
  })
}
