import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig } from 'axios'

import { useAuthStore } from '@/stores/authStore'

const API_BASE_URL = 'https://posapi.ipos.vn/api'

const STATIC_ACCESS_TOKEN = import.meta.env.VITE_POS_ACCESS_TOKEN || '5c885b2ef8c34fb7b1d1fad11eef7bec'

export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    Accept: 'application/json, text/plain, */*',
    Connection: 'keep-alive',
    Origin: 'https://fabi.ipos.vn',
    Referer: 'https://fabi.ipos.vn/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent':
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept-language': 'vi',
    access_token: STATIC_ACCESS_TOKEN,
    fabi_type: 'pos-cms',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'x-client-timezone': '25200000'
  }
})

apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const { accessToken, jwtToken } = useAuthStore.getState().auth

    const tokenToUse = accessToken || STATIC_ACCESS_TOKEN
    if (tokenToUse) {
      config.headers.set('access_token', tokenToUse)
    }

    if (jwtToken) {
      config.headers.set('Authorization', `${jwtToken}`)
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  error => {
    if (error.response?.status === 401) {
      useAuthStore.getState().auth.reset()
    }
    return Promise.reject(error.response || error)
  }
)

export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  success?: boolean
}

export const api = {
  get: <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.get(url, config),

  post: <T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> => apiClient.post(url, data, config),

  put: <T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> => apiClient.put(url, data, config),

  patch: <T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> => apiClient.patch(url, data, config),

  delete: <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.delete(url, config)
}

export default api
