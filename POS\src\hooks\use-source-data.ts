import { useMemo } from 'react'

import { useQuery } from '@tanstack/react-query'

import { usePosStores, useCurrentCompany } from '@/stores/posStore'

import { apiClient } from '@/lib/api/pos/pos-api'

interface SourceDataItem {
  source_id: string
  source_name: string
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  source_type: string[]
  description: string | null
  extra_data: unknown
  is_fb: number
  active: number
  revision: string | null
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  store_uid: string
  partner_config: number
  row: number
  stores: number
}

interface SourceDataResponse {
  data: SourceDataItem[]
  message?: string
  track_id?: string
}

interface SourceOption {
  value: string
  label: string
  id: string
}

interface UseSourceDataReturn {
  sources: SourceDataItem[]
  sourceOptions: SourceOption[]
  isLoading: boolean
  error: string | null
  refetch: () => void
  selectedBrand: unknown
}

export const sourceDataKeys = {
  all: ['sourceData'] as const,
  lists: () => [...sourceDataKeys.all, 'list'] as const,
  list: (filters: { companyUid?: string; brandUid?: string; storeUid?: string; page?: number; skipLimit?: boolean }) =>
    [...sourceDataKeys.lists(), filters] as const
}

export function useSourceData(
  options: {
    autoFetch?: boolean
    storeUid?: string
    brandUid?: string
    skipLimit?: boolean
  } = {}
): UseSourceDataReturn {
  const { autoFetch = true, storeUid, brandUid, skipLimit } = options
  const { selectedBrand } = usePosStores()
  const { company } = useCurrentCompany()

  const brandId = brandUid || selectedBrand?.id
  const companyId = company?.id

  const {
    data: sourceData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: sourceDataKeys.list({
      companyUid: companyId,
      brandUid: brandId,
      storeUid: storeUid,
      skipLimit: skipLimit,
      page: 1
    }),
    queryFn: async (): Promise<SourceDataResponse> => {
      if (!brandId || !companyId) {
        throw new Error('Brand or company not selected')
      }

      const queryParams = new URLSearchParams({
        company_uid: companyId,
        brand_uid: brandId,
        page: '1'
      })

      if (storeUid) {
        queryParams.append('store_uid', storeUid)
      }

      if (skipLimit) {
        queryParams.append('skip_limit', 'true')
      }

      const response = await apiClient.get(`/mdata/v1/sources?${queryParams.toString()}`)

      return response.data as SourceDataResponse
    },
    enabled: autoFetch && !!brandId && !!companyId,
    staleTime: 10 * 60 * 1000, // 10 minutes - source data doesn't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount: number, error: Error) => {
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 3
    }
  })

  // Process the data to return only active sources
  const activeSources = useMemo(() => {
    if (!sourceData?.data) return []
    return sourceData.data.filter(source => source.active === 1)
  }, [sourceData?.data])

  // Create options for select components
  const sourceOptions = useMemo(() => {
    return activeSources.map(source => ({
      value: source.source_id,
      label: source.source_name,
      id: source.id
    }))
  }, [activeSources])

  return {
    sources: activeSources,
    sourceOptions,
    isLoading,
    error: error?.message || null,
    refetch,
    selectedBrand
  }
}

export default useSourceData
