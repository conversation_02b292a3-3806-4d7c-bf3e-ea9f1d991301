import React, { useState } from 'react'

import { useDraggable, useDroppable } from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'
import { Trash2 } from 'lucide-react'

import { cn } from '@/lib/utils'

import { useDeleteTable } from '@/hooks/api/use-tables'

import type { TableLayoutItem } from '../../data/table-layout-types'

interface TableItemProps {
  table: TableLayoutItem
  isSelected?: boolean
  isDragging?: boolean
  isOverlay?: boolean
  onClick?: () => void
  storeUid?: string
}

export const TableItem: React.FC<TableItemProps> = ({
  table,
  isSelected = false,
  isDragging = false,
  isOverlay = false,
  onClick,
  storeUid
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const { deleteTable, isDeleting } = useDeleteTable(storeUid || '')
  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    transform,
    isDragging: isDraggingFromHook
  } = useDraggable({
    id: table.id,
    data: {
      table
    }
  })

  const { setNodeRef: setDropRef, isOver } = useDroppable({
    id: table.id
  })

  const setNodeRef = (node: HTMLElement | null) => {
    setDragRef(node)
    setDropRef(node)
  }

  const dynamicStyle = isOverlay
    ? {
        width: table.size?.width || 180,
        height: 150
      }
    : {
        transform: CSS.Translate.toString(transform),
        left: table.position?.x || table.position_x || 0,
        top: table.position?.y || table.position_y || 0,
        width: table.size?.width || 180,
        height: 150
      }
  const isCurrentlyDragging = isDragging || isDraggingFromHook

  const semiCircleColor = isSelected ? 'bg-blue-500' : isOver ? 'bg-yellow-400' : 'bg-[#b9d9c2]'

  const getTableIdSuffix = (tableId: string | undefined) => {
    if (!tableId) return ''
    const parts = tableId.split('-')
    return parts.length > 1 ? parts[parts.length - 1] : tableId
  }
  const handleDeleteTable = async (event: React.MouseEvent) => {
    event.stopPropagation()

    if (!storeUid) {
      return
    }

    try {
      await deleteTable(table.id)
    } catch (error) {
      console.error('Error deleting table:', error)
    }
  }
  if (isCurrentlyDragging && !isOverlay) {
    return null
  }

  return (
    <div
      ref={isOverlay ? undefined : setNodeRef}
      style={dynamicStyle}
      {...(isOverlay ? {} : listeners)}
      {...(isOverlay ? {} : attributes)}
      onClick={isOverlay ? undefined : onClick}
      onMouseEnter={() => !isOverlay && setIsHovered(true)}
      onMouseLeave={() => !isOverlay && setIsHovered(false)}
      className={cn(
        'relative cursor-pointer rounded-[20px] transition-all duration-200 select-none',
        'bg-[#dbf2e2] shadow-lg hover:shadow-xl',
        {
          absolute: !isOverlay,
          'z-[1000]': (isDragging || isDraggingFromHook) && !isOverlay,
          'z-[1]': !(isDragging || isDraggingFromHook) && !isOverlay,
          'bg-blue-200/70': isSelected,
          'bg-yellow-200/70': isOver && !isSelected,
          'scale-105': isSelected,
          'scale-102': isOver && !isSelected,
          'scale-110 rotate-3 shadow-2xl': isOverlay
        }
      )}
    >
      <div
        className={cn(
          'pointer-events-none absolute -top-[18px] left-1/2 h-[18px] w-9 -translate-x-1/2',
          'rounded-t-[18px]',
          semiCircleColor
        )}
      ></div>

      <div
        className={cn(
          'pointer-events-none absolute top-1/2 -right-[18px] h-9 w-[18px] -translate-y-1/2',
          'rounded-r-[18px]',
          semiCircleColor
        )}
      ></div>

      <div
        className={cn(
          'pointer-events-none absolute -bottom-[18px] left-1/2 h-[18px] w-9 -translate-x-1/2',
          'rounded-b-[18px]',
          semiCircleColor
        )}
      ></div>

      <div
        className={cn(
          'pointer-events-none absolute top-1/2 -left-[18px] h-9 w-[18px] -translate-y-1/2',
          'rounded-l-[18px]',
          semiCircleColor
        )}
      ></div>
      <div className='pointer-events-none absolute top-3 left-3 text-sm font-bold text-gray-800'>
        {table.table_name}
      </div>

      <div className='pointer-events-none absolute top-3 right-3 text-sm font-medium text-gray-700'>
        {getTableIdSuffix('table_id' in table ? (table as any).table_id : table.id)}
      </div>

      <div className='pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2'>
        <div className='rounded-lg bg-[#b9d9c2] px-3 py-1.5 text-xs font-medium text-gray-800'>
          {(table as any).area?.area_name || 'N/A'} - {getTableIdSuffix((table as any).area?.area_id)}
        </div>
      </div>

      {table.extra_data?.order_list && table.extra_data.order_list.length > 0 && (
        <div className='pointer-events-none absolute bottom-3 left-1/2 -translate-x-1/2 text-xs font-medium text-blue-600'>
          {table.extra_data.order_list.length} món đặt trước
        </div>
      )}

      {isHovered && !isOverlay && (
        <button
          onClick={handleDeleteTable}
          disabled={isDeleting}
          className='absolute right-2 bottom-2 rounded-full bg-red-500 p-1.5 text-white shadow-lg transition-all hover:bg-red-600 disabled:opacity-50'
          title='Xóa bàn'
        >
          <Trash2 className='h-3 w-3' />
        </button>
      )}
    </div>
  )
}
