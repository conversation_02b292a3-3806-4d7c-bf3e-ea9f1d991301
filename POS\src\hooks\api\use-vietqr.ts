import { useQuery, useMutation } from '@tanstack/react-query'

import type { VietQRQuickLinkRequest } from '@/types/api/vietqr-types'
import { toast } from 'sonner'

import { vietqrApi } from '@/lib/vietqr-api'

import { QUERY_KEYS } from '@/constants/query-keys'

/**
 * Hook to fetch VietQR banks list
 */
export const useVietQRBanks = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.VIETQR_BANKS],
    queryFn: async () => {
      const response = await vietqrApi.getBanks()
      return response.data || []
    },
    staleTime: 24 * 60 * 60 * 1000, // 24 hours - banks list doesn't change often
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}

/**
 * Hook to generate VietQR quick link
 */
export const useGenerateVietQR = () => {
  return useMutation({
    mutationFn: async (data: VietQRQuickLinkRequest) => {
      return await vietqrApi.generateQuickLink(data)
    },
    onSuccess: () => {
      toast.success('Tạo mã QR thành công!')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo mã QR'
      toast.error(errorMessage)
    }
  })
}
