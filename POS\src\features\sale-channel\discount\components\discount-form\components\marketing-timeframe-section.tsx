import { HelpCircle } from 'lucide-react'

import { <PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from '@/components/ui'

import { useDiscountFormData } from '../stores'

const DAYS_OF_WEEK = [
  { label: 'T2', value: '0' }, // Monday = 0
  { label: 'T3', value: '1' }, // Tuesday = 1
  { label: 'T4', value: '2' }, // Wednesday = 2
  { label: 'T5', value: '3' }, // Thursday = 3
  { label: 'T6', value: '4' }, // Friday = 4
  { label: 'T7', value: '5' }, // Saturday = 5
  { label: 'CN', value: '6' } // Sunday = 6
]

const TIME_SLOTS = [
  { value: '0' },
  { value: '1' },
  { value: '2' },
  { value: '3' },
  { value: '4' },
  { value: '5' },
  { value: '6' },
  { value: '7' },
  { value: '8' },
  { value: '9' },
  { value: '10' },
  { value: '11' },
  { value: '12' },
  { value: '13' },
  { value: '14' },
  { value: '15' },
  { value: '16' },
  { value: '17' },
  { value: '18' },
  { value: '19' },
  { value: '20' },
  { value: '21' },
  { value: '22' },
  { value: '23' }
]

export function MarketingTimeframeSection() {
  const { formData, updateFormData } = useDiscountFormData()
  const handleDayToggle = (day: string) => {
    const currentDays = formData.marketingDays || []
    const updatedDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day]

    updateFormData({ marketingDays: updatedDays })
  }

  const handleTimeToggle = (time: string) => {
    const currentTimes = formData.marketingHours || []
    const updatedTimes = currentTimes.includes(time) ? currentTimes.filter(t => t !== time) : [...currentTimes, time]

    updateFormData({ marketingHours: updatedTimes })
  }

  return (
    <TooltipProvider>
      <div className='space-y-4'>
        <h2 className='text-lg font-medium text-gray-900'>Khung thời gian áp dụng</h2>

        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Label className='text-sm font-medium text-gray-500'>Chọn ngày</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
              </TooltipTrigger>
              <TooltipContent className='max-w-xs'>
                <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <div className='flex gap-2'>
            {DAYS_OF_WEEK.map(day => (
              <Button
                key={day.value}
                type='button'
                variant={formData.marketingDays?.includes(day.value) ? 'default' : 'outline'}
                size='sm'
                onClick={() => handleDayToggle(day.value)}
                className='flex-1'
              >
                {day.label}
              </Button>
            ))}
          </div>
        </div>

        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Label className='text-sm font-medium text-gray-500'>Chọn giờ</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
              </TooltipTrigger>
              <TooltipContent className='max-w-xs'>
                <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <div className='grid grid-cols-6 gap-2'>
            {TIME_SLOTS.map(time => (
              <Button
                key={time.value}
                type='button'
                variant={formData.marketingHours?.includes(time.value) ? 'default' : 'outline'}
                size='sm'
                onClick={() => handleTimeToggle(time.value)}
                className='text-xs'
              >
                {time.value}:00
              </Button>
            ))}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
