import { useQuery } from '@tanstack/react-query'

import { useCurrentBrand } from '@/stores'
import type { GetRegisterPageConfigResponse } from '@/types/api/crm'

import { marketingApi } from '@/lib/api/crm'

import { CRM_QUERY_KEYS } from '@/constants/crm'

interface UseRegisterPageConfigOptions {
  pos_parent?: string
}

export function useRegisterPageConfig(options: UseRegisterPageConfigOptions = {}) {
  const { selectedBrand } = useCurrentBrand()
  const resolvedPosParent = options.pos_parent || selectedBrand?.brandId || ''

  return useQuery<GetRegisterPageConfigResponse>({
    queryKey: ['crm', CRM_QUERY_KEYS.REGISTER_PAGE_CONFIG, resolvedPosParent],
    queryFn: async () => marketingApi.getRegisterPageConfig(resolvedPosParent),
    enabled: !!resolvedPosParent
  })
}
