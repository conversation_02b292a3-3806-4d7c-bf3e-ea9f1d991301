/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ClerkRouteImport } from './routes/clerk/route'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedMembershipImport } from './routes/_authenticated/membership'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignUpImport } from './routes/(auth)/sign-up'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordImport } from './routes/(auth)/forgot-password'
import { Route as ClerkAuthenticatedRouteImport } from './routes/clerk/_authenticated/route'
import { Route as ClerkauthRouteImport } from './routes/clerk/(auth)/route'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedUsersIndexImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedHelpCenterIndexImport } from './routes/_authenticated/help-center/index'
import { Route as ClerkAuthenticatedUserManagementImport } from './routes/clerk/_authenticated/user-management'
import { Route as ClerkauthSignUpImport } from './routes/clerk/(auth)/sign-up'
import { Route as ClerkauthSignInImport } from './routes/clerk/(auth)/sign-in'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedSettingTableLayoutImport } from './routes/_authenticated/setting/table-layout'
import { Route as AuthenticatedSettingTableImport } from './routes/_authenticated/setting/table'
import { Route as AuthenticatedSettingStoreImport } from './routes/_authenticated/setting/store'
import { Route as AuthenticatedSettingAreaImport } from './routes/_authenticated/setting/area'
import { Route as AuthenticatedSaleServiceChargeImport } from './routes/_authenticated/sale/service-charge'
import { Route as AuthenticatedSaleDiscountPaymentImport } from './routes/_authenticated/sale/discount-payment'
import { Route as AuthenticatedSaleComboImport } from './routes/_authenticated/sale/combo'
import { Route as AuthenticatedSaleChannelDiscountImport } from './routes/_authenticated/sale-channel/discount'
import { Route as AuthenticatedMenuScheduleImport } from './routes/_authenticated/menu/schedule'
import { Route as AuthenticatedMenuQuantityDayImport } from './routes/_authenticated/menu/quantity-day'
import { Route as AuthenticatedMenuItemClassImport } from './routes/_authenticated/menu/item-class'
import { Route as AuthenticatedGeneralSetupsCreateUserImport } from './routes/_authenticated/general-setups/create-user'
import { Route as AuthenticatedGeneralSetupsAccountImport } from './routes/_authenticated/general-setups/account'
import { Route as AuthenticatedEmployeeRoleImport } from './routes/_authenticated/employee/role'
import { Route as AuthenticatedEmployeeListImport } from './routes/_authenticated/employee/list'
import { Route as AuthenticatedEmployeeDetailImport } from './routes/_authenticated/employee/detail'
import { Route as AuthenticatedDevicesTypesImport } from './routes/_authenticated/devices/types'
import { Route as AuthenticatedDevicesNewImport } from './routes/_authenticated/devices/new'
import { Route as AuthenticatedDevicesListImport } from './routes/_authenticated/devices/list'
import { Route as AuthenticatedCrmUsingMonthImport } from './routes/_authenticated/crm/using-month'
import { Route as AuthenticatedCrmSystemLogImport } from './routes/_authenticated/crm/system-log'
import { Route as AuthenticatedCrmSettingsImport } from './routes/_authenticated/crm/settings'
import { Route as AuthenticatedCrmPricingTableImport } from './routes/_authenticated/crm/pricing-table'
import { Route as AuthenticatedCrmCustomerProfileImport } from './routes/_authenticated/crm/customer-profile'
import { Route as AuthenticatedCrmCustomerListImport } from './routes/_authenticated/crm/customer-list'
import { Route as AuthenticatedCrmCustomerGroupImport } from './routes/_authenticated/crm/customer-group'
import { Route as AuthenticatedCrmConnectCrmImport } from './routes/_authenticated/crm/connect-crm'
import { Route as AuthenticatedCrmConfigRegisterPageImport } from './routes/_authenticated/crm/config-register-page'
import { Route as AuthenticatedCrmBillingDetailImport } from './routes/_authenticated/crm/billing-detail'
import { Route as AuthenticatedSettingTableIndexImport } from './routes/_authenticated/setting/table/index'
import { Route as AuthenticatedSettingTableLayoutIndexImport } from './routes/_authenticated/setting/table-layout/index'
import { Route as AuthenticatedSettingStoreIndexImport } from './routes/_authenticated/setting/store/index'
import { Route as AuthenticatedSettingSourceIndexImport } from './routes/_authenticated/setting/source/index'
import { Route as AuthenticatedSettingPaymentMethodIndexImport } from './routes/_authenticated/setting/payment-method/index'
import { Route as AuthenticatedSettingBillModelIndexImport } from './routes/_authenticated/setting/bill-model/index'
import { Route as AuthenticatedSettingAreaIndexImport } from './routes/_authenticated/setting/area/index'
import { Route as AuthenticatedSaleServiceChargeIndexImport } from './routes/_authenticated/sale/service-charge/index'
import { Route as AuthenticatedSalePromotionIndexImport } from './routes/_authenticated/sale/promotion/index'
import { Route as AuthenticatedSaleDiscountPaymentIndexImport } from './routes/_authenticated/sale/discount-payment/index'
import { Route as AuthenticatedSaleChannelDiscountIndexImport } from './routes/_authenticated/sale-channel/discount/index'
import { Route as AuthenticatedSaleChannelChannelIndexImport } from './routes/_authenticated/sale-channel/channel/index'
import { Route as AuthenticatedMenuItemClassIndexImport } from './routes/_authenticated/menu/item-class/index'
import { Route as AuthenticatedEmployeeRoleIndexImport } from './routes/_authenticated/employee/role/index'
import { Route as AuthenticatedSettingTableDetailImport } from './routes/_authenticated/setting/table/detail'
import { Route as AuthenticatedSettingStoreDetailImport } from './routes/_authenticated/setting/store/detail'
import { Route as AuthenticatedSettingAreaDetailImport } from './routes/_authenticated/setting/area/detail'
import { Route as AuthenticatedSaleServiceChargeDetailImport } from './routes/_authenticated/sale/service-charge/detail'
import { Route as AuthenticatedSaleDiscountRegularImport } from './routes/_authenticated/sale/discount/regular'
import { Route as AuthenticatedSaleDiscountMembershipImport } from './routes/_authenticated/sale/discount/membership'
import { Route as AuthenticatedSaleDiscountPaymentDetailImport } from './routes/_authenticated/sale/discount-payment/detail'
import { Route as AuthenticatedSaleComboDetailImport } from './routes/_authenticated/sale/combo/detail'
import { Route as AuthenticatedSaleChannelDiscountDetailImport } from './routes/_authenticated/sale-channel/discount/detail'
import { Route as AuthenticatedSaleChannelChannelDetailImport } from './routes/_authenticated/sale-channel/channel/detail'
import { Route as AuthenticatedReportRevenueSaleSummaryImport } from './routes/_authenticated/report/revenue/sale-summary'
import { Route as AuthenticatedReportAccountingSaleDetailAuditImport } from './routes/_authenticated/report/accounting/sale-detail-audit'
import { Route as AuthenticatedMenuItemRemovedItemRemovedInStoreImport } from './routes/_authenticated/menu/item-removed/item-removed-in-store'
import { Route as AuthenticatedMenuItemRemovedItemRemovedInCityImport } from './routes/_authenticated/menu/item-removed/item-removed-in-city'
import { Route as AuthenticatedMenuItemClassDetailImport } from './routes/_authenticated/menu/item-class/detail'
import { Route as AuthenticatedMenuCustomizationNewImport } from './routes/_authenticated/menu/customization/new'
import { Route as AuthenticatedMenuCategoryDetailImport } from './routes/_authenticated/menu/category/detail'
import { Route as AuthenticatedMenuCategoryInStoreDetailImport } from './routes/_authenticated/menu/category-in-store/detail'
import { Route as AuthenticatedMenuCategoriesCategoriesInStoreImport } from './routes/_authenticated/menu/categories/categories-in-store'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandImport } from './routes/_authenticated/menu/categories/categories-in-brand'
import { Route as AuthenticatedEmployeeRoleDetailImport } from './routes/_authenticated/employee/role/detail'
import { Route as AuthenticatedEmployeeDetailUserIdImport } from './routes/_authenticated/employee/detail.$userId'
import { Route as AuthenticatedDevicesDetailIdImport } from './routes/_authenticated/devices/detail.$id'
import { Route as AuthenticatedCrmReportVoucherImport } from './routes/_authenticated/crm/report/voucher'
import { Route as AuthenticatedCrmReportSaleManagerImport } from './routes/_authenticated/crm/report/sale-manager'
import { Route as AuthenticatedCrmReportRevenueImport } from './routes/_authenticated/crm/report/revenue'
import { Route as AuthenticatedCrmReportRatingFeedbackImport } from './routes/_authenticated/crm/report/rating-feedback'
import { Route as AuthenticatedCrmReportCustomerImport } from './routes/_authenticated/crm/report/customer'
import { Route as AuthenticatedCrmLoyaltyMembershipTypeImport } from './routes/_authenticated/crm/loyalty/membership-type'
import { Route as AuthenticatedCrmLoyaltyExtraPointImport } from './routes/_authenticated/crm/loyalty/extra-point'
import { Route as AuthenticatedCrmGeneralSetupsStoreMenuImport } from './routes/_authenticated/crm/general-setups/store-menu'
import { Route as AuthenticatedCrmGeneralSetupsItemsImport } from './routes/_authenticated/crm/general-setups/items'
import { Route as AuthenticatedCrmGeneralSetupsItemTypeImport } from './routes/_authenticated/crm/general-setups/item-type'
import { Route as AuthenticatedCrmGeneralSetupsCreateUserImport } from './routes/_authenticated/crm/general-setups/create-user'
import { Route as AuthenticatedCrmGeneralSetupsComboSpecialImport } from './routes/_authenticated/crm/general-setups/combo-special'
import { Route as AuthenticatedCrmGeneralSetupsComboImport } from './routes/_authenticated/crm/general-setups/combo'
import { Route as AuthenticatedCrmGeneralSetupsAccountImport } from './routes/_authenticated/crm/general-setups/account'
import { Route as AuthenticatedCrmFoodItemReviewItemsImport } from './routes/_authenticated/crm/food-item-review/items'
import { Route as AuthenticatedSettingTableDetailIndexImport } from './routes/_authenticated/setting/table/detail/index'
import { Route as AuthenticatedSettingStoreDetailIndexImport } from './routes/_authenticated/setting/store/detail/index'
import { Route as AuthenticatedSettingSourceDetailIndexImport } from './routes/_authenticated/setting/source/detail/index'
import { Route as AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexImport } from './routes/_authenticated/setting/printer-position/printer-position-in-store/index'
import { Route as AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexImport } from './routes/_authenticated/setting/printer-position/printer-position-in-brand/index'
import { Route as AuthenticatedSettingPaymentMethodDetailIndexImport } from './routes/_authenticated/setting/payment-method/detail/index'
import { Route as AuthenticatedSettingAreaDetailIndexImport } from './routes/_authenticated/setting/area/detail/index'
import { Route as AuthenticatedSaleServiceChargeDetailIndexImport } from './routes/_authenticated/sale/service-charge/detail/index'
import { Route as AuthenticatedSaleDiscountRegularIndexImport } from './routes/_authenticated/sale/discount/regular/index'
import { Route as AuthenticatedSaleDiscountMembershipIndexImport } from './routes/_authenticated/sale/discount/membership/index'
import { Route as AuthenticatedSaleDiscountPaymentDetailIndexImport } from './routes/_authenticated/sale/discount-payment/detail/index'
import { Route as AuthenticatedSaleComboDetailIndexImport } from './routes/_authenticated/sale/combo/detail/index'
import { Route as AuthenticatedSaleChannelDiscountDetailIndexImport } from './routes/_authenticated/sale-channel/discount/detail/index'
import { Route as AuthenticatedMenuItemsItemsInStoreIndexImport } from './routes/_authenticated/menu/items/items-in-store/index'
import { Route as AuthenticatedMenuItemsItemsInCityIndexImport } from './routes/_authenticated/menu/items/items-in-city/index'
import { Route as AuthenticatedMenuItemClassDetailIndexImport } from './routes/_authenticated/menu/item-class/detail/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreIndexImport } from './routes/_authenticated/menu/customization/customization-in-store/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityIndexImport } from './routes/_authenticated/menu/customization/customization-in-city/index'
import { Route as AuthenticatedMenuCategoryDetailIndexImport } from './routes/_authenticated/menu/category/detail/index'
import { Route as AuthenticatedMenuCategoryInStoreDetailIndexImport } from './routes/_authenticated/menu/category-in-store/detail/index'
import { Route as AuthenticatedSettingTableDetailTableIdImport } from './routes/_authenticated/setting/table/detail/$tableId'
import { Route as AuthenticatedSettingTableDetailAreaIdImport } from './routes/_authenticated/setting/table/detail/$areaId'
import { Route as AuthenticatedSettingStoreDetailStoreIdImport } from './routes/_authenticated/setting/store/detail/$storeId'
import { Route as AuthenticatedSettingSourceDetailSourceIdImport } from './routes/_authenticated/setting/source/detail/$sourceId'
import { Route as AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport } from './routes/_authenticated/setting/payment-method/detail/$paymentMethodId'
import { Route as AuthenticatedSettingAreaDetailAreaIdImport } from './routes/_authenticated/setting/area/detail/$areaId'
import { Route as AuthenticatedSaleServiceChargeDetailIdImport } from './routes/_authenticated/sale/service-charge/detail/$id'
import { Route as AuthenticatedSaleDiscountRegularDetailImport } from './routes/_authenticated/sale/discount/regular/detail'
import { Route as AuthenticatedSaleDiscountMembershipDetailImport } from './routes/_authenticated/sale/discount/membership/detail'
import { Route as AuthenticatedSaleDiscountPaymentDetailIdImport } from './routes/_authenticated/sale/discount-payment/detail/$id'
import { Route as AuthenticatedSaleComboDetailIdImport } from './routes/_authenticated/sale/combo/detail/$id'
import { Route as AuthenticatedSaleChannelDiscountDetailIdImport } from './routes/_authenticated/sale-channel/discount/detail/$id'
import { Route as AuthenticatedSaleChannelChannelDetailUuidImport } from './routes/_authenticated/sale-channel/channel/detail/$uuid'
import { Route as AuthenticatedReportRevenueRevenueGeneralImport } from './routes/_authenticated/report/revenue/revenue/general'
import { Route as AuthenticatedReportRevenueCategoriesSourceImport } from './routes/_authenticated/report/revenue/categories/source'
import { Route as AuthenticatedReportRevenueCategoriesPromotionImport } from './routes/_authenticated/report/revenue/categories/promotion'
import { Route as AuthenticatedReportRevenueCategoriesPaymentMethodImport } from './routes/_authenticated/report/revenue/categories/payment-method'
import { Route as AuthenticatedReportAccountingInvoicesSaleSyncVatImport } from './routes/_authenticated/report/accounting/invoices/sale-sync-vat'
import { Route as AuthenticatedMenuItemsItemsInStoreDetailImport } from './routes/_authenticated/menu/items/items-in-store/detail'
import { Route as AuthenticatedMenuItemsItemsInCityDetailImport } from './routes/_authenticated/menu/items/items-in-city/detail'
import { Route as AuthenticatedMenuItemClassDetailIdImport } from './routes/_authenticated/menu/item-class/detail/$id'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailImport } from './routes/_authenticated/menu/customization/customization-in-store/detail'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreCreateImport } from './routes/_authenticated/menu/customization/customization-in-store/create'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityDetailImport } from './routes/_authenticated/menu/customization/customization-in-city/detail'
import { Route as AuthenticatedMenuCategoryDetailIdImport } from './routes/_authenticated/menu/category/detail/$id'
import { Route as AuthenticatedMenuCategoryInStoreDetailIdImport } from './routes/_authenticated/menu/category-in-store/detail/$id'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandDetailImport } from './routes/_authenticated/menu/categories/categories-in-brand/detail'
import { Route as AuthenticatedEmployeeRoleDetailRoleIdImport } from './routes/_authenticated/employee/role/detail/$roleId'
import { Route as AuthenticatedSaleDiscountRegularDetailIndexImport } from './routes/_authenticated/sale/discount/regular/detail/index'
import { Route as AuthenticatedSaleDiscountMembershipDetailIndexImport } from './routes/_authenticated/sale/discount/membership/detail/index'
import { Route as AuthenticatedMenuItemsItemsInStoreDetailIndexImport } from './routes/_authenticated/menu/items/items-in-store/detail/index'
import { Route as AuthenticatedMenuItemsItemsInCityDetailIndexImport } from './routes/_authenticated/menu/items/items-in-city/detail/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport } from './routes/_authenticated/menu/customization/customization-in-store/detail/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityDetailIndexImport } from './routes/_authenticated/menu/customization/customization-in-city/detail/index'
import { Route as AuthenticatedSaleDiscountRegularDetailIdImport } from './routes/_authenticated/sale/discount/regular/detail/$id'
import { Route as AuthenticatedSaleDiscountMembershipDetailIdImport } from './routes/_authenticated/sale/discount/membership/detail/$id'
import { Route as AuthenticatedMenuItemsItemsInStoreDetailIdImport } from './routes/_authenticated/menu/items/items-in-store/detail/$id'
import { Route as AuthenticatedMenuItemsItemsInCityDetailIdImport } from './routes/_authenticated/menu/items/items-in-city/detail/$id'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport } from './routes/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport } from './routes/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport } from './routes/_authenticated/menu/categories/categories-in-brand/detail/$id'
import { Route as AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdImport } from './routes/_authenticated/sale/discount/membership/detail/$companyId/$brandId'

// Create/Update Routes

const ClerkRouteRoute = ClerkRouteImport.update({
  id: '/clerk',
  path: '/clerk',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedMembershipRoute = AuthenticatedMembershipImport.update({
  id: '/membership',
  path: '/membership',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignUpRoute = authSignUpImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authForgotPasswordRoute = authForgotPasswordImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const ClerkAuthenticatedRouteRoute = ClerkAuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const ClerkauthRouteRoute = ClerkauthRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const ClerkAuthenticatedUserManagementRoute =
  ClerkAuthenticatedUserManagementImport.update({
    id: '/user-management',
    path: '/user-management',
    getParentRoute: () => ClerkAuthenticatedRouteRoute,
  } as any)

const ClerkauthSignUpRoute = ClerkauthSignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const ClerkauthSignInRoute = ClerkauthSignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingTableLayoutRoute =
  AuthenticatedSettingTableLayoutImport.update({
    id: '/setting/table-layout',
    path: '/setting/table-layout',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingTableRoute = AuthenticatedSettingTableImport.update({
  id: '/setting/table',
  path: '/setting/table',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingStoreRoute = AuthenticatedSettingStoreImport.update({
  id: '/setting/store',
  path: '/setting/store',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingAreaRoute = AuthenticatedSettingAreaImport.update({
  id: '/setting/area',
  path: '/setting/area',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSaleServiceChargeRoute =
  AuthenticatedSaleServiceChargeImport.update({
    id: '/sale/service-charge',
    path: '/sale/service-charge',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSaleDiscountPaymentRoute =
  AuthenticatedSaleDiscountPaymentImport.update({
    id: '/sale/discount-payment',
    path: '/sale/discount-payment',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSaleComboRoute = AuthenticatedSaleComboImport.update({
  id: '/sale/combo',
  path: '/sale/combo',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSaleChannelDiscountRoute =
  AuthenticatedSaleChannelDiscountImport.update({
    id: '/sale-channel/discount',
    path: '/sale-channel/discount',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuScheduleRoute = AuthenticatedMenuScheduleImport.update({
  id: '/menu/schedule',
  path: '/menu/schedule',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedMenuQuantityDayRoute =
  AuthenticatedMenuQuantityDayImport.update({
    id: '/menu/quantity-day',
    path: '/menu/quantity-day',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassRoute = AuthenticatedMenuItemClassImport.update(
  {
    id: '/menu/item-class',
    path: '/menu/item-class',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedGeneralSetupsCreateUserRoute =
  AuthenticatedGeneralSetupsCreateUserImport.update({
    id: '/general-setups/create-user',
    path: '/general-setups/create-user',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedGeneralSetupsAccountRoute =
  AuthenticatedGeneralSetupsAccountImport.update({
    id: '/general-setups/account',
    path: '/general-setups/account',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedEmployeeRoleRoute = AuthenticatedEmployeeRoleImport.update({
  id: '/employee/role',
  path: '/employee/role',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedEmployeeListRoute = AuthenticatedEmployeeListImport.update({
  id: '/employee/list',
  path: '/employee/list',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedEmployeeDetailRoute =
  AuthenticatedEmployeeDetailImport.update({
    id: '/employee/detail',
    path: '/employee/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedDevicesTypesRoute = AuthenticatedDevicesTypesImport.update({
  id: '/devices/types',
  path: '/devices/types',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedDevicesNewRoute = AuthenticatedDevicesNewImport.update({
  id: '/devices/new',
  path: '/devices/new',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedDevicesListRoute = AuthenticatedDevicesListImport.update({
  id: '/devices/list',
  path: '/devices/list',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedCrmUsingMonthRoute = AuthenticatedCrmUsingMonthImport.update(
  {
    id: '/crm/using-month',
    path: '/crm/using-month',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedCrmSystemLogRoute = AuthenticatedCrmSystemLogImport.update({
  id: '/crm/system-log',
  path: '/crm/system-log',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedCrmSettingsRoute = AuthenticatedCrmSettingsImport.update({
  id: '/crm/settings',
  path: '/crm/settings',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedCrmPricingTableRoute =
  AuthenticatedCrmPricingTableImport.update({
    id: '/crm/pricing-table',
    path: '/crm/pricing-table',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmCustomerProfileRoute =
  AuthenticatedCrmCustomerProfileImport.update({
    id: '/crm/customer-profile',
    path: '/crm/customer-profile',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmCustomerListRoute =
  AuthenticatedCrmCustomerListImport.update({
    id: '/crm/customer-list',
    path: '/crm/customer-list',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmCustomerGroupRoute =
  AuthenticatedCrmCustomerGroupImport.update({
    id: '/crm/customer-group',
    path: '/crm/customer-group',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmConnectCrmRoute = AuthenticatedCrmConnectCrmImport.update(
  {
    id: '/crm/connect-crm',
    path: '/crm/connect-crm',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedCrmConfigRegisterPageRoute =
  AuthenticatedCrmConfigRegisterPageImport.update({
    id: '/crm/config-register-page',
    path: '/crm/config-register-page',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmBillingDetailRoute =
  AuthenticatedCrmBillingDetailImport.update({
    id: '/crm/billing-detail',
    path: '/crm/billing-detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingTableIndexRoute =
  AuthenticatedSettingTableIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingTableRoute,
  } as any)

const AuthenticatedSettingTableLayoutIndexRoute =
  AuthenticatedSettingTableLayoutIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingTableLayoutRoute,
  } as any)

const AuthenticatedSettingStoreIndexRoute =
  AuthenticatedSettingStoreIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingStoreRoute,
  } as any)

const AuthenticatedSettingSourceIndexRoute =
  AuthenticatedSettingSourceIndexImport.update({
    id: '/setting/source/',
    path: '/setting/source/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodIndexRoute =
  AuthenticatedSettingPaymentMethodIndexImport.update({
    id: '/setting/payment-method/',
    path: '/setting/payment-method/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingBillModelIndexRoute =
  AuthenticatedSettingBillModelIndexImport.update({
    id: '/setting/bill-model/',
    path: '/setting/bill-model/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaIndexRoute =
  AuthenticatedSettingAreaIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingAreaRoute,
  } as any)

const AuthenticatedSaleServiceChargeIndexRoute =
  AuthenticatedSaleServiceChargeIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleServiceChargeRoute,
  } as any)

const AuthenticatedSalePromotionIndexRoute =
  AuthenticatedSalePromotionIndexImport.update({
    id: '/sale/promotion/',
    path: '/sale/promotion/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSaleDiscountPaymentIndexRoute =
  AuthenticatedSaleDiscountPaymentIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountPaymentRoute,
  } as any)

const AuthenticatedSaleChannelDiscountIndexRoute =
  AuthenticatedSaleChannelDiscountIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleChannelDiscountRoute,
  } as any)

const AuthenticatedSaleChannelChannelIndexRoute =
  AuthenticatedSaleChannelChannelIndexImport.update({
    id: '/sale-channel/channel/',
    path: '/sale-channel/channel/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassIndexRoute =
  AuthenticatedMenuItemClassIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemClassRoute,
  } as any)

const AuthenticatedEmployeeRoleIndexRoute =
  AuthenticatedEmployeeRoleIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedEmployeeRoleRoute,
  } as any)

const AuthenticatedSettingTableDetailRoute =
  AuthenticatedSettingTableDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSettingTableRoute,
  } as any)

const AuthenticatedSettingStoreDetailRoute =
  AuthenticatedSettingStoreDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSettingStoreRoute,
  } as any)

const AuthenticatedSettingAreaDetailRoute =
  AuthenticatedSettingAreaDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSettingAreaRoute,
  } as any)

const AuthenticatedSaleServiceChargeDetailRoute =
  AuthenticatedSaleServiceChargeDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleServiceChargeRoute,
  } as any)

const AuthenticatedSaleDiscountRegularRoute =
  AuthenticatedSaleDiscountRegularImport.update({
    id: '/sale/discount/regular',
    path: '/sale/discount/regular',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipRoute =
  AuthenticatedSaleDiscountMembershipImport.update({
    id: '/sale/discount/membership',
    path: '/sale/discount/membership',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSaleDiscountPaymentDetailRoute =
  AuthenticatedSaleDiscountPaymentDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleDiscountPaymentRoute,
  } as any)

const AuthenticatedSaleComboDetailRoute =
  AuthenticatedSaleComboDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleComboRoute,
  } as any)

const AuthenticatedSaleChannelDiscountDetailRoute =
  AuthenticatedSaleChannelDiscountDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleChannelDiscountRoute,
  } as any)

const AuthenticatedSaleChannelChannelDetailRoute =
  AuthenticatedSaleChannelChannelDetailImport.update({
    id: '/sale-channel/channel/detail',
    path: '/sale-channel/channel/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportRevenueSaleSummaryRoute =
  AuthenticatedReportRevenueSaleSummaryImport.update({
    id: '/report/revenue/sale-summary',
    path: '/report/revenue/sale-summary',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportAccountingSaleDetailAuditRoute =
  AuthenticatedReportAccountingSaleDetailAuditImport.update({
    id: '/report/accounting/sale-detail-audit',
    path: '/report/accounting/sale-detail-audit',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemRemovedItemRemovedInStoreRoute =
  AuthenticatedMenuItemRemovedItemRemovedInStoreImport.update({
    id: '/menu/item-removed/item-removed-in-store',
    path: '/menu/item-removed/item-removed-in-store',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemRemovedItemRemovedInCityRoute =
  AuthenticatedMenuItemRemovedItemRemovedInCityImport.update({
    id: '/menu/item-removed/item-removed-in-city',
    path: '/menu/item-removed/item-removed-in-city',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassDetailRoute =
  AuthenticatedMenuItemClassDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedMenuItemClassRoute,
  } as any)

const AuthenticatedMenuCustomizationNewRoute =
  AuthenticatedMenuCustomizationNewImport.update({
    id: '/menu/customization/new',
    path: '/menu/customization/new',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailRoute =
  AuthenticatedMenuCategoryDetailImport.update({
    id: '/menu/category/detail',
    path: '/menu/category/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryInStoreDetailRoute =
  AuthenticatedMenuCategoryInStoreDetailImport.update({
    id: '/menu/category-in-store/detail',
    path: '/menu/category-in-store/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoriesCategoriesInStoreRoute =
  AuthenticatedMenuCategoriesCategoriesInStoreImport.update({
    id: '/menu/categories/categories-in-store',
    path: '/menu/categories/categories-in-store',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoriesCategoriesInBrandRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandImport.update({
    id: '/menu/categories/categories-in-brand',
    path: '/menu/categories/categories-in-brand',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedEmployeeRoleDetailRoute =
  AuthenticatedEmployeeRoleDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedEmployeeRoleRoute,
  } as any)

const AuthenticatedEmployeeDetailUserIdRoute =
  AuthenticatedEmployeeDetailUserIdImport.update({
    id: '/$userId',
    path: '/$userId',
    getParentRoute: () => AuthenticatedEmployeeDetailRoute,
  } as any)

const AuthenticatedDevicesDetailIdRoute =
  AuthenticatedDevicesDetailIdImport.update({
    id: '/devices/detail/$id',
    path: '/devices/detail/$id',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmReportVoucherRoute =
  AuthenticatedCrmReportVoucherImport.update({
    id: '/crm/report/voucher',
    path: '/crm/report/voucher',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmReportSaleManagerRoute =
  AuthenticatedCrmReportSaleManagerImport.update({
    id: '/crm/report/sale-manager',
    path: '/crm/report/sale-manager',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmReportRevenueRoute =
  AuthenticatedCrmReportRevenueImport.update({
    id: '/crm/report/revenue',
    path: '/crm/report/revenue',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmReportRatingFeedbackRoute =
  AuthenticatedCrmReportRatingFeedbackImport.update({
    id: '/crm/report/rating-feedback',
    path: '/crm/report/rating-feedback',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmReportCustomerRoute =
  AuthenticatedCrmReportCustomerImport.update({
    id: '/crm/report/customer',
    path: '/crm/report/customer',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmLoyaltyMembershipTypeRoute =
  AuthenticatedCrmLoyaltyMembershipTypeImport.update({
    id: '/crm/loyalty/membership-type',
    path: '/crm/loyalty/membership-type',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmLoyaltyExtraPointRoute =
  AuthenticatedCrmLoyaltyExtraPointImport.update({
    id: '/crm/loyalty/extra-point',
    path: '/crm/loyalty/extra-point',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsStoreMenuRoute =
  AuthenticatedCrmGeneralSetupsStoreMenuImport.update({
    id: '/crm/general-setups/store-menu',
    path: '/crm/general-setups/store-menu',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsItemsRoute =
  AuthenticatedCrmGeneralSetupsItemsImport.update({
    id: '/crm/general-setups/items',
    path: '/crm/general-setups/items',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsItemTypeRoute =
  AuthenticatedCrmGeneralSetupsItemTypeImport.update({
    id: '/crm/general-setups/item-type',
    path: '/crm/general-setups/item-type',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsCreateUserRoute =
  AuthenticatedCrmGeneralSetupsCreateUserImport.update({
    id: '/crm/general-setups/create-user',
    path: '/crm/general-setups/create-user',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsComboSpecialRoute =
  AuthenticatedCrmGeneralSetupsComboSpecialImport.update({
    id: '/crm/general-setups/combo-special',
    path: '/crm/general-setups/combo-special',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsComboRoute =
  AuthenticatedCrmGeneralSetupsComboImport.update({
    id: '/crm/general-setups/combo',
    path: '/crm/general-setups/combo',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmGeneralSetupsAccountRoute =
  AuthenticatedCrmGeneralSetupsAccountImport.update({
    id: '/crm/general-setups/account',
    path: '/crm/general-setups/account',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmFoodItemReviewItemsRoute =
  AuthenticatedCrmFoodItemReviewItemsImport.update({
    id: '/crm/food-item-review/items',
    path: '/crm/food-item-review/items',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingTableDetailIndexRoute =
  AuthenticatedSettingTableDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingTableDetailRoute,
  } as any)

const AuthenticatedSettingStoreDetailIndexRoute =
  AuthenticatedSettingStoreDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingStoreDetailRoute,
  } as any)

const AuthenticatedSettingSourceDetailIndexRoute =
  AuthenticatedSettingSourceDetailIndexImport.update({
    id: '/setting/source/detail/',
    path: '/setting/source/detail/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute =
  AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexImport.update({
    id: '/setting/printer-position/printer-position-in-store/',
    path: '/setting/printer-position/printer-position-in-store/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute =
  AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexImport.update({
    id: '/setting/printer-position/printer-position-in-brand/',
    path: '/setting/printer-position/printer-position-in-brand/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodDetailIndexRoute =
  AuthenticatedSettingPaymentMethodDetailIndexImport.update({
    id: '/setting/payment-method/detail/',
    path: '/setting/payment-method/detail/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaDetailIndexRoute =
  AuthenticatedSettingAreaDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingAreaDetailRoute,
  } as any)

const AuthenticatedSaleServiceChargeDetailIndexRoute =
  AuthenticatedSaleServiceChargeDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleServiceChargeDetailRoute,
  } as any)

const AuthenticatedSaleDiscountRegularIndexRoute =
  AuthenticatedSaleDiscountRegularIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountRegularRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipIndexRoute =
  AuthenticatedSaleDiscountMembershipIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountMembershipRoute,
  } as any)

const AuthenticatedSaleDiscountPaymentDetailIndexRoute =
  AuthenticatedSaleDiscountPaymentDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountPaymentDetailRoute,
  } as any)

const AuthenticatedSaleComboDetailIndexRoute =
  AuthenticatedSaleComboDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleComboDetailRoute,
  } as any)

const AuthenticatedSaleChannelDiscountDetailIndexRoute =
  AuthenticatedSaleChannelDiscountDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleChannelDiscountDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInStoreIndexRoute =
  AuthenticatedMenuItemsItemsInStoreIndexImport.update({
    id: '/menu/items/items-in-store/',
    path: '/menu/items/items-in-store/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemsItemsInCityIndexRoute =
  AuthenticatedMenuItemsItemsInCityIndexImport.update({
    id: '/menu/items/items-in-city/',
    path: '/menu/items/items-in-city/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassDetailIndexRoute =
  AuthenticatedMenuItemClassDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemClassDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreIndexImport.update({
    id: '/menu/customization/customization-in-store/',
    path: '/menu/customization/customization-in-store/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInCityIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInCityIndexImport.update({
    id: '/menu/customization/customization-in-city/',
    path: '/menu/customization/customization-in-city/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailIndexRoute =
  AuthenticatedMenuCategoryDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuCategoryDetailRoute,
  } as any)

const AuthenticatedMenuCategoryInStoreDetailIndexRoute =
  AuthenticatedMenuCategoryInStoreDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuCategoryInStoreDetailRoute,
  } as any)

const AuthenticatedSettingTableDetailTableIdRoute =
  AuthenticatedSettingTableDetailTableIdImport.update({
    id: '/$tableId',
    path: '/$tableId',
    getParentRoute: () => AuthenticatedSettingTableDetailRoute,
  } as any)

const AuthenticatedSettingTableDetailAreaIdRoute =
  AuthenticatedSettingTableDetailAreaIdImport.update({
    id: '/$areaId',
    path: '/$areaId',
    getParentRoute: () => AuthenticatedSettingTableDetailRoute,
  } as any)

const AuthenticatedSettingStoreDetailStoreIdRoute =
  AuthenticatedSettingStoreDetailStoreIdImport.update({
    id: '/$storeId',
    path: '/$storeId',
    getParentRoute: () => AuthenticatedSettingStoreDetailRoute,
  } as any)

const AuthenticatedSettingSourceDetailSourceIdRoute =
  AuthenticatedSettingSourceDetailSourceIdImport.update({
    id: '/setting/source/detail/$sourceId',
    path: '/setting/source/detail/$sourceId',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute =
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport.update({
    id: '/setting/payment-method/detail/$paymentMethodId',
    path: '/setting/payment-method/detail/$paymentMethodId',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaDetailAreaIdRoute =
  AuthenticatedSettingAreaDetailAreaIdImport.update({
    id: '/$areaId',
    path: '/$areaId',
    getParentRoute: () => AuthenticatedSettingAreaDetailRoute,
  } as any)

const AuthenticatedSaleServiceChargeDetailIdRoute =
  AuthenticatedSaleServiceChargeDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleServiceChargeDetailRoute,
  } as any)

const AuthenticatedSaleDiscountRegularDetailRoute =
  AuthenticatedSaleDiscountRegularDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleDiscountRegularRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipDetailRoute =
  AuthenticatedSaleDiscountMembershipDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSaleDiscountMembershipRoute,
  } as any)

const AuthenticatedSaleDiscountPaymentDetailIdRoute =
  AuthenticatedSaleDiscountPaymentDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleDiscountPaymentDetailRoute,
  } as any)

const AuthenticatedSaleComboDetailIdRoute =
  AuthenticatedSaleComboDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleComboDetailRoute,
  } as any)

const AuthenticatedSaleChannelDiscountDetailIdRoute =
  AuthenticatedSaleChannelDiscountDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleChannelDiscountDetailRoute,
  } as any)

const AuthenticatedSaleChannelChannelDetailUuidRoute =
  AuthenticatedSaleChannelChannelDetailUuidImport.update({
    id: '/$uuid',
    path: '/$uuid',
    getParentRoute: () => AuthenticatedSaleChannelChannelDetailRoute,
  } as any)

const AuthenticatedReportRevenueRevenueGeneralRoute =
  AuthenticatedReportRevenueRevenueGeneralImport.update({
    id: '/report/revenue/revenue/general',
    path: '/report/revenue/revenue/general',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportRevenueCategoriesSourceRoute =
  AuthenticatedReportRevenueCategoriesSourceImport.update({
    id: '/report/revenue/categories/source',
    path: '/report/revenue/categories/source',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportRevenueCategoriesPromotionRoute =
  AuthenticatedReportRevenueCategoriesPromotionImport.update({
    id: '/report/revenue/categories/promotion',
    path: '/report/revenue/categories/promotion',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportRevenueCategoriesPaymentMethodRoute =
  AuthenticatedReportRevenueCategoriesPaymentMethodImport.update({
    id: '/report/revenue/categories/payment-method',
    path: '/report/revenue/categories/payment-method',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedReportAccountingInvoicesSaleSyncVatRoute =
  AuthenticatedReportAccountingInvoicesSaleSyncVatImport.update({
    id: '/report/accounting/invoices/sale-sync-vat',
    path: '/report/accounting/invoices/sale-sync-vat',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemsItemsInStoreDetailRoute =
  AuthenticatedMenuItemsItemsInStoreDetailImport.update({
    id: '/menu/items/items-in-store/detail',
    path: '/menu/items/items-in-store/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemsItemsInCityDetailRoute =
  AuthenticatedMenuItemsItemsInCityDetailImport.update({
    id: '/menu/items/items-in-city/detail',
    path: '/menu/items/items-in-city/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassDetailIdRoute =
  AuthenticatedMenuItemClassDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuItemClassDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailImport.update({
    id: '/menu/customization/customization-in-store/detail',
    path: '/menu/customization/customization-in-store/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreCreateImport.update({
    id: '/menu/customization/customization-in-store/create',
    path: '/menu/customization/customization-in-store/create',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInCityDetailRoute =
  AuthenticatedMenuCustomizationCustomizationInCityDetailImport.update({
    id: '/menu/customization/customization-in-city/detail',
    path: '/menu/customization/customization-in-city/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailIdRoute =
  AuthenticatedMenuCategoryDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuCategoryDetailRoute,
  } as any)

const AuthenticatedMenuCategoryInStoreDetailIdRoute =
  AuthenticatedMenuCategoryInStoreDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuCategoryInStoreDetailRoute,
  } as any)

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedMenuCategoriesCategoriesInBrandRoute,
  } as any)

const AuthenticatedEmployeeRoleDetailRoleIdRoute =
  AuthenticatedEmployeeRoleDetailRoleIdImport.update({
    id: '/$roleId',
    path: '/$roleId',
    getParentRoute: () => AuthenticatedEmployeeRoleDetailRoute,
  } as any)

const AuthenticatedSaleDiscountRegularDetailIndexRoute =
  AuthenticatedSaleDiscountRegularDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountRegularDetailRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipDetailIndexRoute =
  AuthenticatedSaleDiscountMembershipDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSaleDiscountMembershipDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInStoreDetailIndexRoute =
  AuthenticatedMenuItemsItemsInStoreDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemsItemsInStoreDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInCityDetailIndexRoute =
  AuthenticatedMenuItemsItemsInCityDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemsItemsInCityDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () =>
      AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInCityDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () =>
      AuthenticatedMenuCustomizationCustomizationInCityDetailRoute,
  } as any)

const AuthenticatedSaleDiscountRegularDetailIdRoute =
  AuthenticatedSaleDiscountRegularDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleDiscountRegularDetailRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipDetailIdRoute =
  AuthenticatedSaleDiscountMembershipDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedSaleDiscountMembershipDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInStoreDetailIdRoute =
  AuthenticatedMenuItemsItemsInStoreDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuItemsItemsInStoreDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInCityDetailIdRoute =
  AuthenticatedMenuItemsItemsInCityDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuItemsItemsInCityDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport.update(
    {
      id: '/$customizationId',
      path: '/$customizationId',
      getParentRoute: () =>
        AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute,
    } as any,
  )

const AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute =
  AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport.update(
    {
      id: '/$customizationId',
      path: '/$customizationId',
      getParentRoute: () =>
        AuthenticatedMenuCustomizationCustomizationInCityDetailRoute,
    } as any,
  )

const AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () =>
      AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute,
  } as any)

const AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute =
  AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdImport.update({
    id: '/$companyId/$brandId',
    path: '/$companyId/$brandId',
    getParentRoute: () => AuthenticatedSaleDiscountMembershipDetailRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/clerk': {
      id: '/clerk'
      path: '/clerk'
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/clerk/(auth)': {
      id: '/clerk/(auth)'
      path: '/'
      fullPath: '/clerk/'
      preLoaderRoute: typeof ClerkauthRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/clerk/_authenticated': {
      id: '/clerk/_authenticated'
      path: ''
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkAuthenticatedRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/membership': {
      id: '/_authenticated/membership'
      path: '/membership'
      fullPath: '/membership'
      preLoaderRoute: typeof AuthenticatedMembershipImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/billing-detail': {
      id: '/_authenticated/crm/billing-detail'
      path: '/crm/billing-detail'
      fullPath: '/crm/billing-detail'
      preLoaderRoute: typeof AuthenticatedCrmBillingDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/config-register-page': {
      id: '/_authenticated/crm/config-register-page'
      path: '/crm/config-register-page'
      fullPath: '/crm/config-register-page'
      preLoaderRoute: typeof AuthenticatedCrmConfigRegisterPageImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/connect-crm': {
      id: '/_authenticated/crm/connect-crm'
      path: '/crm/connect-crm'
      fullPath: '/crm/connect-crm'
      preLoaderRoute: typeof AuthenticatedCrmConnectCrmImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/customer-group': {
      id: '/_authenticated/crm/customer-group'
      path: '/crm/customer-group'
      fullPath: '/crm/customer-group'
      preLoaderRoute: typeof AuthenticatedCrmCustomerGroupImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/customer-list': {
      id: '/_authenticated/crm/customer-list'
      path: '/crm/customer-list'
      fullPath: '/crm/customer-list'
      preLoaderRoute: typeof AuthenticatedCrmCustomerListImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/customer-profile': {
      id: '/_authenticated/crm/customer-profile'
      path: '/crm/customer-profile'
      fullPath: '/crm/customer-profile'
      preLoaderRoute: typeof AuthenticatedCrmCustomerProfileImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/pricing-table': {
      id: '/_authenticated/crm/pricing-table'
      path: '/crm/pricing-table'
      fullPath: '/crm/pricing-table'
      preLoaderRoute: typeof AuthenticatedCrmPricingTableImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/settings': {
      id: '/_authenticated/crm/settings'
      path: '/crm/settings'
      fullPath: '/crm/settings'
      preLoaderRoute: typeof AuthenticatedCrmSettingsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/system-log': {
      id: '/_authenticated/crm/system-log'
      path: '/crm/system-log'
      fullPath: '/crm/system-log'
      preLoaderRoute: typeof AuthenticatedCrmSystemLogImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/using-month': {
      id: '/_authenticated/crm/using-month'
      path: '/crm/using-month'
      fullPath: '/crm/using-month'
      preLoaderRoute: typeof AuthenticatedCrmUsingMonthImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/list': {
      id: '/_authenticated/devices/list'
      path: '/devices/list'
      fullPath: '/devices/list'
      preLoaderRoute: typeof AuthenticatedDevicesListImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/new': {
      id: '/_authenticated/devices/new'
      path: '/devices/new'
      fullPath: '/devices/new'
      preLoaderRoute: typeof AuthenticatedDevicesNewImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/types': {
      id: '/_authenticated/devices/types'
      path: '/devices/types'
      fullPath: '/devices/types'
      preLoaderRoute: typeof AuthenticatedDevicesTypesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/detail': {
      id: '/_authenticated/employee/detail'
      path: '/employee/detail'
      fullPath: '/employee/detail'
      preLoaderRoute: typeof AuthenticatedEmployeeDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/list': {
      id: '/_authenticated/employee/list'
      path: '/employee/list'
      fullPath: '/employee/list'
      preLoaderRoute: typeof AuthenticatedEmployeeListImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/role': {
      id: '/_authenticated/employee/role'
      path: '/employee/role'
      fullPath: '/employee/role'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/general-setups/account': {
      id: '/_authenticated/general-setups/account'
      path: '/general-setups/account'
      fullPath: '/general-setups/account'
      preLoaderRoute: typeof AuthenticatedGeneralSetupsAccountImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/general-setups/create-user': {
      id: '/_authenticated/general-setups/create-user'
      path: '/general-setups/create-user'
      fullPath: '/general-setups/create-user'
      preLoaderRoute: typeof AuthenticatedGeneralSetupsCreateUserImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class': {
      id: '/_authenticated/menu/item-class'
      path: '/menu/item-class'
      fullPath: '/menu/item-class'
      preLoaderRoute: typeof AuthenticatedMenuItemClassImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/quantity-day': {
      id: '/_authenticated/menu/quantity-day'
      path: '/menu/quantity-day'
      fullPath: '/menu/quantity-day'
      preLoaderRoute: typeof AuthenticatedMenuQuantityDayImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/schedule': {
      id: '/_authenticated/menu/schedule'
      path: '/menu/schedule'
      fullPath: '/menu/schedule'
      preLoaderRoute: typeof AuthenticatedMenuScheduleImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/discount': {
      id: '/_authenticated/sale-channel/discount'
      path: '/sale-channel/discount'
      fullPath: '/sale-channel/discount'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/combo': {
      id: '/_authenticated/sale/combo'
      path: '/sale/combo'
      fullPath: '/sale/combo'
      preLoaderRoute: typeof AuthenticatedSaleComboImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/discount-payment': {
      id: '/_authenticated/sale/discount-payment'
      path: '/sale/discount-payment'
      fullPath: '/sale/discount-payment'
      preLoaderRoute: typeof AuthenticatedSaleDiscountPaymentImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/service-charge': {
      id: '/_authenticated/sale/service-charge'
      path: '/sale/service-charge'
      fullPath: '/sale/service-charge'
      preLoaderRoute: typeof AuthenticatedSaleServiceChargeImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/area': {
      id: '/_authenticated/setting/area'
      path: '/setting/area'
      fullPath: '/setting/area'
      preLoaderRoute: typeof AuthenticatedSettingAreaImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/store': {
      id: '/_authenticated/setting/store'
      path: '/setting/store'
      fullPath: '/setting/store'
      preLoaderRoute: typeof AuthenticatedSettingStoreImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/table': {
      id: '/_authenticated/setting/table'
      path: '/setting/table'
      fullPath: '/setting/table'
      preLoaderRoute: typeof AuthenticatedSettingTableImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/table-layout': {
      id: '/_authenticated/setting/table-layout'
      path: '/setting/table-layout'
      fullPath: '/setting/table-layout'
      preLoaderRoute: typeof AuthenticatedSettingTableLayoutImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/clerk/(auth)/sign-in': {
      id: '/clerk/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/clerk/sign-in'
      preLoaderRoute: typeof ClerkauthSignInImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/(auth)/sign-up': {
      id: '/clerk/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/clerk/sign-up'
      preLoaderRoute: typeof ClerkauthSignUpImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/_authenticated/user-management': {
      id: '/clerk/_authenticated/user-management'
      path: '/user-management'
      fullPath: '/clerk/user-management'
      preLoaderRoute: typeof ClerkAuthenticatedUserManagementImport
      parentRoute: typeof ClerkAuthenticatedRouteImport
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/food-item-review/items': {
      id: '/_authenticated/crm/food-item-review/items'
      path: '/crm/food-item-review/items'
      fullPath: '/crm/food-item-review/items'
      preLoaderRoute: typeof AuthenticatedCrmFoodItemReviewItemsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/account': {
      id: '/_authenticated/crm/general-setups/account'
      path: '/crm/general-setups/account'
      fullPath: '/crm/general-setups/account'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsAccountImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/combo': {
      id: '/_authenticated/crm/general-setups/combo'
      path: '/crm/general-setups/combo'
      fullPath: '/crm/general-setups/combo'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsComboImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/combo-special': {
      id: '/_authenticated/crm/general-setups/combo-special'
      path: '/crm/general-setups/combo-special'
      fullPath: '/crm/general-setups/combo-special'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsComboSpecialImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/create-user': {
      id: '/_authenticated/crm/general-setups/create-user'
      path: '/crm/general-setups/create-user'
      fullPath: '/crm/general-setups/create-user'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsCreateUserImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/item-type': {
      id: '/_authenticated/crm/general-setups/item-type'
      path: '/crm/general-setups/item-type'
      fullPath: '/crm/general-setups/item-type'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsItemTypeImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/items': {
      id: '/_authenticated/crm/general-setups/items'
      path: '/crm/general-setups/items'
      fullPath: '/crm/general-setups/items'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsItemsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/general-setups/store-menu': {
      id: '/_authenticated/crm/general-setups/store-menu'
      path: '/crm/general-setups/store-menu'
      fullPath: '/crm/general-setups/store-menu'
      preLoaderRoute: typeof AuthenticatedCrmGeneralSetupsStoreMenuImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/loyalty/extra-point': {
      id: '/_authenticated/crm/loyalty/extra-point'
      path: '/crm/loyalty/extra-point'
      fullPath: '/crm/loyalty/extra-point'
      preLoaderRoute: typeof AuthenticatedCrmLoyaltyExtraPointImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/loyalty/membership-type': {
      id: '/_authenticated/crm/loyalty/membership-type'
      path: '/crm/loyalty/membership-type'
      fullPath: '/crm/loyalty/membership-type'
      preLoaderRoute: typeof AuthenticatedCrmLoyaltyMembershipTypeImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/report/customer': {
      id: '/_authenticated/crm/report/customer'
      path: '/crm/report/customer'
      fullPath: '/crm/report/customer'
      preLoaderRoute: typeof AuthenticatedCrmReportCustomerImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/report/rating-feedback': {
      id: '/_authenticated/crm/report/rating-feedback'
      path: '/crm/report/rating-feedback'
      fullPath: '/crm/report/rating-feedback'
      preLoaderRoute: typeof AuthenticatedCrmReportRatingFeedbackImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/report/revenue': {
      id: '/_authenticated/crm/report/revenue'
      path: '/crm/report/revenue'
      fullPath: '/crm/report/revenue'
      preLoaderRoute: typeof AuthenticatedCrmReportRevenueImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/report/sale-manager': {
      id: '/_authenticated/crm/report/sale-manager'
      path: '/crm/report/sale-manager'
      fullPath: '/crm/report/sale-manager'
      preLoaderRoute: typeof AuthenticatedCrmReportSaleManagerImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm/report/voucher': {
      id: '/_authenticated/crm/report/voucher'
      path: '/crm/report/voucher'
      fullPath: '/crm/report/voucher'
      preLoaderRoute: typeof AuthenticatedCrmReportVoucherImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/detail/$id': {
      id: '/_authenticated/devices/detail/$id'
      path: '/devices/detail/$id'
      fullPath: '/devices/detail/$id'
      preLoaderRoute: typeof AuthenticatedDevicesDetailIdImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/detail/$userId': {
      id: '/_authenticated/employee/detail/$userId'
      path: '/$userId'
      fullPath: '/employee/detail/$userId'
      preLoaderRoute: typeof AuthenticatedEmployeeDetailUserIdImport
      parentRoute: typeof AuthenticatedEmployeeDetailImport
    }
    '/_authenticated/employee/role/detail': {
      id: '/_authenticated/employee/role/detail'
      path: '/detail'
      fullPath: '/employee/role/detail'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleDetailImport
      parentRoute: typeof AuthenticatedEmployeeRoleImport
    }
    '/_authenticated/menu/categories/categories-in-brand': {
      id: '/_authenticated/menu/categories/categories-in-brand'
      path: '/menu/categories/categories-in-brand'
      fullPath: '/menu/categories/categories-in-brand'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/categories/categories-in-store': {
      id: '/_authenticated/menu/categories/categories-in-store'
      path: '/menu/categories/categories-in-store'
      fullPath: '/menu/categories/categories-in-store'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInStoreImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/category-in-store/detail': {
      id: '/_authenticated/menu/category-in-store/detail'
      path: '/menu/category-in-store/detail'
      fullPath: '/menu/category-in-store/detail'
      preLoaderRoute: typeof AuthenticatedMenuCategoryInStoreDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/category/detail': {
      id: '/_authenticated/menu/category/detail'
      path: '/menu/category/detail'
      fullPath: '/menu/category/detail'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/new': {
      id: '/_authenticated/menu/customization/new'
      path: '/menu/customization/new'
      fullPath: '/menu/customization/new'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationNewImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail': {
      id: '/_authenticated/menu/item-class/detail'
      path: '/detail'
      fullPath: '/menu/item-class/detail'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailImport
      parentRoute: typeof AuthenticatedMenuItemClassImport
    }
    '/_authenticated/menu/item-removed/item-removed-in-city': {
      id: '/_authenticated/menu/item-removed/item-removed-in-city'
      path: '/menu/item-removed/item-removed-in-city'
      fullPath: '/menu/item-removed/item-removed-in-city'
      preLoaderRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInCityImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-removed/item-removed-in-store': {
      id: '/_authenticated/menu/item-removed/item-removed-in-store'
      path: '/menu/item-removed/item-removed-in-store'
      fullPath: '/menu/item-removed/item-removed-in-store'
      preLoaderRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInStoreImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/accounting/sale-detail-audit': {
      id: '/_authenticated/report/accounting/sale-detail-audit'
      path: '/report/accounting/sale-detail-audit'
      fullPath: '/report/accounting/sale-detail-audit'
      preLoaderRoute: typeof AuthenticatedReportAccountingSaleDetailAuditImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/revenue/sale-summary': {
      id: '/_authenticated/report/revenue/sale-summary'
      path: '/report/revenue/sale-summary'
      fullPath: '/report/revenue/sale-summary'
      preLoaderRoute: typeof AuthenticatedReportRevenueSaleSummaryImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/channel/detail': {
      id: '/_authenticated/sale-channel/channel/detail'
      path: '/sale-channel/channel/detail'
      fullPath: '/sale-channel/channel/detail'
      preLoaderRoute: typeof AuthenticatedSaleChannelChannelDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/discount/detail': {
      id: '/_authenticated/sale-channel/discount/detail'
      path: '/detail'
      fullPath: '/sale-channel/discount/detail'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountDetailImport
      parentRoute: typeof AuthenticatedSaleChannelDiscountImport
    }
    '/_authenticated/sale/combo/detail': {
      id: '/_authenticated/sale/combo/detail'
      path: '/detail'
      fullPath: '/sale/combo/detail'
      preLoaderRoute: typeof AuthenticatedSaleComboDetailImport
      parentRoute: typeof AuthenticatedSaleComboImport
    }
    '/_authenticated/sale/discount-payment/detail': {
      id: '/_authenticated/sale/discount-payment/detail'
      path: '/detail'
      fullPath: '/sale/discount-payment/detail'
      preLoaderRoute: typeof AuthenticatedSaleDiscountPaymentDetailImport
      parentRoute: typeof AuthenticatedSaleDiscountPaymentImport
    }
    '/_authenticated/sale/discount/membership': {
      id: '/_authenticated/sale/discount/membership'
      path: '/sale/discount/membership'
      fullPath: '/sale/discount/membership'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/discount/regular': {
      id: '/_authenticated/sale/discount/regular'
      path: '/sale/discount/regular'
      fullPath: '/sale/discount/regular'
      preLoaderRoute: typeof AuthenticatedSaleDiscountRegularImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/service-charge/detail': {
      id: '/_authenticated/sale/service-charge/detail'
      path: '/detail'
      fullPath: '/sale/service-charge/detail'
      preLoaderRoute: typeof AuthenticatedSaleServiceChargeDetailImport
      parentRoute: typeof AuthenticatedSaleServiceChargeImport
    }
    '/_authenticated/setting/area/detail': {
      id: '/_authenticated/setting/area/detail'
      path: '/detail'
      fullPath: '/setting/area/detail'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailImport
      parentRoute: typeof AuthenticatedSettingAreaImport
    }
    '/_authenticated/setting/store/detail': {
      id: '/_authenticated/setting/store/detail'
      path: '/detail'
      fullPath: '/setting/store/detail'
      preLoaderRoute: typeof AuthenticatedSettingStoreDetailImport
      parentRoute: typeof AuthenticatedSettingStoreImport
    }
    '/_authenticated/setting/table/detail': {
      id: '/_authenticated/setting/table/detail'
      path: '/detail'
      fullPath: '/setting/table/detail'
      preLoaderRoute: typeof AuthenticatedSettingTableDetailImport
      parentRoute: typeof AuthenticatedSettingTableImport
    }
    '/_authenticated/employee/role/': {
      id: '/_authenticated/employee/role/'
      path: '/'
      fullPath: '/employee/role/'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleIndexImport
      parentRoute: typeof AuthenticatedEmployeeRoleImport
    }
    '/_authenticated/menu/item-class/': {
      id: '/_authenticated/menu/item-class/'
      path: '/'
      fullPath: '/menu/item-class/'
      preLoaderRoute: typeof AuthenticatedMenuItemClassIndexImport
      parentRoute: typeof AuthenticatedMenuItemClassImport
    }
    '/_authenticated/sale-channel/channel/': {
      id: '/_authenticated/sale-channel/channel/'
      path: '/sale-channel/channel'
      fullPath: '/sale-channel/channel'
      preLoaderRoute: typeof AuthenticatedSaleChannelChannelIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/discount/': {
      id: '/_authenticated/sale-channel/discount/'
      path: '/'
      fullPath: '/sale-channel/discount/'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountIndexImport
      parentRoute: typeof AuthenticatedSaleChannelDiscountImport
    }
    '/_authenticated/sale/discount-payment/': {
      id: '/_authenticated/sale/discount-payment/'
      path: '/'
      fullPath: '/sale/discount-payment/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountPaymentIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountPaymentImport
    }
    '/_authenticated/sale/promotion/': {
      id: '/_authenticated/sale/promotion/'
      path: '/sale/promotion'
      fullPath: '/sale/promotion'
      preLoaderRoute: typeof AuthenticatedSalePromotionIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/service-charge/': {
      id: '/_authenticated/sale/service-charge/'
      path: '/'
      fullPath: '/sale/service-charge/'
      preLoaderRoute: typeof AuthenticatedSaleServiceChargeIndexImport
      parentRoute: typeof AuthenticatedSaleServiceChargeImport
    }
    '/_authenticated/setting/area/': {
      id: '/_authenticated/setting/area/'
      path: '/'
      fullPath: '/setting/area/'
      preLoaderRoute: typeof AuthenticatedSettingAreaIndexImport
      parentRoute: typeof AuthenticatedSettingAreaImport
    }
    '/_authenticated/setting/bill-model/': {
      id: '/_authenticated/setting/bill-model/'
      path: '/setting/bill-model'
      fullPath: '/setting/bill-model'
      preLoaderRoute: typeof AuthenticatedSettingBillModelIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/payment-method/': {
      id: '/_authenticated/setting/payment-method/'
      path: '/setting/payment-method'
      fullPath: '/setting/payment-method'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/': {
      id: '/_authenticated/setting/source/'
      path: '/setting/source'
      fullPath: '/setting/source'
      preLoaderRoute: typeof AuthenticatedSettingSourceIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/store/': {
      id: '/_authenticated/setting/store/'
      path: '/'
      fullPath: '/setting/store/'
      preLoaderRoute: typeof AuthenticatedSettingStoreIndexImport
      parentRoute: typeof AuthenticatedSettingStoreImport
    }
    '/_authenticated/setting/table-layout/': {
      id: '/_authenticated/setting/table-layout/'
      path: '/'
      fullPath: '/setting/table-layout/'
      preLoaderRoute: typeof AuthenticatedSettingTableLayoutIndexImport
      parentRoute: typeof AuthenticatedSettingTableLayoutImport
    }
    '/_authenticated/setting/table/': {
      id: '/_authenticated/setting/table/'
      path: '/'
      fullPath: '/setting/table/'
      preLoaderRoute: typeof AuthenticatedSettingTableIndexImport
      parentRoute: typeof AuthenticatedSettingTableImport
    }
    '/_authenticated/employee/role/detail/$roleId': {
      id: '/_authenticated/employee/role/detail/$roleId'
      path: '/$roleId'
      fullPath: '/employee/role/detail/$roleId'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleDetailRoleIdImport
      parentRoute: typeof AuthenticatedEmployeeRoleDetailImport
    }
    '/_authenticated/menu/categories/categories-in-brand/detail': {
      id: '/_authenticated/menu/categories/categories-in-brand/detail'
      path: '/detail'
      fullPath: '/menu/categories/categories-in-brand/detail'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailImport
      parentRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandImport
    }
    '/_authenticated/menu/category-in-store/detail/$id': {
      id: '/_authenticated/menu/category-in-store/detail/$id'
      path: '/$id'
      fullPath: '/menu/category-in-store/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuCategoryInStoreDetailIdImport
      parentRoute: typeof AuthenticatedMenuCategoryInStoreDetailImport
    }
    '/_authenticated/menu/category/detail/$id': {
      id: '/_authenticated/menu/category/detail/$id'
      path: '/$id'
      fullPath: '/menu/category/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailIdImport
      parentRoute: typeof AuthenticatedMenuCategoryDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/detail': {
      id: '/_authenticated/menu/customization/customization-in-city/detail'
      path: '/menu/customization/customization-in-city/detail'
      fullPath: '/menu/customization/customization-in-city/detail'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/create': {
      id: '/_authenticated/menu/customization/customization-in-store/create'
      path: '/menu/customization/customization-in-store/create'
      fullPath: '/menu/customization/customization-in-store/create'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail': {
      id: '/_authenticated/menu/customization/customization-in-store/detail'
      path: '/menu/customization/customization-in-store/detail'
      fullPath: '/menu/customization/customization-in-store/detail'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail/$id': {
      id: '/_authenticated/menu/item-class/detail/$id'
      path: '/$id'
      fullPath: '/menu/item-class/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailIdImport
      parentRoute: typeof AuthenticatedMenuItemClassDetailImport
    }
    '/_authenticated/menu/items/items-in-city/detail': {
      id: '/_authenticated/menu/items/items-in-city/detail'
      path: '/menu/items/items-in-city/detail'
      fullPath: '/menu/items/items-in-city/detail'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInCityDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/items/items-in-store/detail': {
      id: '/_authenticated/menu/items/items-in-store/detail'
      path: '/menu/items/items-in-store/detail'
      fullPath: '/menu/items/items-in-store/detail'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/accounting/invoices/sale-sync-vat': {
      id: '/_authenticated/report/accounting/invoices/sale-sync-vat'
      path: '/report/accounting/invoices/sale-sync-vat'
      fullPath: '/report/accounting/invoices/sale-sync-vat'
      preLoaderRoute: typeof AuthenticatedReportAccountingInvoicesSaleSyncVatImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/revenue/categories/payment-method': {
      id: '/_authenticated/report/revenue/categories/payment-method'
      path: '/report/revenue/categories/payment-method'
      fullPath: '/report/revenue/categories/payment-method'
      preLoaderRoute: typeof AuthenticatedReportRevenueCategoriesPaymentMethodImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/revenue/categories/promotion': {
      id: '/_authenticated/report/revenue/categories/promotion'
      path: '/report/revenue/categories/promotion'
      fullPath: '/report/revenue/categories/promotion'
      preLoaderRoute: typeof AuthenticatedReportRevenueCategoriesPromotionImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/revenue/categories/source': {
      id: '/_authenticated/report/revenue/categories/source'
      path: '/report/revenue/categories/source'
      fullPath: '/report/revenue/categories/source'
      preLoaderRoute: typeof AuthenticatedReportRevenueCategoriesSourceImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/report/revenue/revenue/general': {
      id: '/_authenticated/report/revenue/revenue/general'
      path: '/report/revenue/revenue/general'
      fullPath: '/report/revenue/revenue/general'
      preLoaderRoute: typeof AuthenticatedReportRevenueRevenueGeneralImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/channel/detail/$uuid': {
      id: '/_authenticated/sale-channel/channel/detail/$uuid'
      path: '/$uuid'
      fullPath: '/sale-channel/channel/detail/$uuid'
      preLoaderRoute: typeof AuthenticatedSaleChannelChannelDetailUuidImport
      parentRoute: typeof AuthenticatedSaleChannelChannelDetailImport
    }
    '/_authenticated/sale-channel/discount/detail/$id': {
      id: '/_authenticated/sale-channel/discount/detail/$id'
      path: '/$id'
      fullPath: '/sale-channel/discount/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountDetailIdImport
      parentRoute: typeof AuthenticatedSaleChannelDiscountDetailImport
    }
    '/_authenticated/sale/combo/detail/$id': {
      id: '/_authenticated/sale/combo/detail/$id'
      path: '/$id'
      fullPath: '/sale/combo/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleComboDetailIdImport
      parentRoute: typeof AuthenticatedSaleComboDetailImport
    }
    '/_authenticated/sale/discount-payment/detail/$id': {
      id: '/_authenticated/sale/discount-payment/detail/$id'
      path: '/$id'
      fullPath: '/sale/discount-payment/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleDiscountPaymentDetailIdImport
      parentRoute: typeof AuthenticatedSaleDiscountPaymentDetailImport
    }
    '/_authenticated/sale/discount/membership/detail': {
      id: '/_authenticated/sale/discount/membership/detail'
      path: '/detail'
      fullPath: '/sale/discount/membership/detail'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipDetailImport
      parentRoute: typeof AuthenticatedSaleDiscountMembershipImport
    }
    '/_authenticated/sale/discount/regular/detail': {
      id: '/_authenticated/sale/discount/regular/detail'
      path: '/detail'
      fullPath: '/sale/discount/regular/detail'
      preLoaderRoute: typeof AuthenticatedSaleDiscountRegularDetailImport
      parentRoute: typeof AuthenticatedSaleDiscountRegularImport
    }
    '/_authenticated/sale/service-charge/detail/$id': {
      id: '/_authenticated/sale/service-charge/detail/$id'
      path: '/$id'
      fullPath: '/sale/service-charge/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleServiceChargeDetailIdImport
      parentRoute: typeof AuthenticatedSaleServiceChargeDetailImport
    }
    '/_authenticated/setting/area/detail/$areaId': {
      id: '/_authenticated/setting/area/detail/$areaId'
      path: '/$areaId'
      fullPath: '/setting/area/detail/$areaId'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailAreaIdImport
      parentRoute: typeof AuthenticatedSettingAreaDetailImport
    }
    '/_authenticated/setting/payment-method/detail/$paymentMethodId': {
      id: '/_authenticated/setting/payment-method/detail/$paymentMethodId'
      path: '/setting/payment-method/detail/$paymentMethodId'
      fullPath: '/setting/payment-method/detail/$paymentMethodId'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/detail/$sourceId': {
      id: '/_authenticated/setting/source/detail/$sourceId'
      path: '/setting/source/detail/$sourceId'
      fullPath: '/setting/source/detail/$sourceId'
      preLoaderRoute: typeof AuthenticatedSettingSourceDetailSourceIdImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/store/detail/$storeId': {
      id: '/_authenticated/setting/store/detail/$storeId'
      path: '/$storeId'
      fullPath: '/setting/store/detail/$storeId'
      preLoaderRoute: typeof AuthenticatedSettingStoreDetailStoreIdImport
      parentRoute: typeof AuthenticatedSettingStoreDetailImport
    }
    '/_authenticated/setting/table/detail/$areaId': {
      id: '/_authenticated/setting/table/detail/$areaId'
      path: '/$areaId'
      fullPath: '/setting/table/detail/$areaId'
      preLoaderRoute: typeof AuthenticatedSettingTableDetailAreaIdImport
      parentRoute: typeof AuthenticatedSettingTableDetailImport
    }
    '/_authenticated/setting/table/detail/$tableId': {
      id: '/_authenticated/setting/table/detail/$tableId'
      path: '/$tableId'
      fullPath: '/setting/table/detail/$tableId'
      preLoaderRoute: typeof AuthenticatedSettingTableDetailTableIdImport
      parentRoute: typeof AuthenticatedSettingTableDetailImport
    }
    '/_authenticated/menu/category-in-store/detail/': {
      id: '/_authenticated/menu/category-in-store/detail/'
      path: '/'
      fullPath: '/menu/category-in-store/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCategoryInStoreDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCategoryInStoreDetailImport
    }
    '/_authenticated/menu/category/detail/': {
      id: '/_authenticated/menu/category/detail/'
      path: '/'
      fullPath: '/menu/category/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCategoryDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/': {
      id: '/_authenticated/menu/customization/customization-in-city/'
      path: '/menu/customization/customization-in-city'
      fullPath: '/menu/customization/customization-in-city'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/': {
      id: '/_authenticated/menu/customization/customization-in-store/'
      path: '/menu/customization/customization-in-store'
      fullPath: '/menu/customization/customization-in-store'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail/': {
      id: '/_authenticated/menu/item-class/detail/'
      path: '/'
      fullPath: '/menu/item-class/detail/'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailIndexImport
      parentRoute: typeof AuthenticatedMenuItemClassDetailImport
    }
    '/_authenticated/menu/items/items-in-city/': {
      id: '/_authenticated/menu/items/items-in-city/'
      path: '/menu/items/items-in-city'
      fullPath: '/menu/items/items-in-city'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInCityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/items/items-in-store/': {
      id: '/_authenticated/menu/items/items-in-store/'
      path: '/menu/items/items-in-store'
      fullPath: '/menu/items/items-in-store'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInStoreIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/discount/detail/': {
      id: '/_authenticated/sale-channel/discount/detail/'
      path: '/'
      fullPath: '/sale-channel/discount/detail/'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountDetailIndexImport
      parentRoute: typeof AuthenticatedSaleChannelDiscountDetailImport
    }
    '/_authenticated/sale/combo/detail/': {
      id: '/_authenticated/sale/combo/detail/'
      path: '/'
      fullPath: '/sale/combo/detail/'
      preLoaderRoute: typeof AuthenticatedSaleComboDetailIndexImport
      parentRoute: typeof AuthenticatedSaleComboDetailImport
    }
    '/_authenticated/sale/discount-payment/detail/': {
      id: '/_authenticated/sale/discount-payment/detail/'
      path: '/'
      fullPath: '/sale/discount-payment/detail/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountPaymentDetailIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountPaymentDetailImport
    }
    '/_authenticated/sale/discount/membership/': {
      id: '/_authenticated/sale/discount/membership/'
      path: '/'
      fullPath: '/sale/discount/membership/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountMembershipImport
    }
    '/_authenticated/sale/discount/regular/': {
      id: '/_authenticated/sale/discount/regular/'
      path: '/'
      fullPath: '/sale/discount/regular/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountRegularIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountRegularImport
    }
    '/_authenticated/sale/service-charge/detail/': {
      id: '/_authenticated/sale/service-charge/detail/'
      path: '/'
      fullPath: '/sale/service-charge/detail/'
      preLoaderRoute: typeof AuthenticatedSaleServiceChargeDetailIndexImport
      parentRoute: typeof AuthenticatedSaleServiceChargeDetailImport
    }
    '/_authenticated/setting/area/detail/': {
      id: '/_authenticated/setting/area/detail/'
      path: '/'
      fullPath: '/setting/area/detail/'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailIndexImport
      parentRoute: typeof AuthenticatedSettingAreaDetailImport
    }
    '/_authenticated/setting/payment-method/detail/': {
      id: '/_authenticated/setting/payment-method/detail/'
      path: '/setting/payment-method/detail'
      fullPath: '/setting/payment-method/detail'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodDetailIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/printer-position/printer-position-in-brand/': {
      id: '/_authenticated/setting/printer-position/printer-position-in-brand/'
      path: '/setting/printer-position/printer-position-in-brand'
      fullPath: '/setting/printer-position/printer-position-in-brand'
      preLoaderRoute: typeof AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/printer-position/printer-position-in-store/': {
      id: '/_authenticated/setting/printer-position/printer-position-in-store/'
      path: '/setting/printer-position/printer-position-in-store'
      fullPath: '/setting/printer-position/printer-position-in-store'
      preLoaderRoute: typeof AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/detail/': {
      id: '/_authenticated/setting/source/detail/'
      path: '/setting/source/detail'
      fullPath: '/setting/source/detail'
      preLoaderRoute: typeof AuthenticatedSettingSourceDetailIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/store/detail/': {
      id: '/_authenticated/setting/store/detail/'
      path: '/'
      fullPath: '/setting/store/detail/'
      preLoaderRoute: typeof AuthenticatedSettingStoreDetailIndexImport
      parentRoute: typeof AuthenticatedSettingStoreDetailImport
    }
    '/_authenticated/setting/table/detail/': {
      id: '/_authenticated/setting/table/detail/'
      path: '/'
      fullPath: '/setting/table/detail/'
      preLoaderRoute: typeof AuthenticatedSettingTableDetailIndexImport
      parentRoute: typeof AuthenticatedSettingTableDetailImport
    }
    '/_authenticated/menu/categories/categories-in-brand/detail/$id': {
      id: '/_authenticated/menu/categories/categories-in-brand/detail/$id'
      path: '/$id'
      fullPath: '/menu/categories/categories-in-brand/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport
      parentRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/detail/$customizationId': {
      id: '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
      path: '/$customizationId'
      fullPath: '/menu/customization/customization-in-city/detail/$customizationId'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail/$customizationId': {
      id: '/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
      path: '/$customizationId'
      fullPath: '/menu/customization/customization-in-store/detail/$customizationId'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
    }
    '/_authenticated/menu/items/items-in-city/detail/$id': {
      id: '/_authenticated/menu/items/items-in-city/detail/$id'
      path: '/$id'
      fullPath: '/menu/items/items-in-city/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInCityDetailIdImport
      parentRoute: typeof AuthenticatedMenuItemsItemsInCityDetailImport
    }
    '/_authenticated/menu/items/items-in-store/detail/$id': {
      id: '/_authenticated/menu/items/items-in-store/detail/$id'
      path: '/$id'
      fullPath: '/menu/items/items-in-store/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailIdImport
      parentRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailImport
    }
    '/_authenticated/sale/discount/membership/detail/$id': {
      id: '/_authenticated/sale/discount/membership/detail/$id'
      path: '/$id'
      fullPath: '/sale/discount/membership/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipDetailIdImport
      parentRoute: typeof AuthenticatedSaleDiscountMembershipDetailImport
    }
    '/_authenticated/sale/discount/regular/detail/$id': {
      id: '/_authenticated/sale/discount/regular/detail/$id'
      path: '/$id'
      fullPath: '/sale/discount/regular/detail/$id'
      preLoaderRoute: typeof AuthenticatedSaleDiscountRegularDetailIdImport
      parentRoute: typeof AuthenticatedSaleDiscountRegularDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/detail/': {
      id: '/_authenticated/menu/customization/customization-in-city/detail/'
      path: '/'
      fullPath: '/menu/customization/customization-in-city/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail/': {
      id: '/_authenticated/menu/customization/customization-in-store/detail/'
      path: '/'
      fullPath: '/menu/customization/customization-in-store/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
    }
    '/_authenticated/menu/items/items-in-city/detail/': {
      id: '/_authenticated/menu/items/items-in-city/detail/'
      path: '/'
      fullPath: '/menu/items/items-in-city/detail/'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInCityDetailIndexImport
      parentRoute: typeof AuthenticatedMenuItemsItemsInCityDetailImport
    }
    '/_authenticated/menu/items/items-in-store/detail/': {
      id: '/_authenticated/menu/items/items-in-store/detail/'
      path: '/'
      fullPath: '/menu/items/items-in-store/detail/'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailIndexImport
      parentRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailImport
    }
    '/_authenticated/sale/discount/membership/detail/': {
      id: '/_authenticated/sale/discount/membership/detail/'
      path: '/'
      fullPath: '/sale/discount/membership/detail/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipDetailIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountMembershipDetailImport
    }
    '/_authenticated/sale/discount/regular/detail/': {
      id: '/_authenticated/sale/discount/regular/detail/'
      path: '/'
      fullPath: '/sale/discount/regular/detail/'
      preLoaderRoute: typeof AuthenticatedSaleDiscountRegularDetailIndexImport
      parentRoute: typeof AuthenticatedSaleDiscountRegularDetailImport
    }
    '/_authenticated/sale/discount/membership/detail/$companyId/$brandId': {
      id: '/_authenticated/sale/discount/membership/detail/$companyId/$brandId'
      path: '/$companyId/$brandId'
      fullPath: '/sale/discount/membership/detail/$companyId/$brandId'
      preLoaderRoute: typeof AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdImport
      parentRoute: typeof AuthenticatedSaleDiscountMembershipDetailImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedEmployeeDetailRouteChildren {
  AuthenticatedEmployeeDetailUserIdRoute: typeof AuthenticatedEmployeeDetailUserIdRoute
}

const AuthenticatedEmployeeDetailRouteChildren: AuthenticatedEmployeeDetailRouteChildren =
  {
    AuthenticatedEmployeeDetailUserIdRoute:
      AuthenticatedEmployeeDetailUserIdRoute,
  }

const AuthenticatedEmployeeDetailRouteWithChildren =
  AuthenticatedEmployeeDetailRoute._addFileChildren(
    AuthenticatedEmployeeDetailRouteChildren,
  )

interface AuthenticatedEmployeeRoleDetailRouteChildren {
  AuthenticatedEmployeeRoleDetailRoleIdRoute: typeof AuthenticatedEmployeeRoleDetailRoleIdRoute
}

const AuthenticatedEmployeeRoleDetailRouteChildren: AuthenticatedEmployeeRoleDetailRouteChildren =
  {
    AuthenticatedEmployeeRoleDetailRoleIdRoute:
      AuthenticatedEmployeeRoleDetailRoleIdRoute,
  }

const AuthenticatedEmployeeRoleDetailRouteWithChildren =
  AuthenticatedEmployeeRoleDetailRoute._addFileChildren(
    AuthenticatedEmployeeRoleDetailRouteChildren,
  )

interface AuthenticatedEmployeeRoleRouteChildren {
  AuthenticatedEmployeeRoleDetailRoute: typeof AuthenticatedEmployeeRoleDetailRouteWithChildren
  AuthenticatedEmployeeRoleIndexRoute: typeof AuthenticatedEmployeeRoleIndexRoute
}

const AuthenticatedEmployeeRoleRouteChildren: AuthenticatedEmployeeRoleRouteChildren =
  {
    AuthenticatedEmployeeRoleDetailRoute:
      AuthenticatedEmployeeRoleDetailRouteWithChildren,
    AuthenticatedEmployeeRoleIndexRoute: AuthenticatedEmployeeRoleIndexRoute,
  }

const AuthenticatedEmployeeRoleRouteWithChildren =
  AuthenticatedEmployeeRoleRoute._addFileChildren(
    AuthenticatedEmployeeRoleRouteChildren,
  )

interface AuthenticatedMenuItemClassDetailRouteChildren {
  AuthenticatedMenuItemClassDetailIdRoute: typeof AuthenticatedMenuItemClassDetailIdRoute
  AuthenticatedMenuItemClassDetailIndexRoute: typeof AuthenticatedMenuItemClassDetailIndexRoute
}

const AuthenticatedMenuItemClassDetailRouteChildren: AuthenticatedMenuItemClassDetailRouteChildren =
  {
    AuthenticatedMenuItemClassDetailIdRoute:
      AuthenticatedMenuItemClassDetailIdRoute,
    AuthenticatedMenuItemClassDetailIndexRoute:
      AuthenticatedMenuItemClassDetailIndexRoute,
  }

const AuthenticatedMenuItemClassDetailRouteWithChildren =
  AuthenticatedMenuItemClassDetailRoute._addFileChildren(
    AuthenticatedMenuItemClassDetailRouteChildren,
  )

interface AuthenticatedMenuItemClassRouteChildren {
  AuthenticatedMenuItemClassDetailRoute: typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  AuthenticatedMenuItemClassIndexRoute: typeof AuthenticatedMenuItemClassIndexRoute
}

const AuthenticatedMenuItemClassRouteChildren: AuthenticatedMenuItemClassRouteChildren =
  {
    AuthenticatedMenuItemClassDetailRoute:
      AuthenticatedMenuItemClassDetailRouteWithChildren,
    AuthenticatedMenuItemClassIndexRoute: AuthenticatedMenuItemClassIndexRoute,
  }

const AuthenticatedMenuItemClassRouteWithChildren =
  AuthenticatedMenuItemClassRoute._addFileChildren(
    AuthenticatedMenuItemClassRouteChildren,
  )

interface AuthenticatedSaleChannelDiscountDetailRouteChildren {
  AuthenticatedSaleChannelDiscountDetailIdRoute: typeof AuthenticatedSaleChannelDiscountDetailIdRoute
  AuthenticatedSaleChannelDiscountDetailIndexRoute: typeof AuthenticatedSaleChannelDiscountDetailIndexRoute
}

const AuthenticatedSaleChannelDiscountDetailRouteChildren: AuthenticatedSaleChannelDiscountDetailRouteChildren =
  {
    AuthenticatedSaleChannelDiscountDetailIdRoute:
      AuthenticatedSaleChannelDiscountDetailIdRoute,
    AuthenticatedSaleChannelDiscountDetailIndexRoute:
      AuthenticatedSaleChannelDiscountDetailIndexRoute,
  }

const AuthenticatedSaleChannelDiscountDetailRouteWithChildren =
  AuthenticatedSaleChannelDiscountDetailRoute._addFileChildren(
    AuthenticatedSaleChannelDiscountDetailRouteChildren,
  )

interface AuthenticatedSaleChannelDiscountRouteChildren {
  AuthenticatedSaleChannelDiscountDetailRoute: typeof AuthenticatedSaleChannelDiscountDetailRouteWithChildren
  AuthenticatedSaleChannelDiscountIndexRoute: typeof AuthenticatedSaleChannelDiscountIndexRoute
}

const AuthenticatedSaleChannelDiscountRouteChildren: AuthenticatedSaleChannelDiscountRouteChildren =
  {
    AuthenticatedSaleChannelDiscountDetailRoute:
      AuthenticatedSaleChannelDiscountDetailRouteWithChildren,
    AuthenticatedSaleChannelDiscountIndexRoute:
      AuthenticatedSaleChannelDiscountIndexRoute,
  }

const AuthenticatedSaleChannelDiscountRouteWithChildren =
  AuthenticatedSaleChannelDiscountRoute._addFileChildren(
    AuthenticatedSaleChannelDiscountRouteChildren,
  )

interface AuthenticatedSaleComboDetailRouteChildren {
  AuthenticatedSaleComboDetailIdRoute: typeof AuthenticatedSaleComboDetailIdRoute
  AuthenticatedSaleComboDetailIndexRoute: typeof AuthenticatedSaleComboDetailIndexRoute
}

const AuthenticatedSaleComboDetailRouteChildren: AuthenticatedSaleComboDetailRouteChildren =
  {
    AuthenticatedSaleComboDetailIdRoute: AuthenticatedSaleComboDetailIdRoute,
    AuthenticatedSaleComboDetailIndexRoute:
      AuthenticatedSaleComboDetailIndexRoute,
  }

const AuthenticatedSaleComboDetailRouteWithChildren =
  AuthenticatedSaleComboDetailRoute._addFileChildren(
    AuthenticatedSaleComboDetailRouteChildren,
  )

interface AuthenticatedSaleComboRouteChildren {
  AuthenticatedSaleComboDetailRoute: typeof AuthenticatedSaleComboDetailRouteWithChildren
}

const AuthenticatedSaleComboRouteChildren: AuthenticatedSaleComboRouteChildren =
  {
    AuthenticatedSaleComboDetailRoute:
      AuthenticatedSaleComboDetailRouteWithChildren,
  }

const AuthenticatedSaleComboRouteWithChildren =
  AuthenticatedSaleComboRoute._addFileChildren(
    AuthenticatedSaleComboRouteChildren,
  )

interface AuthenticatedSaleDiscountPaymentDetailRouteChildren {
  AuthenticatedSaleDiscountPaymentDetailIdRoute: typeof AuthenticatedSaleDiscountPaymentDetailIdRoute
  AuthenticatedSaleDiscountPaymentDetailIndexRoute: typeof AuthenticatedSaleDiscountPaymentDetailIndexRoute
}

const AuthenticatedSaleDiscountPaymentDetailRouteChildren: AuthenticatedSaleDiscountPaymentDetailRouteChildren =
  {
    AuthenticatedSaleDiscountPaymentDetailIdRoute:
      AuthenticatedSaleDiscountPaymentDetailIdRoute,
    AuthenticatedSaleDiscountPaymentDetailIndexRoute:
      AuthenticatedSaleDiscountPaymentDetailIndexRoute,
  }

const AuthenticatedSaleDiscountPaymentDetailRouteWithChildren =
  AuthenticatedSaleDiscountPaymentDetailRoute._addFileChildren(
    AuthenticatedSaleDiscountPaymentDetailRouteChildren,
  )

interface AuthenticatedSaleDiscountPaymentRouteChildren {
  AuthenticatedSaleDiscountPaymentDetailRoute: typeof AuthenticatedSaleDiscountPaymentDetailRouteWithChildren
  AuthenticatedSaleDiscountPaymentIndexRoute: typeof AuthenticatedSaleDiscountPaymentIndexRoute
}

const AuthenticatedSaleDiscountPaymentRouteChildren: AuthenticatedSaleDiscountPaymentRouteChildren =
  {
    AuthenticatedSaleDiscountPaymentDetailRoute:
      AuthenticatedSaleDiscountPaymentDetailRouteWithChildren,
    AuthenticatedSaleDiscountPaymentIndexRoute:
      AuthenticatedSaleDiscountPaymentIndexRoute,
  }

const AuthenticatedSaleDiscountPaymentRouteWithChildren =
  AuthenticatedSaleDiscountPaymentRoute._addFileChildren(
    AuthenticatedSaleDiscountPaymentRouteChildren,
  )

interface AuthenticatedSaleServiceChargeDetailRouteChildren {
  AuthenticatedSaleServiceChargeDetailIdRoute: typeof AuthenticatedSaleServiceChargeDetailIdRoute
  AuthenticatedSaleServiceChargeDetailIndexRoute: typeof AuthenticatedSaleServiceChargeDetailIndexRoute
}

const AuthenticatedSaleServiceChargeDetailRouteChildren: AuthenticatedSaleServiceChargeDetailRouteChildren =
  {
    AuthenticatedSaleServiceChargeDetailIdRoute:
      AuthenticatedSaleServiceChargeDetailIdRoute,
    AuthenticatedSaleServiceChargeDetailIndexRoute:
      AuthenticatedSaleServiceChargeDetailIndexRoute,
  }

const AuthenticatedSaleServiceChargeDetailRouteWithChildren =
  AuthenticatedSaleServiceChargeDetailRoute._addFileChildren(
    AuthenticatedSaleServiceChargeDetailRouteChildren,
  )

interface AuthenticatedSaleServiceChargeRouteChildren {
  AuthenticatedSaleServiceChargeDetailRoute: typeof AuthenticatedSaleServiceChargeDetailRouteWithChildren
  AuthenticatedSaleServiceChargeIndexRoute: typeof AuthenticatedSaleServiceChargeIndexRoute
}

const AuthenticatedSaleServiceChargeRouteChildren: AuthenticatedSaleServiceChargeRouteChildren =
  {
    AuthenticatedSaleServiceChargeDetailRoute:
      AuthenticatedSaleServiceChargeDetailRouteWithChildren,
    AuthenticatedSaleServiceChargeIndexRoute:
      AuthenticatedSaleServiceChargeIndexRoute,
  }

const AuthenticatedSaleServiceChargeRouteWithChildren =
  AuthenticatedSaleServiceChargeRoute._addFileChildren(
    AuthenticatedSaleServiceChargeRouteChildren,
  )

interface AuthenticatedSettingAreaDetailRouteChildren {
  AuthenticatedSettingAreaDetailAreaIdRoute: typeof AuthenticatedSettingAreaDetailAreaIdRoute
  AuthenticatedSettingAreaDetailIndexRoute: typeof AuthenticatedSettingAreaDetailIndexRoute
}

const AuthenticatedSettingAreaDetailRouteChildren: AuthenticatedSettingAreaDetailRouteChildren =
  {
    AuthenticatedSettingAreaDetailAreaIdRoute:
      AuthenticatedSettingAreaDetailAreaIdRoute,
    AuthenticatedSettingAreaDetailIndexRoute:
      AuthenticatedSettingAreaDetailIndexRoute,
  }

const AuthenticatedSettingAreaDetailRouteWithChildren =
  AuthenticatedSettingAreaDetailRoute._addFileChildren(
    AuthenticatedSettingAreaDetailRouteChildren,
  )

interface AuthenticatedSettingAreaRouteChildren {
  AuthenticatedSettingAreaDetailRoute: typeof AuthenticatedSettingAreaDetailRouteWithChildren
  AuthenticatedSettingAreaIndexRoute: typeof AuthenticatedSettingAreaIndexRoute
}

const AuthenticatedSettingAreaRouteChildren: AuthenticatedSettingAreaRouteChildren =
  {
    AuthenticatedSettingAreaDetailRoute:
      AuthenticatedSettingAreaDetailRouteWithChildren,
    AuthenticatedSettingAreaIndexRoute: AuthenticatedSettingAreaIndexRoute,
  }

const AuthenticatedSettingAreaRouteWithChildren =
  AuthenticatedSettingAreaRoute._addFileChildren(
    AuthenticatedSettingAreaRouteChildren,
  )

interface AuthenticatedSettingStoreDetailRouteChildren {
  AuthenticatedSettingStoreDetailStoreIdRoute: typeof AuthenticatedSettingStoreDetailStoreIdRoute
  AuthenticatedSettingStoreDetailIndexRoute: typeof AuthenticatedSettingStoreDetailIndexRoute
}

const AuthenticatedSettingStoreDetailRouteChildren: AuthenticatedSettingStoreDetailRouteChildren =
  {
    AuthenticatedSettingStoreDetailStoreIdRoute:
      AuthenticatedSettingStoreDetailStoreIdRoute,
    AuthenticatedSettingStoreDetailIndexRoute:
      AuthenticatedSettingStoreDetailIndexRoute,
  }

const AuthenticatedSettingStoreDetailRouteWithChildren =
  AuthenticatedSettingStoreDetailRoute._addFileChildren(
    AuthenticatedSettingStoreDetailRouteChildren,
  )

interface AuthenticatedSettingStoreRouteChildren {
  AuthenticatedSettingStoreDetailRoute: typeof AuthenticatedSettingStoreDetailRouteWithChildren
  AuthenticatedSettingStoreIndexRoute: typeof AuthenticatedSettingStoreIndexRoute
}

const AuthenticatedSettingStoreRouteChildren: AuthenticatedSettingStoreRouteChildren =
  {
    AuthenticatedSettingStoreDetailRoute:
      AuthenticatedSettingStoreDetailRouteWithChildren,
    AuthenticatedSettingStoreIndexRoute: AuthenticatedSettingStoreIndexRoute,
  }

const AuthenticatedSettingStoreRouteWithChildren =
  AuthenticatedSettingStoreRoute._addFileChildren(
    AuthenticatedSettingStoreRouteChildren,
  )

interface AuthenticatedSettingTableDetailRouteChildren {
  AuthenticatedSettingTableDetailAreaIdRoute: typeof AuthenticatedSettingTableDetailAreaIdRoute
  AuthenticatedSettingTableDetailTableIdRoute: typeof AuthenticatedSettingTableDetailTableIdRoute
  AuthenticatedSettingTableDetailIndexRoute: typeof AuthenticatedSettingTableDetailIndexRoute
}

const AuthenticatedSettingTableDetailRouteChildren: AuthenticatedSettingTableDetailRouteChildren =
  {
    AuthenticatedSettingTableDetailAreaIdRoute:
      AuthenticatedSettingTableDetailAreaIdRoute,
    AuthenticatedSettingTableDetailTableIdRoute:
      AuthenticatedSettingTableDetailTableIdRoute,
    AuthenticatedSettingTableDetailIndexRoute:
      AuthenticatedSettingTableDetailIndexRoute,
  }

const AuthenticatedSettingTableDetailRouteWithChildren =
  AuthenticatedSettingTableDetailRoute._addFileChildren(
    AuthenticatedSettingTableDetailRouteChildren,
  )

interface AuthenticatedSettingTableRouteChildren {
  AuthenticatedSettingTableDetailRoute: typeof AuthenticatedSettingTableDetailRouteWithChildren
  AuthenticatedSettingTableIndexRoute: typeof AuthenticatedSettingTableIndexRoute
}

const AuthenticatedSettingTableRouteChildren: AuthenticatedSettingTableRouteChildren =
  {
    AuthenticatedSettingTableDetailRoute:
      AuthenticatedSettingTableDetailRouteWithChildren,
    AuthenticatedSettingTableIndexRoute: AuthenticatedSettingTableIndexRoute,
  }

const AuthenticatedSettingTableRouteWithChildren =
  AuthenticatedSettingTableRoute._addFileChildren(
    AuthenticatedSettingTableRouteChildren,
  )

interface AuthenticatedSettingTableLayoutRouteChildren {
  AuthenticatedSettingTableLayoutIndexRoute: typeof AuthenticatedSettingTableLayoutIndexRoute
}

const AuthenticatedSettingTableLayoutRouteChildren: AuthenticatedSettingTableLayoutRouteChildren =
  {
    AuthenticatedSettingTableLayoutIndexRoute:
      AuthenticatedSettingTableLayoutIndexRoute,
  }

const AuthenticatedSettingTableLayoutRouteWithChildren =
  AuthenticatedSettingTableLayoutRoute._addFileChildren(
    AuthenticatedSettingTableLayoutRouteChildren,
  )

interface AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren {
  AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
}

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren: AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren =
  {
    AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute:
      AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute,
  }

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute._addFileChildren(
    AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren,
  )

interface AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren {
  AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
}

const AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren: AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren =
  {
    AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute:
      AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren,
  }

const AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren =
  AuthenticatedMenuCategoriesCategoriesInBrandRoute._addFileChildren(
    AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren,
  )

interface AuthenticatedMenuCategoryInStoreDetailRouteChildren {
  AuthenticatedMenuCategoryInStoreDetailIdRoute: typeof AuthenticatedMenuCategoryInStoreDetailIdRoute
  AuthenticatedMenuCategoryInStoreDetailIndexRoute: typeof AuthenticatedMenuCategoryInStoreDetailIndexRoute
}

const AuthenticatedMenuCategoryInStoreDetailRouteChildren: AuthenticatedMenuCategoryInStoreDetailRouteChildren =
  {
    AuthenticatedMenuCategoryInStoreDetailIdRoute:
      AuthenticatedMenuCategoryInStoreDetailIdRoute,
    AuthenticatedMenuCategoryInStoreDetailIndexRoute:
      AuthenticatedMenuCategoryInStoreDetailIndexRoute,
  }

const AuthenticatedMenuCategoryInStoreDetailRouteWithChildren =
  AuthenticatedMenuCategoryInStoreDetailRoute._addFileChildren(
    AuthenticatedMenuCategoryInStoreDetailRouteChildren,
  )

interface AuthenticatedMenuCategoryDetailRouteChildren {
  AuthenticatedMenuCategoryDetailIdRoute: typeof AuthenticatedMenuCategoryDetailIdRoute
  AuthenticatedMenuCategoryDetailIndexRoute: typeof AuthenticatedMenuCategoryDetailIndexRoute
}

const AuthenticatedMenuCategoryDetailRouteChildren: AuthenticatedMenuCategoryDetailRouteChildren =
  {
    AuthenticatedMenuCategoryDetailIdRoute:
      AuthenticatedMenuCategoryDetailIdRoute,
    AuthenticatedMenuCategoryDetailIndexRoute:
      AuthenticatedMenuCategoryDetailIndexRoute,
  }

const AuthenticatedMenuCategoryDetailRouteWithChildren =
  AuthenticatedMenuCategoryDetailRoute._addFileChildren(
    AuthenticatedMenuCategoryDetailRouteChildren,
  )

interface AuthenticatedSaleChannelChannelDetailRouteChildren {
  AuthenticatedSaleChannelChannelDetailUuidRoute: typeof AuthenticatedSaleChannelChannelDetailUuidRoute
}

const AuthenticatedSaleChannelChannelDetailRouteChildren: AuthenticatedSaleChannelChannelDetailRouteChildren =
  {
    AuthenticatedSaleChannelChannelDetailUuidRoute:
      AuthenticatedSaleChannelChannelDetailUuidRoute,
  }

const AuthenticatedSaleChannelChannelDetailRouteWithChildren =
  AuthenticatedSaleChannelChannelDetailRoute._addFileChildren(
    AuthenticatedSaleChannelChannelDetailRouteChildren,
  )

interface AuthenticatedSaleDiscountMembershipDetailRouteChildren {
  AuthenticatedSaleDiscountMembershipDetailIdRoute: typeof AuthenticatedSaleDiscountMembershipDetailIdRoute
  AuthenticatedSaleDiscountMembershipDetailIndexRoute: typeof AuthenticatedSaleDiscountMembershipDetailIndexRoute
  AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute: typeof AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute
}

const AuthenticatedSaleDiscountMembershipDetailRouteChildren: AuthenticatedSaleDiscountMembershipDetailRouteChildren =
  {
    AuthenticatedSaleDiscountMembershipDetailIdRoute:
      AuthenticatedSaleDiscountMembershipDetailIdRoute,
    AuthenticatedSaleDiscountMembershipDetailIndexRoute:
      AuthenticatedSaleDiscountMembershipDetailIndexRoute,
    AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute:
      AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute,
  }

const AuthenticatedSaleDiscountMembershipDetailRouteWithChildren =
  AuthenticatedSaleDiscountMembershipDetailRoute._addFileChildren(
    AuthenticatedSaleDiscountMembershipDetailRouteChildren,
  )

interface AuthenticatedSaleDiscountMembershipRouteChildren {
  AuthenticatedSaleDiscountMembershipDetailRoute: typeof AuthenticatedSaleDiscountMembershipDetailRouteWithChildren
  AuthenticatedSaleDiscountMembershipIndexRoute: typeof AuthenticatedSaleDiscountMembershipIndexRoute
}

const AuthenticatedSaleDiscountMembershipRouteChildren: AuthenticatedSaleDiscountMembershipRouteChildren =
  {
    AuthenticatedSaleDiscountMembershipDetailRoute:
      AuthenticatedSaleDiscountMembershipDetailRouteWithChildren,
    AuthenticatedSaleDiscountMembershipIndexRoute:
      AuthenticatedSaleDiscountMembershipIndexRoute,
  }

const AuthenticatedSaleDiscountMembershipRouteWithChildren =
  AuthenticatedSaleDiscountMembershipRoute._addFileChildren(
    AuthenticatedSaleDiscountMembershipRouteChildren,
  )

interface AuthenticatedSaleDiscountRegularDetailRouteChildren {
  AuthenticatedSaleDiscountRegularDetailIdRoute: typeof AuthenticatedSaleDiscountRegularDetailIdRoute
  AuthenticatedSaleDiscountRegularDetailIndexRoute: typeof AuthenticatedSaleDiscountRegularDetailIndexRoute
}

const AuthenticatedSaleDiscountRegularDetailRouteChildren: AuthenticatedSaleDiscountRegularDetailRouteChildren =
  {
    AuthenticatedSaleDiscountRegularDetailIdRoute:
      AuthenticatedSaleDiscountRegularDetailIdRoute,
    AuthenticatedSaleDiscountRegularDetailIndexRoute:
      AuthenticatedSaleDiscountRegularDetailIndexRoute,
  }

const AuthenticatedSaleDiscountRegularDetailRouteWithChildren =
  AuthenticatedSaleDiscountRegularDetailRoute._addFileChildren(
    AuthenticatedSaleDiscountRegularDetailRouteChildren,
  )

interface AuthenticatedSaleDiscountRegularRouteChildren {
  AuthenticatedSaleDiscountRegularDetailRoute: typeof AuthenticatedSaleDiscountRegularDetailRouteWithChildren
  AuthenticatedSaleDiscountRegularIndexRoute: typeof AuthenticatedSaleDiscountRegularIndexRoute
}

const AuthenticatedSaleDiscountRegularRouteChildren: AuthenticatedSaleDiscountRegularRouteChildren =
  {
    AuthenticatedSaleDiscountRegularDetailRoute:
      AuthenticatedSaleDiscountRegularDetailRouteWithChildren,
    AuthenticatedSaleDiscountRegularIndexRoute:
      AuthenticatedSaleDiscountRegularIndexRoute,
  }

const AuthenticatedSaleDiscountRegularRouteWithChildren =
  AuthenticatedSaleDiscountRegularRoute._addFileChildren(
    AuthenticatedSaleDiscountRegularRouteChildren,
  )

interface AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren {
  AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute
}

const AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren: AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren =
  {
    AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute:
      AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute,
    AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute:
      AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute,
  }

const AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren =
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute._addFileChildren(
    AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren,
  )

interface AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren {
  AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
}

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren: AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren =
  {
    AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute:
      AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute,
    AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute:
      AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute,
  }

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute._addFileChildren(
    AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren,
  )

interface AuthenticatedMenuItemsItemsInCityDetailRouteChildren {
  AuthenticatedMenuItemsItemsInCityDetailIdRoute: typeof AuthenticatedMenuItemsItemsInCityDetailIdRoute
  AuthenticatedMenuItemsItemsInCityDetailIndexRoute: typeof AuthenticatedMenuItemsItemsInCityDetailIndexRoute
}

const AuthenticatedMenuItemsItemsInCityDetailRouteChildren: AuthenticatedMenuItemsItemsInCityDetailRouteChildren =
  {
    AuthenticatedMenuItemsItemsInCityDetailIdRoute:
      AuthenticatedMenuItemsItemsInCityDetailIdRoute,
    AuthenticatedMenuItemsItemsInCityDetailIndexRoute:
      AuthenticatedMenuItemsItemsInCityDetailIndexRoute,
  }

const AuthenticatedMenuItemsItemsInCityDetailRouteWithChildren =
  AuthenticatedMenuItemsItemsInCityDetailRoute._addFileChildren(
    AuthenticatedMenuItemsItemsInCityDetailRouteChildren,
  )

interface AuthenticatedMenuItemsItemsInStoreDetailRouteChildren {
  AuthenticatedMenuItemsItemsInStoreDetailIdRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailIdRoute
  AuthenticatedMenuItemsItemsInStoreDetailIndexRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailIndexRoute
}

const AuthenticatedMenuItemsItemsInStoreDetailRouteChildren: AuthenticatedMenuItemsItemsInStoreDetailRouteChildren =
  {
    AuthenticatedMenuItemsItemsInStoreDetailIdRoute:
      AuthenticatedMenuItemsItemsInStoreDetailIdRoute,
    AuthenticatedMenuItemsItemsInStoreDetailIndexRoute:
      AuthenticatedMenuItemsItemsInStoreDetailIndexRoute,
  }

const AuthenticatedMenuItemsItemsInStoreDetailRouteWithChildren =
  AuthenticatedMenuItemsItemsInStoreDetailRoute._addFileChildren(
    AuthenticatedMenuItemsItemsInStoreDetailRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedMembershipRoute: typeof AuthenticatedMembershipRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedCrmBillingDetailRoute: typeof AuthenticatedCrmBillingDetailRoute
  AuthenticatedCrmConfigRegisterPageRoute: typeof AuthenticatedCrmConfigRegisterPageRoute
  AuthenticatedCrmConnectCrmRoute: typeof AuthenticatedCrmConnectCrmRoute
  AuthenticatedCrmCustomerGroupRoute: typeof AuthenticatedCrmCustomerGroupRoute
  AuthenticatedCrmCustomerListRoute: typeof AuthenticatedCrmCustomerListRoute
  AuthenticatedCrmCustomerProfileRoute: typeof AuthenticatedCrmCustomerProfileRoute
  AuthenticatedCrmPricingTableRoute: typeof AuthenticatedCrmPricingTableRoute
  AuthenticatedCrmSettingsRoute: typeof AuthenticatedCrmSettingsRoute
  AuthenticatedCrmSystemLogRoute: typeof AuthenticatedCrmSystemLogRoute
  AuthenticatedCrmUsingMonthRoute: typeof AuthenticatedCrmUsingMonthRoute
  AuthenticatedDevicesListRoute: typeof AuthenticatedDevicesListRoute
  AuthenticatedDevicesNewRoute: typeof AuthenticatedDevicesNewRoute
  AuthenticatedDevicesTypesRoute: typeof AuthenticatedDevicesTypesRoute
  AuthenticatedEmployeeDetailRoute: typeof AuthenticatedEmployeeDetailRouteWithChildren
  AuthenticatedEmployeeListRoute: typeof AuthenticatedEmployeeListRoute
  AuthenticatedEmployeeRoleRoute: typeof AuthenticatedEmployeeRoleRouteWithChildren
  AuthenticatedGeneralSetupsAccountRoute: typeof AuthenticatedGeneralSetupsAccountRoute
  AuthenticatedGeneralSetupsCreateUserRoute: typeof AuthenticatedGeneralSetupsCreateUserRoute
  AuthenticatedMenuItemClassRoute: typeof AuthenticatedMenuItemClassRouteWithChildren
  AuthenticatedMenuQuantityDayRoute: typeof AuthenticatedMenuQuantityDayRoute
  AuthenticatedMenuScheduleRoute: typeof AuthenticatedMenuScheduleRoute
  AuthenticatedSaleChannelDiscountRoute: typeof AuthenticatedSaleChannelDiscountRouteWithChildren
  AuthenticatedSaleComboRoute: typeof AuthenticatedSaleComboRouteWithChildren
  AuthenticatedSaleDiscountPaymentRoute: typeof AuthenticatedSaleDiscountPaymentRouteWithChildren
  AuthenticatedSaleServiceChargeRoute: typeof AuthenticatedSaleServiceChargeRouteWithChildren
  AuthenticatedSettingAreaRoute: typeof AuthenticatedSettingAreaRouteWithChildren
  AuthenticatedSettingStoreRoute: typeof AuthenticatedSettingStoreRouteWithChildren
  AuthenticatedSettingTableRoute: typeof AuthenticatedSettingTableRouteWithChildren
  AuthenticatedSettingTableLayoutRoute: typeof AuthenticatedSettingTableLayoutRouteWithChildren
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
  AuthenticatedCrmFoodItemReviewItemsRoute: typeof AuthenticatedCrmFoodItemReviewItemsRoute
  AuthenticatedCrmGeneralSetupsAccountRoute: typeof AuthenticatedCrmGeneralSetupsAccountRoute
  AuthenticatedCrmGeneralSetupsComboRoute: typeof AuthenticatedCrmGeneralSetupsComboRoute
  AuthenticatedCrmGeneralSetupsComboSpecialRoute: typeof AuthenticatedCrmGeneralSetupsComboSpecialRoute
  AuthenticatedCrmGeneralSetupsCreateUserRoute: typeof AuthenticatedCrmGeneralSetupsCreateUserRoute
  AuthenticatedCrmGeneralSetupsItemTypeRoute: typeof AuthenticatedCrmGeneralSetupsItemTypeRoute
  AuthenticatedCrmGeneralSetupsItemsRoute: typeof AuthenticatedCrmGeneralSetupsItemsRoute
  AuthenticatedCrmGeneralSetupsStoreMenuRoute: typeof AuthenticatedCrmGeneralSetupsStoreMenuRoute
  AuthenticatedCrmLoyaltyExtraPointRoute: typeof AuthenticatedCrmLoyaltyExtraPointRoute
  AuthenticatedCrmLoyaltyMembershipTypeRoute: typeof AuthenticatedCrmLoyaltyMembershipTypeRoute
  AuthenticatedCrmReportCustomerRoute: typeof AuthenticatedCrmReportCustomerRoute
  AuthenticatedCrmReportRatingFeedbackRoute: typeof AuthenticatedCrmReportRatingFeedbackRoute
  AuthenticatedCrmReportRevenueRoute: typeof AuthenticatedCrmReportRevenueRoute
  AuthenticatedCrmReportSaleManagerRoute: typeof AuthenticatedCrmReportSaleManagerRoute
  AuthenticatedCrmReportVoucherRoute: typeof AuthenticatedCrmReportVoucherRoute
  AuthenticatedDevicesDetailIdRoute: typeof AuthenticatedDevicesDetailIdRoute
  AuthenticatedMenuCategoriesCategoriesInBrandRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  AuthenticatedMenuCategoriesCategoriesInStoreRoute: typeof AuthenticatedMenuCategoriesCategoriesInStoreRoute
  AuthenticatedMenuCategoryInStoreDetailRoute: typeof AuthenticatedMenuCategoryInStoreDetailRouteWithChildren
  AuthenticatedMenuCategoryDetailRoute: typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  AuthenticatedMenuCustomizationNewRoute: typeof AuthenticatedMenuCustomizationNewRoute
  AuthenticatedMenuItemRemovedItemRemovedInCityRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  AuthenticatedMenuItemRemovedItemRemovedInStoreRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  AuthenticatedReportAccountingSaleDetailAuditRoute: typeof AuthenticatedReportAccountingSaleDetailAuditRoute
  AuthenticatedReportRevenueSaleSummaryRoute: typeof AuthenticatedReportRevenueSaleSummaryRoute
  AuthenticatedSaleChannelChannelDetailRoute: typeof AuthenticatedSaleChannelChannelDetailRouteWithChildren
  AuthenticatedSaleDiscountMembershipRoute: typeof AuthenticatedSaleDiscountMembershipRouteWithChildren
  AuthenticatedSaleDiscountRegularRoute: typeof AuthenticatedSaleDiscountRegularRouteWithChildren
  AuthenticatedSaleChannelChannelIndexRoute: typeof AuthenticatedSaleChannelChannelIndexRoute
  AuthenticatedSalePromotionIndexRoute: typeof AuthenticatedSalePromotionIndexRoute
  AuthenticatedSettingBillModelIndexRoute: typeof AuthenticatedSettingBillModelIndexRoute
  AuthenticatedSettingPaymentMethodIndexRoute: typeof AuthenticatedSettingPaymentMethodIndexRoute
  AuthenticatedSettingSourceIndexRoute: typeof AuthenticatedSettingSourceIndexRoute
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  AuthenticatedMenuItemsItemsInCityDetailRoute: typeof AuthenticatedMenuItemsItemsInCityDetailRouteWithChildren
  AuthenticatedMenuItemsItemsInStoreDetailRoute: typeof AuthenticatedMenuItemsItemsInStoreDetailRouteWithChildren
  AuthenticatedReportAccountingInvoicesSaleSyncVatRoute: typeof AuthenticatedReportAccountingInvoicesSaleSyncVatRoute
  AuthenticatedReportRevenueCategoriesPaymentMethodRoute: typeof AuthenticatedReportRevenueCategoriesPaymentMethodRoute
  AuthenticatedReportRevenueCategoriesPromotionRoute: typeof AuthenticatedReportRevenueCategoriesPromotionRoute
  AuthenticatedReportRevenueCategoriesSourceRoute: typeof AuthenticatedReportRevenueCategoriesSourceRoute
  AuthenticatedReportRevenueRevenueGeneralRoute: typeof AuthenticatedReportRevenueRevenueGeneralRoute
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute: typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  AuthenticatedSettingSourceDetailSourceIdRoute: typeof AuthenticatedSettingSourceDetailSourceIdRoute
  AuthenticatedMenuCustomizationCustomizationInCityIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  AuthenticatedMenuItemsItemsInCityIndexRoute: typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  AuthenticatedMenuItemsItemsInStoreIndexRoute: typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  AuthenticatedSettingPaymentMethodDetailIndexRoute: typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute: typeof AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute
  AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute: typeof AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute
  AuthenticatedSettingSourceDetailIndexRoute: typeof AuthenticatedSettingSourceDetailIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedMembershipRoute: AuthenticatedMembershipRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedCrmBillingDetailRoute: AuthenticatedCrmBillingDetailRoute,
  AuthenticatedCrmConfigRegisterPageRoute:
    AuthenticatedCrmConfigRegisterPageRoute,
  AuthenticatedCrmConnectCrmRoute: AuthenticatedCrmConnectCrmRoute,
  AuthenticatedCrmCustomerGroupRoute: AuthenticatedCrmCustomerGroupRoute,
  AuthenticatedCrmCustomerListRoute: AuthenticatedCrmCustomerListRoute,
  AuthenticatedCrmCustomerProfileRoute: AuthenticatedCrmCustomerProfileRoute,
  AuthenticatedCrmPricingTableRoute: AuthenticatedCrmPricingTableRoute,
  AuthenticatedCrmSettingsRoute: AuthenticatedCrmSettingsRoute,
  AuthenticatedCrmSystemLogRoute: AuthenticatedCrmSystemLogRoute,
  AuthenticatedCrmUsingMonthRoute: AuthenticatedCrmUsingMonthRoute,
  AuthenticatedDevicesListRoute: AuthenticatedDevicesListRoute,
  AuthenticatedDevicesNewRoute: AuthenticatedDevicesNewRoute,
  AuthenticatedDevicesTypesRoute: AuthenticatedDevicesTypesRoute,
  AuthenticatedEmployeeDetailRoute:
    AuthenticatedEmployeeDetailRouteWithChildren,
  AuthenticatedEmployeeListRoute: AuthenticatedEmployeeListRoute,
  AuthenticatedEmployeeRoleRoute: AuthenticatedEmployeeRoleRouteWithChildren,
  AuthenticatedGeneralSetupsAccountRoute:
    AuthenticatedGeneralSetupsAccountRoute,
  AuthenticatedGeneralSetupsCreateUserRoute:
    AuthenticatedGeneralSetupsCreateUserRoute,
  AuthenticatedMenuItemClassRoute: AuthenticatedMenuItemClassRouteWithChildren,
  AuthenticatedMenuQuantityDayRoute: AuthenticatedMenuQuantityDayRoute,
  AuthenticatedMenuScheduleRoute: AuthenticatedMenuScheduleRoute,
  AuthenticatedSaleChannelDiscountRoute:
    AuthenticatedSaleChannelDiscountRouteWithChildren,
  AuthenticatedSaleComboRoute: AuthenticatedSaleComboRouteWithChildren,
  AuthenticatedSaleDiscountPaymentRoute:
    AuthenticatedSaleDiscountPaymentRouteWithChildren,
  AuthenticatedSaleServiceChargeRoute:
    AuthenticatedSaleServiceChargeRouteWithChildren,
  AuthenticatedSettingAreaRoute: AuthenticatedSettingAreaRouteWithChildren,
  AuthenticatedSettingStoreRoute: AuthenticatedSettingStoreRouteWithChildren,
  AuthenticatedSettingTableRoute: AuthenticatedSettingTableRouteWithChildren,
  AuthenticatedSettingTableLayoutRoute:
    AuthenticatedSettingTableLayoutRouteWithChildren,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
  AuthenticatedCrmFoodItemReviewItemsRoute:
    AuthenticatedCrmFoodItemReviewItemsRoute,
  AuthenticatedCrmGeneralSetupsAccountRoute:
    AuthenticatedCrmGeneralSetupsAccountRoute,
  AuthenticatedCrmGeneralSetupsComboRoute:
    AuthenticatedCrmGeneralSetupsComboRoute,
  AuthenticatedCrmGeneralSetupsComboSpecialRoute:
    AuthenticatedCrmGeneralSetupsComboSpecialRoute,
  AuthenticatedCrmGeneralSetupsCreateUserRoute:
    AuthenticatedCrmGeneralSetupsCreateUserRoute,
  AuthenticatedCrmGeneralSetupsItemTypeRoute:
    AuthenticatedCrmGeneralSetupsItemTypeRoute,
  AuthenticatedCrmGeneralSetupsItemsRoute:
    AuthenticatedCrmGeneralSetupsItemsRoute,
  AuthenticatedCrmGeneralSetupsStoreMenuRoute:
    AuthenticatedCrmGeneralSetupsStoreMenuRoute,
  AuthenticatedCrmLoyaltyExtraPointRoute:
    AuthenticatedCrmLoyaltyExtraPointRoute,
  AuthenticatedCrmLoyaltyMembershipTypeRoute:
    AuthenticatedCrmLoyaltyMembershipTypeRoute,
  AuthenticatedCrmReportCustomerRoute: AuthenticatedCrmReportCustomerRoute,
  AuthenticatedCrmReportRatingFeedbackRoute:
    AuthenticatedCrmReportRatingFeedbackRoute,
  AuthenticatedCrmReportRevenueRoute: AuthenticatedCrmReportRevenueRoute,
  AuthenticatedCrmReportSaleManagerRoute:
    AuthenticatedCrmReportSaleManagerRoute,
  AuthenticatedCrmReportVoucherRoute: AuthenticatedCrmReportVoucherRoute,
  AuthenticatedDevicesDetailIdRoute: AuthenticatedDevicesDetailIdRoute,
  AuthenticatedMenuCategoriesCategoriesInBrandRoute:
    AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren,
  AuthenticatedMenuCategoriesCategoriesInStoreRoute:
    AuthenticatedMenuCategoriesCategoriesInStoreRoute,
  AuthenticatedMenuCategoryInStoreDetailRoute:
    AuthenticatedMenuCategoryInStoreDetailRouteWithChildren,
  AuthenticatedMenuCategoryDetailRoute:
    AuthenticatedMenuCategoryDetailRouteWithChildren,
  AuthenticatedMenuCustomizationNewRoute:
    AuthenticatedMenuCustomizationNewRoute,
  AuthenticatedMenuItemRemovedItemRemovedInCityRoute:
    AuthenticatedMenuItemRemovedItemRemovedInCityRoute,
  AuthenticatedMenuItemRemovedItemRemovedInStoreRoute:
    AuthenticatedMenuItemRemovedItemRemovedInStoreRoute,
  AuthenticatedReportAccountingSaleDetailAuditRoute:
    AuthenticatedReportAccountingSaleDetailAuditRoute,
  AuthenticatedReportRevenueSaleSummaryRoute:
    AuthenticatedReportRevenueSaleSummaryRoute,
  AuthenticatedSaleChannelChannelDetailRoute:
    AuthenticatedSaleChannelChannelDetailRouteWithChildren,
  AuthenticatedSaleDiscountMembershipRoute:
    AuthenticatedSaleDiscountMembershipRouteWithChildren,
  AuthenticatedSaleDiscountRegularRoute:
    AuthenticatedSaleDiscountRegularRouteWithChildren,
  AuthenticatedSaleChannelChannelIndexRoute:
    AuthenticatedSaleChannelChannelIndexRoute,
  AuthenticatedSalePromotionIndexRoute: AuthenticatedSalePromotionIndexRoute,
  AuthenticatedSettingBillModelIndexRoute:
    AuthenticatedSettingBillModelIndexRoute,
  AuthenticatedSettingPaymentMethodIndexRoute:
    AuthenticatedSettingPaymentMethodIndexRoute,
  AuthenticatedSettingSourceIndexRoute: AuthenticatedSettingSourceIndexRoute,
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute:
    AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren,
  AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute,
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren,
  AuthenticatedMenuItemsItemsInCityDetailRoute:
    AuthenticatedMenuItemsItemsInCityDetailRouteWithChildren,
  AuthenticatedMenuItemsItemsInStoreDetailRoute:
    AuthenticatedMenuItemsItemsInStoreDetailRouteWithChildren,
  AuthenticatedReportAccountingInvoicesSaleSyncVatRoute:
    AuthenticatedReportAccountingInvoicesSaleSyncVatRoute,
  AuthenticatedReportRevenueCategoriesPaymentMethodRoute:
    AuthenticatedReportRevenueCategoriesPaymentMethodRoute,
  AuthenticatedReportRevenueCategoriesPromotionRoute:
    AuthenticatedReportRevenueCategoriesPromotionRoute,
  AuthenticatedReportRevenueCategoriesSourceRoute:
    AuthenticatedReportRevenueCategoriesSourceRoute,
  AuthenticatedReportRevenueRevenueGeneralRoute:
    AuthenticatedReportRevenueRevenueGeneralRoute,
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute:
    AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute,
  AuthenticatedSettingSourceDetailSourceIdRoute:
    AuthenticatedSettingSourceDetailSourceIdRoute,
  AuthenticatedMenuCustomizationCustomizationInCityIndexRoute:
    AuthenticatedMenuCustomizationCustomizationInCityIndexRoute,
  AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute,
  AuthenticatedMenuItemsItemsInCityIndexRoute:
    AuthenticatedMenuItemsItemsInCityIndexRoute,
  AuthenticatedMenuItemsItemsInStoreIndexRoute:
    AuthenticatedMenuItemsItemsInStoreIndexRoute,
  AuthenticatedSettingPaymentMethodDetailIndexRoute:
    AuthenticatedSettingPaymentMethodDetailIndexRoute,
  AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute:
    AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute,
  AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute:
    AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute,
  AuthenticatedSettingSourceDetailIndexRoute:
    AuthenticatedSettingSourceDetailIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

interface ClerkauthRouteRouteChildren {
  ClerkauthSignInRoute: typeof ClerkauthSignInRoute
  ClerkauthSignUpRoute: typeof ClerkauthSignUpRoute
}

const ClerkauthRouteRouteChildren: ClerkauthRouteRouteChildren = {
  ClerkauthSignInRoute: ClerkauthSignInRoute,
  ClerkauthSignUpRoute: ClerkauthSignUpRoute,
}

const ClerkauthRouteRouteWithChildren = ClerkauthRouteRoute._addFileChildren(
  ClerkauthRouteRouteChildren,
)

interface ClerkAuthenticatedRouteRouteChildren {
  ClerkAuthenticatedUserManagementRoute: typeof ClerkAuthenticatedUserManagementRoute
}

const ClerkAuthenticatedRouteRouteChildren: ClerkAuthenticatedRouteRouteChildren =
  {
    ClerkAuthenticatedUserManagementRoute:
      ClerkAuthenticatedUserManagementRoute,
  }

const ClerkAuthenticatedRouteRouteWithChildren =
  ClerkAuthenticatedRouteRoute._addFileChildren(
    ClerkAuthenticatedRouteRouteChildren,
  )

interface ClerkRouteRouteChildren {
  ClerkauthRouteRoute: typeof ClerkauthRouteRouteWithChildren
  ClerkAuthenticatedRouteRoute: typeof ClerkAuthenticatedRouteRouteWithChildren
}

const ClerkRouteRouteChildren: ClerkRouteRouteChildren = {
  ClerkauthRouteRoute: ClerkauthRouteRouteWithChildren,
  ClerkAuthenticatedRouteRoute: ClerkAuthenticatedRouteRouteWithChildren,
}

const ClerkRouteRouteWithChildren = ClerkRouteRoute._addFileChildren(
  ClerkRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/': typeof ClerkauthRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/membership': typeof AuthenticatedMembershipRoute
  '/': typeof AuthenticatedIndexRoute
  '/crm/billing-detail': typeof AuthenticatedCrmBillingDetailRoute
  '/crm/config-register-page': typeof AuthenticatedCrmConfigRegisterPageRoute
  '/crm/connect-crm': typeof AuthenticatedCrmConnectCrmRoute
  '/crm/customer-group': typeof AuthenticatedCrmCustomerGroupRoute
  '/crm/customer-list': typeof AuthenticatedCrmCustomerListRoute
  '/crm/customer-profile': typeof AuthenticatedCrmCustomerProfileRoute
  '/crm/pricing-table': typeof AuthenticatedCrmPricingTableRoute
  '/crm/settings': typeof AuthenticatedCrmSettingsRoute
  '/crm/system-log': typeof AuthenticatedCrmSystemLogRoute
  '/crm/using-month': typeof AuthenticatedCrmUsingMonthRoute
  '/devices/list': typeof AuthenticatedDevicesListRoute
  '/devices/new': typeof AuthenticatedDevicesNewRoute
  '/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/employee/list': typeof AuthenticatedEmployeeListRoute
  '/employee/role': typeof AuthenticatedEmployeeRoleRouteWithChildren
  '/general-setups/account': typeof AuthenticatedGeneralSetupsAccountRoute
  '/general-setups/create-user': typeof AuthenticatedGeneralSetupsCreateUserRoute
  '/menu/item-class': typeof AuthenticatedMenuItemClassRouteWithChildren
  '/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountRouteWithChildren
  '/sale/combo': typeof AuthenticatedSaleComboRouteWithChildren
  '/sale/discount-payment': typeof AuthenticatedSaleDiscountPaymentRouteWithChildren
  '/sale/service-charge': typeof AuthenticatedSaleServiceChargeRouteWithChildren
  '/setting/area': typeof AuthenticatedSettingAreaRouteWithChildren
  '/setting/store': typeof AuthenticatedSettingStoreRouteWithChildren
  '/setting/table': typeof AuthenticatedSettingTableRouteWithChildren
  '/setting/table-layout': typeof AuthenticatedSettingTableLayoutRouteWithChildren
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/crm/food-item-review/items': typeof AuthenticatedCrmFoodItemReviewItemsRoute
  '/crm/general-setups/account': typeof AuthenticatedCrmGeneralSetupsAccountRoute
  '/crm/general-setups/combo': typeof AuthenticatedCrmGeneralSetupsComboRoute
  '/crm/general-setups/combo-special': typeof AuthenticatedCrmGeneralSetupsComboSpecialRoute
  '/crm/general-setups/create-user': typeof AuthenticatedCrmGeneralSetupsCreateUserRoute
  '/crm/general-setups/item-type': typeof AuthenticatedCrmGeneralSetupsItemTypeRoute
  '/crm/general-setups/items': typeof AuthenticatedCrmGeneralSetupsItemsRoute
  '/crm/general-setups/store-menu': typeof AuthenticatedCrmGeneralSetupsStoreMenuRoute
  '/crm/loyalty/extra-point': typeof AuthenticatedCrmLoyaltyExtraPointRoute
  '/crm/loyalty/membership-type': typeof AuthenticatedCrmLoyaltyMembershipTypeRoute
  '/crm/report/customer': typeof AuthenticatedCrmReportCustomerRoute
  '/crm/report/rating-feedback': typeof AuthenticatedCrmReportRatingFeedbackRoute
  '/crm/report/revenue': typeof AuthenticatedCrmReportRevenueRoute
  '/crm/report/sale-manager': typeof AuthenticatedCrmReportSaleManagerRoute
  '/crm/report/voucher': typeof AuthenticatedCrmReportVoucherRoute
  '/devices/detail/$id': typeof AuthenticatedDevicesDetailIdRoute
  '/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRouteWithChildren
  '/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/menu/categories/categories-in-store': typeof AuthenticatedMenuCategoriesCategoriesInStoreRoute
  '/menu/category-in-store/detail': typeof AuthenticatedMenuCategoryInStoreDetailRouteWithChildren
  '/menu/category/detail': typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  '/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  '/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/report/accounting/sale-detail-audit': typeof AuthenticatedReportAccountingSaleDetailAuditRoute
  '/report/revenue/sale-summary': typeof AuthenticatedReportRevenueSaleSummaryRoute
  '/sale-channel/channel/detail': typeof AuthenticatedSaleChannelChannelDetailRouteWithChildren
  '/sale-channel/discount/detail': typeof AuthenticatedSaleChannelDiscountDetailRouteWithChildren
  '/sale/combo/detail': typeof AuthenticatedSaleComboDetailRouteWithChildren
  '/sale/discount-payment/detail': typeof AuthenticatedSaleDiscountPaymentDetailRouteWithChildren
  '/sale/discount/membership': typeof AuthenticatedSaleDiscountMembershipRouteWithChildren
  '/sale/discount/regular': typeof AuthenticatedSaleDiscountRegularRouteWithChildren
  '/sale/service-charge/detail': typeof AuthenticatedSaleServiceChargeDetailRouteWithChildren
  '/setting/area/detail': typeof AuthenticatedSettingAreaDetailRouteWithChildren
  '/setting/store/detail': typeof AuthenticatedSettingStoreDetailRouteWithChildren
  '/setting/table/detail': typeof AuthenticatedSettingTableDetailRouteWithChildren
  '/employee/role/': typeof AuthenticatedEmployeeRoleIndexRoute
  '/menu/item-class/': typeof AuthenticatedMenuItemClassIndexRoute
  '/sale-channel/channel': typeof AuthenticatedSaleChannelChannelIndexRoute
  '/sale-channel/discount/': typeof AuthenticatedSaleChannelDiscountIndexRoute
  '/sale/discount-payment/': typeof AuthenticatedSaleDiscountPaymentIndexRoute
  '/sale/promotion': typeof AuthenticatedSalePromotionIndexRoute
  '/sale/service-charge/': typeof AuthenticatedSaleServiceChargeIndexRoute
  '/setting/area/': typeof AuthenticatedSettingAreaIndexRoute
  '/setting/bill-model': typeof AuthenticatedSettingBillModelIndexRoute
  '/setting/payment-method': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/setting/source': typeof AuthenticatedSettingSourceIndexRoute
  '/setting/store/': typeof AuthenticatedSettingStoreIndexRoute
  '/setting/table-layout/': typeof AuthenticatedSettingTableLayoutIndexRoute
  '/setting/table/': typeof AuthenticatedSettingTableIndexRoute
  '/employee/role/detail/$roleId': typeof AuthenticatedEmployeeRoleDetailRoleIdRoute
  '/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/menu/category-in-store/detail/$id': typeof AuthenticatedMenuCategoryInStoreDetailIdRoute
  '/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  '/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  '/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/menu/items/items-in-city/detail': typeof AuthenticatedMenuItemsItemsInCityDetailRouteWithChildren
  '/menu/items/items-in-store/detail': typeof AuthenticatedMenuItemsItemsInStoreDetailRouteWithChildren
  '/report/accounting/invoices/sale-sync-vat': typeof AuthenticatedReportAccountingInvoicesSaleSyncVatRoute
  '/report/revenue/categories/payment-method': typeof AuthenticatedReportRevenueCategoriesPaymentMethodRoute
  '/report/revenue/categories/promotion': typeof AuthenticatedReportRevenueCategoriesPromotionRoute
  '/report/revenue/categories/source': typeof AuthenticatedReportRevenueCategoriesSourceRoute
  '/report/revenue/revenue/general': typeof AuthenticatedReportRevenueRevenueGeneralRoute
  '/sale-channel/channel/detail/$uuid': typeof AuthenticatedSaleChannelChannelDetailUuidRoute
  '/sale-channel/discount/detail/$id': typeof AuthenticatedSaleChannelDiscountDetailIdRoute
  '/sale/combo/detail/$id': typeof AuthenticatedSaleComboDetailIdRoute
  '/sale/discount-payment/detail/$id': typeof AuthenticatedSaleDiscountPaymentDetailIdRoute
  '/sale/discount/membership/detail': typeof AuthenticatedSaleDiscountMembershipDetailRouteWithChildren
  '/sale/discount/regular/detail': typeof AuthenticatedSaleDiscountRegularDetailRouteWithChildren
  '/sale/service-charge/detail/$id': typeof AuthenticatedSaleServiceChargeDetailIdRoute
  '/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/setting/store/detail/$storeId': typeof AuthenticatedSettingStoreDetailStoreIdRoute
  '/setting/table/detail/$areaId': typeof AuthenticatedSettingTableDetailAreaIdRoute
  '/setting/table/detail/$tableId': typeof AuthenticatedSettingTableDetailTableIdRoute
  '/menu/category-in-store/detail/': typeof AuthenticatedMenuCategoryInStoreDetailIndexRoute
  '/menu/category/detail/': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/menu/customization/customization-in-city': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/menu/customization/customization-in-store': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/menu/item-class/detail/': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/menu/items/items-in-city': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/menu/items/items-in-store': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/sale-channel/discount/detail/': typeof AuthenticatedSaleChannelDiscountDetailIndexRoute
  '/sale/combo/detail/': typeof AuthenticatedSaleComboDetailIndexRoute
  '/sale/discount-payment/detail/': typeof AuthenticatedSaleDiscountPaymentDetailIndexRoute
  '/sale/discount/membership/': typeof AuthenticatedSaleDiscountMembershipIndexRoute
  '/sale/discount/regular/': typeof AuthenticatedSaleDiscountRegularIndexRoute
  '/sale/service-charge/detail/': typeof AuthenticatedSaleServiceChargeDetailIndexRoute
  '/setting/area/detail/': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/setting/payment-method/detail': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/setting/printer-position/printer-position-in-brand': typeof AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute
  '/setting/printer-position/printer-position-in-store': typeof AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute
  '/setting/source/detail': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/setting/store/detail/': typeof AuthenticatedSettingStoreDetailIndexRoute
  '/setting/table/detail/': typeof AuthenticatedSettingTableDetailIndexRoute
  '/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/menu/items/items-in-city/detail/$id': typeof AuthenticatedMenuItemsItemsInCityDetailIdRoute
  '/menu/items/items-in-store/detail/$id': typeof AuthenticatedMenuItemsItemsInStoreDetailIdRoute
  '/sale/discount/membership/detail/$id': typeof AuthenticatedSaleDiscountMembershipDetailIdRoute
  '/sale/discount/regular/detail/$id': typeof AuthenticatedSaleDiscountRegularDetailIdRoute
  '/menu/customization/customization-in-city/detail/': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute
  '/menu/customization/customization-in-store/detail/': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
  '/menu/items/items-in-city/detail/': typeof AuthenticatedMenuItemsItemsInCityDetailIndexRoute
  '/menu/items/items-in-store/detail/': typeof AuthenticatedMenuItemsItemsInStoreDetailIndexRoute
  '/sale/discount/membership/detail/': typeof AuthenticatedSaleDiscountMembershipDetailIndexRoute
  '/sale/discount/regular/detail/': typeof AuthenticatedSaleDiscountRegularDetailIndexRoute
  '/sale/discount/membership/detail/$companyId/$brandId': typeof AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute
}

export interface FileRoutesByTo {
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/membership': typeof AuthenticatedMembershipRoute
  '/': typeof AuthenticatedIndexRoute
  '/crm/billing-detail': typeof AuthenticatedCrmBillingDetailRoute
  '/crm/config-register-page': typeof AuthenticatedCrmConfigRegisterPageRoute
  '/crm/connect-crm': typeof AuthenticatedCrmConnectCrmRoute
  '/crm/customer-group': typeof AuthenticatedCrmCustomerGroupRoute
  '/crm/customer-list': typeof AuthenticatedCrmCustomerListRoute
  '/crm/customer-profile': typeof AuthenticatedCrmCustomerProfileRoute
  '/crm/pricing-table': typeof AuthenticatedCrmPricingTableRoute
  '/crm/settings': typeof AuthenticatedCrmSettingsRoute
  '/crm/system-log': typeof AuthenticatedCrmSystemLogRoute
  '/crm/using-month': typeof AuthenticatedCrmUsingMonthRoute
  '/devices/list': typeof AuthenticatedDevicesListRoute
  '/devices/new': typeof AuthenticatedDevicesNewRoute
  '/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/employee/list': typeof AuthenticatedEmployeeListRoute
  '/general-setups/account': typeof AuthenticatedGeneralSetupsAccountRoute
  '/general-setups/create-user': typeof AuthenticatedGeneralSetupsCreateUserRoute
  '/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/sale/combo': typeof AuthenticatedSaleComboRouteWithChildren
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/crm/food-item-review/items': typeof AuthenticatedCrmFoodItemReviewItemsRoute
  '/crm/general-setups/account': typeof AuthenticatedCrmGeneralSetupsAccountRoute
  '/crm/general-setups/combo': typeof AuthenticatedCrmGeneralSetupsComboRoute
  '/crm/general-setups/combo-special': typeof AuthenticatedCrmGeneralSetupsComboSpecialRoute
  '/crm/general-setups/create-user': typeof AuthenticatedCrmGeneralSetupsCreateUserRoute
  '/crm/general-setups/item-type': typeof AuthenticatedCrmGeneralSetupsItemTypeRoute
  '/crm/general-setups/items': typeof AuthenticatedCrmGeneralSetupsItemsRoute
  '/crm/general-setups/store-menu': typeof AuthenticatedCrmGeneralSetupsStoreMenuRoute
  '/crm/loyalty/extra-point': typeof AuthenticatedCrmLoyaltyExtraPointRoute
  '/crm/loyalty/membership-type': typeof AuthenticatedCrmLoyaltyMembershipTypeRoute
  '/crm/report/customer': typeof AuthenticatedCrmReportCustomerRoute
  '/crm/report/rating-feedback': typeof AuthenticatedCrmReportRatingFeedbackRoute
  '/crm/report/revenue': typeof AuthenticatedCrmReportRevenueRoute
  '/crm/report/sale-manager': typeof AuthenticatedCrmReportSaleManagerRoute
  '/crm/report/voucher': typeof AuthenticatedCrmReportVoucherRoute
  '/devices/detail/$id': typeof AuthenticatedDevicesDetailIdRoute
  '/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRouteWithChildren
  '/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/menu/categories/categories-in-store': typeof AuthenticatedMenuCategoriesCategoriesInStoreRoute
  '/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/report/accounting/sale-detail-audit': typeof AuthenticatedReportAccountingSaleDetailAuditRoute
  '/report/revenue/sale-summary': typeof AuthenticatedReportRevenueSaleSummaryRoute
  '/sale-channel/channel/detail': typeof AuthenticatedSaleChannelChannelDetailRouteWithChildren
  '/employee/role': typeof AuthenticatedEmployeeRoleIndexRoute
  '/menu/item-class': typeof AuthenticatedMenuItemClassIndexRoute
  '/sale-channel/channel': typeof AuthenticatedSaleChannelChannelIndexRoute
  '/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountIndexRoute
  '/sale/discount-payment': typeof AuthenticatedSaleDiscountPaymentIndexRoute
  '/sale/promotion': typeof AuthenticatedSalePromotionIndexRoute
  '/sale/service-charge': typeof AuthenticatedSaleServiceChargeIndexRoute
  '/setting/area': typeof AuthenticatedSettingAreaIndexRoute
  '/setting/bill-model': typeof AuthenticatedSettingBillModelIndexRoute
  '/setting/payment-method': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/setting/source': typeof AuthenticatedSettingSourceIndexRoute
  '/setting/store': typeof AuthenticatedSettingStoreIndexRoute
  '/setting/table-layout': typeof AuthenticatedSettingTableLayoutIndexRoute
  '/setting/table': typeof AuthenticatedSettingTableIndexRoute
  '/employee/role/detail/$roleId': typeof AuthenticatedEmployeeRoleDetailRoleIdRoute
  '/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/menu/category-in-store/detail/$id': typeof AuthenticatedMenuCategoryInStoreDetailIdRoute
  '/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/report/accounting/invoices/sale-sync-vat': typeof AuthenticatedReportAccountingInvoicesSaleSyncVatRoute
  '/report/revenue/categories/payment-method': typeof AuthenticatedReportRevenueCategoriesPaymentMethodRoute
  '/report/revenue/categories/promotion': typeof AuthenticatedReportRevenueCategoriesPromotionRoute
  '/report/revenue/categories/source': typeof AuthenticatedReportRevenueCategoriesSourceRoute
  '/report/revenue/revenue/general': typeof AuthenticatedReportRevenueRevenueGeneralRoute
  '/sale-channel/channel/detail/$uuid': typeof AuthenticatedSaleChannelChannelDetailUuidRoute
  '/sale-channel/discount/detail/$id': typeof AuthenticatedSaleChannelDiscountDetailIdRoute
  '/sale/combo/detail/$id': typeof AuthenticatedSaleComboDetailIdRoute
  '/sale/discount-payment/detail/$id': typeof AuthenticatedSaleDiscountPaymentDetailIdRoute
  '/sale/service-charge/detail/$id': typeof AuthenticatedSaleServiceChargeDetailIdRoute
  '/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/setting/store/detail/$storeId': typeof AuthenticatedSettingStoreDetailStoreIdRoute
  '/setting/table/detail/$areaId': typeof AuthenticatedSettingTableDetailAreaIdRoute
  '/setting/table/detail/$tableId': typeof AuthenticatedSettingTableDetailTableIdRoute
  '/menu/category-in-store/detail': typeof AuthenticatedMenuCategoryInStoreDetailIndexRoute
  '/menu/category/detail': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/menu/customization/customization-in-city': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/menu/customization/customization-in-store': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/menu/items/items-in-city': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/menu/items/items-in-store': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/sale-channel/discount/detail': typeof AuthenticatedSaleChannelDiscountDetailIndexRoute
  '/sale/combo/detail': typeof AuthenticatedSaleComboDetailIndexRoute
  '/sale/discount-payment/detail': typeof AuthenticatedSaleDiscountPaymentDetailIndexRoute
  '/sale/discount/membership': typeof AuthenticatedSaleDiscountMembershipIndexRoute
  '/sale/discount/regular': typeof AuthenticatedSaleDiscountRegularIndexRoute
  '/sale/service-charge/detail': typeof AuthenticatedSaleServiceChargeDetailIndexRoute
  '/setting/area/detail': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/setting/payment-method/detail': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/setting/printer-position/printer-position-in-brand': typeof AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute
  '/setting/printer-position/printer-position-in-store': typeof AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute
  '/setting/source/detail': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/setting/store/detail': typeof AuthenticatedSettingStoreDetailIndexRoute
  '/setting/table/detail': typeof AuthenticatedSettingTableDetailIndexRoute
  '/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/menu/items/items-in-city/detail/$id': typeof AuthenticatedMenuItemsItemsInCityDetailIdRoute
  '/menu/items/items-in-store/detail/$id': typeof AuthenticatedMenuItemsItemsInStoreDetailIdRoute
  '/sale/discount/membership/detail/$id': typeof AuthenticatedSaleDiscountMembershipDetailIdRoute
  '/sale/discount/regular/detail/$id': typeof AuthenticatedSaleDiscountRegularDetailIdRoute
  '/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute
  '/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
  '/menu/items/items-in-city/detail': typeof AuthenticatedMenuItemsItemsInCityDetailIndexRoute
  '/menu/items/items-in-store/detail': typeof AuthenticatedMenuItemsItemsInStoreDetailIndexRoute
  '/sale/discount/membership/detail': typeof AuthenticatedSaleDiscountMembershipDetailIndexRoute
  '/sale/discount/regular/detail': typeof AuthenticatedSaleDiscountRegularDetailIndexRoute
  '/sale/discount/membership/detail/$companyId/$brandId': typeof AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/(auth)': typeof ClerkauthRouteRouteWithChildren
  '/clerk/_authenticated': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/membership': typeof AuthenticatedMembershipRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/crm/billing-detail': typeof AuthenticatedCrmBillingDetailRoute
  '/_authenticated/crm/config-register-page': typeof AuthenticatedCrmConfigRegisterPageRoute
  '/_authenticated/crm/connect-crm': typeof AuthenticatedCrmConnectCrmRoute
  '/_authenticated/crm/customer-group': typeof AuthenticatedCrmCustomerGroupRoute
  '/_authenticated/crm/customer-list': typeof AuthenticatedCrmCustomerListRoute
  '/_authenticated/crm/customer-profile': typeof AuthenticatedCrmCustomerProfileRoute
  '/_authenticated/crm/pricing-table': typeof AuthenticatedCrmPricingTableRoute
  '/_authenticated/crm/settings': typeof AuthenticatedCrmSettingsRoute
  '/_authenticated/crm/system-log': typeof AuthenticatedCrmSystemLogRoute
  '/_authenticated/crm/using-month': typeof AuthenticatedCrmUsingMonthRoute
  '/_authenticated/devices/list': typeof AuthenticatedDevicesListRoute
  '/_authenticated/devices/new': typeof AuthenticatedDevicesNewRoute
  '/_authenticated/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/_authenticated/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/_authenticated/employee/list': typeof AuthenticatedEmployeeListRoute
  '/_authenticated/employee/role': typeof AuthenticatedEmployeeRoleRouteWithChildren
  '/_authenticated/general-setups/account': typeof AuthenticatedGeneralSetupsAccountRoute
  '/_authenticated/general-setups/create-user': typeof AuthenticatedGeneralSetupsCreateUserRoute
  '/_authenticated/menu/item-class': typeof AuthenticatedMenuItemClassRouteWithChildren
  '/_authenticated/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/_authenticated/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/_authenticated/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountRouteWithChildren
  '/_authenticated/sale/combo': typeof AuthenticatedSaleComboRouteWithChildren
  '/_authenticated/sale/discount-payment': typeof AuthenticatedSaleDiscountPaymentRouteWithChildren
  '/_authenticated/sale/service-charge': typeof AuthenticatedSaleServiceChargeRouteWithChildren
  '/_authenticated/setting/area': typeof AuthenticatedSettingAreaRouteWithChildren
  '/_authenticated/setting/store': typeof AuthenticatedSettingStoreRouteWithChildren
  '/_authenticated/setting/table': typeof AuthenticatedSettingTableRouteWithChildren
  '/_authenticated/setting/table-layout': typeof AuthenticatedSettingTableLayoutRouteWithChildren
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/(auth)/sign-in': typeof ClerkauthSignInRoute
  '/clerk/(auth)/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/_authenticated/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
  '/_authenticated/crm/food-item-review/items': typeof AuthenticatedCrmFoodItemReviewItemsRoute
  '/_authenticated/crm/general-setups/account': typeof AuthenticatedCrmGeneralSetupsAccountRoute
  '/_authenticated/crm/general-setups/combo': typeof AuthenticatedCrmGeneralSetupsComboRoute
  '/_authenticated/crm/general-setups/combo-special': typeof AuthenticatedCrmGeneralSetupsComboSpecialRoute
  '/_authenticated/crm/general-setups/create-user': typeof AuthenticatedCrmGeneralSetupsCreateUserRoute
  '/_authenticated/crm/general-setups/item-type': typeof AuthenticatedCrmGeneralSetupsItemTypeRoute
  '/_authenticated/crm/general-setups/items': typeof AuthenticatedCrmGeneralSetupsItemsRoute
  '/_authenticated/crm/general-setups/store-menu': typeof AuthenticatedCrmGeneralSetupsStoreMenuRoute
  '/_authenticated/crm/loyalty/extra-point': typeof AuthenticatedCrmLoyaltyExtraPointRoute
  '/_authenticated/crm/loyalty/membership-type': typeof AuthenticatedCrmLoyaltyMembershipTypeRoute
  '/_authenticated/crm/report/customer': typeof AuthenticatedCrmReportCustomerRoute
  '/_authenticated/crm/report/rating-feedback': typeof AuthenticatedCrmReportRatingFeedbackRoute
  '/_authenticated/crm/report/revenue': typeof AuthenticatedCrmReportRevenueRoute
  '/_authenticated/crm/report/sale-manager': typeof AuthenticatedCrmReportSaleManagerRoute
  '/_authenticated/crm/report/voucher': typeof AuthenticatedCrmReportVoucherRoute
  '/_authenticated/devices/detail/$id': typeof AuthenticatedDevicesDetailIdRoute
  '/_authenticated/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/_authenticated/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRouteWithChildren
  '/_authenticated/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/_authenticated/menu/categories/categories-in-store': typeof AuthenticatedMenuCategoriesCategoriesInStoreRoute
  '/_authenticated/menu/category-in-store/detail': typeof AuthenticatedMenuCategoryInStoreDetailRouteWithChildren
  '/_authenticated/menu/category/detail': typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  '/_authenticated/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/_authenticated/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  '/_authenticated/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/_authenticated/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/_authenticated/report/accounting/sale-detail-audit': typeof AuthenticatedReportAccountingSaleDetailAuditRoute
  '/_authenticated/report/revenue/sale-summary': typeof AuthenticatedReportRevenueSaleSummaryRoute
  '/_authenticated/sale-channel/channel/detail': typeof AuthenticatedSaleChannelChannelDetailRouteWithChildren
  '/_authenticated/sale-channel/discount/detail': typeof AuthenticatedSaleChannelDiscountDetailRouteWithChildren
  '/_authenticated/sale/combo/detail': typeof AuthenticatedSaleComboDetailRouteWithChildren
  '/_authenticated/sale/discount-payment/detail': typeof AuthenticatedSaleDiscountPaymentDetailRouteWithChildren
  '/_authenticated/sale/discount/membership': typeof AuthenticatedSaleDiscountMembershipRouteWithChildren
  '/_authenticated/sale/discount/regular': typeof AuthenticatedSaleDiscountRegularRouteWithChildren
  '/_authenticated/sale/service-charge/detail': typeof AuthenticatedSaleServiceChargeDetailRouteWithChildren
  '/_authenticated/setting/area/detail': typeof AuthenticatedSettingAreaDetailRouteWithChildren
  '/_authenticated/setting/store/detail': typeof AuthenticatedSettingStoreDetailRouteWithChildren
  '/_authenticated/setting/table/detail': typeof AuthenticatedSettingTableDetailRouteWithChildren
  '/_authenticated/employee/role/': typeof AuthenticatedEmployeeRoleIndexRoute
  '/_authenticated/menu/item-class/': typeof AuthenticatedMenuItemClassIndexRoute
  '/_authenticated/sale-channel/channel/': typeof AuthenticatedSaleChannelChannelIndexRoute
  '/_authenticated/sale-channel/discount/': typeof AuthenticatedSaleChannelDiscountIndexRoute
  '/_authenticated/sale/discount-payment/': typeof AuthenticatedSaleDiscountPaymentIndexRoute
  '/_authenticated/sale/promotion/': typeof AuthenticatedSalePromotionIndexRoute
  '/_authenticated/sale/service-charge/': typeof AuthenticatedSaleServiceChargeIndexRoute
  '/_authenticated/setting/area/': typeof AuthenticatedSettingAreaIndexRoute
  '/_authenticated/setting/bill-model/': typeof AuthenticatedSettingBillModelIndexRoute
  '/_authenticated/setting/payment-method/': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/_authenticated/setting/source/': typeof AuthenticatedSettingSourceIndexRoute
  '/_authenticated/setting/store/': typeof AuthenticatedSettingStoreIndexRoute
  '/_authenticated/setting/table-layout/': typeof AuthenticatedSettingTableLayoutIndexRoute
  '/_authenticated/setting/table/': typeof AuthenticatedSettingTableIndexRoute
  '/_authenticated/employee/role/detail/$roleId': typeof AuthenticatedEmployeeRoleDetailRoleIdRoute
  '/_authenticated/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/_authenticated/menu/category-in-store/detail/$id': typeof AuthenticatedMenuCategoryInStoreDetailIdRoute
  '/_authenticated/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/_authenticated/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  '/_authenticated/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/_authenticated/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  '/_authenticated/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/_authenticated/menu/items/items-in-city/detail': typeof AuthenticatedMenuItemsItemsInCityDetailRouteWithChildren
  '/_authenticated/menu/items/items-in-store/detail': typeof AuthenticatedMenuItemsItemsInStoreDetailRouteWithChildren
  '/_authenticated/report/accounting/invoices/sale-sync-vat': typeof AuthenticatedReportAccountingInvoicesSaleSyncVatRoute
  '/_authenticated/report/revenue/categories/payment-method': typeof AuthenticatedReportRevenueCategoriesPaymentMethodRoute
  '/_authenticated/report/revenue/categories/promotion': typeof AuthenticatedReportRevenueCategoriesPromotionRoute
  '/_authenticated/report/revenue/categories/source': typeof AuthenticatedReportRevenueCategoriesSourceRoute
  '/_authenticated/report/revenue/revenue/general': typeof AuthenticatedReportRevenueRevenueGeneralRoute
  '/_authenticated/sale-channel/channel/detail/$uuid': typeof AuthenticatedSaleChannelChannelDetailUuidRoute
  '/_authenticated/sale-channel/discount/detail/$id': typeof AuthenticatedSaleChannelDiscountDetailIdRoute
  '/_authenticated/sale/combo/detail/$id': typeof AuthenticatedSaleComboDetailIdRoute
  '/_authenticated/sale/discount-payment/detail/$id': typeof AuthenticatedSaleDiscountPaymentDetailIdRoute
  '/_authenticated/sale/discount/membership/detail': typeof AuthenticatedSaleDiscountMembershipDetailRouteWithChildren
  '/_authenticated/sale/discount/regular/detail': typeof AuthenticatedSaleDiscountRegularDetailRouteWithChildren
  '/_authenticated/sale/service-charge/detail/$id': typeof AuthenticatedSaleServiceChargeDetailIdRoute
  '/_authenticated/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/_authenticated/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/_authenticated/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/_authenticated/setting/store/detail/$storeId': typeof AuthenticatedSettingStoreDetailStoreIdRoute
  '/_authenticated/setting/table/detail/$areaId': typeof AuthenticatedSettingTableDetailAreaIdRoute
  '/_authenticated/setting/table/detail/$tableId': typeof AuthenticatedSettingTableDetailTableIdRoute
  '/_authenticated/menu/category-in-store/detail/': typeof AuthenticatedMenuCategoryInStoreDetailIndexRoute
  '/_authenticated/menu/category/detail/': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/_authenticated/menu/customization/customization-in-city/': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/_authenticated/menu/customization/customization-in-store/': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/_authenticated/menu/item-class/detail/': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/_authenticated/menu/items/items-in-city/': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/_authenticated/menu/items/items-in-store/': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/_authenticated/sale-channel/discount/detail/': typeof AuthenticatedSaleChannelDiscountDetailIndexRoute
  '/_authenticated/sale/combo/detail/': typeof AuthenticatedSaleComboDetailIndexRoute
  '/_authenticated/sale/discount-payment/detail/': typeof AuthenticatedSaleDiscountPaymentDetailIndexRoute
  '/_authenticated/sale/discount/membership/': typeof AuthenticatedSaleDiscountMembershipIndexRoute
  '/_authenticated/sale/discount/regular/': typeof AuthenticatedSaleDiscountRegularIndexRoute
  '/_authenticated/sale/service-charge/detail/': typeof AuthenticatedSaleServiceChargeDetailIndexRoute
  '/_authenticated/setting/area/detail/': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/_authenticated/setting/payment-method/detail/': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/_authenticated/setting/printer-position/printer-position-in-brand/': typeof AuthenticatedSettingPrinterPositionPrinterPositionInBrandIndexRoute
  '/_authenticated/setting/printer-position/printer-position-in-store/': typeof AuthenticatedSettingPrinterPositionPrinterPositionInStoreIndexRoute
  '/_authenticated/setting/source/detail/': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/_authenticated/setting/store/detail/': typeof AuthenticatedSettingStoreDetailIndexRoute
  '/_authenticated/setting/table/detail/': typeof AuthenticatedSettingTableDetailIndexRoute
  '/_authenticated/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/_authenticated/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/_authenticated/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/_authenticated/menu/items/items-in-city/detail/$id': typeof AuthenticatedMenuItemsItemsInCityDetailIdRoute
  '/_authenticated/menu/items/items-in-store/detail/$id': typeof AuthenticatedMenuItemsItemsInStoreDetailIdRoute
  '/_authenticated/sale/discount/membership/detail/$id': typeof AuthenticatedSaleDiscountMembershipDetailIdRoute
  '/_authenticated/sale/discount/regular/detail/$id': typeof AuthenticatedSaleDiscountRegularDetailIdRoute
  '/_authenticated/menu/customization/customization-in-city/detail/': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailIndexRoute
  '/_authenticated/menu/customization/customization-in-store/detail/': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
  '/_authenticated/menu/items/items-in-city/detail/': typeof AuthenticatedMenuItemsItemsInCityDetailIndexRoute
  '/_authenticated/menu/items/items-in-store/detail/': typeof AuthenticatedMenuItemsItemsInStoreDetailIndexRoute
  '/_authenticated/sale/discount/membership/detail/': typeof AuthenticatedSaleDiscountMembershipDetailIndexRoute
  '/_authenticated/sale/discount/regular/detail/': typeof AuthenticatedSaleDiscountRegularDetailIndexRoute
  '/_authenticated/sale/discount/membership/detail/$companyId/$brandId': typeof AuthenticatedSaleDiscountMembershipDetailCompanyIdBrandIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/clerk'
    | '/settings'
    | '/clerk/'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/membership'
    | '/'
    | '/crm/billing-detail'
    | '/crm/config-register-page'
    | '/crm/connect-crm'
    | '/crm/customer-group'
    | '/crm/customer-list'
    | '/crm/customer-profile'
    | '/crm/pricing-table'
    | '/crm/settings'
    | '/crm/system-log'
    | '/crm/using-month'
    | '/devices/list'
    | '/devices/new'
    | '/devices/types'
    | '/employee/detail'
    | '/employee/list'
    | '/employee/role'
    | '/general-setups/account'
    | '/general-setups/create-user'
    | '/menu/item-class'
    | '/menu/quantity-day'
    | '/menu/schedule'
    | '/sale-channel/discount'
    | '/sale/combo'
    | '/sale/discount-payment'
    | '/sale/service-charge'
    | '/setting/area'
    | '/setting/store'
    | '/setting/table'
    | '/setting/table-layout'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/help-center'
    | '/settings/'
    | '/users'
    | '/crm/food-item-review/items'
    | '/crm/general-setups/account'
    | '/crm/general-setups/combo'
    | '/crm/general-setups/combo-special'
    | '/crm/general-setups/create-user'
    | '/crm/general-setups/item-type'
    | '/crm/general-setups/items'
    | '/crm/general-setups/store-menu'
    | '/crm/loyalty/extra-point'
    | '/crm/loyalty/membership-type'
    | '/crm/report/customer'
    | '/crm/report/rating-feedback'
    | '/crm/report/revenue'
    | '/crm/report/sale-manager'
    | '/crm/report/voucher'
    | '/devices/detail/$id'
    | '/employee/detail/$userId'
    | '/employee/role/detail'
    | '/menu/categories/categories-in-brand'
    | '/menu/categories/categories-in-store'
    | '/menu/category-in-store/detail'
    | '/menu/category/detail'
    | '/menu/customization/new'
    | '/menu/item-class/detail'
    | '/menu/item-removed/item-removed-in-city'
    | '/menu/item-removed/item-removed-in-store'
    | '/report/accounting/sale-detail-audit'
    | '/report/revenue/sale-summary'
    | '/sale-channel/channel/detail'
    | '/sale-channel/discount/detail'
    | '/sale/combo/detail'
    | '/sale/discount-payment/detail'
    | '/sale/discount/membership'
    | '/sale/discount/regular'
    | '/sale/service-charge/detail'
    | '/setting/area/detail'
    | '/setting/store/detail'
    | '/setting/table/detail'
    | '/employee/role/'
    | '/menu/item-class/'
    | '/sale-channel/channel'
    | '/sale-channel/discount/'
    | '/sale/discount-payment/'
    | '/sale/promotion'
    | '/sale/service-charge/'
    | '/setting/area/'
    | '/setting/bill-model'
    | '/setting/payment-method'
    | '/setting/source'
    | '/setting/store/'
    | '/setting/table-layout/'
    | '/setting/table/'
    | '/employee/role/detail/$roleId'
    | '/menu/categories/categories-in-brand/detail'
    | '/menu/category-in-store/detail/$id'
    | '/menu/category/detail/$id'
    | '/menu/customization/customization-in-city/detail'
    | '/menu/customization/customization-in-store/create'
    | '/menu/customization/customization-in-store/detail'
    | '/menu/item-class/detail/$id'
    | '/menu/items/items-in-city/detail'
    | '/menu/items/items-in-store/detail'
    | '/report/accounting/invoices/sale-sync-vat'
    | '/report/revenue/categories/payment-method'
    | '/report/revenue/categories/promotion'
    | '/report/revenue/categories/source'
    | '/report/revenue/revenue/general'
    | '/sale-channel/channel/detail/$uuid'
    | '/sale-channel/discount/detail/$id'
    | '/sale/combo/detail/$id'
    | '/sale/discount-payment/detail/$id'
    | '/sale/discount/membership/detail'
    | '/sale/discount/regular/detail'
    | '/sale/service-charge/detail/$id'
    | '/setting/area/detail/$areaId'
    | '/setting/payment-method/detail/$paymentMethodId'
    | '/setting/source/detail/$sourceId'
    | '/setting/store/detail/$storeId'
    | '/setting/table/detail/$areaId'
    | '/setting/table/detail/$tableId'
    | '/menu/category-in-store/detail/'
    | '/menu/category/detail/'
    | '/menu/customization/customization-in-city'
    | '/menu/customization/customization-in-store'
    | '/menu/item-class/detail/'
    | '/menu/items/items-in-city'
    | '/menu/items/items-in-store'
    | '/sale-channel/discount/detail/'
    | '/sale/combo/detail/'
    | '/sale/discount-payment/detail/'
    | '/sale/discount/membership/'
    | '/sale/discount/regular/'
    | '/sale/service-charge/detail/'
    | '/setting/area/detail/'
    | '/setting/payment-method/detail'
    | '/setting/printer-position/printer-position-in-brand'
    | '/setting/printer-position/printer-position-in-store'
    | '/setting/source/detail'
    | '/setting/store/detail/'
    | '/setting/table/detail/'
    | '/menu/categories/categories-in-brand/detail/$id'
    | '/menu/customization/customization-in-city/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail/$customizationId'
    | '/menu/items/items-in-city/detail/$id'
    | '/menu/items/items-in-store/detail/$id'
    | '/sale/discount/membership/detail/$id'
    | '/sale/discount/regular/detail/$id'
    | '/menu/customization/customization-in-city/detail/'
    | '/menu/customization/customization-in-store/detail/'
    | '/menu/items/items-in-city/detail/'
    | '/menu/items/items-in-store/detail/'
    | '/sale/discount/membership/detail/'
    | '/sale/discount/regular/detail/'
    | '/sale/discount/membership/detail/$companyId/$brandId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/clerk'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/membership'
    | '/'
    | '/crm/billing-detail'
    | '/crm/config-register-page'
    | '/crm/connect-crm'
    | '/crm/customer-group'
    | '/crm/customer-list'
    | '/crm/customer-profile'
    | '/crm/pricing-table'
    | '/crm/settings'
    | '/crm/system-log'
    | '/crm/using-month'
    | '/devices/list'
    | '/devices/new'
    | '/devices/types'
    | '/employee/detail'
    | '/employee/list'
    | '/general-setups/account'
    | '/general-setups/create-user'
    | '/menu/quantity-day'
    | '/menu/schedule'
    | '/sale/combo'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/help-center'
    | '/settings'
    | '/users'
    | '/crm/food-item-review/items'
    | '/crm/general-setups/account'
    | '/crm/general-setups/combo'
    | '/crm/general-setups/combo-special'
    | '/crm/general-setups/create-user'
    | '/crm/general-setups/item-type'
    | '/crm/general-setups/items'
    | '/crm/general-setups/store-menu'
    | '/crm/loyalty/extra-point'
    | '/crm/loyalty/membership-type'
    | '/crm/report/customer'
    | '/crm/report/rating-feedback'
    | '/crm/report/revenue'
    | '/crm/report/sale-manager'
    | '/crm/report/voucher'
    | '/devices/detail/$id'
    | '/employee/detail/$userId'
    | '/employee/role/detail'
    | '/menu/categories/categories-in-brand'
    | '/menu/categories/categories-in-store'
    | '/menu/customization/new'
    | '/menu/item-removed/item-removed-in-city'
    | '/menu/item-removed/item-removed-in-store'
    | '/report/accounting/sale-detail-audit'
    | '/report/revenue/sale-summary'
    | '/sale-channel/channel/detail'
    | '/employee/role'
    | '/menu/item-class'
    | '/sale-channel/channel'
    | '/sale-channel/discount'
    | '/sale/discount-payment'
    | '/sale/promotion'
    | '/sale/service-charge'
    | '/setting/area'
    | '/setting/bill-model'
    | '/setting/payment-method'
    | '/setting/source'
    | '/setting/store'
    | '/setting/table-layout'
    | '/setting/table'
    | '/employee/role/detail/$roleId'
    | '/menu/categories/categories-in-brand/detail'
    | '/menu/category-in-store/detail/$id'
    | '/menu/category/detail/$id'
    | '/menu/customization/customization-in-store/create'
    | '/menu/item-class/detail/$id'
    | '/report/accounting/invoices/sale-sync-vat'
    | '/report/revenue/categories/payment-method'
    | '/report/revenue/categories/promotion'
    | '/report/revenue/categories/source'
    | '/report/revenue/revenue/general'
    | '/sale-channel/channel/detail/$uuid'
    | '/sale-channel/discount/detail/$id'
    | '/sale/combo/detail/$id'
    | '/sale/discount-payment/detail/$id'
    | '/sale/service-charge/detail/$id'
    | '/setting/area/detail/$areaId'
    | '/setting/payment-method/detail/$paymentMethodId'
    | '/setting/source/detail/$sourceId'
    | '/setting/store/detail/$storeId'
    | '/setting/table/detail/$areaId'
    | '/setting/table/detail/$tableId'
    | '/menu/category-in-store/detail'
    | '/menu/category/detail'
    | '/menu/customization/customization-in-city'
    | '/menu/customization/customization-in-store'
    | '/menu/item-class/detail'
    | '/menu/items/items-in-city'
    | '/menu/items/items-in-store'
    | '/sale-channel/discount/detail'
    | '/sale/combo/detail'
    | '/sale/discount-payment/detail'
    | '/sale/discount/membership'
    | '/sale/discount/regular'
    | '/sale/service-charge/detail'
    | '/setting/area/detail'
    | '/setting/payment-method/detail'
    | '/setting/printer-position/printer-position-in-brand'
    | '/setting/printer-position/printer-position-in-store'
    | '/setting/source/detail'
    | '/setting/store/detail'
    | '/setting/table/detail'
    | '/menu/categories/categories-in-brand/detail/$id'
    | '/menu/customization/customization-in-city/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail/$customizationId'
    | '/menu/items/items-in-city/detail/$id'
    | '/menu/items/items-in-store/detail/$id'
    | '/sale/discount/membership/detail/$id'
    | '/sale/discount/regular/detail/$id'
    | '/menu/customization/customization-in-city/detail'
    | '/menu/customization/customization-in-store/detail'
    | '/menu/items/items-in-city/detail'
    | '/menu/items/items-in-store/detail'
    | '/sale/discount/membership/detail'
    | '/sale/discount/regular/detail'
    | '/sale/discount/membership/detail/$companyId/$brandId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/clerk'
    | '/_authenticated/settings'
    | '/clerk/(auth)'
    | '/clerk/_authenticated'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/membership'
    | '/_authenticated/'
    | '/_authenticated/crm/billing-detail'
    | '/_authenticated/crm/config-register-page'
    | '/_authenticated/crm/connect-crm'
    | '/_authenticated/crm/customer-group'
    | '/_authenticated/crm/customer-list'
    | '/_authenticated/crm/customer-profile'
    | '/_authenticated/crm/pricing-table'
    | '/_authenticated/crm/settings'
    | '/_authenticated/crm/system-log'
    | '/_authenticated/crm/using-month'
    | '/_authenticated/devices/list'
    | '/_authenticated/devices/new'
    | '/_authenticated/devices/types'
    | '/_authenticated/employee/detail'
    | '/_authenticated/employee/list'
    | '/_authenticated/employee/role'
    | '/_authenticated/general-setups/account'
    | '/_authenticated/general-setups/create-user'
    | '/_authenticated/menu/item-class'
    | '/_authenticated/menu/quantity-day'
    | '/_authenticated/menu/schedule'
    | '/_authenticated/sale-channel/discount'
    | '/_authenticated/sale/combo'
    | '/_authenticated/sale/discount-payment'
    | '/_authenticated/sale/service-charge'
    | '/_authenticated/setting/area'
    | '/_authenticated/setting/store'
    | '/_authenticated/setting/table'
    | '/_authenticated/setting/table-layout'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/clerk/(auth)/sign-in'
    | '/clerk/(auth)/sign-up'
    | '/clerk/_authenticated/user-management'
    | '/_authenticated/help-center/'
    | '/_authenticated/settings/'
    | '/_authenticated/users/'
    | '/_authenticated/crm/food-item-review/items'
    | '/_authenticated/crm/general-setups/account'
    | '/_authenticated/crm/general-setups/combo'
    | '/_authenticated/crm/general-setups/combo-special'
    | '/_authenticated/crm/general-setups/create-user'
    | '/_authenticated/crm/general-setups/item-type'
    | '/_authenticated/crm/general-setups/items'
    | '/_authenticated/crm/general-setups/store-menu'
    | '/_authenticated/crm/loyalty/extra-point'
    | '/_authenticated/crm/loyalty/membership-type'
    | '/_authenticated/crm/report/customer'
    | '/_authenticated/crm/report/rating-feedback'
    | '/_authenticated/crm/report/revenue'
    | '/_authenticated/crm/report/sale-manager'
    | '/_authenticated/crm/report/voucher'
    | '/_authenticated/devices/detail/$id'
    | '/_authenticated/employee/detail/$userId'
    | '/_authenticated/employee/role/detail'
    | '/_authenticated/menu/categories/categories-in-brand'
    | '/_authenticated/menu/categories/categories-in-store'
    | '/_authenticated/menu/category-in-store/detail'
    | '/_authenticated/menu/category/detail'
    | '/_authenticated/menu/customization/new'
    | '/_authenticated/menu/item-class/detail'
    | '/_authenticated/menu/item-removed/item-removed-in-city'
    | '/_authenticated/menu/item-removed/item-removed-in-store'
    | '/_authenticated/report/accounting/sale-detail-audit'
    | '/_authenticated/report/revenue/sale-summary'
    | '/_authenticated/sale-channel/channel/detail'
    | '/_authenticated/sale-channel/discount/detail'
    | '/_authenticated/sale/combo/detail'
    | '/_authenticated/sale/discount-payment/detail'
    | '/_authenticated/sale/discount/membership'
    | '/_authenticated/sale/discount/regular'
    | '/_authenticated/sale/service-charge/detail'
    | '/_authenticated/setting/area/detail'
    | '/_authenticated/setting/store/detail'
    | '/_authenticated/setting/table/detail'
    | '/_authenticated/employee/role/'
    | '/_authenticated/menu/item-class/'
    | '/_authenticated/sale-channel/channel/'
    | '/_authenticated/sale-channel/discount/'
    | '/_authenticated/sale/discount-payment/'
    | '/_authenticated/sale/promotion/'
    | '/_authenticated/sale/service-charge/'
    | '/_authenticated/setting/area/'
    | '/_authenticated/setting/bill-model/'
    | '/_authenticated/setting/payment-method/'
    | '/_authenticated/setting/source/'
    | '/_authenticated/setting/store/'
    | '/_authenticated/setting/table-layout/'
    | '/_authenticated/setting/table/'
    | '/_authenticated/employee/role/detail/$roleId'
    | '/_authenticated/menu/categories/categories-in-brand/detail'
    | '/_authenticated/menu/category-in-store/detail/$id'
    | '/_authenticated/menu/category/detail/$id'
    | '/_authenticated/menu/customization/customization-in-city/detail'
    | '/_authenticated/menu/customization/customization-in-store/create'
    | '/_authenticated/menu/customization/customization-in-store/detail'
    | '/_authenticated/menu/item-class/detail/$id'
    | '/_authenticated/menu/items/items-in-city/detail'
    | '/_authenticated/menu/items/items-in-store/detail'
    | '/_authenticated/report/accounting/invoices/sale-sync-vat'
    | '/_authenticated/report/revenue/categories/payment-method'
    | '/_authenticated/report/revenue/categories/promotion'
    | '/_authenticated/report/revenue/categories/source'
    | '/_authenticated/report/revenue/revenue/general'
    | '/_authenticated/sale-channel/channel/detail/$uuid'
    | '/_authenticated/sale-channel/discount/detail/$id'
    | '/_authenticated/sale/combo/detail/$id'
    | '/_authenticated/sale/discount-payment/detail/$id'
    | '/_authenticated/sale/discount/membership/detail'
    | '/_authenticated/sale/discount/regular/detail'
    | '/_authenticated/sale/service-charge/detail/$id'
    | '/_authenticated/setting/area/detail/$areaId'
    | '/_authenticated/setting/payment-method/detail/$paymentMethodId'
    | '/_authenticated/setting/source/detail/$sourceId'
    | '/_authenticated/setting/store/detail/$storeId'
    | '/_authenticated/setting/table/detail/$areaId'
    | '/_authenticated/setting/table/detail/$tableId'
    | '/_authenticated/menu/category-in-store/detail/'
    | '/_authenticated/menu/category/detail/'
    | '/_authenticated/menu/customization/customization-in-city/'
    | '/_authenticated/menu/customization/customization-in-store/'
    | '/_authenticated/menu/item-class/detail/'
    | '/_authenticated/menu/items/items-in-city/'
    | '/_authenticated/menu/items/items-in-store/'
    | '/_authenticated/sale-channel/discount/detail/'
    | '/_authenticated/sale/combo/detail/'
    | '/_authenticated/sale/discount-payment/detail/'
    | '/_authenticated/sale/discount/membership/'
    | '/_authenticated/sale/discount/regular/'
    | '/_authenticated/sale/service-charge/detail/'
    | '/_authenticated/setting/area/detail/'
    | '/_authenticated/setting/payment-method/detail/'
    | '/_authenticated/setting/printer-position/printer-position-in-brand/'
    | '/_authenticated/setting/printer-position/printer-position-in-store/'
    | '/_authenticated/setting/source/detail/'
    | '/_authenticated/setting/store/detail/'
    | '/_authenticated/setting/table/detail/'
    | '/_authenticated/menu/categories/categories-in-brand/detail/$id'
    | '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
    | '/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
    | '/_authenticated/menu/items/items-in-city/detail/$id'
    | '/_authenticated/menu/items/items-in-store/detail/$id'
    | '/_authenticated/sale/discount/membership/detail/$id'
    | '/_authenticated/sale/discount/regular/detail/$id'
    | '/_authenticated/menu/customization/customization-in-city/detail/'
    | '/_authenticated/menu/customization/customization-in-store/detail/'
    | '/_authenticated/menu/items/items-in-city/detail/'
    | '/_authenticated/menu/items/items-in-store/detail/'
    | '/_authenticated/sale/discount/membership/detail/'
    | '/_authenticated/sale/discount/regular/detail/'
    | '/_authenticated/sale/discount/membership/detail/$companyId/$brandId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  ClerkRouteRoute: typeof ClerkRouteRouteWithChildren
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  ClerkRouteRoute: ClerkRouteRouteWithChildren,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/clerk",
        "/(auth)/forgot-password",
        "/(auth)/otp",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(auth)/sign-up",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/settings",
        "/_authenticated/membership",
        "/_authenticated/",
        "/_authenticated/crm/billing-detail",
        "/_authenticated/crm/config-register-page",
        "/_authenticated/crm/connect-crm",
        "/_authenticated/crm/customer-group",
        "/_authenticated/crm/customer-list",
        "/_authenticated/crm/customer-profile",
        "/_authenticated/crm/pricing-table",
        "/_authenticated/crm/settings",
        "/_authenticated/crm/system-log",
        "/_authenticated/crm/using-month",
        "/_authenticated/devices/list",
        "/_authenticated/devices/new",
        "/_authenticated/devices/types",
        "/_authenticated/employee/detail",
        "/_authenticated/employee/list",
        "/_authenticated/employee/role",
        "/_authenticated/general-setups/account",
        "/_authenticated/general-setups/create-user",
        "/_authenticated/menu/item-class",
        "/_authenticated/menu/quantity-day",
        "/_authenticated/menu/schedule",
        "/_authenticated/sale-channel/discount",
        "/_authenticated/sale/combo",
        "/_authenticated/sale/discount-payment",
        "/_authenticated/sale/service-charge",
        "/_authenticated/setting/area",
        "/_authenticated/setting/store",
        "/_authenticated/setting/table",
        "/_authenticated/setting/table-layout",
        "/_authenticated/help-center/",
        "/_authenticated/users/",
        "/_authenticated/crm/food-item-review/items",
        "/_authenticated/crm/general-setups/account",
        "/_authenticated/crm/general-setups/combo",
        "/_authenticated/crm/general-setups/combo-special",
        "/_authenticated/crm/general-setups/create-user",
        "/_authenticated/crm/general-setups/item-type",
        "/_authenticated/crm/general-setups/items",
        "/_authenticated/crm/general-setups/store-menu",
        "/_authenticated/crm/loyalty/extra-point",
        "/_authenticated/crm/loyalty/membership-type",
        "/_authenticated/crm/report/customer",
        "/_authenticated/crm/report/rating-feedback",
        "/_authenticated/crm/report/revenue",
        "/_authenticated/crm/report/sale-manager",
        "/_authenticated/crm/report/voucher",
        "/_authenticated/devices/detail/$id",
        "/_authenticated/menu/categories/categories-in-brand",
        "/_authenticated/menu/categories/categories-in-store",
        "/_authenticated/menu/category-in-store/detail",
        "/_authenticated/menu/category/detail",
        "/_authenticated/menu/customization/new",
        "/_authenticated/menu/item-removed/item-removed-in-city",
        "/_authenticated/menu/item-removed/item-removed-in-store",
        "/_authenticated/report/accounting/sale-detail-audit",
        "/_authenticated/report/revenue/sale-summary",
        "/_authenticated/sale-channel/channel/detail",
        "/_authenticated/sale/discount/membership",
        "/_authenticated/sale/discount/regular",
        "/_authenticated/sale-channel/channel/",
        "/_authenticated/sale/promotion/",
        "/_authenticated/setting/bill-model/",
        "/_authenticated/setting/payment-method/",
        "/_authenticated/setting/source/",
        "/_authenticated/menu/customization/customization-in-city/detail",
        "/_authenticated/menu/customization/customization-in-store/create",
        "/_authenticated/menu/customization/customization-in-store/detail",
        "/_authenticated/menu/items/items-in-city/detail",
        "/_authenticated/menu/items/items-in-store/detail",
        "/_authenticated/report/accounting/invoices/sale-sync-vat",
        "/_authenticated/report/revenue/categories/payment-method",
        "/_authenticated/report/revenue/categories/promotion",
        "/_authenticated/report/revenue/categories/source",
        "/_authenticated/report/revenue/revenue/general",
        "/_authenticated/setting/payment-method/detail/$paymentMethodId",
        "/_authenticated/setting/source/detail/$sourceId",
        "/_authenticated/menu/customization/customization-in-city/",
        "/_authenticated/menu/customization/customization-in-store/",
        "/_authenticated/menu/items/items-in-city/",
        "/_authenticated/menu/items/items-in-store/",
        "/_authenticated/setting/payment-method/detail/",
        "/_authenticated/setting/printer-position/printer-position-in-brand/",
        "/_authenticated/setting/printer-position/printer-position-in-store/",
        "/_authenticated/setting/source/detail/"
      ]
    },
    "/clerk": {
      "filePath": "clerk/route.tsx",
      "children": [
        "/clerk/(auth)",
        "/clerk/_authenticated"
      ]
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/"
      ]
    },
    "/clerk/(auth)": {
      "filePath": "clerk/(auth)/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/(auth)/sign-in",
        "/clerk/(auth)/sign-up"
      ]
    },
    "/clerk/_authenticated": {
      "filePath": "clerk/_authenticated/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/_authenticated/user-management"
      ]
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(auth)/sign-up": {
      "filePath": "(auth)/sign-up.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/membership": {
      "filePath": "_authenticated/membership.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/billing-detail": {
      "filePath": "_authenticated/crm/billing-detail.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/config-register-page": {
      "filePath": "_authenticated/crm/config-register-page.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/connect-crm": {
      "filePath": "_authenticated/crm/connect-crm.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/customer-group": {
      "filePath": "_authenticated/crm/customer-group.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/customer-list": {
      "filePath": "_authenticated/crm/customer-list.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/customer-profile": {
      "filePath": "_authenticated/crm/customer-profile.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/pricing-table": {
      "filePath": "_authenticated/crm/pricing-table.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/settings": {
      "filePath": "_authenticated/crm/settings.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/system-log": {
      "filePath": "_authenticated/crm/system-log.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/using-month": {
      "filePath": "_authenticated/crm/using-month.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/list": {
      "filePath": "_authenticated/devices/list.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/new": {
      "filePath": "_authenticated/devices/new.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/types": {
      "filePath": "_authenticated/devices/types.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/detail": {
      "filePath": "_authenticated/employee/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/employee/detail/$userId"
      ]
    },
    "/_authenticated/employee/list": {
      "filePath": "_authenticated/employee/list.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/role": {
      "filePath": "_authenticated/employee/role.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/employee/role/detail",
        "/_authenticated/employee/role/"
      ]
    },
    "/_authenticated/general-setups/account": {
      "filePath": "_authenticated/general-setups/account.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/general-setups/create-user": {
      "filePath": "_authenticated/general-setups/create-user.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-class": {
      "filePath": "_authenticated/menu/item-class.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/item-class/detail",
        "/_authenticated/menu/item-class/"
      ]
    },
    "/_authenticated/menu/quantity-day": {
      "filePath": "_authenticated/menu/quantity-day.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/schedule": {
      "filePath": "_authenticated/menu/schedule.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/discount": {
      "filePath": "_authenticated/sale-channel/discount.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale-channel/discount/detail",
        "/_authenticated/sale-channel/discount/"
      ]
    },
    "/_authenticated/sale/combo": {
      "filePath": "_authenticated/sale/combo.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale/combo/detail"
      ]
    },
    "/_authenticated/sale/discount-payment": {
      "filePath": "_authenticated/sale/discount-payment.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale/discount-payment/detail",
        "/_authenticated/sale/discount-payment/"
      ]
    },
    "/_authenticated/sale/service-charge": {
      "filePath": "_authenticated/sale/service-charge.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale/service-charge/detail",
        "/_authenticated/sale/service-charge/"
      ]
    },
    "/_authenticated/setting/area": {
      "filePath": "_authenticated/setting/area.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/setting/area/detail",
        "/_authenticated/setting/area/"
      ]
    },
    "/_authenticated/setting/store": {
      "filePath": "_authenticated/setting/store.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/setting/store/detail",
        "/_authenticated/setting/store/"
      ]
    },
    "/_authenticated/setting/table": {
      "filePath": "_authenticated/setting/table.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/setting/table/detail",
        "/_authenticated/setting/table/"
      ]
    },
    "/_authenticated/setting/table-layout": {
      "filePath": "_authenticated/setting/table-layout.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/setting/table-layout/"
      ]
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/clerk/(auth)/sign-in": {
      "filePath": "clerk/(auth)/sign-in.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/(auth)/sign-up": {
      "filePath": "clerk/(auth)/sign-up.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/_authenticated/user-management": {
      "filePath": "clerk/_authenticated/user-management.tsx",
      "parent": "/clerk/_authenticated"
    },
    "/_authenticated/help-center/": {
      "filePath": "_authenticated/help-center/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/users/": {
      "filePath": "_authenticated/users/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/food-item-review/items": {
      "filePath": "_authenticated/crm/food-item-review/items.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/account": {
      "filePath": "_authenticated/crm/general-setups/account.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/combo": {
      "filePath": "_authenticated/crm/general-setups/combo.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/combo-special": {
      "filePath": "_authenticated/crm/general-setups/combo-special.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/create-user": {
      "filePath": "_authenticated/crm/general-setups/create-user.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/item-type": {
      "filePath": "_authenticated/crm/general-setups/item-type.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/items": {
      "filePath": "_authenticated/crm/general-setups/items.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/general-setups/store-menu": {
      "filePath": "_authenticated/crm/general-setups/store-menu.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/loyalty/extra-point": {
      "filePath": "_authenticated/crm/loyalty/extra-point.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/loyalty/membership-type": {
      "filePath": "_authenticated/crm/loyalty/membership-type.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/report/customer": {
      "filePath": "_authenticated/crm/report/customer.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/report/rating-feedback": {
      "filePath": "_authenticated/crm/report/rating-feedback.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/report/revenue": {
      "filePath": "_authenticated/crm/report/revenue.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/report/sale-manager": {
      "filePath": "_authenticated/crm/report/sale-manager.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm/report/voucher": {
      "filePath": "_authenticated/crm/report/voucher.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/detail/$id": {
      "filePath": "_authenticated/devices/detail.$id.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/detail/$userId": {
      "filePath": "_authenticated/employee/detail.$userId.tsx",
      "parent": "/_authenticated/employee/detail"
    },
    "/_authenticated/employee/role/detail": {
      "filePath": "_authenticated/employee/role/detail.tsx",
      "parent": "/_authenticated/employee/role",
      "children": [
        "/_authenticated/employee/role/detail/$roleId"
      ]
    },
    "/_authenticated/menu/categories/categories-in-brand": {
      "filePath": "_authenticated/menu/categories/categories-in-brand.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/categories/categories-in-brand/detail"
      ]
    },
    "/_authenticated/menu/categories/categories-in-store": {
      "filePath": "_authenticated/menu/categories/categories-in-store.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/category-in-store/detail": {
      "filePath": "_authenticated/menu/category-in-store/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/category-in-store/detail/$id",
        "/_authenticated/menu/category-in-store/detail/"
      ]
    },
    "/_authenticated/menu/category/detail": {
      "filePath": "_authenticated/menu/category/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/category/detail/$id",
        "/_authenticated/menu/category/detail/"
      ]
    },
    "/_authenticated/menu/customization/new": {
      "filePath": "_authenticated/menu/customization/new.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-class/detail": {
      "filePath": "_authenticated/menu/item-class/detail.tsx",
      "parent": "/_authenticated/menu/item-class",
      "children": [
        "/_authenticated/menu/item-class/detail/$id",
        "/_authenticated/menu/item-class/detail/"
      ]
    },
    "/_authenticated/menu/item-removed/item-removed-in-city": {
      "filePath": "_authenticated/menu/item-removed/item-removed-in-city.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-removed/item-removed-in-store": {
      "filePath": "_authenticated/menu/item-removed/item-removed-in-store.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/accounting/sale-detail-audit": {
      "filePath": "_authenticated/report/accounting/sale-detail-audit.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/revenue/sale-summary": {
      "filePath": "_authenticated/report/revenue/sale-summary.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/channel/detail": {
      "filePath": "_authenticated/sale-channel/channel/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale-channel/channel/detail/$uuid"
      ]
    },
    "/_authenticated/sale-channel/discount/detail": {
      "filePath": "_authenticated/sale-channel/discount/detail.tsx",
      "parent": "/_authenticated/sale-channel/discount",
      "children": [
        "/_authenticated/sale-channel/discount/detail/$id",
        "/_authenticated/sale-channel/discount/detail/"
      ]
    },
    "/_authenticated/sale/combo/detail": {
      "filePath": "_authenticated/sale/combo/detail.tsx",
      "parent": "/_authenticated/sale/combo",
      "children": [
        "/_authenticated/sale/combo/detail/$id",
        "/_authenticated/sale/combo/detail/"
      ]
    },
    "/_authenticated/sale/discount-payment/detail": {
      "filePath": "_authenticated/sale/discount-payment/detail.tsx",
      "parent": "/_authenticated/sale/discount-payment",
      "children": [
        "/_authenticated/sale/discount-payment/detail/$id",
        "/_authenticated/sale/discount-payment/detail/"
      ]
    },
    "/_authenticated/sale/discount/membership": {
      "filePath": "_authenticated/sale/discount/membership.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale/discount/membership/detail",
        "/_authenticated/sale/discount/membership/"
      ]
    },
    "/_authenticated/sale/discount/regular": {
      "filePath": "_authenticated/sale/discount/regular.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sale/discount/regular/detail",
        "/_authenticated/sale/discount/regular/"
      ]
    },
    "/_authenticated/sale/service-charge/detail": {
      "filePath": "_authenticated/sale/service-charge/detail.tsx",
      "parent": "/_authenticated/sale/service-charge",
      "children": [
        "/_authenticated/sale/service-charge/detail/$id",
        "/_authenticated/sale/service-charge/detail/"
      ]
    },
    "/_authenticated/setting/area/detail": {
      "filePath": "_authenticated/setting/area/detail.tsx",
      "parent": "/_authenticated/setting/area",
      "children": [
        "/_authenticated/setting/area/detail/$areaId",
        "/_authenticated/setting/area/detail/"
      ]
    },
    "/_authenticated/setting/store/detail": {
      "filePath": "_authenticated/setting/store/detail.tsx",
      "parent": "/_authenticated/setting/store",
      "children": [
        "/_authenticated/setting/store/detail/$storeId",
        "/_authenticated/setting/store/detail/"
      ]
    },
    "/_authenticated/setting/table/detail": {
      "filePath": "_authenticated/setting/table/detail.tsx",
      "parent": "/_authenticated/setting/table",
      "children": [
        "/_authenticated/setting/table/detail/$areaId",
        "/_authenticated/setting/table/detail/$tableId",
        "/_authenticated/setting/table/detail/"
      ]
    },
    "/_authenticated/employee/role/": {
      "filePath": "_authenticated/employee/role/index.tsx",
      "parent": "/_authenticated/employee/role"
    },
    "/_authenticated/menu/item-class/": {
      "filePath": "_authenticated/menu/item-class/index.tsx",
      "parent": "/_authenticated/menu/item-class"
    },
    "/_authenticated/sale-channel/channel/": {
      "filePath": "_authenticated/sale-channel/channel/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/discount/": {
      "filePath": "_authenticated/sale-channel/discount/index.tsx",
      "parent": "/_authenticated/sale-channel/discount"
    },
    "/_authenticated/sale/discount-payment/": {
      "filePath": "_authenticated/sale/discount-payment/index.tsx",
      "parent": "/_authenticated/sale/discount-payment"
    },
    "/_authenticated/sale/promotion/": {
      "filePath": "_authenticated/sale/promotion/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale/service-charge/": {
      "filePath": "_authenticated/sale/service-charge/index.tsx",
      "parent": "/_authenticated/sale/service-charge"
    },
    "/_authenticated/setting/area/": {
      "filePath": "_authenticated/setting/area/index.tsx",
      "parent": "/_authenticated/setting/area"
    },
    "/_authenticated/setting/bill-model/": {
      "filePath": "_authenticated/setting/bill-model/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/payment-method/": {
      "filePath": "_authenticated/setting/payment-method/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/": {
      "filePath": "_authenticated/setting/source/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/store/": {
      "filePath": "_authenticated/setting/store/index.tsx",
      "parent": "/_authenticated/setting/store"
    },
    "/_authenticated/setting/table-layout/": {
      "filePath": "_authenticated/setting/table-layout/index.tsx",
      "parent": "/_authenticated/setting/table-layout"
    },
    "/_authenticated/setting/table/": {
      "filePath": "_authenticated/setting/table/index.tsx",
      "parent": "/_authenticated/setting/table"
    },
    "/_authenticated/employee/role/detail/$roleId": {
      "filePath": "_authenticated/employee/role/detail/$roleId.tsx",
      "parent": "/_authenticated/employee/role/detail"
    },
    "/_authenticated/menu/categories/categories-in-brand/detail": {
      "filePath": "_authenticated/menu/categories/categories-in-brand/detail.tsx",
      "parent": "/_authenticated/menu/categories/categories-in-brand",
      "children": [
        "/_authenticated/menu/categories/categories-in-brand/detail/$id"
      ]
    },
    "/_authenticated/menu/category-in-store/detail/$id": {
      "filePath": "_authenticated/menu/category-in-store/detail/$id.tsx",
      "parent": "/_authenticated/menu/category-in-store/detail"
    },
    "/_authenticated/menu/category/detail/$id": {
      "filePath": "_authenticated/menu/category/detail/$id.tsx",
      "parent": "/_authenticated/menu/category/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/detail": {
      "filePath": "_authenticated/menu/customization/customization-in-city/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/customization/customization-in-city/detail/$customizationId",
        "/_authenticated/menu/customization/customization-in-city/detail/"
      ]
    },
    "/_authenticated/menu/customization/customization-in-store/create": {
      "filePath": "_authenticated/menu/customization/customization-in-store/create.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/customization/customization-in-store/detail": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/customization/customization-in-store/detail/$customizationId",
        "/_authenticated/menu/customization/customization-in-store/detail/"
      ]
    },
    "/_authenticated/menu/item-class/detail/$id": {
      "filePath": "_authenticated/menu/item-class/detail/$id.tsx",
      "parent": "/_authenticated/menu/item-class/detail"
    },
    "/_authenticated/menu/items/items-in-city/detail": {
      "filePath": "_authenticated/menu/items/items-in-city/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/items/items-in-city/detail/$id",
        "/_authenticated/menu/items/items-in-city/detail/"
      ]
    },
    "/_authenticated/menu/items/items-in-store/detail": {
      "filePath": "_authenticated/menu/items/items-in-store/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/items/items-in-store/detail/$id",
        "/_authenticated/menu/items/items-in-store/detail/"
      ]
    },
    "/_authenticated/report/accounting/invoices/sale-sync-vat": {
      "filePath": "_authenticated/report/accounting/invoices/sale-sync-vat.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/revenue/categories/payment-method": {
      "filePath": "_authenticated/report/revenue/categories/payment-method.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/revenue/categories/promotion": {
      "filePath": "_authenticated/report/revenue/categories/promotion.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/revenue/categories/source": {
      "filePath": "_authenticated/report/revenue/categories/source.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/report/revenue/revenue/general": {
      "filePath": "_authenticated/report/revenue/revenue/general.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/channel/detail/$uuid": {
      "filePath": "_authenticated/sale-channel/channel/detail/$uuid.tsx",
      "parent": "/_authenticated/sale-channel/channel/detail"
    },
    "/_authenticated/sale-channel/discount/detail/$id": {
      "filePath": "_authenticated/sale-channel/discount/detail/$id.tsx",
      "parent": "/_authenticated/sale-channel/discount/detail"
    },
    "/_authenticated/sale/combo/detail/$id": {
      "filePath": "_authenticated/sale/combo/detail/$id.tsx",
      "parent": "/_authenticated/sale/combo/detail"
    },
    "/_authenticated/sale/discount-payment/detail/$id": {
      "filePath": "_authenticated/sale/discount-payment/detail/$id.tsx",
      "parent": "/_authenticated/sale/discount-payment/detail"
    },
    "/_authenticated/sale/discount/membership/detail": {
      "filePath": "_authenticated/sale/discount/membership/detail.tsx",
      "parent": "/_authenticated/sale/discount/membership",
      "children": [
        "/_authenticated/sale/discount/membership/detail/$id",
        "/_authenticated/sale/discount/membership/detail/",
        "/_authenticated/sale/discount/membership/detail/$companyId/$brandId"
      ]
    },
    "/_authenticated/sale/discount/regular/detail": {
      "filePath": "_authenticated/sale/discount/regular/detail.tsx",
      "parent": "/_authenticated/sale/discount/regular",
      "children": [
        "/_authenticated/sale/discount/regular/detail/$id",
        "/_authenticated/sale/discount/regular/detail/"
      ]
    },
    "/_authenticated/sale/service-charge/detail/$id": {
      "filePath": "_authenticated/sale/service-charge/detail/$id.tsx",
      "parent": "/_authenticated/sale/service-charge/detail"
    },
    "/_authenticated/setting/area/detail/$areaId": {
      "filePath": "_authenticated/setting/area/detail/$areaId.tsx",
      "parent": "/_authenticated/setting/area/detail"
    },
    "/_authenticated/setting/payment-method/detail/$paymentMethodId": {
      "filePath": "_authenticated/setting/payment-method/detail/$paymentMethodId.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/detail/$sourceId": {
      "filePath": "_authenticated/setting/source/detail/$sourceId.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/store/detail/$storeId": {
      "filePath": "_authenticated/setting/store/detail/$storeId.tsx",
      "parent": "/_authenticated/setting/store/detail"
    },
    "/_authenticated/setting/table/detail/$areaId": {
      "filePath": "_authenticated/setting/table/detail/$areaId.tsx",
      "parent": "/_authenticated/setting/table/detail"
    },
    "/_authenticated/setting/table/detail/$tableId": {
      "filePath": "_authenticated/setting/table/detail/$tableId.tsx",
      "parent": "/_authenticated/setting/table/detail"
    },
    "/_authenticated/menu/category-in-store/detail/": {
      "filePath": "_authenticated/menu/category-in-store/detail/index.tsx",
      "parent": "/_authenticated/menu/category-in-store/detail"
    },
    "/_authenticated/menu/category/detail/": {
      "filePath": "_authenticated/menu/category/detail/index.tsx",
      "parent": "/_authenticated/menu/category/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/": {
      "filePath": "_authenticated/menu/customization/customization-in-city/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/customization/customization-in-store/": {
      "filePath": "_authenticated/menu/customization/customization-in-store/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-class/detail/": {
      "filePath": "_authenticated/menu/item-class/detail/index.tsx",
      "parent": "/_authenticated/menu/item-class/detail"
    },
    "/_authenticated/menu/items/items-in-city/": {
      "filePath": "_authenticated/menu/items/items-in-city/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/items/items-in-store/": {
      "filePath": "_authenticated/menu/items/items-in-store/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/discount/detail/": {
      "filePath": "_authenticated/sale-channel/discount/detail/index.tsx",
      "parent": "/_authenticated/sale-channel/discount/detail"
    },
    "/_authenticated/sale/combo/detail/": {
      "filePath": "_authenticated/sale/combo/detail/index.tsx",
      "parent": "/_authenticated/sale/combo/detail"
    },
    "/_authenticated/sale/discount-payment/detail/": {
      "filePath": "_authenticated/sale/discount-payment/detail/index.tsx",
      "parent": "/_authenticated/sale/discount-payment/detail"
    },
    "/_authenticated/sale/discount/membership/": {
      "filePath": "_authenticated/sale/discount/membership/index.tsx",
      "parent": "/_authenticated/sale/discount/membership"
    },
    "/_authenticated/sale/discount/regular/": {
      "filePath": "_authenticated/sale/discount/regular/index.tsx",
      "parent": "/_authenticated/sale/discount/regular"
    },
    "/_authenticated/sale/service-charge/detail/": {
      "filePath": "_authenticated/sale/service-charge/detail/index.tsx",
      "parent": "/_authenticated/sale/service-charge/detail"
    },
    "/_authenticated/setting/area/detail/": {
      "filePath": "_authenticated/setting/area/detail/index.tsx",
      "parent": "/_authenticated/setting/area/detail"
    },
    "/_authenticated/setting/payment-method/detail/": {
      "filePath": "_authenticated/setting/payment-method/detail/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/printer-position/printer-position-in-brand/": {
      "filePath": "_authenticated/setting/printer-position/printer-position-in-brand/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/printer-position/printer-position-in-store/": {
      "filePath": "_authenticated/setting/printer-position/printer-position-in-store/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/detail/": {
      "filePath": "_authenticated/setting/source/detail/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/store/detail/": {
      "filePath": "_authenticated/setting/store/detail/index.tsx",
      "parent": "/_authenticated/setting/store/detail"
    },
    "/_authenticated/setting/table/detail/": {
      "filePath": "_authenticated/setting/table/detail/index.tsx",
      "parent": "/_authenticated/setting/table/detail"
    },
    "/_authenticated/menu/categories/categories-in-brand/detail/$id": {
      "filePath": "_authenticated/menu/categories/categories-in-brand/detail/$id.tsx",
      "parent": "/_authenticated/menu/categories/categories-in-brand/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/detail/$customizationId": {
      "filePath": "_authenticated/menu/customization/customization-in-city/detail/$customizationId.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-city/detail"
    },
    "/_authenticated/menu/customization/customization-in-store/detail/$customizationId": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail/$customizationId.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-store/detail"
    },
    "/_authenticated/menu/items/items-in-city/detail/$id": {
      "filePath": "_authenticated/menu/items/items-in-city/detail/$id.tsx",
      "parent": "/_authenticated/menu/items/items-in-city/detail"
    },
    "/_authenticated/menu/items/items-in-store/detail/$id": {
      "filePath": "_authenticated/menu/items/items-in-store/detail/$id.tsx",
      "parent": "/_authenticated/menu/items/items-in-store/detail"
    },
    "/_authenticated/sale/discount/membership/detail/$id": {
      "filePath": "_authenticated/sale/discount/membership/detail/$id.tsx",
      "parent": "/_authenticated/sale/discount/membership/detail"
    },
    "/_authenticated/sale/discount/regular/detail/$id": {
      "filePath": "_authenticated/sale/discount/regular/detail/$id.tsx",
      "parent": "/_authenticated/sale/discount/regular/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/detail/": {
      "filePath": "_authenticated/menu/customization/customization-in-city/detail/index.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-city/detail"
    },
    "/_authenticated/menu/customization/customization-in-store/detail/": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail/index.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-store/detail"
    },
    "/_authenticated/menu/items/items-in-city/detail/": {
      "filePath": "_authenticated/menu/items/items-in-city/detail/index.tsx",
      "parent": "/_authenticated/menu/items/items-in-city/detail"
    },
    "/_authenticated/menu/items/items-in-store/detail/": {
      "filePath": "_authenticated/menu/items/items-in-store/detail/index.tsx",
      "parent": "/_authenticated/menu/items/items-in-store/detail"
    },
    "/_authenticated/sale/discount/membership/detail/": {
      "filePath": "_authenticated/sale/discount/membership/detail/index.tsx",
      "parent": "/_authenticated/sale/discount/membership/detail"
    },
    "/_authenticated/sale/discount/regular/detail/": {
      "filePath": "_authenticated/sale/discount/regular/detail/index.tsx",
      "parent": "/_authenticated/sale/discount/regular/detail"
    },
    "/_authenticated/sale/discount/membership/detail/$companyId/$brandId": {
      "filePath": "_authenticated/sale/discount/membership/detail/$companyId/$brandId.tsx",
      "parent": "/_authenticated/sale/discount/membership/detail"
    }
  }
}
ROUTE_MANIFEST_END */
