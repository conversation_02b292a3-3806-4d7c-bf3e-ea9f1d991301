import { api, ApiResponse } from './api/pos/pos-api'

export interface CreatePrinterPositionRequest {
  printer_position_name: string
  company_uid: string
  brand_uid: string
  store_uid?: string
  printer_position_id: string
  list_item_type_id: string
  list_item_id: string
  sources: string
  area_ids?: string
  apply_with_store?: number
  sort?: number
}

export interface UpdatePrinterPositionRequest extends CreatePrinterPositionRequest {
  id: string
}

export interface PrinterPosition {
  id: string
  printer_position_name: string
  company_uid: string
  brand_uid: string
  store_uid?: string | null
  printer_position_id: string
  list_item_type_id: string
  list_item_id: string
  sources: string
  area_ids?: string
  apply_with_store?: number
  sort?: number
  revision?: number
  created_at: string // ISO string, not timestamp
  updated_at: string // ISO string, not timestamp
  created_by?: string
  updated_by?: string
  deleted?: boolean
  deleted_by?: string | null
  deleted_at?: string | null // ISO string, not timestamp
}

export interface PrinterPositionApiResponse {
  success: boolean
  data: PrinterPosition
  message?: string
}

export interface GetPrinterPositionsParams {
  company_uid: string
  brand_uid: string
  store_uid?: string
  page?: number
  limit?: number
  search?: string
  active?: number
  apply_with_store?: number
}

export interface GetPrinterPositionDetailParams {
  company_uid: string
  brand_uid: string
  id: string
}

export interface DeletePrinterPositionParams {
  company_uid: string
  brand_uid: string
  id: string[]
}

export interface PrinterPositionsListResponse {
  success: boolean
  data: PrinterPosition[]
  total_item: number
  message?: string
}

// Printer Position API Service
export const printerPositionApi = {
  /**
   * Create a new printer position
   */
  createPrinterPosition: async (data: CreatePrinterPositionRequest): Promise<ApiResponse<unknown>> => {
    const response = await api.post('/v3/pos-cms/printer-position', data)

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from printer position API')
    }

    return response.data
  },

  /**
   * Update an existing printer position (API expects POST with full body)
   */
  updatePrinterPosition: async (data: UpdatePrinterPositionRequest): Promise<ApiResponse<unknown>> => {
    // Backend expects POST to the same endpoint with the full object including id
    const response = await api.post('/v3/pos-cms/printer-position', data)

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from printer position API')
    }

    return response.data
  },

  /**
   * Get list of printer positions
   */
  getPrinterPositions: async (params: GetPrinterPositionsParams): Promise<ApiResponse<unknown>> => {
    const queryParams = new URLSearchParams()

    queryParams.append('company_uid', params.company_uid)
    queryParams.append('brand_uid', params.brand_uid)

    if (params.store_uid) queryParams.append('store_uid', params.store_uid)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.active !== undefined) queryParams.append('active', params.active.toString())
    if (params.apply_with_store !== undefined)
      queryParams.append('apply_with_store', params.apply_with_store.toString())

    const url = `/v3/pos-cms/printer-position?${queryParams.toString()}`

    const response = await api.get(url)

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from printer position API')
    }

    return response.data
  },

  /**
   * Get printer position detail
   */
  getPrinterPositionDetail: async (params: GetPrinterPositionDetailParams): Promise<ApiResponse<unknown>> => {
    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', params.company_uid)
    queryParams.append('brand_uid', params.brand_uid)
    queryParams.append('id', params.id)

    const response = await api.get(`/pos/v1/printer-position/detail?${queryParams.toString()}`)

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from printer position detail API')
    }

    return response.data
  },

  /**
   * Delete printer position(s)
   */
  deletePrinterPosition: async (params: DeletePrinterPositionParams): Promise<ApiResponse<unknown>> => {
    const response = await api.delete('/pos/v1/printer-position', {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      data: {
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        id: params.id
      }
    })

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from printer position delete API')
    }

    return response.data
  }
}
