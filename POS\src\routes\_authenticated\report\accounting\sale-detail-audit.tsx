import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import SaleDetailAudit from '@/features/reports/accounting/sale-detail-audit'

export const Route = createFileRoute('/_authenticated/report/accounting/sale-detail-audit')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: SaleDetailAudit
})
