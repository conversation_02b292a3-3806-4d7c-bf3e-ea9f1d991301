import { useCurrentBrand as usePosCurrentBrand } from '@/stores/posStore'

import { usePosData } from './use-pos-data'

/**
 * Hook to get the currently selected brand and related information
 * @deprecated Use useCurrentBrand from @/stores/posStore directly
 */
export const useCurrentBrand = () => {
  const posCurrentBrand = usePosCurrentBrand()
  const { getBrandById, getStoresByBrand, getActiveStoresByBrand } = usePosData()

  const brandStores = posCurrentBrand.selectedBrand?.id ? getStoresByBrand(posCurrentBrand.selectedBrand.id) : []
  const activeBrandStores = posCurrentBrand.selectedBrand?.id
    ? getActiveStoresByBrand(posCurrentBrand.selectedBrand.id)
    : []

  const fullBrandData = posCurrentBrand.selectedBrand?.id ? getBrandById(posCurrentBrand.selectedBrand.id) : null

  return {
    selectedBrand: posCurrentBrand.selectedBrand,
    setSelectedBrand: posCurrentBrand.setSelectedBrand,
    isLoading: posCurrentBrand.isLoading,

    brandStores,
    activeBrandStores,
    fullBrandData,

    hasStores: brandStores.length > 0,
    hasActiveStores: activeBrandStores.length > 0,
    storeCount: brandStores.length,
    activeStoreCount: activeBrandStores.length,

    brandName: posCurrentBrand.selectedBrand?.name || 'No Brand Selected',
    brandCurrency: posCurrentBrand.selectedBrand?.currency || 'VND',
    brandId: posCurrentBrand.selectedBrand?.brandId || posCurrentBrand.selectedBrand?.id || ''
  }
}

/**
 * Hook to check if a specific brand is currently selected
 */
export const useIsBrandSelected = (brandId: string) => {
  const { selectedBrand } = usePosCurrentBrand()
  return selectedBrand?.id === brandId || selectedBrand?.brandId === brandId
}

/**
 * Hook to switch to a specific brand by ID
 */
export const useSwitchToBrand = () => {
  const { setSelectedBrand } = usePosCurrentBrand()
  const { getBrandById } = usePosData()

  return (brandId: string) => {
    const brand = getBrandById(brandId)
    if (brand) {
      const brandForSwitcher = {
        id: brand.id,
        name: brand.brand_name,
        logo: () => null,
        plan: `${brand.currency} • ${brand.brand_id}`,
        brandId: brand.brand_id,
        currency: brand.currency,
        active: brand.active === 1
      }
      setSelectedBrand(brandForSwitcher)
    }
  }
}
