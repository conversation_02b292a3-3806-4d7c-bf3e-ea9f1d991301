import type { UseFormReturn } from 'react-hook-form'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Checkbox } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface SelfOrderConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function SelfOrderConfigSection({ form, isLoading = false }: SelfOrderConfigSectionProps) {
  return (
    <div className='space-y-6'>
      {/* Section Header with Toggle */}
      <div className='flex items-center justify-between'>
        <h2 className='text-lg font-semibold'>C<PERSON>u hình thiết bị Self Order</h2>
      </div>

      {/* Section Content */}
      <div className='space-y-4'>
        {/* Hiển thị nhập thẻ bán với mọi phương thức thanh toán */}
        <FormField
          control={form.control}
          name='allway_show_tag_so'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị nhập thẻ bán với mọi phương thức thanh toán</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép S.O sử dụng ca hiện tại của thu ngân */}
        <FormField
          control={form.control}
          name='use_shift_pos'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cho phép S.O sử dụng ca hiện tại của thu ngân</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
