import { AlertTriangle } from 'lucide-react'

import { STORE_CONSTANTS } from '../../../data'

interface ExpiredLicenseAlertProps {
  isVisible: boolean
}

export function ExpiredLicenseAlert({ isVisible }: ExpiredLicenseAlertProps) {
  if (!isVisible) return null

  return (
    <div className='mb-6 rounded-lg bg-red-500 px-4 py-3 text-white'>
      <div className='flex items-center gap-2'>
        <AlertTriangle className='h-5 w-5' />
        <span className='font-medium'>{STORE_CONSTANTS.EXPIRED_LICENSE}</span>
      </div>
    </div>
  )
}
