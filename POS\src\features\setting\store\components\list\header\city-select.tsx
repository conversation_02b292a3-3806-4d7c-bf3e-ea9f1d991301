import { FilterDropdown } from '@/components/filter-dropdown'

interface City {
  id: string
  name: string
}

interface CitySelectProps {
  value: string
  onValueChange: (value: string) => void
  cities?: City[]
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export function CitySelect({
  value,
  onValueChange,
  cities,
  isLoading,
  placeholder = 'Tất cả thành phố',
  className = 'w-48'
}: CitySelectProps) {
  const options =
    cities?.map(city => ({
      value: city.id,
      label: city.name
    })) || []

  return (
    <FilterDropdown
      value={value}
      onValueChange={onValueChange}
      options={options}
      isLoading={isLoading}
      placeholder={placeholder}
      className={className}
      allOptionLabel='Tất cả thành phố'
      loadingText='Đang tải thành phố...'
      emptyText='Không có thành phố'
    />
  )
}
