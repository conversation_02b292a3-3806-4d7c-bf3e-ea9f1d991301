/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { deletedOrdersApi } from '@/lib/deleted-orders-api'

interface UseTopDeletedStoresOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  limit?: number
  autoFetch?: boolean
}

interface StoreDeletedStats {
  storeUid: string
  storeName: string
  totalDeleted: number
  totalDeletedAmount: number
  deletedPercentage: number
  topEmployee?: {
    employeeName: string
    deletedCount: number
  }
  topReason?: {
    reason: string
    count: number
  }
}

interface UseTopDeletedStoresReturn {
  // Top stores by deleted orders
  topStores: StoreDeletedStats[]

  // Loading and error states
  isLoading: boolean
  error: string | null
  loadingMessage?: string

  // Progress tracking
  progress: {
    current: number
    total: number
    percentage: number
  }

  // Summary
  totalStores: number
  totalDeleted: number
  totalDeletedAmount: number

  refetch: () => Promise<void>

  selectedBrand: any
  currentBrandStores: any[]
}

/**
 * Optimized hook for fetching deleted orders across multiple stores
 * Uses batching, caching, and parallel requests for better performance
 */
export function useTopDeletedStores(
  options: UseTopDeletedStoresOptions = {}
): UseTopDeletedStoresReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    limit = 10,
    autoFetch = true,
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()

  const [topStores, setTopStores] = useState<StoreDeletedStats[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(
    undefined
  )
  const [progress, setProgress] = useState({
    current: 0,
    total: 0,
    percentage: 0,
  })
  const [lastFetchKey, setLastFetchKey] = useState<string | null>(null)
  const isMountedRef = useRef(true)

  const startTime = useMemo(() => dateRange?.from.getTime(), [dateRange?.from])
  const endTime = useMemo(() => dateRange?.to.getTime(), [dateRange?.to])
  const brandId = selectedBrand?.id
  const companyId = company?.id

  // Get list of stores to fetch with stable reference
  const storesToFetch = useMemo(() => {
    if (!selectedStores || selectedStores.includes('all-stores')) {
      return currentBrandStores.filter((store) => store.active === 1)
    }

    return currentBrandStores.filter(
      (store) => selectedStores.includes(store.id) && store.active === 1
    )
  }, [selectedStores, currentBrandStores])

  // Create stable store IDs string for dependency
  const storeIds = useMemo(() => {
    return storesToFetch
      .map((s) => s.id)
      .sort()
      .join(',')
  }, [storesToFetch])

  const fetchDeletedOrdersForStores = useCallback(
    async (retryCount = 0) => {
      if (!brandId || !companyId) {
        setError('Brand or company not selected')
        return
      }

      if (!startTime || !endTime) {
        setError('Date range is required')
        return
      }

      if (storesToFetch.length === 0) {
        setError('No stores available')
        return
      }

      const fetchKey = `${brandId}-${companyId}-${startTime}-${endTime}-${storeIds}-${limit}`

      if (lastFetchKey === fetchKey && topStores.length > 0) {
        return
      }

      setIsLoading(true)
      setError(null)
      setProgress({ current: 0, total: storesToFetch.length, percentage: 0 })

      if (storesToFetch.length > 20) {
        setLoadingMessage(
          `Loading data for ${storesToFetch.length} stores, this may take a while...`
        )
      } else if (retryCount > 0) {
        setLoadingMessage(`Retrying... (attempt ${retryCount + 1}/3)`)
      } else {
        setLoadingMessage(
          `Loading deleted orders for ${storesToFetch.length} stores...`
        )
      }

      try {
        const storeStats: StoreDeletedStats[] = []
        const batchSize = 5

        for (let i = 0; i < storesToFetch.length; i += batchSize) {
          const batch = storesToFetch.slice(i, i + batchSize)

          const batchPromises = batch.map(async (store) => {
            try {
              const response = await deletedOrdersApi.getDeletedOrders({
                companyUid: companyId,
                brandUid: brandId,
                storeUid: store.id,
                startDate: startTime,
                endDate: endTime,
                page: 1,
              })

              // Process the response for this store
              const deletedOrders = response.data.filter(
                (order) => order.status === 'DELETE'
              )
              const totalDeleted = deletedOrders.length
              const totalDeletedAmount = deletedOrders.reduce(
                (sum, order) => sum + order.total_amount_origin,
                0
              )

              // Find top employee
              const employeeGroups = new Map<string, any[]>()
              deletedOrders.forEach((order) => {
                if (!employeeGroups.has(order.employee_uid)) {
                  employeeGroups.set(order.employee_uid, [])
                }
                employeeGroups.get(order.employee_uid)!.push(order)
              })

              const topEmployee = Array.from(employeeGroups.entries())
                .map(([_, orders]) => ({
                  employeeName: orders[0].employee_name,
                  deletedCount: orders.length,
                }))
                .sort((a, b) => b.deletedCount - a.deletedCount)[0]

              // Find top reason
              const reasonGroups = new Map<string, any[]>()
              deletedOrders.forEach((order) => {
                const reason = order.sale_note || 'Không có lý do'
                if (!reasonGroups.has(reason)) {
                  reasonGroups.set(reason, [])
                }
                reasonGroups.get(reason)!.push(order)
              })

              const topReason = Array.from(reasonGroups.entries())
                .map(([reason, orders]) => ({
                  reason,
                  count: orders.length,
                }))
                .sort((a, b) => b.count - a.count)[0]

              return {
                storeUid: store.id,
                storeName: store.store_name,
                totalDeleted,
                totalDeletedAmount,
                deletedPercentage: 100,
                topEmployee,
                topReason,
              }
            } catch {
              return {
                storeUid: store.id,
                storeName: store.store_name,
                totalDeleted: 0,
                totalDeletedAmount: 0,
                deletedPercentage: 0,
                topEmployee: undefined,
                topReason: undefined,
              }
            }
          })

          // Wait for current batch to complete
          const batchResults = await Promise.all(batchPromises)
          storeStats.push(...batchResults)

          // Update progress
          const currentProgress = Math.min(i + batchSize, storesToFetch.length)
          setProgress({
            current: currentProgress,
            total: storesToFetch.length,
            percentage: Math.round(
              (currentProgress / storesToFetch.length) * 100
            ),
          })

          // Update loading message with progress
          setLoadingMessage(
            `Loaded ${currentProgress}/${storesToFetch.length} stores...`
          )

          // Small delay between batches to be nice to the API
          if (i + batchSize < storesToFetch.length) {
            await new Promise((resolve) => setTimeout(resolve, 500))
          }
        }

        // Sort by total deleted orders (descending) and limit results
        const sortedStores = storeStats
          .filter((store) => store.totalDeleted > 0) // Only include stores with deleted orders
          .sort((a, b) => b.totalDeleted - a.totalDeleted)
          .slice(0, limit)

        // Only update state if component is still mounted
        if (isMountedRef.current) {
          setTopStores(sortedStores)
          setLastFetchKey(fetchKey) // Mark this request as completed
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to fetch deleted orders data'

        if (
          (errorMessage.includes('timeout') || errorMessage.includes('504')) &&
          retryCount < 2
        ) {
          await new Promise((resolve) =>
            setTimeout(resolve, (retryCount + 1) * 2000)
          )
          return fetchDeletedOrdersForStores(retryCount + 1)
        }

        if (errorMessage.includes('504')) {
          setError(
            'Server is taking too long to respond. Please try again with a smaller date range or fewer stores.'
          )
        } else {
          setError(errorMessage)
        }
      } finally {
        if (isMountedRef.current) {
          setIsLoading(false)
          setLoadingMessage(undefined)
          setProgress({ current: 0, total: 0, percentage: 0 })
        }
      }
    },
    [
      brandId,
      companyId,
      startTime,
      endTime,
      storeIds,
      limit,
      lastFetchKey,
      topStores.length,
    ]
  )

  useEffect(() => {
    if (autoFetch) {
      fetchDeletedOrdersForStores()
    }
  }, [autoFetch, fetchDeletedOrdersForStores])

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const totalStores = useMemo(() => topStores.length, [topStores])
  const totalDeleted = useMemo(
    () => topStores.reduce((sum, store) => sum + store.totalDeleted, 0),
    [topStores]
  )
  const totalDeletedAmount = useMemo(
    () => topStores.reduce((sum, store) => sum + store.totalDeletedAmount, 0),
    [topStores]
  )

  return {
    topStores,

    isLoading,
    error,
    loadingMessage,

    progress,

    totalStores,
    totalDeleted,
    totalDeletedAmount,

    refetch: fetchDeletedOrdersForStores,

    selectedBrand,
    currentBrandStores,
  }
}

export default useTopDeletedStores
