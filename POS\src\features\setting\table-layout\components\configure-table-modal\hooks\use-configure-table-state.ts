import { useState, useMemo } from 'react'

interface Store {
  id: string
  store_name: string
  active: number
}

interface TableConfig {
  color: string
  fontSize: string
}

export const useConfigureTableState = () => {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [selectedAreaId, setSelectedAreaId] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTables, setSelectedTables] = useState<Set<string>>(new Set())
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [config, setConfig] = useState<TableConfig>({
    color: '#1E40AF',
    fontSize: '15'
  })
  const [selectAllInArea, setSelectAllInArea] = useState(false)
  const [applyToAllTables, setApplyToAllTables] = useState(false)

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      return []
    }
  }, [])

  const resetState = () => {
    setSelectedStoreId('')
    setSelectedAreaId('')
    setSearchTerm('')
    setSelectedTables(new Set())
    setShowColorPicker(false)
    setSelectAllInArea(false)
    setApplyToAllTables(false)
    setConfig({
      color: '#1E40AF',
      fontSize: '15'
    })
  }

  return {
    // State
    selectedStoreId,
    selectedAreaId,
    searchTerm,
    selectedTables,
    showColorPicker,
    config,
    selectAllInArea,
    applyToAllTables,
    stores,
    
    // Setters
    setSelectedStoreId,
    setSelectedAreaId,
    setSearchTerm,
    setSelectedTables,
    setShowColorPicker,
    setConfig,
    setSelectAllInArea,
    setApplyToAllTables,
    
    // Actions
    resetState
  }
}
