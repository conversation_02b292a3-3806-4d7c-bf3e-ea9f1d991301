import type {
  OrderSource,
  OrderSourceDetail,
  OrderSourcesResponse,
  CreateOrderSourceRequest,
  UpdateOrderSourceRequest,
  DeleteOrderSourceRequest
} from '@/types/api/order-sources'

import { apiClient } from './api/pos/pos-api'

export const orderSourcesApi = {
  getOrderSources: async (
    companyUid: string,
    brandUid: string,
    page: number = 1,
    search?: string,
    storeUid?: string
  ): Promise<OrderSourcesResponse> => {
    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: companyUid,
      brand_uid: brandUid,
      partner_config: '0'
    })

    // Only add page if not using skip_limit
    if (!queryParams.get('skip_limit')) {
      queryParams.append('page', page.toString())
    }

    if (search && search.trim()) {
      queryParams.append('search', search.trim())
    }

    if (storeUid && storeUid !== 'all') {
      queryParams.append('store_uid', storeUid)
    }

    const response = await apiClient.get(`/mdata/v1/sources?${queryParams.toString()}`)
    return response.data
  },

  createOrderSource: async (data: CreateOrderSourceRequest): Promise<OrderSource> => {
    const response = await apiClient.post('/mdata/v1/source', data)
    return response.data.data
  },

  getOrderSourceById: async (id: string): Promise<OrderSourceDetail> => {
    const response = await apiClient.get(`/mdata/v1/source?id=${id}`)
    return response.data.data
  },

  updateOrderSourceById: async (data: CreateOrderSourceRequest): Promise<OrderSource> => {
    const response = await apiClient.put('/mdata/v1/source', data)
    return response.data.data
  },

  updateOrderSource: async (data: UpdateOrderSourceRequest): Promise<OrderSource> => {
    const response = await apiClient.put(`/mdata/v1/sources/${data.id}`, data)
    return response.data.data
  },

  deleteOrderSource: async (data: DeleteOrderSourceRequest): Promise<void> => {
    const queryParams = new URLSearchParams({
      company_uid: data.company_uid,
      brand_uid: data.brand_uid,
      id: data.id
    })

    await apiClient.delete(`/mdata/v1/source?${queryParams.toString()}`)
  },

  getOrderSourceDetail: async (id: string): Promise<any> => {
    const response = await apiClient.get(`/mdata/v1/source?id=${id}`)
    return response.data
  },

  getOrderSourcesForSort: async (companyUid: string, brandUid: string): Promise<OrderSourcesResponse> => {
    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: companyUid,
      brand_uid: brandUid
    })

    const response = await apiClient.get(`/mdata/v1/sources?${queryParams.toString()}`)
    return response.data
  },

  updateOrderSourcesSort: async (
    sortData: Array<{
      source_id: string
      sort: number
      company_uid: string
      brand_uid: string
    }>
  ): Promise<void> => {
    await apiClient.put('/mdata/v1/source/sort', sortData)
  }
}
