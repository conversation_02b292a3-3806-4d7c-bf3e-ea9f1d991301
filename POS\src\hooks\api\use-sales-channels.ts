import { useQuery } from '@tanstack/react-query'

import type { GetSourcesParams } from '@/types/sources'

import { getSalesChannels } from '@/lib/sales-channels-api'

export function useSalesChannels(params: GetSourcesParams = {}) {
  return useQuery({
    queryKey: ['sales-channels', params],
    queryFn: () => getSalesChannels(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
