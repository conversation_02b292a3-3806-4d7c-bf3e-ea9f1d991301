import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import {
  type GetPrinterPositionsParams,
  type PrinterPosition,
  printerPositionApi,
  type PrinterPositionsListResponse
} from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UsePrinterPositionsDataOptions {
  params?: Partial<GetPrinterPositionsParams>
  enabled?: boolean
}

export const usePrinterPositionsData = (options: UsePrinterPositionsDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetPrinterPositionsParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    limit: 1000,
    ...params
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.PRINTER_POSITIONS, dynamicParams],
    queryFn: async (): Promise<PrinterPosition[]> => {
      const response = (await printerPositionApi.getPrinterPositions(dynamicParams)) as PrinterPositionsListResponse

      return Array.isArray(response.data) ? response.data : []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 0,
    gcTime: 0,
    retry: false,
    initialData: [] as PrinterPosition[]
  })
}
