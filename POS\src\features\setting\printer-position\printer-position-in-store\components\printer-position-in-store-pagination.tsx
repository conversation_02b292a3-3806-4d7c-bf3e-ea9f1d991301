import { ChevronLeft, ChevronRight } from 'lucide-react'

import { Button } from '@/components/ui'

interface PrinterPositionInStorePaginationProps {
  currentPage: number
  onPageChange: (page: number) => void
  hasNextPage: boolean
}

export function PrinterPositionInStorePagination({ 
  currentPage, 
  onPageChange, 
  hasNextPage 
}: PrinterPositionInStorePaginationProps) {
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (hasNextPage) {
      onPageChange(currentPage + 1)
    }
  }

  return (
    <div className='flex items-center justify-center gap-4 py-4'>
      <Button
        variant='outline'
        size='sm'
        onClick={handlePreviousPage}
        disabled={currentPage === 1}
        className='flex items-center gap-2'
      >
        <ChevronLeft className='h-4 w-4' />
        Trước
      </Button>

      <span className='text-sm font-medium'>{currentPage}</span>

      <Button
        variant='outline'
        size='sm'
        onClick={handleNextPage}
        disabled={!hasNextPage}
        className='flex items-center gap-2'
      >
        Sau
        <ChevronRight className='h-4 w-4' />
      </Button>
    </div>
  )
}
