import { useMemo } from 'react'

import type { Area } from '@/lib/api'

import { useAreasData, useStoresData  } from '@/hooks/api'

import type { AreaOption } from '../data/table-layout-types'
import { useTableLayout } from './use-table-layout'

interface UseTableLayoutDataProps {
  selectedStoreId: string
  selectedAreaId: string
}

export const useTableLayoutData = ({ selectedStoreId, selectedAreaId }: UseTableLayoutDataProps) => {
  const { data: allStores, isLoading: isLoadingStores } = useStoresData()

  const { data: areasData, isLoading: areasLoading } = useAreasData({
    storeUid: selectedStoreId
  })

  const areas: AreaOption[] = useMemo(() => {
    if (!areasData) return []
    return areasData
      .sort((a, b) => a.sort - b.sort)
      .map((area: Area) => ({
        id: area.id,
        area_name: area.area_name,
        area_id: area.area_id
      }))
  }, [areasData])

  const { data: tables = [], isLoading: tablesLoading } = useTableLayout({
    areaUid: selectedAreaId,
    storeUid: selectedStoreId,
    enabled: !!selectedAreaId && !!selectedStoreId
  })

  return {
    allStores,
    isLoadingStores,
    areas,
    areasLoading,
    tables,
    tablesLoading
  }
}
