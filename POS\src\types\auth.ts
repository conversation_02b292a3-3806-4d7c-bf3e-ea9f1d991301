// Auth API Types
export interface LoginRequest {
  email: string
  password: string
}

export interface UserRole {
  id: string
  role_id: string
  role_name: string
  description: string
  scope: string
  scope_value: string | null
  allow_access: string[]
  reject_permissions: string[]
  created_at: string
  updated_at: string
}

export interface UserPermissions {
  id: string
  user_uid: string
  company_uid: string
  stores: Record<string, unknown>
  tables: Record<string, unknown>
}

export interface Company {
  id: string
  company_id: string
  company_name: string
  description: string | null
  extra_data: Record<string, unknown>
  active: number
  revision: string
}

export interface Brand {
  id: string
  brand_id: string
  brand_name: string
  extra_data: Record<string, unknown>
  active: number
  is_fabi: number
  sort: number
  created_at: string
  currency: string
}

export interface City {
  id: string
  city_id: string
  city_name: string
  active: number
}

export interface Store {
  id: string
  brand_uid: string
  city_uid: string
  company_uid: string
  store_id: string
  store_name: string
  logo: string
  background: string
  facebook: string
  website: string
  fb_store_id: number
  phone: string
  address: string
  latitude: number
  active: number
  open_at: number
  expiry_date: number
  sort: number
  enable_change_item_in_store: number
  enable_change_item_type_in_store: number
  enable_change_printer_position_in_store: number
  enable_turn_order_report: number
  sale_change_vat_enable: number
  bill_template: number
  is_franchise: number
  tracking_sale: number
  dateend: string
  istrial: string
  change_log_detail: number
}

export interface User {
  id: string
  email: string
  phone: string
  full_name: string
  profile_image_path: string | null
  role_uid: string
  company_uid: string
  country_id: string
  active: number
  is_verified: number
  partner_company_uid: string | null
  fixed_account: boolean
  created_at: string
  updated_at: number
  last_login_at: number
  is_fabi: number | null
}

export interface GenAIPermissions {
  REVENUE_ANALYSIS: boolean
  CONTENT_WRITING: boolean
  FEEDBACK_REVIEW: boolean
  OPERATION_SUSGESTION: boolean
}

export interface LoginResponse {
  user_role: UserRole
  user_permissions: UserPermissions
  company: Company
  brands: Brand[]
  cities: City[]
  stores: Store[]
  genai_access: boolean
  genai_perms: GenAIPermissions
  user: User
  token: string
  track_id: string
}
