import type { Source, SourceApiData } from '@/types/sources'
import { convertApiSourceToSource } from '@/types/sources'

import { apiClient } from './api/pos/pos-api'

// API response interface
interface SourcesApiResponse {
  data: SourceApiData[]
  track_id: string
}

// Parameters for fetching sources
export interface GetSourcesParams {
  company_uid?: string
  brand_uid?: string
  city_uid?: string
  store_uid?: string
  // Support passing multiple store ids similar to list_store_uid in legacy API
  list_store_uid?: string | string[]
  skip_limit?: boolean
}

// Sources API service
export const sourcesApi = {
  /**
   * Get sources list
   */
  getSources: async (params: GetSourcesParams): Promise<Source[]> => {
    const queryParams = new URLSearchParams()

    if (params.company_uid) {
      queryParams.append('company_uid', params.company_uid)
    }
    if (params.brand_uid) {
      queryParams.append('brand_uid', params.brand_uid)
    }
    if (params.city_uid) {
      queryParams.append('city_uid', params.city_uid)
    }
    if (params.store_uid) {
      queryParams.append('store_uid', params.store_uid)
    }
    if (params.list_store_uid) {
      const listValue = Array.isArray(params.list_store_uid) ? params.list_store_uid.join(',') : params.list_store_uid
      if (listValue) {
        queryParams.append('list_store_uid', listValue)
      }
    }
    if (params.skip_limit) {
      queryParams.append('skip_limit', 'true')
    }

    const response = await apiClient.get<SourcesApiResponse>(`/mdata/v1/sources?${queryParams.toString()}`)

    return response.data.data?.map(convertApiSourceToSource) || []
  },

  /**
   * Update a source
   */
  updateSource: async (source: Source): Promise<Source> => {
    const response = await apiClient.put<{ data: SourceApiData }>(`/mdata/v1/sources/${source.id}`, source)
    return convertApiSourceToSource(response.data.data)
  },

  /**
   * Delete a source
   */
  deleteSource: async (sourceId: string): Promise<void> => {
    await apiClient.delete(`/mdata/v1/sources/${sourceId}`)
  },

  /**
   * Get sources for autocomplete (with is_fb=1 parameter)
   */
  getSourcesForAutocomplete: async (): Promise<{ source_id: string; source_name: string }[]> => {
    const response = await apiClient.get<SourcesApiResponse>('/mdata/v1/sources?skip_limit=true&is_fb=1')

    return (
      response.data.data?.map(source => ({
        source_id: source.source_id,
        source_name: source.source_name
      })) || []
    )
  }
}

// Legacy export for backward compatibility
export const fetchSources = sourcesApi.getSources
