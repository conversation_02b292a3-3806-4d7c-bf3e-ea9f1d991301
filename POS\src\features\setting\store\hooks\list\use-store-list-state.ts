import { useState } from 'react'

import { STORE_CONSTANTS } from '../../data'

export interface UseStoreListStateReturn {
  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedCity: string
  setSelectedCity: (city: string) => void
}

/**
 * Custom hook for managing store list state (search and filter)
 */
export function useStoreListState(): UseStoreListStateReturn {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCity, setSelectedCity] = useState<string>(STORE_CONSTANTS.CITY_ALL)

  return {
    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity
  }
}
