import { SalesResponse } from './sales-types'

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Cache interfaces
interface CachedSalesData {
  data: SalesResponse
  timestamp: number
}

// Cache storage
export const salesCache = new Map<string, CachedSalesData>()
export const pendingRequests = new Map<string, Promise<SalesResponse>>()

// Cache utility functions
export const salesCacheUtils = {
  /**
   * Get cached data if still valid
   */
  get: (key: string): SalesResponse | null => {
    const cached = salesCache.get(key)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }
    return null
  },

  /**
   * Set cache data
   */
  set: (key: string, data: SalesResponse): void => {
    salesCache.set(key, {
      data,
      timestamp: Date.now(),
    })
  },

  /**
   * Check if request is pending
   */
  getPendingRequest: (key: string): Promise<SalesResponse> | null => {
    return pendingRequests.get(key) || null
  },

  /**
   * Set pending request
   */
  setPendingRequest: (key: string, promise: Promise<SalesResponse>): void => {
    pendingRequests.set(key, promise)
  },

  /**
   * Remove pending request
   */
  removePendingRequest: (key: string): void => {
    pendingRequests.delete(key)
  },

  /**
   * Generate cache key for sales requests
   */
  generateKey: (params: {
    companyUid: string
    brandUid: string
    listStoreUid: string
    startDate: number
    endDate: number
    page?: number
    sourceId?: number
    isDiscount?: number
  }): string => {
    return `sales-${params.companyUid}-${params.brandUid}-${params.listStoreUid}-${params.startDate}-${params.endDate}-${params.page || 1}-${params.sourceId || ''}-${params.isDiscount || ''}`
  },

  /**
   * Clear all cache
   */
  clear: (): void => {
    salesCache.clear()
    pendingRequests.clear()
  },

  /**
   * Clear expired cache entries
   */
  clearExpired: (): void => {
    const now = Date.now()
    for (const [key, cached] of salesCache.entries()) {
      if (now - cached.timestamp >= CACHE_DURATION) {
        salesCache.delete(key)
      }
    }
  },

  /**
   * Get cache statistics
   */
  getStats: () => {
    return {
      cacheSize: salesCache.size,
      pendingRequests: pendingRequests.size,
      cacheDuration: CACHE_DURATION,
    }
  },
}

// Auto cleanup expired cache every 10 minutes
setInterval(
  () => {
    salesCacheUtils.clearExpired()
  },
  10 * 60 * 1000
)
