import { useEffect, useState } from 'react'

import { ChevronDown } from 'lucide-react'

import { useStoresData } from '@/hooks/api'

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import type { BillTemplateActionBarProps } from '../types'
import { SyncBillTemplateModal } from './sync-bill-template-modal'

export function BillTemplateActionBar({
  selectedStoreId,
  onStoreChange,
  onUseBillTemplate,
  onCopyTemplates,
  isUsingTemplate
}: Omit<BillTemplateActionBarProps, 'stores'>) {
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false)
  const { data: storesData = [] } = useStoresData()
  const stores = storesData.map(store => ({
    id: store.id,
    name: store.name
  }))

  useEffect(() => {
    if (!selectedStoreId && stores.length > 0) {
      onStoreChange(stores[0].id)
    }
  }, [stores, selectedStoreId, onStoreChange])

  const handleCopyTemplates = () => {
    setIsSyncModalOpen(true)
    onCopyTemplates()
  }

  return (
    <div className='flex items-center justify-between gap-4 py-4'>
      <div className='min-w-[200px]'>
        <Select value={selectedStoreId} onValueChange={onStoreChange}>
          <SelectTrigger>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex items-center gap-3'>
        <Button onClick={onUseBillTemplate} variant='default' disabled={isUsingTemplate}>
          {isUsingTemplate ? 'Đang áp dụng...' : 'Sử dụng mẫu hóa đơn'}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline'>
              Tiện ích
              <ChevronDown className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={handleCopyTemplates}>Sao chép các mẫu phiếu in</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <SyncBillTemplateModal open={isSyncModalOpen} onOpenChange={setIsSyncModalOpen} stores={stores} />
    </div>
  )
}
