import { useEffect } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { DatePicker } from '@/components/ui/date'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormControl, FormMessage } from '@/components/ui'

import type { StoreFormValues } from '../../../data'

interface RevenueLockSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

const REVENUE_LOCK_OPTIONS = [
  { value: '0', label: 'Không khóa' },
  { value: '0.25', label: '6 giờ' },
  { value: '0.5', label: '12 giờ' },
  ...Array.from({ length: 30 }, (_, i) => ({
    value: `${i + 1}`,
    label: `${i + 1} ngày`
  }))
]

export function RevenueLockSection({ form, isLoading = false }: RevenueLockSectionProps) {
  useEffect(() => {
    const current = form.getValues('time_after_lock') as unknown as number | undefined
    if (current === undefined || current === null || Number.isNaN(current)) {
      form.setValue('time_after_lock', 0)
    }
  }, [form])
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Cấu hình khóa doanh thu</h2>
        <p className='text-sm text-gray-600'>
          Khi cấu hình khóa doanh thu, các hoạt động liên quan doanh thu như bán hàng sẽ bị khóa
        </p>
        <p className='text-sm text-gray-600'>
          Mục đích: khóa chính sửa hóa đơn (hủy và sửa hóa đơn) sau khoảng thời gian setup
        </p>
      </div>

      <div className='space-y-4'>
        {/* Chọn số ngày tự động khóa dữ liệu */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <div className='flex items-center gap-2'>
              <label className='text-sm font-medium text-gray-700'>Chọn số ngày tự động khóa dữ liệu</label>
            </div>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='time_after_lock'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Combobox
                      options={REVENUE_LOCK_OPTIONS}
                      value={field.value?.toString() || '0'}
                      onValueChange={val => field.onChange(parseFloat(val))}
                      placeholder='Không khóa'
                      searchPlaceholder='Tìm kiếm...'
                      emptyText='Không tìm thấy tùy chọn nào.'
                      disabled={isLoading}
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Thời điểm khóa phát sinh doanh thu */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <div className='flex items-center gap-2'>
              <label className='text-sm font-medium text-gray-700'>Thời điểm khóa phát sinh doanh thu</label>
            </div>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='time_lock_data'
              render={({ field }) => {
                const parseTimestampToDate = (value?: number): Date | undefined => {
                  if (!value || value <= 0) return undefined
                  const ms = value > 1_000_000_000_000 ? value : value * 1000
                  const d = new Date(ms)
                  return isNaN(d.getTime()) ? undefined : d
                }

                const dateValue = parseTimestampToDate(field.value as unknown as number | undefined)

                return (
                  <FormItem>
                    <FormControl>
                      <DatePicker
                        date={dateValue}
                        onDateChange={d => field.onChange(d ? Math.floor(d.getTime() / 1000) : 0)}
                        placeholder='Chọn ngày'
                        disabled={isLoading}
                        className='w-full'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
