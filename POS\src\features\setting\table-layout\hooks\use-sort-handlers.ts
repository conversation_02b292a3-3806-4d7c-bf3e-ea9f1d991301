import { toast } from 'sonner'

import type { TableLayoutItem } from '../data/table-layout-types'
import { useUpdateAreaSort } from './use-area-sort'

interface UseSortHandlersProps {
  selectedAreaId: string
  localTables: TableLayoutItem[]
  setLocalTables: React.Dispatch<React.SetStateAction<TableLayoutItem[]>>
  setIsSortModalOpen: (open: boolean) => void
}

export const useSortHandlers = ({
  selectedAreaId,
  localTables: _localTables,
  setLocalTables,
  setIsSortModalOpen
}: UseSortHandlersProps) => {
  const { updateAreaSort, isUpdating: isUpdatingSort } = useUpdateAreaSort()

  const handleSortModalSave = async (sortedTables: TableLayoutItem[]) => {
    if (!selectedAreaId) {
      toast.error('Không tìm thấy khu vực được chọn')
      return
    }

    try {
      const sortData = sortedTables.map((table, index) => ({
        id: table.id,
        area_id: table.area_uid || '',
        area_name: table.table_name,
        description: '',
        extra_data: {},
        active: table.active,
        revision: 1,
        sort: index + 1,
        store_uid: table.store_uid,
        company_uid: table.company_uid,
        brand_uid: table.brand_uid,
        is_fabi: 1,
        created_by: 'system',
        created_at: table.created_at,
        updated_at: table.updated_at,
        list_table_id: []
      }))

      await updateAreaSort(sortData)

      // Update local state
      setLocalTables(prev =>
        prev.map(table => {
          const sortedTable = sortedTables.find(st => st.id === table.id)
          return sortedTable ? { ...table, sort: sortedTable.sort } : table
        })
      )

      setIsSortModalOpen(false)
      toast.success('Đã cập nhật thứ tự bàn thành công')
    } catch (error) {
      console.error('Lỗi khi cập nhật thứ tự bàn:', error)
      toast.error('Không thể cập nhật thứ tự bàn. Vui lòng thử lại.')
    }
  }

  return {
    handleSortModalSave,
    isUpdatingSort
  }
}
