import { X, Upload } from 'lucide-react'

import { Button } from '@/components/ui/button'

import { PosModal } from '@/components/pos/modal'

interface ValidationError {
  row: number
  message: string
}

interface ValidationErrorModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  errors: ValidationError[]
  onRetry: () => void
  onClose?: () => void
  storeName?: string
}

export function ValidationErrorModal({
  open,
  onOpenChange,
  errors,
  onRetry,
  onClose,
  storeName = 'cửa hàng'
}: ValidationErrorModalProps) {
  const handleRetry = () => {
    // Close modal and trigger retry
    onOpenChange(false)
    onRetry()
  }

  // Get the first error to display as main message
  const firstError = errors[0]
  const mainMessage = firstError
    ? `Dòng ${firstError.row}: ${firstError.message}`
    : 'Dữ liệu không hợp lệ'

  const handleModalClose = (isOpen: boolean) => {
    if (!isOpen) {
      onClose?.()
    }
    onOpenChange(isOpen)
  }

  return (
    <PosModal
      title='Lỗi dữ liệu'
      open={open}
      onOpenChange={handleModalClose}
      onCancel={() => onClose?.()}
      onConfirm={() => {}}
      hideButtons={true}
      maxWidth='sm:max-w-md'
      centerTitle={true}
    >
      <div className='flex flex-col items-center space-y-6 py-4'>
        {/* Error Icon */}
        <div className='flex h-16 w-16 items-center justify-center rounded-full bg-red-100'>
          <X className='h-8 w-8 text-red-600' />
        </div>

        {/* Title */}
        <div className='text-center'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Thêm kênh bán hàng cho {storeName}
          </h3>
        </div>

        {/* Main Error Message */}
        <div className='text-center'>
          <p className='font-medium text-red-600'>{mainMessage}</p>
        </div>

        {/* Action Button */}
        <Button
          onClick={handleRetry}
          className='flex items-center gap-2 bg-blue-600 hover:bg-blue-700'
        >
          <Upload className='h-4 w-4' />
          Tải file lên
        </Button>
      </div>
    </PosModal>
  )
}
