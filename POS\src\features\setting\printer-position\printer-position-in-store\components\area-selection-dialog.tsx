import { useEffect, useState } from 'react'

import type { Area } from '@/lib/api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'

interface AreaSelectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  areas: Area[]
  selectedAreaIds: string[] // This will contain area_id codes like "AREA-XXXX"
  onConfirm: (selectedIds: string[]) => void
  onCancel: () => void
}

export function AreaSelectionDialog({
  open,
  onOpenChange,
  areas,
  selectedAreaIds,
  onConfirm,
  onCancel
}: AreaSelectionDialogProps) {
  const [tempSelectedAreaIds, setTempSelectedAreaIds] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const uniqueAreas = areas.filter((area, index, self) => index === self.findIndex(s => s.id === area.id))

  useEffect(() => {
    if (open) {
      setTempSelectedAreaIds([...selectedAreaIds])
    }
  }, [open, selectedAreaIds])

  const selectedAreas = uniqueAreas.filter(
    area =>
      tempSelectedAreaIds.includes(area.area_id) && area.area_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const unselectedAreas = uniqueAreas.filter(
    area =>
      !tempSelectedAreaIds.includes(area.area_id) && area.area_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleConfirm = () => {
    onConfirm(tempSelectedAreaIds)
    setSearchTerm('')
  }

  const handleCancel = () => {
    setTempSelectedAreaIds([])
    setSearchTerm('')
    onCancel()
  }

  const handleToggleArea = (areaIdCode: string, checked: boolean) => {
    if (checked) {
      setTempSelectedAreaIds(prev => (prev.includes(areaIdCode) ? prev : [...prev, areaIdCode]))
    } else {
      setTempSelectedAreaIds(prev => prev.filter(id => id !== areaIdCode))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl'>
        <div className='space-y-6 p-2'>
          <Input placeholder='Tìm kiếm khu vực...' value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />

          <ScrollArea className='h-[60vh] w-full'>
            <div className='space-y-4'>
              {selectedAreas.length > 0 && (
                <div>
                  <div className='mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600'>
                    <span>✓ Đã chọn {selectedAreas.length} khu vực</span>
                  </div>
                  <div className='space-y-3'>
                    {selectedAreas.map(area => (
                      <AreaItem key={area.id} area={area} isSelected={true} onToggle={handleToggleArea} />
                    ))}
                  </div>
                </div>
              )}

              {unselectedAreas.length > 0 && (
                <div>
                  <div className='mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600'>
                    <span>Còn lại {unselectedAreas.length} khu vực</span>
                  </div>
                  <div className='space-y-3'>
                    {unselectedAreas.map(area => (
                      <AreaItem key={area.id} area={area} isSelected={false} onToggle={handleToggleArea} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleConfirm}>Xong</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface AreaItemProps {
  area: Area
  isSelected: boolean
  onToggle: (areaIdCode: string, checked: boolean) => void
}

function AreaItem({ area, isSelected, onToggle }: AreaItemProps) {
  const code = area.area_id
  return (
    <div className='flex items-center space-x-3 rounded p-2 hover:bg-gray-50'>
      <Checkbox
        id={code}
        checked={isSelected}
        onCheckedChange={checked => onToggle(code, !!checked)}
        className='h-5 w-5'
      />
      <Label htmlFor={code} className='flex-1 cursor-pointer text-base'>
        {area.area_name}
      </Label>
    </div>
  )
}
