import { useState, useEffect } from 'react'

import { HelpCircle } from 'lucide-react'

import { Label, Input, Button, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui'

import { ChannelFormData } from '../../data'
import { DAYS_OF_WEEK, TIME_SLOTS } from '../../utils'
import type { FormMode } from '../forms/channel-form'

// Utility functions for number formatting
const formatNumberWithCommas = (value: number | string): string => {
  if (!value && value !== 0) return ''
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return ''
  return numValue.toLocaleString('vi-VN')
}

const parseFormattedNumber = (value: string): number => {
  if (!value) return 0
  // Remove commas and parse as number
  const cleanValue = value.replace(/,/g, '')
  const parsed = parseFloat(cleanValue)
  return isNaN(parsed) ? 0 : parsed
}

interface MarketingConfigSectionProps {
  formData: ChannelFormData
  onFormDataChange: (updates: Partial<ChannelFormData>) => void
  mode: FormMode
  isLoading?: boolean
}

export function MarketingConfigSection({ formData, onFormDataChange, isLoading = false }: MarketingConfigSectionProps) {
  const [costType, setCostType] = useState<'amount' | 'percentage'>('amount')
  const [displayValue, setDisplayValue] = useState<string>('')

  useEffect(() => {
    if (formData.voucherCostType) {
      const type = formData.voucherCostType === 'PERCENT' ? 'percentage' : 'amount'
      setCostType(type)
    }
  }, [formData.voucherCostType])

  useEffect(() => {
    // Update display value when formData changes
    if (costType === 'amount' && formData.marketingPartnerCost) {
      setDisplayValue(formatNumberWithCommas(formData.marketingPartnerCost))
    } else {
      setDisplayValue(formData.marketingPartnerCost?.toString() || '')
    }
  }, [formData.marketingPartnerCost, costType])

  const handleMarketingCostChange = (inputValue: string) => {
    if (costType === 'amount') {
      // For amount type, allow only numbers and commas
      const cleanInput = inputValue.replace(/[^\d,]/g, '')
      const numericValue = parseFormattedNumber(cleanInput)
      setDisplayValue(cleanInput)
      onFormDataChange({ marketingPartnerCost: numericValue })
    } else {
      // For percentage type, allow only numbers and decimal point
      const cleanInput = inputValue.replace(/[^\d.]/g, '')
      const numericValue = parseFloat(cleanInput) || 0
      if (numericValue > 100) {
        setDisplayValue('100')
        onFormDataChange({ marketingPartnerCost: 100 })
      } else {
        setDisplayValue(cleanInput)
        onFormDataChange({ marketingPartnerCost: numericValue })
      }
    }
  }

  const handleInputBlur = () => {
    // Reformat the display value on blur for amount type
    if (costType === 'amount' && formData.marketingPartnerCost) {
      setDisplayValue(formatNumberWithCommas(formData.marketingPartnerCost))
    }
  }

  const handleCostTypeChange = (type: 'amount' | 'percentage') => {
    setCostType(type)
    onFormDataChange({ voucherCostType: type === 'percentage' ? 'PERCENTAGE' : 'AMOUNT' })

    // Update display value based on new type
    if (type === 'amount' && formData.marketingPartnerCost) {
      setDisplayValue(formatNumberWithCommas(formData.marketingPartnerCost))
    } else {
      setDisplayValue(formData.marketingPartnerCost?.toString() || '')
    }
  }

  const handleDayToggle = (day: string) => {
    const currentDays = formData.marketingDays || []
    const updatedDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day]

    onFormDataChange({ marketingDays: updatedDays })
  }

  const handleTimeToggle = (time: string) => {
    const currentTimes = formData.marketingHours || []
    const updatedTimes = currentTimes.includes(time) ? currentTimes.filter(t => t !== time) : [...currentTimes, time]

    onFormDataChange({ marketingHours: updatedTimes })
  }

  return (
    <TooltipProvider>
      <div className='rounded-lg border p-6'>
        <h2 className='mb-6 text-xl font-semibold'>Cấu hình chi phí đối tác marketing</h2>

        <div className='space-y-6'>
          <div className='flex items-center gap-4'>
            <div className='flex w-[200px] items-center gap-2'>
              <Label htmlFor='marketing-cost' className='text-sm font-medium'>
                {costType === 'amount' ? 'Số tiền/đơn' : 'Phần trăm/đơn'} <span className='text-red-500'>*</span>
              </Label>
              {costType === 'percentage' && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-xs'>
                    <p className='text-sm'>Phần trăm chỉ áp dụng với chiết khấu toàn hóa đơn</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <div className='flex flex-1 items-center gap-2'>
              <Input
                id='marketing-cost'
                type='text'
                value={displayValue}
                onChange={e => handleMarketingCostChange(e.target.value)}
                onBlur={handleInputBlur}
                placeholder='0'
                className='flex-1'
                disabled={isLoading}
              />
              <div className='flex rounded-md border'>
                <Button
                  type='button'
                  variant={costType === 'amount' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => handleCostTypeChange('amount')}
                  disabled={isLoading}
                  className='rounded-r-none border-r px-3 py-1 text-sm'
                >
                  đ
                </Button>
                <Button
                  type='button'
                  variant={costType === 'percentage' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => handleCostTypeChange('percentage')}
                  disabled={isLoading}
                  className='rounded-l-none px-3 py-1 text-sm'
                >
                  %
                </Button>
              </div>
            </div>
          </div>

          <div className='flex items-center gap-4'>
            <div className='flex w-[200px] items-center gap-2'>
              <Label className='text-sm font-medium'>Voucher cho chương trình</Label>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <p className='text-sm'>
                    Nếu có cấu hình voucher, POS sẽ yêu cầu phải nhập voucher mới được thanh toán!
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Input
              value={formData.voucherRunPartner}
              onChange={e => onFormDataChange({ voucherRunPartner: e.target.value })}
              placeholder='Nhập voucher'
              className='flex-1'
              disabled={isLoading}
            />
          </div>

          <div className='space-y-4'>
            <Label className='text-sm font-medium'>Ngày áp dụng hỗ trợ marketing</Label>
            <div className='flex gap-4'>
              <div className='flex flex-1 items-center gap-2'>
                <Label className='w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600'>
                  Ngày bắt đầu
                </Label>
                <input
                  type='date'
                  value={formData.marketingStartDate}
                  onChange={e => onFormDataChange({ marketingStartDate: e.target.value })}
                  className='border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                  style={{ colorScheme: 'light' }}
                  disabled={isLoading}
                />
              </div>
              <div className='flex flex-1 items-center gap-2'>
                <Label className='w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600'>
                  Ngày kết thúc
                </Label>
                <input
                  type='date'
                  value={formData.marketingEndDate}
                  onChange={e => onFormDataChange({ marketingEndDate: e.target.value })}
                  className='border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                  style={{ colorScheme: 'light' }}
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <Label className='text-sm font-medium'>Khung thời gian áp dụng hỗ trợ marketing</Label>

            <div className='space-y-4'>
              <div className='flex items-center gap-2'>
                <Label className='text-sm font-medium text-gray-500'>Chọn ngày</Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-xs'>
                    <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần</p>
                  </TooltipContent>
                </Tooltip>
              </div>

              <div className='flex gap-2'>
                {DAYS_OF_WEEK.map(day => (
                  <Button
                    key={day.value}
                    type='button'
                    variant={formData.marketingDays?.includes(day.value) ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => handleDayToggle(day.value)}
                    disabled={isLoading}
                    className='flex-1'
                  >
                    {day.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <div className='flex items-center gap-2'>
              <Label className='text-sm font-medium text-gray-500'>Chọn giờ</Label>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <div className='grid grid-cols-6 gap-2'>
              {TIME_SLOTS.map(time => (
                <Button
                  key={time.value}
                  type='button'
                  variant={formData.marketingHours?.includes(time.value) ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => handleTimeToggle(time.value)}
                  disabled={isLoading}
                  className='text-xs'
                >
                  {time.value}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
