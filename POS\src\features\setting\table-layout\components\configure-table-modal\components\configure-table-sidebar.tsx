import React from 'react'

import { TriangleUpIcon, TriangleDownIcon } from '@radix-ui/react-icons'

interface Area {
  id: string
  area_name: string
  sort?: number
}

interface Table {
  id: string
  area_uid: string
}

interface ConfigureTableSidebarProps {
  areas: Area[]
  tables: Table[]
  selectedAreaId: string
  isLoadingTables: boolean
  onAreaSelect: (areaId: string) => void
}

export const ConfigureTableSidebar: React.FC<ConfigureTableSidebarProps> = ({
  areas,
  selectedAreaId,
  isLoadingTables,
  onAreaSelect
}) => {
  if (isLoadingTables) {
    return (
      <div className='w-80 overflow-y-auto border-r bg-gray-50'>
        <div className='p-4 text-center text-gray-500'>Đang tải dữ liệu bàn...</div>
      </div>
    )
  }

  // Sort areas by sort field
  const sortedAreas = [...areas].sort((a, b) => {
    const sortA = a.sort ?? 0
    const sortB = b.sort ?? 0
    return sortA - sortB
  })

  return (
    <div className='w-80 overflow-y-auto border-r bg-gray-50'>
      {sortedAreas.map(area => {
        const isSelected = selectedAreaId === area.id

        return (
          <div
            key={area.id}
            className={`cursor-pointer border-b transition-colors ${
              isSelected ? 'border-blue-200 bg-blue-50' : 'hover:bg-gray-100'
            }`}
            onClick={() => onAreaSelect(area.id)}
          >
            <div className='flex items-center gap-3 p-4'>
              <div className='flex flex-col items-center justify-center'>
                <TriangleUpIcon className={`h-4 w-4 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />
                <TriangleDownIcon className={`h-4 w-4 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />
              </div>

              <span className={`text-sm font-medium ${isSelected ? 'text-blue-900' : 'text-gray-700'}`}>
                {area.area_name}
              </span>
            </div>
          </div>
        )
      })}
    </div>
  )
}
