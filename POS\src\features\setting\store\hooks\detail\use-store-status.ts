import { useMutation, useQueryClient } from '@tanstack/react-query'

import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { updateStoreStatus } from '@/lib/stores-api'
import type { ApiStore } from '@/lib/stores-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UseStoreStatusProps {
  onSuccess?: () => void
}

export const useStoreStatus = ({ onSuccess }: UseStoreStatusProps = {}) => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const { mutate: updateStatus, isPending: isUpdating } = useMutation({
    mutationFn: async (storeData: ApiStore) => {
      return updateStoreStatus(storeData)
    },
    onSuccess: (_data, variables) => {
      // Invalidate stores list to refresh data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.STORES_LIST]
      })

      // Also invalidate store detail data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.STORES_DETAIL]
      })

      const statusText = variables.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`Đã ${statusText} cửa hàng "${variables.store_name}" thành công`)

      // Navigate back to stores list and reload page
      navigate({ to: '/setting/store' }).then(() => {
        // Reload the page to ensure fresh data
        window.location.reload()
      })

      onSuccess?.()
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật trạng thái cửa hàng')
    }
  })

  const handleToggleStatus = (storeData: ApiStore) => {
    const updatedStore = {
      ...storeData,
      active: storeData.active === 1 ? 0 : 1
    }

    updateStatus(updatedStore)
  }

  return {
    handleToggleStatus,
    isUpdating
  }
}
