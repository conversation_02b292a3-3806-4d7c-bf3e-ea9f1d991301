import type { UseFormReturn } from 'react-hook-form'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Checkbox } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface OrderReportSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function OrderReportSection({ form, isLoading = false }: OrderReportSectionProps) {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Báo cáo lượt order</h2>
        <div className='space-y-2 text-sm text-gray-600'>
          <p>Báo cáo được ghi nhận từ thời điểm được kích hoạt.</p>
          <p>Bạn cần đăng nhập lại CMS để sử dụng đầy đủ tính năng sau kích hoạt.</p>
        </div>
      </div>

      <div className='space-y-4'>
        {/* Kích hoạt báo cáo lượt order */}
        <FormField
          control={form.control}
          name='enable_turn_order_report'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Kích hoạt báo cáo lượt order</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tìm kiếm chi tiết món */}
        <FormField
          control={form.control}
          name='change_log_detail'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Tìm kiếm chi tiết món</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
