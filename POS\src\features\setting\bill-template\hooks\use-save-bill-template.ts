import { useMutation, useQueryClient } from '@tanstack/react-query'

import { billTemplateApi } from '@/lib/bill-template-api'

import type { SaveBillTemplateRequest, SaveBillTemplateResponse } from '../types'

interface UseSaveBillTemplateOptions {
  onSuccess?: (data: SaveBillTemplateResponse) => void
  onError?: (error: Error) => void
}

export const useSaveBillTemplate = (options?: UseSaveBillTemplateOptions) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: SaveBillTemplateRequest) => {
      const result = await billTemplateApi.saveBillTemplate(payload)
      return result
    },
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: ['bill-template']
      })

      options?.onSuccess?.(data)
    },
    onError: (error: Error) => {
      options?.onError?.(error)
    }
  })
}
