import type { LoyaltySettingResponse, PosParentSettings, SettingConfigResponse } from '@/types/api/crm'

import type { CrmSettingsFormValues } from '@/features/crm/settings/data'

import { crmApi } from './crm-api'

/**
 * CRM Settings API Service
 */
export const crmSettingsApi = {
  /**
   * Get CRM settings data
   */
  getCrmSettings: async (pos_parent: string): Promise<PosParentSettings> => {
    try {
      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.get(`/settings/get-pos-parent?${queryParams.toString()}`)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from CRM settings API')
      }

      return response.data as PosParentSettings
    } catch (error) {
      console.error('Error fetching CRM settings:', error)
      throw error
    }
  },

  /**
   * Update brand configuration
   */
  updateBrandConfig: async (pos_parent: string, data: any): Promise<any> => {
    try {
      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.post(`/settings/update_pos_parent?${queryParams.toString()}`, {
        json_update: JSON.stringify(data)
      })

      return response.data
    } catch (error) {
      console.error('Error updating brand config:', error)
      throw error
    }
  },

  /**
   * Update setting configuration (VAT, service charge, email alerts)
   */
  updateSettingConfig: async (pos_parent: string, data: any): Promise<SettingConfigResponse> => {
    try {
      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.post(`/settings/update-setting-config?${queryParams.toString()}`, data)

      return response.data as SettingConfigResponse
    } catch (error) {
      console.error('Error updating setting config:', error)
      throw error
    }
  },

  /**
   * Update loyalty program settings
   */
  updateLoyaltySettings: async (pos_parent: string, data: any): Promise<LoyaltySettingResponse> => {
    try {
      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.post(`/settings/update-setting-loyalty?${queryParams.toString()}`, data)

      return response.data as LoyaltySettingResponse
    } catch (error) {
      console.error('Error updating loyalty settings:', error)
      throw error
    }
  },

  /**
   * Update cheat detection configuration
   */
  updateCheatConfig: async (pos_parent: string, data: any): Promise<any> => {
    try {
      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.post(`/settings/update-cheat-config?${queryParams.toString()}`, data)

      return response.data
    } catch (error) {
      console.error('Error updating cheat config:', error)
      throw error
    }
  }
}

/**
 * Helper function to transform API response to form data
 */
export const transformApiResponseToFormData = (apiResponse: PosParentSettings): Partial<CrmSettingsFormValues> => {
  const data = apiResponse

  // Parse email list
  const emailList = data.Manager_Email_List
    ? data.Manager_Email_List.split(',')
        .map(email => email.trim())
        .filter(email => email)
    : []

  return {
    brandName: data.name,
    bannerImage: data.image,
    logo: data.Logo_Image,
    hotline: data.Hotline,
    email: emailList,
    serviceCharge: 0,
    vat: 0,
    enablePointAccumulation: data.Using_Cloud_Loyalty === 1,
    enableAccountBalanceAlert: data.Limit_Eat_Count_Per_Day > 0,
    balanceThreshold: data.Limit_Eat_Count_Per_Day,
    enableAccountBalanceAlertVND: data.Limit_Pay_Amount_Per_Day > 0,
    balanceThresholdVND: data.Limit_Pay_Amount_Per_Day
  }
}
