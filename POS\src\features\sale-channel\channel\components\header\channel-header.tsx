import { IconPlus } from '@tabler/icons-react'

import { Button } from '@/components/ui/button'

import { ChannelActionsDropdown } from './channel-actions-dropdown'
import { SourceSelect } from './source-select'
import { StoreSelect } from './store-select'

interface Store {
  id: string
  name: string
}

interface SourceOption {
  id: string
  value: string
  label: string
}

interface ChannelHeaderProps {
  selectedStore: string
  onStoreChange: (value: string) => void
  selectedSource: string
  onSourceChange: (value: string) => void
  stores?: Store[]
  sourceOptions?: SourceOption[]
  storesLoading?: boolean
  sourcesLoading?: boolean
  onCreateChannel: () => void
  onCopyChannels: () => void
  onImportFromFile: () => void
}

export function ChannelHeader({
  selectedStore,
  onStoreChange,
  selectedSource,
  onSourceChange,
  stores,
  sourceOptions,
  storesLoading,
  sourcesLoading,
  onCreateChannel,
  onCopyChannels,
  onImportFromFile
}: ChannelHeaderProps) {
  return (
    <div className='mb-4 flex items-center justify-between'>
      {/* Filters Section */}
      <div className='flex items-center gap-4'>
        <h2 className='text-xl font-semibold'>Kênh bán hàng</h2>
        <StoreSelect value={selectedStore} onValueChange={onStoreChange} stores={stores} isLoading={storesLoading} />
        <SourceSelect
          value={selectedSource}
          onValueChange={onSourceChange}
          sourceOptions={sourceOptions}
          isLoading={sourcesLoading}
        />
      </div>

      {/* Actions Section */}
      <div className='flex items-center gap-2'>
        <ChannelActionsDropdown onCopyChannels={onCopyChannels} onImportFromFile={onImportFromFile} />
        <Button size='sm' onClick={onCreateChannel}>
          <IconPlus className='mr-2 h-4 w-4' />
          Thêm kênh bán hàng
        </Button>
      </div>
    </div>
  )
}
