import { useMutation, useQueryClient } from '@tanstack/react-query'

import type { CreateExtraPointRequest, CreateExtraPointParams } from '@/types/api/crm'

import { extraPointApi } from '@/lib/api/crm/extra-point-api'

import { CRM_QUERY_KEYS } from '@/constants/crm'

interface CreateExtraPointMutationData {
  data: CreateExtraPointRequest
  params: CreateExtraPointParams
}

export function useCreateExtraPoint() {
  const queryClient = useQueryClient()

  return useMutation<any, Error, CreateExtraPointMutationData>({
    mutationFn: async ({ data, params }: CreateExtraPointMutationData) => {
      return await extraPointApi.create(data, params)
    },
    onSuccess: () => {
      // Invalidate and refetch extra point list
      queryClient.invalidateQueries({ queryKey: ['crm', CRM_QUERY_KEYS.EXTRA_POINT] })
    }
  })
}
