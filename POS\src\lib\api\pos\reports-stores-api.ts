import type { GetReportsStoresParams, ReportsStoresResponse } from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Get reports stores data
 */
export const getReportsStores = async (
  params: GetReportsStoresParams
): Promise<ReportsStoresResponse> => {
  const response = await apiClient.get<ReportsStoresResponse>('/v1/reports/sale-summary/stores', {
    params: {
      brand_uid: params.brand_uid,
      company_uid: params.company_uid,
      list_store_uid: params.list_store_uid,
      start_date: params.start_date,
      end_date: params.end_date,
      store_open_at: params.store_open_at ?? 0,
      limit: params.limit ?? 5
    }
  })

  return response.data
}
