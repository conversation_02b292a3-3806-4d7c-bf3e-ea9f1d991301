import { createContext, useContext, ReactNode } from 'react'

import type { ServiceChargeFormData } from '../hooks'

interface ServiceChargeFormContextType {
  formData: ServiceChargeFormData
  updateFormData: (updates: Partial<ServiceChargeFormData>) => void
  handleBack: () => void
  handleSave: () => void
  isFormValid: boolean
  isLoading: boolean
  isEditMode: boolean
}

const ServiceChargeFormContext = createContext<ServiceChargeFormContextType | undefined>(undefined)

interface ServiceChargeFormProviderProps {
  children: ReactNode
  value: ServiceChargeFormContextType
}

export function ServiceChargeFormProvider({ children, value }: ServiceChargeFormProviderProps) {
  return <ServiceChargeFormContext.Provider value={value}>{children}</ServiceChargeFormContext.Provider>
}

export function useServiceChargeFormContext() {
  const context = useContext(ServiceChargeFormContext)
  if (context === undefined) {
    throw new Error('useServiceChargeFormContext must be used within a ServiceChargeFormProvider')
  }
  return context
}

export function useServiceChargeFormData() {
  const { formData, updateFormData } = useServiceChargeFormContext()
  return { formData, updateFormData }
}

export function useServiceChargeFormActions() {
  const { handleBack, handleSave } = useServiceChargeFormContext()
  return { handleBack, handleSave }
}

export function useServiceChargeFormStatus() {
  const { isFormValid, isLoading, isEditMode } = useServiceChargeFormContext()
  return { isFormValid, isLoading, isEditMode }
}
