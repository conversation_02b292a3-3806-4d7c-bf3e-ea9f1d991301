import { salesApiCore } from './sales-api-core'
import { salesApiVat } from './sales-api-vat'
import { salesApiVoucher } from './sales-api-voucher'
import { salesCacheUtils } from './sales-cache'

// Re-export all types
export * from './sales-types'

/**
 * Unified Sales API
 * Combines all sales-related API functions into a single interface
 */
export const salesApi = {
  // Core sales functions
  ...salesApiCore,

  // Voucher-related functions
  ...salesApiVoucher,

  // VAT-related functions
  ...salesApiVat,

  // Cache utilities
  clearCache: salesCacheUtils.clear,
  getCacheStats: salesCacheUtils.getStats,
}

export default salesApi
