import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import {
  type GetPrinterPositionsParams,
  type PrinterPosition,
  printerPositionApi,
  type PrinterPositionsListResponse
} from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface PrinterPositionsWithPagination {
  data: PrinterPosition[]
  total: number
  hasNextPage: boolean
}

export interface UsePrinterPositionsInStoreDataOptions {
  params?: Partial<GetPrinterPositionsParams>
  enabled?: boolean
}

export const usePrinterPositionsInStoreData = (options: UsePrinterPositionsInStoreDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetPrinterPositionsParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    ...params
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.PRINTER_POSITIONS_IN_STORE, dynamicParams],
    queryFn: async (): Promise<PrinterPositionsWithPagination> => {
      const response = (await printerPositionApi.getPrinterPositions(dynamicParams)) as PrinterPositionsListResponse

      const data = Array.isArray(response.data) ? response.data : []
      const total = response.total_item || 0
      const currentPage = dynamicParams.page || 1
      const limit = dynamicParams.limit || 10
      const hasNextPage = currentPage * limit < total

      return {
        data,
        total,
        hasNextPage
      }
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 0,
    gcTime: 0,
    retry: false,
    initialData: { data: [], total: 0, hasNextPage: false }
  })
}
