import React, { useState, useCallback } from 'react'

import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter
} from '@dnd-kit/core'

import type { TableLayoutItem } from '../../data/table-layout-types'
import { TableItem } from './table-item'

interface TableLayoutCanvasProps {
  tables: TableLayoutItem[]
  onTableSelect?: (table: TableLayoutItem) => void
  selectedTableId?: string
  onTablePositionUpdate?: (tableId: string, newPosition: { x: number; y: number }) => void
  storeUid?: string
}

export const TableLayoutCanvas: React.FC<TableLayoutCanvasProps> = ({
  tables,
  onTableSelect,
  selectedTableId,
  onTablePositionUpdate,
  storeUid
}) => {
  const [activeTable, setActiveTable] = useState<TableLayoutItem | null>(null)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5
      }
    })
  )

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event
      const table = tables.find(t => t.id === active.id)
      if (table) {
        setActiveTable(table)
      }
    },
    [tables]
  )

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over, delta } = event
      const activeTable = tables.find(t => t.id === active.id)

      if (activeTable && (delta.x !== 0 || delta.y !== 0) && onTablePositionUpdate) {
        const currentX = activeTable.position?.x || activeTable.position_x || 0
        const currentY = activeTable.position?.y || activeTable.position_y || 0

        const overTable = over ? tables.find(t => t.id === over.id) : null

        if (overTable && activeTable.id !== overTable.id) {
          const overX = overTable.position?.x || overTable.position_x || 0
          const overY = overTable.position?.y || overTable.position_y || 0

          onTablePositionUpdate(activeTable.id, { x: overX, y: overY })
          onTablePositionUpdate(overTable.id, { x: currentX, y: currentY })
        } else {
          const newX = currentX + delta.x
          const newY = currentY + delta.y

          const tableWidth = activeTable.size?.width || 180
          const tableHeight = 150
          const semiCircleOffset = 18

          const canvasWidth = 1200
          const canvasHeight = 600

          const boundedX = Math.max(semiCircleOffset, Math.min(newX, canvasWidth - tableWidth - semiCircleOffset))
          const boundedY = Math.max(semiCircleOffset, Math.min(newY, canvasHeight - tableHeight - semiCircleOffset))

          onTablePositionUpdate(activeTable.id, { x: boundedX, y: boundedY })
        }
      }

      setActiveTable(null)
    },
    [tables, onTablePositionUpdate]
  )

  const handleTableClick = useCallback(
    (table: TableLayoutItem) => {
      onTableSelect?.(table)
    },
    [onTableSelect]
  )

  return (
    <div className='relative h-[600px] w-full overflow-auto rounded-lg border bg-gray-50'>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className='relative h-full w-full min-w-[1200px]'>
          {tables.map(table => (
            <TableItem
              key={table.id}
              table={table}
              isSelected={selectedTableId === table.id}
              onClick={() => handleTableClick(table)}
              storeUid={storeUid}
            />
          ))}
        </div>
        <DragOverlay adjustScale={false} dropAnimation={null}>
          {activeTable && <TableItem table={activeTable} isSelected={false} isDragging={false} isOverlay />}
        </DragOverlay>
      </DndContext>
    </div>
  )
}
