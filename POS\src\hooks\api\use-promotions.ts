import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import {
  fetchPromotions,
  deletePromotion,
  createPromotion,
  updatePromotion,
  type PromotionsApiParams,
  type ApiPromotion,
  type CreatePromotionRequest,
  type UpdatePromotionRequest
} from '@/lib/promotion-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import type { Promotion } from '@/features/sale/promotion/data'

export interface UsePromotionsDataOptions {
  params?: Partial<PromotionsApiParams>
  enabled?: boolean
  searchTerm?: string
  storeUid?: string
}

interface ApiError extends Error {
  response?: {
    data?: {
      message?: string
    }
  }
}

const convertToTableFormat = (apiPromotion: ApiPromotion): Promotion => {
  if (!apiPromotion.promotion_id || !apiPromotion.promotion_name) {
    throw new Error('Invalid promotion data: missing promotion_id or promotion_name')
  }

  // Use list_data for new API format, fallback to promotions for old format
  const promotionDetails = apiPromotion.list_data || apiPromotion.promotions || []

  if (!Array.isArray(promotionDetails)) {
    throw new Error('Invalid promotion data: promotion details must be an array')
  }

  // For new API format, we don't have store_name in the response
  // We'll need to get store names from stores data separately
  const storeUids = promotionDetails.map(p => p.store_uid)
  const storeNames = storeUids.length > 0 ? `${storeUids.length} cửa hàng` : 'Không có cửa hàng'
  const activeStores = promotionDetails.filter(p => p.active === 1)

  // Get latest timestamp from promotion details
  const timestamps = promotionDetails.map(item => item.created_at).filter(Boolean)
  const latestTimestamp = timestamps.length > 0 ? Math.max(...timestamps) : Date.now() / 1000

  return {
    code: apiPromotion.promotion_id,
    name: apiPromotion.promotion_name,
    store: storeNames || 'N/A',
    totalStores: promotionDetails.length,
    isActive: activeStores.length > 0,
    createdAt: new Date(latestTimestamp * 1000),
    originalData: {
      promotion_id: apiPromotion.promotion_id,
      promotion_name: apiPromotion.promotion_name,
      partner_auto_gen: apiPromotion.partner_auto_gen || 0,
      array_agg: apiPromotion.array_agg || [],
      ids_same_promotion: apiPromotion.ids_same_promotion || [],
      promotions: promotionDetails.map(detail => ({
        active: detail.active,
        created_at: detail.created_at,
        store_name: detail.store_name || '',
        store_uid: detail.store_uid,
        promotion_uid: detail.id || ''
      }))
    }
  }
}

interface NestedResponseData {
  data: ApiPromotion[]
}

export const usePromotionsData = (options: UsePromotionsDataOptions = {}) => {
  const { params = {}, enabled = true, searchTerm, storeUid } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const dynamicParams: PromotionsApiParams = {
    company_uid: company?.id || '595e8cb4-674c-49f7-adec-826b211a7ce3',
    brand_uid: selectedBrand?.id || 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e',
    skip_limit: true,
    aggregate: undefined,
    ...params
  }

  if (storeUid && storeUid !== 'all') {
    dynamicParams.store_uid = storeUid
  }

  if (searchTerm) {
    dynamicParams.search = searchTerm
  }

  const query = useQuery({
    queryKey: [QUERY_KEYS.PROMOTIONS_LIST, dynamicParams],
    queryFn: async () => {
      const response = await fetchPromotions(dynamicParams)

      let promotionsArray: ApiPromotion[] = []

      if (Array.isArray(response.data)) {
        promotionsArray = response.data
      } else if (
        response.data &&
        typeof response.data === 'object' &&
        'data' in response.data &&
        Array.isArray((response.data as NestedResponseData).data)
      ) {
        promotionsArray = (response.data as NestedResponseData).data
      } else if (response.data) {
        promotionsArray = [response.data as ApiPromotion]
      } else {
        promotionsArray = []
      }

      const tableData = promotionsArray.map(convertToTableFormat)

      return {
        data: promotionsArray,
        tableData,
        track_id: response.track_id || 'generated-track-id'
      }
    },
    enabled: enabled && !!(company?.id || params.company_uid) && !!(selectedBrand?.id || params.brand_uid),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })

  return {
    promotions: query.data?.tableData || [],
    apiPromotions: query.data?.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    isError: query.isError,
    isSuccess: query.isSuccess
  }
}

export const usePromotionById = (promotionId: string, enabled = true) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.PROMOTIONS_LIST, 'detail', promotionId, company?.id, selectedBrand?.id],
    queryFn: async () => {
      const companyUid = company?.id || '595e8cb4-674c-49f7-adec-826b211a7ce3'
      const brandUid = selectedBrand?.id || 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e'

      const response = await fetchPromotions({
        company_uid: companyUid,
        brand_uid: brandUid,
        page: 1,
        aggregate: true
      })

      const promotion = response.data.find(p => p.promotion_id === promotionId)
      if (!promotion) {
        throw new Error('Promotion not found')
      }

      return convertToTableFormat(promotion)
    },
    enabled: enabled && !!promotionId && !!company?.id && !!selectedBrand?.id,
    staleTime: 5 * 60 * 1000
  })
}

/**
 * Hook to delete a promotion
 */
export const useDeletePromotion = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (promotionUid: string) => {
      const companyUid = company?.id || '595e8cb4-674c-49f7-adec-826b211a7ce3'
      const brandUid = selectedBrand?.id || 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e'

      await deletePromotion(promotionUid, companyUid, brandUid)
    },
    onSuccess: () => {
      // Invalidate and refetch promotions list
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROMOTIONS_LIST]
      })
    }
  })

  return { deletePromotion: mutate, isDeleting: isPending }
}

/**
 * Hook to create a new promotion
 */
export const useCreatePromotion = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreatePromotionRequest) => {
      return await createPromotion(data)
    },
    onSuccess: _data => {
      toast.success('Tạo khuyến mãi thành công!')

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROMOTIONS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi tạo khuyến mãi'

      toast.error(errorMessage)
    }
  })

  return { createPromotion: mutate, isCreating: isPending }
}

/**
 * Hook to update an existing promotion
 */
export const useUpdatePromotion = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: UpdatePromotionRequest) => {
      return await updatePromotion(data)
    },
    onSuccess: _data => {
      toast.success('Cập nhật khuyến mãi thành công!')

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROMOTIONS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi cập nhật khuyến mãi'

      toast.error(errorMessage)
    }
  })

  return { updatePromotion: mutate, isUpdating: isPending }
}