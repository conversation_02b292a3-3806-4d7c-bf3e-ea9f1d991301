import React, { useState, useRef } from 'react'

import { Upload, Image as ImageIcon } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'

interface ImageSelectorDialogProps {
  isOpen: boolean
  onClose: () => void
  onImageSelect: (file: File, useFullSize: boolean) => void
  selectedImage?: string | null
}

export function ImageSelectorDialog({ isOpen, onClose, onImageSelect, selectedImage }: ImageSelectorDialogProps) {
  const [useFullSize, setUseFullSize] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(selectedImage || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  React.useEffect(() => {
    setPreviewUrl(selectedImage || null)
  }, [selectedImage])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }

  const handleDone = () => {
    if (fileInputRef.current?.files?.[0]) {
      onImageSelect(fileInputRef.current.files[0], useFullSize)
    }

    onClose()
  }

  const handleCancel = () => {
    setPreviewUrl(selectedImage || null)
    setUseFullSize(false)
    onClose()
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-lg font-semibold'>Chọn ảnh</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Options */}
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='use-full-size'
              checked={useFullSize}
              onCheckedChange={checked => setUseFullSize(checked as boolean)}
            />
            <Label htmlFor='use-full-size' className='text-sm font-medium'>
              Sử dụng ảnh với kích thước đầy đủ
            </Label>
          </div>

          {/* Image Upload Area */}
          <div className='space-y-2'>
            <div
              className='relative cursor-pointer rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-6 transition-colors hover:bg-gray-100'
              onClick={handleUploadClick}
            >
              {previewUrl ? (
                <div className='flex flex-col items-center justify-center space-y-2'>
                  <img src={previewUrl} alt='Preview' className='max-h-48 max-w-full rounded object-contain' />
                  <div className='flex items-center space-x-2 text-sm text-gray-600'>
                    <Upload className='h-4 w-4' />
                    <span>Click để thay đổi ảnh</span>
                  </div>
                </div>
              ) : (
                <div className='flex flex-col items-center justify-center space-y-2'>
                  <div className='relative'>
                    <ImageIcon className='h-16 w-16 text-gray-400' />
                    <div className='absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500'>
                      <span className='text-xs font-bold text-white'>+</span>
                    </div>
                  </div>
                  <div className='text-center text-sm text-gray-600'>
                    <p>Click để chọn ảnh</p>
                    <p className='text-xs'>hoặc kéo thả ảnh vào đây</p>
                  </div>
                </div>
              )}
            </div>

            <input ref={fileInputRef} type='file' accept='image/*' onChange={handleFileSelect} className='hidden' />
          </div>

          {/* Action Buttons */}
          <div className='flex justify-end space-x-2 pt-4'>
            <Button variant='outline' onClick={handleCancel} className='px-4 py-2'>
              Huỷ
            </Button>
            <Button
              onClick={handleDone}
              disabled={!previewUrl}
              className='bg-blue-600 px-4 py-2 text-white hover:bg-blue-700'
            >
              Xong
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
