import { useState, useRef, useEffect } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { Plus, Image as ImageIcon, HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  FormLabel,
  Button,
  Input,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import { type StoreFormValues } from '../../../../data'

interface BrandBuildingSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function BrandBuildingSection({ form, isLoading = false }: BrandBuildingSectionProps) {
  const [logoPreview, setLogoPreview] = useState<string>('')
  const [backgroundPreview, setBackgroundPreview] = useState<string>('')
  const [backgroundScreen2Preview, setBackgroundScreen2Preview] = useState<string>('')
  const logoInputRef = useRef<HTMLInputElement>(null)
  const backgroundInputRef = useRef<HTMLInputElement>(null)
  const backgroundScreen2InputRef = useRef<HTMLInputElement>(null)

  const handleLogoUpload = () => {
    logoInputRef.current?.click()
  }

  const handleBackgroundUpload = () => {
    backgroundInputRef.current?.click()
  }

  const handleBackgroundScreen2Upload = () => {
    backgroundScreen2InputRef.current?.click()
  }

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = e => {
        const result = e.target?.result as string
        setLogoPreview(result)
        form.setValue('logo', result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleBackgroundChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = e => {
        const result = e.target?.result as string
        setBackgroundPreview(result)
        form.setValue('background', result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleBackgroundScreen2Change = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = e => {
        const result = e.target?.result as string
        setBackgroundScreen2Preview(result)
        form.setValue('secondary_screen_image', result)
      }
      reader.readAsDataURL(file)
    }
  }

  useEffect(() => {
    const logoValue = form.getValues('logo')
    const backgroundValue = form.getValues('background')
    const backgroundScreen2Value = form.getValues('secondary_screen_image')

    if (logoValue && logoValue !== logoPreview) {
      setLogoPreview(logoValue)
    }
    if (backgroundValue && backgroundValue !== backgroundPreview) {
      setBackgroundPreview(backgroundValue)
    }
    if (backgroundScreen2Value && backgroundScreen2Value !== backgroundScreen2Preview) {
      setBackgroundScreen2Preview(backgroundScreen2Value)
    }
  }, [form.getValues('logo'), form.getValues('background'), form.getValues('secondary_screen_image')])

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Xây dựng thương hiệu</h2>
        <p className='text-sm text-gray-600'>
          Thông tin thương hiệu của bạn áp dụng cho giao diện của hóa đơn, đặt hẹn và tiếp thị.
        </p>
      </div>

      <div className='space-y-6'>
        {/* Logo */}
        <div className='space-y-4'>
          <div>
            <h3 className='text-base font-medium'>Logo</h3>
          </div>

          <div className='flex items-start gap-4'>
            <FormField
              control={form.control}
              name='logo'
              render={({ field: _ }) => (
                <FormItem>
                  <FormControl>
                    <div className='flex flex-col items-center'>
                      <div
                        className='flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100'
                        onClick={handleLogoUpload}
                      >
                        {logoPreview ? (
                          <img src={logoPreview} alt='Logo preview' className='h-full w-full rounded-lg object-cover' />
                        ) : (
                          <div className='flex flex-col items-center justify-center'>
                            <ImageIcon className='h-8 w-8 text-gray-400' />
                            <Plus className='mt-1 h-4 w-4 text-blue-500' />
                          </div>
                        )}
                      </div>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        className='mt-2'
                        onClick={handleLogoUpload}
                        disabled={isLoading}
                      >
                        Tải lên
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <p className='text-xs text-gray-500'>Kích thước tối thiểu 300 x 300 (px)</p>
        </div>

        {/* Hình nền trên thiết bị bán hàng */}
        <div className='space-y-4'>
          <div>
            <h3 className='text-base font-medium'>Hình nền trên thiết bị bán hàng</h3>
            <p className='text-sm text-gray-500'>
              Hình ảnh này xuất hiện trên màn hình 2 của các thiết bị pos 2 màn hình. Kích thước đề xuất 1920x1080 px
            </p>
          </div>

          <FormField
            control={form.control}
            name='background'
            render={({ field: _ }) => (
              <FormItem>
                <FormControl>
                  <div
                    className='flex h-48 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100'
                    onClick={handleBackgroundUpload}
                  >
                    {backgroundPreview ? (
                      <img
                        src={backgroundPreview}
                        alt='Background preview'
                        className='h-full w-full rounded-lg object-cover'
                      />
                    ) : (
                      <div className='flex flex-col items-center justify-center'>
                        <div className='rounded-full border-2 border-gray-300 p-4'>
                          <ImageIcon className='h-8 w-8 text-gray-400' />
                        </div>
                        <p className='mt-2 text-sm text-gray-500'>Tải lên một hình ảnh hỗ sơ</p>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className='flex justify-start'>
            <Button
              type='button'
              variant='outline'
              size='sm'
              onClick={handleBackgroundUpload}
              disabled={isLoading}
              className='flex items-center gap-2'
            >
              <Plus className='h-4 w-4' />
              Tải lên
            </Button>
          </div>
        </div>

        {/* Hình nền trên màn hình 2 */}
        <div className='space-y-4'>
          <div>
            <h3 className='text-base font-medium'>Hình nền trên màn hình 2</h3>
            <p className='text-sm text-gray-500'>
              Hình ảnh này xuất hiện trên màn hình 2 tại bước chốt bill. Kích thước đề xuất 1280x1080 px
            </p>
          </div>

          <FormField
            control={form.control}
            name='secondary_screen_image'
            render={({ field: _ }) => (
              <FormItem>
                <FormControl>
                  <div
                    className='flex h-48 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100'
                    onClick={handleBackgroundScreen2Upload}
                  >
                    {backgroundScreen2Preview ? (
                      <img
                        src={backgroundScreen2Preview}
                        alt='Background Screen 2 preview'
                        className='h-full w-full rounded-lg object-cover'
                      />
                    ) : (
                      <div className='flex flex-col items-center justify-center'>
                        <div className='rounded-lg border-2 border-gray-300 p-8'>
                          <ImageIcon className='h-8 w-8 text-gray-400' />
                        </div>
                        <Plus className='mt-2 h-6 w-6 rounded-full bg-blue-500 p-1 text-white' />
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Video màn hình 2 */}
        <div className='space-y-4'>
          <div>
            <h3 className='text-base font-medium'>Video màn hình 2</h3>
            <p className='text-sm text-gray-500'>
              Vui lòng sử dụng đường dẫn cho phép tải video trực tiếp (Direct Link). Dung lượng video khuyến nghị không
              quá 100MB. Bạn có thể tham khảo hướng dẫn lấy Direct Link từ Google Drive tại mục dưới đây
            </p>
          </div>

          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <div className='flex items-center gap-2'>
                <FormLabel className='text-sm font-medium text-gray-700'>Video url</FormLabel>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                    </TooltipTrigger>
                    <TooltipContent className='max-w-md'>
                      <div className='space-y-2 text-sm'>
                        <p className='font-medium'>Hướng dẫn lấy link direct cho video màn hình 2:</p>
                        <div className='space-y-1'>
                          <p>
                            <strong>Bước 1:</strong> Đăng nhập vào tài khoản Google Drive và tải lên video của bạn{' '}
                            <a
                              href='https://drive.google.com/drive/my-drive'
                              target='_blank'
                              rel='noopener noreferrer'
                              className='cursor-pointer text-blue-400 underline hover:text-blue-600'
                            >
                              Tại đây
                            </a>
                          </p>
                          <p>
                            <strong>Bước 2:</strong> Thiết lập chia sẻ video hiển thị với mọi người (General access:
                            Anyone with the link). Sau đó copy link
                          </p>
                          <p>
                            <strong>Bước 3:</strong> Truy cập trang tạo Direct Link của Google Drive{' '}
                            <a
                              href='https://sites.google.com/site/gdocs2direct/'
                              target='_blank'
                              rel='noopener noreferrer'
                              className='cursor-pointer text-blue-400 underline hover:text-blue-600'
                            >
                              Tại đây
                            </a>
                            . Sau đó nhập link vừa bạn copy. Bấm Create Direct Link. Bạn sẽ có Output link sử dụng tại
                            FABi
                          </p>
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className='col-span-9'>
              <FormField
                control={form.control}
                name='secondary_screen_video'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input placeholder='Điền link video cho màn hình 2' disabled={isLoading} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Hidden file inputs */}
      <input ref={logoInputRef} type='file' accept='image/*' onChange={handleLogoChange} className='hidden' />
      <input
        ref={backgroundInputRef}
        type='file'
        accept='image/*'
        onChange={handleBackgroundChange}
        className='hidden'
      />
      <input
        ref={backgroundScreen2InputRef}
        type='file'
        accept='image/*'
        onChange={handleBackgroundScreen2Change}
        className='hidden'
      />
    </div>
  )
}
