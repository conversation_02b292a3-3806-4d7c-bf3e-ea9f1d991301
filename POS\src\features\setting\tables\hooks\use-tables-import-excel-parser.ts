import { toast } from 'sonner'

export interface ParsedTableData {
  table_name: string
  area_name: string
  source_name?: string
  pre_order_items?: string
  description?: string
}

export function useTablesImportExcelParser() {
  const parseExcelFile = async (file: File): Promise<ParsedTableData[]> => {
    try {
      // Dynamic import for xlsx
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      // Parse data according to format: Row 1: headers, Row 2+: data
      const parsedTables: ParsedTableData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]

        // Skip empty rows
        if (!row || row.length === 0 || !row[0]) continue

        const table: ParsedTableData = {
          table_name: String(row[0] || '').trim(), // Column 1: Tên bàn
          area_name: String(row[1] || '').trim(), // Column 2: Khu vực
          source_name: row[2] ? String(row[2]).trim() : undefined, // Column 3: Nguồn
          pre_order_items: row[3] ? String(row[3]).trim() : undefined, // Column 4: Món đặt trước
          description: row[4] ? String(row[4]).trim() : undefined // Column 5: Mô tả
        }

        // Validate required fields
        if (!table.table_name) {
          toast.error(`Dòng ${i + 1}: Tên bàn không được để trống`)
          continue
        }

        if (!table.area_name) {
          toast.error(`Dòng ${i + 1}: Khu vực không được để trống`)
          continue
        }

        parsedTables.push(table)
      }

      if (parsedTables.length === 0) {
        toast.error('Không tìm thấy dữ liệu hợp lệ trong file')
        throw new Error('No valid data found')
      }

      return parsedTables
    } catch (error) {
      console.error('Error parsing Excel file:', error)
      if (error instanceof Error && error.message !== 'No valid data found') {
        toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      }
      throw error
    }
  }

  const downloadTemplate = () => {
    // Create template Excel file with proper headers
    const templateData = [
      ['Tên bàn', 'Khu vực', 'Nguồn', 'Món đặt trước', 'Mô tả'],
      ['Bàn 01', 'Tầng 1', 'Tại chỗ', '', 'Bàn gần cửa sổ'],
      ['Bàn 02', 'Tầng 1', 'Tại chỗ', '', 'Bàn góc'],
      ['Bàn 03', 'Tầng 2', 'Grab Food', '', 'Bàn VIP']
    ]

    // Dynamic import for xlsx
    import('xlsx').then(XLSX => {
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(templateData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Tables Template')
      XLSX.writeFile(workbook, 'import_tables_template.xlsx')
      toast.success('Đã tải file mẫu thành công!')
    })
  }

  return {
    parseExcelFile,
    downloadTemplate
  }
}
