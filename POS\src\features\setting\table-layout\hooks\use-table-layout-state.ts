import { useState, useRef } from 'react'

import type { TableLayoutItem } from '../data/table-layout-types'

export const useTableLayoutState = () => {
  const [selectedAreaId, setSelectedAreaId] = useState<string>('')
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [selectedTable, setSelectedTable] = useState<TableLayoutItem | null>(null)
  const [localTables, setLocalTables] = useState<TableLayoutItem[]>([])
  const [isSortModalOpen, setIsSortModalOpen] = useState<boolean>(false)
  const [isImportModalOpen, setIsImportModalOpen] = useState<boolean>(false)
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false)
  const [isEditTableModalOpen, setIsEditTableModalOpen] = useState<boolean>(false)
  const [isConfigureTableModalOpen, setIsConfigureTableModalOpen] = useState<boolean>(false)
  const [importedTableData, setImportedTableData] = useState<any[]>([])
  const lastTablesRef = useRef<string>('')

  return {
    selectedAreaId,
    setSelectedAreaId,
    selectedStoreId,
    setSelectedStoreId,
    selectedTable,
    setSelectedTable,
    localTables,
    setLocalTables,
    isSortModalOpen,
    setIsSortModalOpen,
    isImportModalOpen,
    setIsImportModalOpen,
    isPreviewModalOpen,
    setIsPreviewModalOpen,
    isEditTableModalOpen,
    setIsEditTableModalOpen,
    isConfigureTableModalOpen,
    setIsConfigureTableModalOpen,
    importedTableData,
    setImportedTableData,
    lastTablesRef
  }
}
