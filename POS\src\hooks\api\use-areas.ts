import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  areasApi,
  type Area,
  type AreasListParams,
  type CreateAreaRequest,
  type DeleteAreaParams,
  type DeleteAreasRequest,
  type BulkImportAreasRequest
} from '@/lib/api/pos/areas-api'
import { imagesApi } from '@/lib/api/pos/images-api'
import { apiClient } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseAreasDataOptions {
  storeUid?: string
  page?: number
  results_per_page?: number
}

/**
 * Hook to fetch areas data
 */
export const useAreasData = (options: UseAreasDataOptions = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const areasQuery = useQuery({
    queryKey: [QUERY_KEYS.AREAS_LIST, 'filter', options.storeUid, options.page, options.results_per_page],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''
      const storeUid = options.storeUid || ''

      if (!companyUid || !brandUid || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      const params: AreasListParams = {
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: storeUid,
        skip_limit: true,
        page: options.page,
        results_per_page: options.results_per_page
      }

      return await areasApi.getAreasList(params)
    },
    enabled: !!(company?.id && selectedBrand?.id && options.storeUid),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })

  return {
    ...areasQuery,
    data: areasQuery.data?.data || []
  }
}

/**
 * Hook to update an area
 */
export const useUpdateArea = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: Area) => {
      return await areasApi.updateArea(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_DETAIL]
      })
      toast.success('Cập nhật khu vực thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật khu vực'
      toast.error(errorMessage)
    }
  })

  return { updateArea: mutate, isUpdating: isPending }
}

/**
 * Hook to delete an area
 */
export const useDeleteArea = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: { id: string; storeUid: string }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      const params: DeleteAreaParams = {
        id: data.id,
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: data.storeUid
      }

      return await areasApi.deleteArea(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      toast.success('Xóa khu vực thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa khu vực'
      toast.error(errorMessage)
    }
  })

  return { deleteArea: mutate, isDeleting: isPending }
}

/**
 * Hook to toggle area active status
 */
export const useToggleAreaStatus = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (area: Area) => {
      // Toggle active status (0 -> 1, 1 -> 0)
      const newActiveStatus = area.active === 1 ? 0 : 1

      // Send the complete area object with toggled active status (like in the cURL)
      const updateData = {
        id: area.id,
        area_id: area.area_id,
        area_name: area.area_name,
        description: area.description || '',
        extra_data: area.extra_data || {},
        active: newActiveStatus,
        revision: area.revision,
        sort: area.sort,
        store_uid: area.store_uid,
        brand_uid: area.brand_uid,
        company_uid: area.company_uid,
        store_id: area.store_id || null,
        brand_id: area.brand_id || null,
        company_id: area.company_id || null,
        is_fabi: area.is_fabi,
        created_by: area.created_by,
        updated_by: area.updated_by || area.created_by,
        created_at: area.created_at,
        updated_at: area.updated_at,
        list_table_id: area.list_table_id || []
      }

      console.log('Sending area update:', updateData)
      console.log('Original area:', area)

      // Use direct API call instead of the typed updateArea method
      console.log('Making POST request to: /pos/v1/area')
      const response = await apiClient.post('/pos/v1/area', updateData)
      return response.data
    },
    onSuccess: updatedArea => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      const statusText = updatedArea.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`${statusText} khu vực thành công`)
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật trạng thái khu vực'
      toast.error(errorMessage)
    }
  })

  return { toggleAreaStatus: mutate, isToggling: isPending }
}

/**
 * Hook to delete multiple areas
 */
export const useDeleteAreas = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)

  const { mutate, isPending } = useMutation({
    mutationFn: async (params: { areaIds: string[]; storeUid: string }) => {
      const companyUid = company?.id || ''
      const brandUid = brands?.[0]?.id || ''

      if (!companyUid || !brandUid || !params.storeUid) {
        throw new Error('Missing required authentication information')
      }

      const deleteRequest: DeleteAreasRequest = {
        company_uid: companyUid,
        brand_uid: brandUid,
        list_id: params.areaIds,
        store_uid: params.storeUid
      }

      console.log('Deleting areas:', deleteRequest)
      return await areasApi.deleteAreas(deleteRequest)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      const count = variables.areaIds.length
      const message = count === 1 ? 'Xóa khu vực thành công' : `Xóa ${count} khu vực thành công`
      toast.success(message)
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa khu vực'
      toast.error(errorMessage)
    }
  })

  return { deleteAreas: mutate, isDeleting: isPending }
}

/**
 * Hook to fetch area detail
 */
export const useAreaDetail = (areaId: string, storeUid: string) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.AREAS_DETAIL, areaId, storeUid],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !areaId || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      return await areasApi.getAreaById(areaId, companyUid, brandUid, storeUid)
    },
    enabled: !!(company?.id && selectedBrand?.id && areaId && storeUid),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Generate a random area ID in the format AREA-XXXX
 */
const generateAreaId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = 'AREA-'
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Hook to create a new area
 */
export const useCreateArea = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: {
      area_name: string
      description?: string
      store_uid: string
      sort?: number
      backgroundFile?: File | null
    }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      // Generate area_id
      const areaId = generateAreaId()

      let imageUrl = ''

      // Step 1: Upload background image if provided
      if (data.backgroundFile) {
        const uploadResponse = await imagesApi.uploadImage(data.backgroundFile)
        imageUrl = uploadResponse.data.image_url
      }

      // Step 2: Create area with image URL in extra_data
      const areaData: CreateAreaRequest = {
        area_name: data.area_name,
        area_id: areaId,
        description: data.description,
        store_uid: data.store_uid,
        brand_uid: brandUid,
        company_uid: companyUid,
        sort: data.sort || 1,
        active: 1,
        extra_data: imageUrl ? { background: imageUrl } : undefined
      }

      return await areasApi.createArea(areaData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      toast.success('Tạo khu vực thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo khu vực'
      toast.error(errorMessage)
    }
  })

  return {
    createArea: mutate,
    isCreating: isPending
  }
}

/**
 * Hook to bulk import areas
 */
export const useBulkImportAreas = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (data: {
      storeUid: string
      areas: { area_name: string; description?: string; sort?: number; active?: number }[]
    }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      // Build the array of area objects as expected by the API
      const requestData: BulkImportAreasRequest = data.areas.map((area, index) => ({
        company_uid: companyUid,
        brand_uid: brandUid,
        area_name: area.area_name,
        area_id: generateAreaId(), // Generate unique area ID
        description: area.description || '',
        store_uid: data.storeUid,
        sort: index + 1 // Auto-increment sort order starting from 1
      }))

      return await areasApi.bulkImportAreas(requestData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi import khu vực'
      toast.error(errorMessage)
    }
  })

  return { mutateAsync, isPending }
}
