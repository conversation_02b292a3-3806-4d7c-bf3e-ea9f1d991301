import { StoreForm, ExpiredLicense<PERSON><PERSON>t, PageHeader, ActionButtons } from '../components/detail'
import { STORE_CONSTANTS } from '../data'
import { useStoreForm, useStoreSave, useCurrentStoreData, useStoreStatus } from '../hooks/detail'

interface StoreDetailPageProps {
  storeId?: string
}

export default function StoreDetailPage({ storeId }: StoreDetailPageProps) {
  const { form, isFormValid, isRequiredFieldsValid, isEditMode, isLoadingStoreData } = useStoreForm({ storeId })
  const { handleBack, handleSave, isCreating } = useStoreSave({
    formData: form.getValues(),
    isFormValid,
    storeId
  })

  const {
    data: currentStore,
    isExpired,
    isActive
  } = useCurrentStoreData({
    storeId,
    enabled: isEditMode
  })

  const { handleToggleStatus, isUpdating } = useStoreStatus()

  const pageTitle = isEditMode ? STORE_CONSTANTS.PAGE_TITLE_EDIT : STORE_CONSTANTS.PAGE_TITLE_CREATE
  const shouldShowExpiredAlert = isEditMode && isExpired

  const handleFormSubmit = () => {
    handleSave()
  }

  const handleStatusToggle = () => {
    if (!currentStore) return
    handleToggleStatus(currentStore)
  }

  const handleSaveClick = () => {
    form.handleSubmit(handleFormSubmit)()
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <ExpiredLicenseAlert isVisible={shouldShowExpiredAlert} />

      <PageHeader title={pageTitle} onBack={handleBack}>
        <ActionButtons
          isEditMode={isEditMode}
          isExpired={isExpired}
          isActive={isActive}
          isUpdating={isUpdating}
          onToggleStatus={handleStatusToggle}
          isRequiredFieldsValid={isRequiredFieldsValid}
          isCreating={isCreating}
          onSave={handleSaveClick}
        />
      </PageHeader>

      <div className='mx-auto max-w-4xl'>
        <StoreForm form={form} mode={isEditMode ? 'edit' : 'add'} isLoading={isLoadingStoreData} storeId={storeId} />
      </div>
    </div>
  )
}
