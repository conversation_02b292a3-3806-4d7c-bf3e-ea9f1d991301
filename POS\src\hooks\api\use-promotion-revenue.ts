import { useQuery } from '@tanstack/react-query'

import type { PromotionRevenueParams, PromotionRevenueResponse } from '@/types/api/revenue-promotion'

import { getPromotionRevenue } from '@/lib/api/pos/promotion-revenue-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UsePromotionRevenueOptions {
  startDate: number
  endDate: number
  selectedStoreIds: string[]
  companyUid: string
  brandUid: string
  enabled?: boolean
}

export const usePromotionRevenue = ({
  startDate,
  endDate,
  selectedStoreIds,
  companyUid,
  brandUid,
  enabled = true
}: UsePromotionRevenueOptions) => {
  const queryKey = [
    QUERY_KEYS.PROMOTIONS,
    'sale-summary',
    companyUid,
    brandUid,
    startDate,
    endDate,
    selectedStoreIds.sort().join(',')
  ]

  const queryFn = async (): Promise<PromotionRevenueResponse> => {
    if (!selectedStoreIds.length) {
      return { data: [], message: 'No stores selected', track_id: '' }
    }

    const params: PromotionRevenueParams = {
      brand_uid: brandUid,
      company_uid: companyUid,
      start_date: startDate,
      end_date: endDate,
      list_store_uid: selectedStoreIds.join(','),
      store_open_at: 0,
      by_days: 1
    }

    return getPromotionRevenue(params)
  }

  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && selectedStoreIds.length > 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })

  return {
    data: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasData: (query.data?.data || []).length > 0
  }
}
