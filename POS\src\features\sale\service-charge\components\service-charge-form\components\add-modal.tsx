import { useState, useEffect } from 'react'

import { useQuery } from '@tanstack/react-query'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import { PosModal } from '@/components/pos'
import { Button, Input, Label } from '@/components/ui'

import { GroupsSection } from './groups-section'
import { ItemsSection } from './items-section'
import { PackagesSection } from './packages-section'

interface AddModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  storeUid: string
  onSave: (selectedItems: string[], applyToAll: boolean, activeFilter: FilterType | null) => void
  initialApplyToAll?: boolean
  initialActiveFilter?: FilterType | null
  initialSelectedItems?: string[]
}

type FilterType = 'groups' | 'items' | 'packages'

export function AddModal({
  open,
  onOpenChange,
  storeUid,
  onSave,
  initialApplyToAll = false,
  initialActiveFilter = null,
  initialSelectedItems = []
}: AddModalProps) {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const [searchTerm, setSearchTerm] = useState('')
  const [applyToAll, setApplyToAll] = useState(false)
  const [activeFilter, setActiveFilter] = useState<FilterType | null>(null)
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const { data: sources = [], isLoading: isLoadingSources } = useQuery({
    queryKey: [QUERY_KEYS.SOURCES, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/mdata/v1/sources?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const { data: areas = [], isLoading: isLoadingAreas } = useQuery({
    queryKey: [QUERY_KEYS.AREAS, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/pos/v1/area?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const { data: tables = [], isLoading: isLoadingTables } = useQuery({
    queryKey: [QUERY_KEYS.TABLES, company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []
      const response = await api.get(
        `/pos/v1/table?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data.data || []
    },
    enabled: open && !!company?.id && !!selectedBrand?.id && !!storeUid
  })

  const isLoading = isLoadingSources || isLoadingAreas || isLoadingTables

  useEffect(() => {
    if (open) {
      setSearchTerm('')
      setApplyToAll(initialApplyToAll)
      setActiveFilter(initialActiveFilter || 'groups') // Use initial filter or default to 'groups'
      setSelectedItems(initialSelectedItems)
    }
  }, [open, initialApplyToAll, initialActiveFilter, initialSelectedItems])

  const handleFilterToggle = (filter: FilterType) => {
    setActiveFilter(prev => {
      const newFilter = prev === filter ? null : filter

      if (newFilter !== prev) {
        setSelectedItems([])
      }
      return newFilter
    })
  }

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => (prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]))
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const handleConfirm = () => {
    if (applyToAll) {
      onSave(['all'], true, null)
    } else {
      onSave(selectedItems, false, activeFilter)
    }
    onOpenChange(false)
  }

  return (
    <PosModal
      title='Áp dụng điều kiện mở rộng'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      cancelText='Hủy'
      confirmText='Lưu'
      maxWidth='sm:max-w-2xl'
      isLoading={isLoading}
      confirmDisabled={selectedItems.length === 0}
    >
      <div className='space-y-4'>
        <div className='flex items-center gap-4'>
          <Input
            placeholder='Tìm kiếm'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='flex-1'
          />
        </div>

        <div className='flex items-center gap-4'>
          <Label className='text-sm font-medium'>Áp dụng cho</Label>
          <div className='flex gap-2'>
            <Button
              type='button'
              variant={activeFilter === 'groups' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('groups')}
            >
              Nguồn
            </Button>
            <Button
              type='button'
              variant={activeFilter === 'items' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('items')}
            >
              Khu vực
            </Button>
            <Button
              type='button'
              variant={activeFilter === 'packages' ? 'default' : 'outline'}
              size='sm'
              onClick={() => handleFilterToggle('packages')}
            >
              Bàn
            </Button>
          </div>
        </div>

        <div className='space-y-2'>
          {activeFilter === 'groups' && (
            <GroupsSection
              itemTypes={sources as any[]}
              selectedItems={selectedItems}
              searchTerm={searchTerm}
              onItemToggle={handleItemToggle}
            />
          )}
          {activeFilter === 'items' && (
            <ItemsSection
              items={areas as any[]}
              selectedItems={selectedItems}
              searchTerm={searchTerm}
              isLoading={isLoading}
              onItemToggle={handleItemToggle}
            />
          )}
          {activeFilter === 'packages' && (
            <PackagesSection
              packages={tables as any[]}
              selectedItems={selectedItems}
              searchTerm={searchTerm}
              isLoading={isLoading}
              onItemToggle={handleItemToggle}
            />
          )}
        </div>
      </div>
    </PosModal>
  )
}
