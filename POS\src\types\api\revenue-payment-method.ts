export interface PaymentMethodRevenueListData {
  date: string
  tran_date: number
  total_bill: number
  total_amount: number
  total_count: number
  payment_fee_amount: number
}

export interface PaymentMethodRevenueData {
  total_bill: number
  total_amount: number
  total_count: number
  payment_method_id: string
  payment_method_name: string
  list_data: Array<PaymentMethodRevenueListData>
  total_payment_fee_amount: number
}

export interface PaymentMethodRevenueParams {
  brand_uid: string
  company_uid: string
  start_date: number
  end_date: number
  list_store_uid: string
  store_open_at: number
  by_days: number
  limit?: number
}

export interface PaymentMethodRevenueResponse {
  data: PaymentMethodRevenueData[]
  message: string
  track_id: string
}

export interface PaymentMethodDetail {
  payment_method_id: string
  total_bill: number
  total_amount: number
  total_payment_fee_amount: number
  total_count: number
}

export interface PaymentMethodDetailsItem {
  date: string
  tran_date: number
  total_sales: number
  discount_amount: number
  commission_amount: number
  peo_count: number
  revenue_net: number
  revenue_gross: number
  discount_extra_amount: number
  amount_discount_detail: number
  partner_marketing_amount: number
  voucher_amount: number
  vat_amount: number
  discount_vat_amount: number
  payment_methods: PaymentMethodDetail[]
  amount: number
  deduct_tax_amount: number
}

export interface PaymentMethodDetailsResponse {
  data: PaymentMethodDetailsItem[]
}

export interface PaymentMethodDetailsParams {
  company_uid: string
  brand_uid: string
  list_store_uid?: string
  start_date?: number
  end_date?: number
  store_open_at?: number
  by_days?: number
}
