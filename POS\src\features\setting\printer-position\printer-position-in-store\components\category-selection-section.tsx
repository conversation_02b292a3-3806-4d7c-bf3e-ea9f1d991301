import { useState } from 'react'

import { ChevronDown, ChevronUp } from 'lucide-react'

import type { ItemType } from '@/lib/item-types-api'

import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'

interface CategorySelectionSectionProps {
  categories: ItemType[]
  selectedCategories: string[]
  onToggleCategory: (categoryId: string, checked: boolean) => void
}

export function CategorySelectionSection({
  categories,
  selectedCategories,
  onToggleCategory
}: CategorySelectionSectionProps) {
  const [isSelectedCollapsed, setIsSelectedCollapsed] = useState(false)

  const selectedCategoriesData = categories.filter(category => selectedCategories.includes(category.id))
  const unselectedCategoriesData = categories.filter(category => !selectedCategories.includes(category.id))

  return (
    <div className='space-y-2'>
      {/* Categories List in single container */}
      <div className='rounded-md border'>
        {/* Selected Count Header with Collapse Toggle */}
        <div
          className='flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100'
          onClick={() => setIsSelectedCollapsed(!isSelectedCollapsed)}
        >
          <span>✓ Đã chọn {selectedCategories.length}</span>
          {selectedCategoriesData.length > 0 &&
            (isSelectedCollapsed ? <ChevronDown className='h-4 w-4' /> : <ChevronUp className='h-4 w-4' />)}
        </div>

        {/* Selected Categories Section with Scroll */}
        {selectedCategoriesData.length > 0 && !isSelectedCollapsed && (
          <div className='border-b'>
            <div className='p-4'>
              <div className='max-h-48 space-y-2 overflow-y-auto'>
                {selectedCategoriesData.map(category => (
                  <CategoryItem key={category.id} category={category} isSelected={true} onToggle={onToggleCategory} />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Unselected Categories Section */}
        {unselectedCategoriesData.length > 0 && (
          <div>
            {/* "Còn lại" header */}
            {selectedCategoriesData.length > 0 && (
              <div className='border-b bg-gray-50 p-3 text-sm text-gray-600'>
                <span>Còn lại {unselectedCategoriesData.length}</span>
              </div>
            )}
            <div className='p-4'>
              <div className='max-h-48 space-y-2 overflow-y-auto'>
                {unselectedCategoriesData.map(category => (
                  <CategoryItem key={category.id} category={category} isSelected={false} onToggle={onToggleCategory} />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Category Item Component
interface CategoryItemProps {
  category: ItemType
  isSelected: boolean
  onToggle: (categoryId: string, checked: boolean) => void
}

function CategoryItem({ category, isSelected, onToggle }: CategoryItemProps) {
  return (
    <div className='flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50'>
      <Checkbox
        id={category.id}
        checked={isSelected}
        onCheckedChange={checked => onToggle(category.id, !!checked)}
        className='h-4 w-4'
      />
      <Label htmlFor={category.id} className='flex-1 cursor-pointer text-sm leading-relaxed'>
        {category.item_type_name}
      </Label>
    </div>
  )
}
