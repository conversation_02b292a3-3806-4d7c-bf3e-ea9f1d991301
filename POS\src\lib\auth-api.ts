import type { LoginRequest, LoginResponse, User } from '@/types/auth'

import { api } from './api/pos/pos-api'

export const authApi = {
  /**
   * Login user with email and password
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/accounts/v1/user/login', credentials)
    // The API returns { data: LoginResponse, track_id: string }
    return response.data.data as LoginResponse
  },

  /**
   * Get current user profile
   */
  getProfile: async (): Promise<User> => {
    const response = await api.get<User>('/accounts/v1/user/profile')
    return response.data.data || response.data
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    await api.post('/accounts/v1/user/logout')
  },

  /**
   * Refresh access token
   */
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/accounts/v1/user/refresh')
    return response.data.data || response.data
  },

  /**
   * Verify token validity
   */
  verifyToken: async (): Promise<{ valid: boolean }> => {
    const response = await api.get<{ valid: boolean }>('/accounts/v1/user/verify')
    return response.data.data || response.data
  }
}

export default authApi
