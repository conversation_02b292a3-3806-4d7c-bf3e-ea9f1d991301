import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { itemApi, type GetItemsParams, type Item } from '@/lib/item-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseItemsDataOptions {
  params?: Partial<GetItemsParams>
  enabled?: boolean
}

export const useItemsData = (options: UseItemsDataOptions = {}) => {
  const { params = { skip_limit: true, active: 1 }, enabled = true } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const dynamicParams: GetItemsParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || ''
  }

  const finalParams = { ...dynamicParams, ...params }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_LIST, finalParams],
    queryFn: async (): Promise<Item[]> => {
      const response = await itemApi.getItems(finalParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 10 * 60 * 1000,
    refetchInterval: 15 * 60 * 1000
  })
}

export const useItemData = (itemId: string, enabled: boolean = true) => {
  const itemsQuery = useItemsData()

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_DETAIL, itemId],
    queryFn: async (): Promise<Item | undefined> => {
      // First try to find in cached items data
      const cachedItem = itemsQuery.data?.find(item => item.id === itemId)
      if (cachedItem) {
        return cachedItem
      }

      // If not found in cache, fetch all items and find the item
      // This is more efficient than creating a separate API endpoint for single item
      const response = await itemApi.getItems({
        company_uid: '', // Will be filled by the hook
        brand_uid: '', // Will be filled by the hook
        skip_limit: true
      })

      return response.data?.find(item => item.id === itemId)
    },
    enabled: enabled && !!itemId,
    staleTime: 15 * 60 * 1000 // 15 minutes
  })
}

export interface UseItemsByIdsOptions {
  itemIds: string[]
  cityUid?: string
  enabled?: boolean
}

export const useItemsByIds = (options: UseItemsByIdsOptions) => {
  const { itemIds, cityUid, enabled = true } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)
  const hasItemIds = itemIds.length > 0

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_LIST, 'by-ids', itemIds.sort().join(','), cityUid, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<Item[]> => {
      const params: GetItemsParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        skip_limit: true,
        is_all: true,
        list_item_id: itemIds.join(','),
        ...(cityUid && { list_city_uid: cityUid })
      }

      const response = await itemApi.getItems(params)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth && hasItemIds,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 15 * 60 * 1000 // 15 minutes
  })
}
