import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import {
  printerPositionApi,
  type GetPrinterPositionDetailParams,
  type PrinterPosition
} from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UsePrinterPositionDetailOptions {
  id?: string
  enabled?: boolean
}

export const usePrinterPositionDetail = (options: UsePrinterPositionDetailOptions = {}) => {
  const { id, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const params: GetPrinterPositionDetailParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    id: id || ''
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id && id)

  return useQuery({
    queryKey: [QUERY_KEYS.PRINTER_POSITIONS, 'detail', id],
    queryFn: async (): Promise<PrinterPosition> => {
      const response = await printerPositionApi.getPrinterPositionDetail(params)
      return response.data as PrinterPosition
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
