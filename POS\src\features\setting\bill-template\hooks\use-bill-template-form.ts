import { useCallback } from 'react'

import type {
  SaveBillTemplateRequest,
  BillTemplateConfig,
  ExtraData,
  ExtraBillTemplate3,
  ExtraBillTemplateCustom,
  ExtraDeposit,
  CurrencyExchange
} from '../types'

interface UseBillTemplateFormOptions {
  companyUid: string
  brandUid: string
  storeUid: string
  templateId?: string
}

export const useBillTemplateForm = (options: UseBillTemplateFormOptions) => {
  const { companyUid, brandUid, storeUid, templateId } = options

  const createSavePayload = useCallback(
    (config: BillTemplateConfig): SaveBillTemplateRequest => {
      const currencyExchanges: CurrencyExchange[] = config.currencyRates.map(rate => ({
        currency: `VND-${rate.currencyCode}`,
        exchange: rate.rate
      }))

      const extraData: ExtraData = {
        logo: config.logoUrl || null,
        custom_text_1: config.customText1,
        custom_text_2: config.customText2,
        enable_cash_change: config.showAmountReceived,
        enable_qr_code: config.showScanQrCode,
        enable_topping: config.showToppings,
        show_voucher_gift: config.showVoucherGift,
        qr_title: config.scanQrTitle,
        qr_content: config.scanQrContent,
        apply_new_logo_template: false,
        font_size_rate: config.fontSize,
        show_payment_id: config.showBankCardCode ? true : false,
        show_count_item_bill: config.showMainItemQuantity ? 1 : 0,
        show_customer_phone: config.secureCustomerInfo ? 0 : 1,
        enable_discount: config.showItemDiscount,
        is_show_unit: config.showUnit ? 1 : 0,
        is_group_source_and_tranno: config.mergeInvoiceAndOrderSource ? 1 : 0,
        show_points: config.showAccumulatedPoints ? 1 : 0,
        show_vat_info: config.showVatInformation ? 1 : 0,
        show_item_class: config.showItemType ? 1 : 0,
        hide_note_sale_change: config.hideInvoiceCorrectionNotes ? 1 : 0,
        show_vat_reverse: config.showVatQrCode ? 1 : 0,
        show_qr_vat_info: config.showVatQrCode ? 1 : 0,
        show_start_end_item_service: config.showServiceItemTime ? 1 : 0,
        momo_qr_aio: config.showAioMomoQr ? 1 : 0,
        hotline: config.hotlineNumber,
        currency_exchanges: currencyExchanges,
        show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0,
        display_debt_amount: config.showDebtInformation ? 1 : 0,
        show_total_item_class_amount: config.showTotalItemType ? 1 : 0,
        show_item_note: config.showFoodItemNotes ? 1 : 0,
        enable_border_bill: config.showBillFrame ? 1 : 0,
        enable_vat_rate: config.showVatPercentage,
        enable_vat_amount: config.showVatAmount,
        is_show_payment_fee: config.showCardFee ? 1 : 0,
        number_table_column: 254
      }

      const extraBillTemplate3: ExtraBillTemplate3 = {
        enable_vat_rate: config.showVatPercentage,
        enable_vat_amount: config.showVatAmount,
        logo: config.logoUrl || null,
        hotline: config.hotlineNumber || null,
        qr_title: config.scanQrTitle || null,
        qr_content: config.scanQrContent || null,
        momo_qr_aio: config.showAioMomoQr ? 1 : 0,
        show_points: config.showAccumulatedPoints ? 1 : 0,
        is_show_unit: config.showUnit ? 1 : 0,
        custom_text_1: config.customText1,
        custom_text_2: config.customText2,
        show_vat_info: config.showVatInformation ? 1 : 0,
        enable_qr_code: config.showScanQrCode,
        enable_topping: config.showToppings,
        font_size_rate: config.fontSize.toString(),
        show_item_note: config.showFoodItemNotes ? 1 : 0,
        enable_discount: config.showItemDiscount,
        show_item_class: config.showItemType ? 1 : 0,
        show_payment_id: config.showBankCardCode,
        show_qr_vat_info: config.showVatQrCode ? 1 : 0,
        show_vat_reverse: config.showVatQrCode ? 1 : 0,
        show_voucher_gift: config.showVoucherGift,
        currency_exchanges: currencyExchanges,
        enable_border_bill: config.showBillFrame ? 1 : 0,
        enable_cash_change: config.showAmountReceived,
        display_debt_amount: config.showDebtInformation ? 1 : 0,
        number_table_column: 62,
        show_customer_phone: config.secureCustomerInfo ? 0 : 1,
        show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0,
        show_count_item_bill: config.showMainItemQuantity ? 1 : 0,
        hide_note_sale_change: config.hideInvoiceCorrectionNotes ? 1 : 0,
        apply_new_logo_template: false,
        is_group_source_and_tranno: config.mergeInvoiceAndOrderSource ? 1 : 0,
        show_start_end_item_service: config.showServiceItemTime ? 1 : 0,
        show_total_item_class_amount: config.showTotalItemType ? 1 : 0
      }

      const extraBillTemplateCustom: ExtraBillTemplateCustom = {
        content: `<div style="width: 580px">
        <div style="text-align:center"><strong><span style="font-size:36px">{title}</span></strong></div>
        <div style="text-align:center"><strong><span style="font-size:36px">Số HĐ: {tran_no}</span></strong></div>
        <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
          <tbody>
            <tr>
              <td><strong><span style="font-size:22px">Mã HĐ: </span></strong><span style="font-size:22px">{tran_id}</span></td>
              <td><strong><span style="font-size:22px">TN: </span></strong><span style="font-size:22px">{cashier}</span></td>
            </tr>
            <tr>
              <td><strong><span style="font-size:22px">Bàn: </span></strong><span style="font-size:22px">{table_name}</span></td>
              <td><strong><span style="font-size:22px">Ngày: </span></strong><span style="font-size:22px">{date}</span></td>
            </tr>
            <tr>
              <td><strong><span style="font-size:22px">Giờ vào: </span></strong><span style="font-size:22px">{time_in}</span></td>
              <td><strong><span style="font-size:22px">Giờ ra: </span></strong><span style="font-size:22px">{time_out}</span></td>
            </tr>
          </tbody>
        </table>
        <div> </div>
        <div><span style="font-size:24px">{sale_detail_table}</span></div>
        <div><hr /><div> </div></div>
        <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
          <tbody>
            <tr>
              <td><strong><span style="font-size:22px">Thành tiền:</span></strong></td>
              <td style="text-align:right"><strong><span style="font-size:22px">{amount_origin}</span></strong></td>
            </tr>
            <tr>
              <td><span style="font-size:22px">Tiền chiết khấu:</span></td>
              <td style="text-align:right"><span style="font-size:22px">{discount_extra_amount}</span></td>
            </tr>
            <tr>
              <td><span style="font-size:22px">Tiền giảm giá món:</span></td>
              <td style="text-align:right"><span style="font-size:22px">{discount_item}</span></td>
            </tr>
            <tr>
              <td><span style="font-size:22px">Tiền thuế(VAT):</span></td>
              <td style="text-align:right"><span style="font-size:22px">{vat_amount}</span></td>
            </tr>
            <tr>
              <td><strong><span style="font-size:22px">Thành tiền VAT:</span></strong></td>
              <td style="text-align:right"><strong><span style="font-size:22px">{amount_after_vat} </span></strong></td>
            </tr>
            <tr>
              <td><span style="font-size:22px">Phí vận chuyển:</span></td>
              <td style="text-align:right"><span style="font-size:22px">{ship_fee}</span></td>
            </tr>
            <tr>
              <td><span style="font-size:22px">Phiếu giảm giá:</span></td>
              <td style="text-align:right"><span style="font-size:22px">{voucher_amount}</span></td>
            </tr>
          </tbody>
        </table>
        <div> </div>
        <div><hr /><div> </div></div>
        <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
          <tbody>
            <tr>
              <td><strong><span style="font-size:26px">Tổng cộng: </span></strong></td>
              <td style="text-align:right"><strong><span style="font-size:26px">{total_amount}</span></strong></td>
            </tr>
          </tbody>
        </table>
        <div><hr />
          <div><span style="font-size:24px">{payment_method_list}</span></div>
          <div style="text-align:center"><strong><span style="font-size:24px">{store_name}</span></strong></div>
          <div style="text-align:center"><strong><span style="font-size:24px">{address}</span></strong></div>
          <div><span style="font-size:22px">{customer_name} - {customer_phone}</span></div>
          <div> </div>
        </div>
      </div>`,
        title_stt: 'TT',
        momo_qr_aio: config.showAioMomoQr ? 1 : 0,
        title_price: 'Đơn giá',
        is_show_unit: config.showUnit ? 1 : 0,
        title_amount: 'Thành tiền',
        enable_topping: config.showToppings,
        show_item_note: config.showFoodItemNotes ? 1 : 0,
        title_discount: 'GG',
        title_quantity: 'SL',
        title_vat_rate: 'VAT',
        enable_discount: config.showItemDiscount,
        show_item_class: config.showItemType ? 1 : 0,
        title_name_item: 'Tên món',
        title_vat_amount: 'Tiền VAT',
        enable_border_bill: config.showBillFrame ? 1 : 0,
        display_debt_amount: config.showDebtInformation ? 1 : 0,
        number_table_column: 62,
        show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0
      }

      const extraDeposit: ExtraDeposit = {
        note: null,
        address: null,
        hotline: config.hotlineNumber || null,
        title_text: null,
        custom_text: null,
        padding_top: 10,
        logo_deposit: null,
        font_size_rate: config.fontSize,
        operating_time: null,
        show_item_type: config.showItemType ? 1 : 0,
        show_raw_quanity: 0,
        item_font_size_rate: 15,
        font_size_title_text: 30,
        price_font_size_rate: 15,
        new_templace_order_bill: 0,
        quantity_font_size_rate: 15,
        disable_unit_in_order_note: config.showUnit ? 0 : 1,
        total_price_font_size_rate: 15,
        display_confirmation_signature: 1
      }

      const payload: SaveBillTemplateRequest = {
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: storeUid,
        id: templateId || crypto.randomUUID(),
        logo: config.logoUrl || null,
        name: null,
        address: null,
        city: null,
        phone: null,
        facebook: null,
        revision: Date.now(),
        extra_data: extraData,
        extra_config: null,
        extra_pos_mini: null,
        extra_bill_template_3: extraBillTemplate3,
        extra_bill_template_4: null,
        extra_bill_template_custom: extraBillTemplateCustom,
        extra_order_note: null,
        extra_label: null,
        extra_provisional: null,
        extra_deposit: extraDeposit,
        store_id: null,
        brand_id: null,
        company_id: null,
        is_fabi: 1,
        created_by: 'system',
        updated_by: 'system',
        deleted_by: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null
      }

      return payload
    },
    [companyUid, brandUid, storeUid, templateId]
  )

  return {
    createSavePayload
  }
}
