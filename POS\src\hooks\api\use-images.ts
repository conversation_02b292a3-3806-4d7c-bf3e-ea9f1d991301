import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { imagesApi } from '@/lib/api/pos'

import { QUERY_KEYS } from '@/constants/query-keys'

/**
 * Hook for uploading images to POS CMS
 */
export const useImageUpload = () => {
  return useMutation({
    mutationFn: imagesApi.uploadImage,
    onError: () => {
      toast.error('Tải ảnh lên thất bại. Vui lòng thử lại.')
    }
  })
}

/**
 * Hook for syncing store background
 */
export const useSyncStoreBackground = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: imagesApi.syncStoreBackground,
    onSuccess: () => {
      // Invalidate all store-related queries to refresh the data
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STORES] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STORES_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STORES_DETAIL] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STORES_STATS] })

      toast.success('Đồng bộ màn hình phụ thành công!')
    },
    onError: () => {
      toast.error('Đồng bộ màn hình phụ thất bại. Vui lòng thử lại.')
    }
  })
}
