/**
 * Utility functions for generating random IDs with specific formats
 */

/**
 * Generate a random category ID in the format ITEM_TYPE-XXXX
 * @returns A string in format ITEM_TYPE-XXXX where X is a random alphanumeric character
 */
export const generateCategoryId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `ITEM_TYPE-${result}`
}

/**
 * Generate a random source ID with format SOURCE-XXXX
 * @returns A string in format SOURCE-XXXX where X is a random alphanumeric character
 */
export const generateSourceId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `SOURCE-${result}`
}

/**
 * Generate a random area ID in the format AREA-XXXX
 * @returns A string in format AREA-XXXX where X is a random alphanumeric character
 */
export const generateAreaId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `AREA-${result}`
}

/**
 * Generate a random table ID in the format TABLE-XXXX
 * @returns A string in format TABLE-XXXX where X is a random alphanumeric character
 */
export const generateTableId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `TABLE-${result}`
}

/**
 * Generate a random item code in the format ITEM-XXXX
 * @returns A string in format ITEM-XXXX where X is a random alphanumeric character
 */
export const generateItemCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `ITEM-${result}`
}

/**
 * Generate a random ID with custom prefix and format PREFIX-XXXX
 * @param prefix The prefix for the ID (e.g., 'CUSTOM', 'USER')
 * @returns A string in format PREFIX-XXXX where X is a random alphanumeric character
 */
export const generateCustomId = (prefix: string): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `${prefix.toUpperCase()}-${result}`
}
