import type { StoreFormValues } from '../schemas/store-schema'

export const initialStoreFormData: StoreFormValues = {
  store_name: '',
  description: '',
  sale_change_vat_enable: 1,
  value_vat: '',
  vat_discount_config: '',
  vat_discount_configs: [],
  print_bill_split: 0,
  invoice_output: '',
  net_work: '0',
  address: '',
  latitude: 0,
  longitude: 0,
  city_uid: '',
  phone: '',
  email: '',
  facebook: '',
  website: '',

  logo: '',
  background: '',
  secondary_screen_image: '',
  secondary_screen_video: '',

  auto_check_momo_aio: 0,

  print_type: 'PROVISIONAL_INVOICE',

  group_item: false,
  bill_template: 0,
  prevent_cashier_edit_printer: false,
  disable_print_button_in_payment_screen: 0,
  is_show_logo_in_provisional_invoice: false,
  report_item_combo_split: 0,
  tem_invoice_fake_bill: false,
  hideItemPriceAfterPrintBill: false,
  hideItemPriceAfterPrintChecklist: false,
  require_pin_reprint: false,
  prevent_print_order_transfer: false,
  allway_show_tag_so: false,
  use_shift_pos: false,
  counter_code: '',
  counter_mails: [],

  sources_print: [],
  print_bill_order_area: false,

  is_ahamove_active: false,
  phone_manager: '',
  ahamove_payment_method: '',
  ahamove_voucher_default: '',

  operate_model: 0,

  exchange_points_for_voucher: false,
  view_voucher_of_member: false,
  enable_checkin_by_phone_number: false,
  multi_voucher: false,
  find_member: false,

  is_run_buffet: false,
  require_buffet_item: false,

  enable_count_money: false,
  disable_shift_total_amount: 0,
  allow_remove_shift_open: false,
  close_shift_auto_logout: false,
  require_close_shift_in_day: false,

  discount_reverse_on_price: false,
  inv_skip_item_no_price: false,
  auto_export_vat: false,
  export_time_vat: '',
  require_vat_info: false,
  pm_export_vat: false,
  bill_auto_export_vat: false,
  sorted_by_print: false,

  enable_cash_drawer: false,
  confirm_request: false,
  use_order_control: false,
  enable_tab_delivery: false,
  enable_note_delete_item: false,
  service_charge_optional: false,
  require_peo_count: false,
  require_confirm_merge_table: false,
  hide_peo_count: false,
  enable_edit_item_price_while_selling: 0,
  role_quick_login: '',
  auto_confirm_o2o_post_paid: false,
  resetItemOutOfStockStatus: false,
  resetItemQuantityNewDay: false,

  pin_code: '',
  time_out_use_pin: 0,

  open_at: 0,

  tracking_sale: 0,

  enable_turn_order_report: false,
  change_log_detail: false,

  enable_change_item_in_store: false,
  enable_change_item_type_in_store: false,
  enable_change_printer_position_in_store: false,
  prevent_create_custom_item: false,
  require_custom_item_vat: false,
  require_category_for_custom_item: false,
  is_menu_by_source: false,

  tran_no_syn_order: 0,
  enable_tran_no_prefix: false,
  tran_no_prefix: '',
  reset_tran_no_period: 'DAILY',
  sources_not_print: [],

  print_order_at_checkout: false,
  print_label_at_checkout: false,
  sources_label_print: [],
  allow_printer_for_invoice_by_location: false,
  split_combo: false,
  ignore_combo_note: false,
  show_item_price_zero: false,
  enable_delete_order_bill: false,
  print_item_switch_table: false,

  fb_store_id: '',
  partner_id: '',
  license_expiry: '',
  license_package: '',
  payment_lock_time: '',

  store_id: '',
  no_kick_pda: false,
  device_receive_online: '',
  active_devices: [],

  time_after_lock: 0,
  time_lock_data: 0,

  bank_id: '',
  bank_acc: '',
  bank_acc_name: '',
  bank_name: '',
  print_qrcode_pay: 0,

  source_ids_selected: []
}

export const VAT_TYPE_OPTIONS = [
  { value: 1, label: 'VAT xuôi' },
  { value: 2, label: 'VAT ngược' },
  { value: 3, label: 'VAT xuôi và hỗ trợ tính VAT trên từng món' },
  { value: 4, label: 'VAT ngược và hỗ trợ tính VAT trên từng món' },
  { value: 5, label: 'VAT cho hộ kinh doanh cá thể' },
  { value: 6, label: 'Không tính VAT' }
]

export const INVOICE_CONFIG_OPTIONS = [
  { value: 0, label: 'In cả hóa đơn tổng và hóa đơn tách' },
  { value: 1, label: 'Chỉ in hóa đơn tổng' },
  { value: 2, label: 'Chỉ in hóa đơn tách' },
  { value: 3, label: 'Không tự động tách hóa đơn khi thanh toán' }
]

export const INVOICE_OUTPUT_OPTIONS = [
  { value: '', label: 'None' },
  { value: 'FABI', label: 'FABI (Lưu thông tin hoá đơn điện tử)' }
]

export const QR_SERVICE_PRINT_OPTIONS = [
  { value: '0', label: 'Không in' },
  { value: '1', label: 'Hiển thị mã VietQr khi in hóa đơn' },
  { value: '2', label: 'Hiển thị mã VietQr khi in hóa đơn và tạm tính' },
  { value: '3', label: 'Hiển thị mã VietQr khi in tạm tính' }
]

export const MOMO_AUTO_CHECK_OPTIONS = [
  { value: 0, label: 'Không cấu hình' },
  {
    value: 1,
    label: 'Quét QR trên phiếu tạm tính /PDA/ mã được in và gửi thông báo tới máy chủ khi thanh toán thành công'
  },
  {
    value: 2,
    label:
      'Hiển thị mã MoMo QR Đa Năng khi in hóa đơn thanh toán tiền mặt, tự động thay đổi PTTT cho hóa đơn nếu khách hàng quét mã QR'
  }
]

export const PRINT_TYPE_OPTIONS = [
  { value: 'PROVISIONAL_INVOICE', label: 'In tạm tính' },
  { value: 'CHECK_LIST', label: 'In chốt đồ' },
  { value: 'CHECK_LIST_AND_PROVISIONAL_INVOICE', label: 'In chốt đồ & tạm tính' },
  { value: 'NO_PRINT', label: 'Không in tạm tính, chốt đồ' }
]

export const AHAMOVE_PAYMENT_OPTIONS = [
  { value: 'CASH', label: 'Tiền mặt' },
  { value: 'PREPAID', label: 'Trả trước' }
]

export const PAYMENT_LOCK_TIME_OPTIONS = [
  { value: 'NO_LOCK', label: 'Không khóa' },
  { value: 'LOCK_5_MIN', label: 'Khóa 5 phút' },
  { value: 'LOCK_10_MIN', label: 'Khóa 10 phút' },
  { value: 'LOCK_15_MIN', label: 'Khóa 15 phút' },
  { value: 'LOCK_30_MIN', label: 'Khóa 30 phút' }
]

export const ONLINE_ORDER_DEVICE_OPTIONS = [
  { value: 'AUTO_SELECT', label: 'Chọn thiết bị nhận đơn online tại cửa hàng' },
  { value: 'DEVICE_1', label: 'Thiết bị 1' },
  { value: 'DEVICE_2', label: 'Thiết bị 2' },
  { value: 'DEVICE_3', label: 'Thiết bị 3' }
]
