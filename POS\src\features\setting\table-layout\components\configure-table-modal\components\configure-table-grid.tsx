import React from 'react'

import { Search } from 'lucide-react'

import { ConfigureTableItem } from './configure-table-item'

interface Table {
  id: string
  table_name: string
  extra_data?: {
    color?: string
    font_size?: string
  }
}

interface Area {
  id: string
  area_name: string
}

interface TableConfig {
  color: string
  fontSize: string
}

interface ConfigureTableGridProps {
  selectedStoreId: string
  selectedAreaId: string
  areas: Area[]
  tablesByArea: Record<string, Table[]>
  selectedTables: Set<string>
  config: TableConfig
  selectAllInArea: boolean
  applyToAllTables: boolean
  searchTerm: string
  onTableSelect: (tableId: string) => void
  onSelectAllInAreaChange: (checked: boolean) => void
  onApplyToAllTablesChange: (checked: boolean) => void
  onSearchChange: (value: string) => void
}

export const ConfigureTableGrid: React.FC<ConfigureTableGridProps> = ({
  selectedStoreId,
  selectedAreaId,
  areas,
  tablesByArea,
  selectedTables,
  config,
  selectAllInArea,
  applyToAllTables,
  searchTerm,
  onTableSelect,
  onSelectAllInAreaChange,
  onApplyToAllTablesChange,
  onSearchChange
}) => {
  if (!selectedStoreId) {
    return <div className='flex flex-1 items-center justify-center text-gray-500'>Vui lòng chọn cửa hàng</div>
  }

  return (
    <div className='flex flex-1 flex-col'>
      {selectedAreaId && (
        <div className='border-b bg-gray-50 p-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <h3 className='font-medium text-gray-900'>
                {areas.find(area => area.id === selectedAreaId)?.area_name || 'Khu vực'}
              </h3>
              <p className='text-sm text-gray-600'>Vui lòng chọn bàn</p>
            </div>

            <div className='flex items-center gap-6'>
              <div className='flex flex-col gap-2'>
                <div className='flex items-center gap-2'>
                  <input
                    type='checkbox'
                    checked={selectAllInArea}
                    onChange={e => onSelectAllInAreaChange(e.target.checked)}
                    disabled={!selectedAreaId}
                    id='selectAllInArea'
                  />
                  <label htmlFor='selectAllInArea' className='text-sm'>
                    Chọn tất cả bàn trong khu vực
                  </label>
                </div>

                <div className='flex items-center gap-2'>
                  <input
                    type='checkbox'
                    checked={applyToAllTables}
                    onChange={e => onApplyToAllTablesChange(e.target.checked)}
                    id='applyToAllTables'
                  />
                  <label htmlFor='applyToAllTables' className='text-sm'>
                    Áp dụng với toàn bộ bàn tại cửa hàng
                  </label>
                </div>
              </div>

              <div className='relative'>
                <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                <input
                  type='text'
                  placeholder='Tìm kiếm tên bàn'
                  value={searchTerm}
                  onChange={e => onSearchChange(e.target.value)}
                  className='w-48 rounded-md border py-2 pr-4 pl-10 text-sm'
                />
              </div>
            </div>
          </div>
        </div>
      )}

      <div className='flex-1 overflow-y-auto p-6'>
        {Object.keys(tablesByArea).length === 0 ? (
          <div className='flex h-full items-center justify-center text-gray-500'>
            {selectedAreaId ? 'Không có bàn nào trong khu vực này' : 'Vui lòng chọn khu vực để xem danh sách bàn'}
          </div>
        ) : (
          Object.entries(tablesByArea).map(([areaName, areaTables]) => (
            <div key={areaName} className='mb-8'>
              <div className='mb-6 grid grid-cols-5 gap-6'>
                {areaTables.map(table => {
                  const isSelected = selectedTables.has(table.id)
                  const currentColor = (table.extra_data as any)?.color || '#6B7280'
                  const currentFontSize = (table.extra_data as any)?.font_size || '15'
                  const displayColor = isSelected ? config.color : currentColor
                  const displayFontSize = isSelected ? config.fontSize : currentFontSize

                  return (
                    <ConfigureTableItem
                      key={table.id}
                      table={table}
                      isSelected={isSelected}
                      displayColor={displayColor}
                      displayFontSize={displayFontSize}
                      onSelect={onTableSelect}
                    />
                  )
                })}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
