import { useQuery } from '@tanstack/react-query'

export interface CrmItemType {
  Item_Type_Name: string
  Item_Type_Id: string
  is_material: number
  max_item_choice: number
  min_item_choice: number
  sort: number
  sort_online: number
}

export interface GetCrmItemTypesParams {
  page?: number
  pos_parent?: string
}

export interface CrmItemTypesResponse {
  data: CrmItemType[]
  total: number
}

// CRM Item Types API Service
export const crmItemTypesApi = {
  getAllItemTypes: async (params: GetCrmItemTypesParams = {}): Promise<CrmItemType[]> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('page', (params.page || 0).toString())
      queryParams.append('pos_parent', params.pos_parent || 'default')

      const response = { data: [] }

      if (!response.data || !Array.isArray(response.data)) {
        console.warn('Invalid response format from CRM item types API:', response.data)
        return []
      }

      return response.data as CrmItemType[]
    } catch (error) {
      console.error('Error fetching CRM item types:', error)
      return []
    }
  }
}

export const useCrmItemTypes = (params: GetCrmItemTypesParams = {}, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['crm-item-types', params],
    queryFn: () => crmItemTypesApi.getAllItemTypes(params),
    enabled,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2
  })
}

// Hook to get item type options for select components
export const useCrmItemTypeOptions = (params: GetCrmItemTypesParams = {}) => {
  const { data: itemTypes, ...query } = useCrmItemTypes(params)

  const options =
    itemTypes?.map(itemType => ({
      value: itemType.Item_Type_Id,
      label: itemType.Item_Type_Name,
      data: itemType
    })) || []

  return {
    ...query,
    data: itemTypes,
    options
  }
}
