import { ColumnDef } from '@tanstack/react-table'

import type { Store } from '@/types'
import { Info } from 'lucide-react'

import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui'

import { ExpiryDateCell, PosIdCell, StatusCell } from './cells'
import { StoreTypeCell } from './store-type-cell'

export const storesColumns: ColumnDef<Store>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const rowNumber = row.index + 1
      return <div className='w-8 text-xs font-medium sm:w-[50px] sm:text-sm'>{rowNumber}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'fb_store_id',
    header: 'Pos ID',
    cell: ({ row }) => <PosIdCell store={row.original} />
  },
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => <span className='font-medium'>{row.original.name}</span>
  },
  {
    accessorKey: 'city_name',
    header: 'Đ<PERSON>a điểm',
    cell: ({ row }) => {
      const store = row.original as any
      const cityName = store.city_name || store.cityName
      const address = store.address
      const latitude = store.latitude
      const longitude = store.longitude

      return (
        <div className='flex items-center gap-2'>
          <span className='font-medium'>{cityName}</span>
          {address && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className='text-muted-foreground hover:text-foreground h-4 w-4 cursor-help transition-colors' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <div className='space-y-1'>
                    <p className='text-sm'>{address}</p>
                    {latitude && longitude && (
                      <p className='text-xs'>
                        lat: {latitude}, long: {longitude}
                      </p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      )
    }
  },
  {
    accessorKey: 'phone',
    header: 'Số điện thoại',
    cell: ({ row }) => {
      const store = row.original
      return store.phone ? (
        <span className='font-medium'>{store.phone}</span>
      ) : (
        <span className='text-muted-foreground'>-</span>
      )
    }
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      const store = row.original
      return store.email ? (
        <span className='font-medium'>{store.email}</span>
      ) : (
        <span className='text-muted-foreground'>-</span>
      )
    }
  },
  {
    accessorKey: 'expiry_date',
    header: 'Thời hạn bán quyền',
    cell: ({ row }) => {
      const store = row.original as any
      const expiryTimestamp = store.expiry_date || store.expiryDate
      return <ExpiryDateCell expiryTimestamp={expiryTimestamp} />
    }
  },
  {
    accessorKey: 'store_type',
    header: 'Loại hình nhà hàng',
    cell: ({ row }) => <StoreTypeCell store={row.original} />
  },
  {
    accessorKey: 'active',
    header: '',
    cell: ({ row }) => <StatusCell store={row.original} />
  }
]
