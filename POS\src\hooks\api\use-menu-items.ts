import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { UpdateMenuItemRequest } from '@/types/api/menu-items'
import { toast } from 'sonner'

import { menuItemsApi } from '@/lib/menu-items-api'

export const useMenuItems = (page: number = 0, posParent: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['menu-items', page, posParent],
    queryFn: () => menuItemsApi.getMenuItems(page, posParent),
    enabled: options?.enabled !== false && !!posParent,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false
  })
}

export const useUpdateMenuItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (updateData: UpdateMenuItemRequest) => menuItemsApi.updateMenuItem(updateData),
    onSuccess: () => {
      toast.success('Cập nhật món ăn thành công')
      // Invalidate and refetch menu items
      queryClient.invalidateQueries({
        queryKey: ['menu-items']
      })
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi cập nhật món ăn')
    }
  })
}

export const useUpdateCombo = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (updateData: UpdateMenuItemRequest) => menuItemsApi.updateCombo(updateData),
    onSuccess: () => {
      toast.success('Cập nhật combo thành công')
      // Invalidate and refetch menu items and combos
      queryClient.invalidateQueries({
        queryKey: ['menu-items']
      })
      queryClient.invalidateQueries({
        queryKey: ['normal-combos']
      })
      queryClient.invalidateQueries({
        queryKey: ['special-combos']
      })      
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi cập nhật combo')
    }
  })
}
