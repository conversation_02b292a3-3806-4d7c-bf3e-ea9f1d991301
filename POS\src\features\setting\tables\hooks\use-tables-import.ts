import { useState, useRef } from 'react'
import { toast } from 'sonner'

import { useTablesImportExcelParser, type ParsedTableData } from './use-tables-import-excel-parser'
import { useBulkImportTables } from '@/hooks/api/use-tables'

export function useTablesImport() {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedTableData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const { parseExcelFile, downloadTemplate } = useTablesImportExcelParser()
  const bulkImportTablesMutation = useBulkImportTables()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    downloadTemplate()
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedTables = await parseExcelFile(file)
      setImportParsedData(parsedTables)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedTables.length} bàn từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedTables = async (storeUid: string, areaMapping: Record<string, string>) => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      // Convert ParsedTableData to BulkImportInput format
      const tablesData = importParsedData.map(table => {
        const areaUid = areaMapping[table.area_name.trim()]
        if (!areaUid) {
          throw new Error(`Không tìm thấy khu vực: ${table.area_name}`)
        }

        return {
          table_name: table.table_name,
          area_uid: areaUid,
          description: table.description || ''
        }
      })

      await bulkImportTablesMutation.mutateAsync({
        storeUid,
        tables: tablesData
      })
      toast.success(`Đã tạo thành công ${importParsedData.length} bàn!`)
      resetImportState()
      return true
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message)
      } else {
        toast.error('Lỗi khi tạo bàn. Vui lòng thử lại.')
      }
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedTables,
    isLoading: bulkImportTablesMutation.isPending,
  }
}
