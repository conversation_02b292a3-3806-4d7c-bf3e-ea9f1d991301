import { useMutation, useQueryClient } from '@tanstack/react-query'

import type { SaveRegisterPageConfigRequest, SaveRegisterPageConfigResponse } from '@/types/api/crm'

import { marketingApi } from '@/lib/api/crm'

import { CRM_QUERY_KEYS } from '@/constants/crm'

export function useSaveRegisterPageConfig() {
  const queryClient = useQueryClient()

  return useMutation<SaveRegisterPageConfigResponse, Error, SaveRegisterPageConfigRequest>({
    mutationFn: async (data: SaveRegisterPageConfigRequest) => marketingApi.saveRegisterPageConfig(data),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['crm', CRM_QUERY_KEYS.REGISTER_PAGE_CONFIG, variables.pos_parent]
      })
    }
  })
}

