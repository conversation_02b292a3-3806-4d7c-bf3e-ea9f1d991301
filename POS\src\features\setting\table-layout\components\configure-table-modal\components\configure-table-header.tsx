import React from 'react'
import { X } from 'lucide-react'

import { But<PERSON> } from '@/components/ui'

interface ConfigureTableHeaderProps {
  onClose: () => void
}

export const ConfigureTableHeader: React.FC<ConfigureTableHeaderProps> = ({ onClose }) => {
  return (
    <div className='flex items-center justify-between border-b p-6'>
      <h2 className='text-xl font-semibold'>Cấu hình bàn</h2>
      <Button variant='ghost' size='sm' onClick={onClose}>
        <X className='h-4 w-4' />
      </Button>
    </div>
  )
}
