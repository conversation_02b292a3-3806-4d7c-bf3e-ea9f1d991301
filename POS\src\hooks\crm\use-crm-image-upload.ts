import { useMutation } from '@tanstack/react-query'

import { crmImagesApi } from '@/lib/api/crm'

interface UseCrmImageUploadOptions {
  pos_parent: string
  onSuccess?: (response: any) => void
  onError?: (error: any) => void
}

export function useCrmImageUpload(options: UseCrmImageUploadOptions) {
  const mutation = useMutation({
    mutationFn: (file: File) => crmImagesApi.uploadImage(file, options.pos_parent),
    onSuccess: response => {
      if (response?.data) {
        options.onSuccess?.(response.data)
      }
      if (!response?.message) {
        const errorMessage = response.message || 'Upload failed - no data received'
        options.onError?.(new Error(errorMessage))
      }
    },
    onError: error => {
      options.onError?.(error)
    }
  })

  const uploadImage = async (file: File) => {
    if (!file) {
      throw new Error('No file provided')
    }
    return mutation.mutateAsync(file)
  }

  return {
    uploadImage,
    isUploading: mutation.isPending,
    uploadError: mutation.error?.message || null,
    clearError: () => mutation.reset(),
    isError: mutation.isError,
    isSuccess: mutation.isSuccess
  }
}
