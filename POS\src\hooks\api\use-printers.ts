import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { printersApi, type PrintersApiParams, type CreatePrinterParams } from '@/lib/printers-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UsePrintersDataOptions {
  params?: Partial<PrintersApiParams>
  enabled?: boolean
}

export const usePrintersData = (options: UsePrintersDataOptions = {}) => {
  const { params = {}, enabled = true } = options

  return useQuery({
    queryKey: [QUERY_KEYS.PRINTERS, params.pos_device_code],
    queryFn: async () => {
      if (!params.pos_device_code) {
        throw new Error('pos_device_code is required')
      }

      const finalParams: PrintersApiParams = {
        pos_device_code: params.pos_device_code,
        results_per_page: params.results_per_page || 15000
      }

      return await printersApi.getPrinters(finalParams)
    },
    enabled: enabled && !!params.pos_device_code,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export const useCreatePrinter = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CreatePrinterParams) => printersApi.createPrinter(params),
    onSuccess: (_, variables) => {
      // Invalidate printers list for the specific device
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PRINTERS, variables.device_code]
      })
    }
  })
}

export const useDeletePrinter = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (printerId: string) => printersApi.deletePrinter(printerId),
    onSuccess: () => {
      // Invalidate all printers queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PRINTERS]
      })
    }
  })
}
