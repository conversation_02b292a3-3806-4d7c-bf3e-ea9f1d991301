import { z } from 'zod'

const vatDiscountConfigSchema = z.object({
  id: z.string(),
  discountPercentage: z.number(),
  vatPercentage: z.number(),
  programName: z.string(),
  startDate: z.string(),
  endDate: z.string()
})

export const storeFormSchema = z.object({
  store_name: z.string().min(1, 'Tên điểm là bắt buộc'),
  address: z.string().min(1, 'Địa chỉ là bắt buộc'),
  phone: z.string().min(1, 'Số điện thoại là bắt buộc'),
  city_uid: z.string().min(1, '<PERSON>ui lòng chọn thành phố'),

  description: z.string().optional(),
  sale_change_vat_enable: z.number().optional().default(1),
  value_vat: z.string().optional(),
  vat_discount_config: z.string().optional(),
  vat_discount_configs: z.array(vatDiscountConfigSchema).optional().default([]),
  print_bill_split: z.coerce.number().optional().default(0),
  invoice_output: z.string().optional().default(''),
  net_work: z.enum(['0', '1']).optional().default('0'),
  latitude: z.coerce.number().min(-90).max(90).optional().default(0),
  longitude: z.coerce.number().min(-180).max(180).optional().default(0),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),

  facebook: z.string().optional(),
  website: z.string().optional(),

  logo: z.string().optional(),
  background: z.string().optional(),
  secondary_screen_image: z.string().optional(),
  secondary_screen_video: z.string().optional(),

  bank_id: z.string().optional(),
  bank_acc: z.string().optional(),
  bank_acc_name: z.string().optional(),
  bank_name: z.string().optional(),
  print_qrcode_pay: z.coerce.number().optional(),

  auto_check_momo_aio: z.coerce.number().optional().default(0),

  print_type: z.string().optional(),
  print_limit: z.string().optional(),

  group_item: z.boolean().default(false),
  bill_template: z.coerce.number().optional(),
  prevent_cashier_edit_printer: z.boolean().default(false),
  disable_print_button_in_payment_screen: z.coerce.number().optional(),
  is_show_logo_in_provisional_invoice: z.boolean().default(false),
  report_item_combo_split: z.coerce.number().optional(),
  tem_invoice_fake_bill: z.boolean().default(false),
  hideItemPriceAfterPrintBill: z.boolean().default(false),
  hideItemPriceAfterPrintChecklist: z.boolean().default(false),
  require_pin_reprint: z.boolean().default(false),
  prevent_print_order_transfer: z.boolean().default(false),
  allway_show_tag_so: z.boolean().default(false),
  use_shift_pos: z.boolean().default(false),
  counter_code: z.string().optional(),
  counter_mails: z.array(z.string()).optional(),

  sources_print: z.array(z.string()).default([]),

  is_ahamove_active: z.boolean(),
  phone_manager: z.string().optional(),
  ahamove_payment_method: z.string().optional(),
  ahamove_voucher_default: z.string().optional(),

  operate_model: z.coerce.number().optional(),

  exchange_points_for_voucher: z.boolean().default(false),
  view_voucher_of_member: z.boolean().default(false),
  enable_checkin_by_phone_number: z.boolean().default(false),
  multi_voucher: z.boolean().default(false),
  find_member: z.boolean().default(false),

  is_run_buffet: z.boolean().default(false),
  require_buffet_item: z.boolean().default(false),

  enable_count_money: z.boolean().default(false),
  disable_shift_total_amount: z.coerce.number().optional(),
  allow_remove_shift_open: z.boolean().default(false),
  close_shift_auto_logout: z.boolean().default(false),
  require_close_shift_in_day: z.boolean().default(false),

  discount_reverse_on_price: z.boolean().default(false),
  inv_skip_item_no_price: z.boolean().default(false),
  auto_export_vat: z.boolean().default(false),
  export_time_vat: z.string().optional(),
  require_vat_info: z.boolean().default(false),
  pm_export_vat: z.boolean().default(false),
  bill_auto_export_vat: z.boolean().default(false),
  sorted_by_print: z.boolean().default(false),

  enable_cash_drawer: z.boolean().default(false),
  confirm_request: z.boolean().default(false),
  use_order_control: z.boolean().default(false),
  enable_tab_delivery: z.boolean().default(false),
  enable_note_delete_item: z.boolean().default(false),
  service_charge_optional: z.boolean().default(false),
  require_peo_count: z.boolean().default(false),
  require_confirm_merge_table: z.boolean().default(false),
  hide_peo_count: z.boolean().default(false),
  enable_edit_item_price_while_selling: z.coerce.number().optional(),
  role_quick_login: z.string().optional(),
  auto_confirm_o2o_post_paid: z.boolean().default(false),
  resetItemOutOfStockStatus: z.boolean().default(false),
  resetItemQuantityNewDay: z.boolean().default(false),

  pin_code: z.string().optional(),
  time_out_use_pin: z.coerce.number().optional(),

  open_at: z.number().min(0).max(23).default(0),

  tracking_sale: z.coerce.number().optional(),

  enable_turn_order_report: z.boolean().default(false),
  change_log_detail: z.boolean().default(false),

  enable_change_item_in_store: z.boolean().default(false),
  enable_change_item_type_in_store: z.boolean().default(false),
  enable_change_printer_position_in_store: z.boolean().default(false),
  prevent_create_custom_item: z.boolean().default(false),
  require_custom_item_vat: z.boolean().default(false),
  require_category_for_custom_item: z.boolean().default(false),
  is_menu_by_source: z.boolean().default(false),

  tran_no_syn_order: z.coerce.number().optional(),
  enable_tran_no_prefix: z.boolean().default(false),
  tran_no_prefix: z.string().optional(),
  reset_tran_no_period: z.string().optional(),
  sources_not_print: z.array(z.string()).default([]),

  print_order_at_checkout: z.boolean().default(false),
  print_label_at_checkout: z.boolean().default(false),
  sources_label_print: z.array(z.string()).default([]),
  print_bill_order_area: z.boolean().default(false),
  allow_printer_for_invoice_by_location: z.boolean().default(false),
  split_combo: z.boolean().default(false),
  ignore_combo_note: z.boolean().default(false),
  show_item_price_zero: z.boolean().default(false),
  enable_delete_order_bill: z.boolean().default(false),
  print_item_switch_table: z.boolean().default(false),

  fb_store_id: z.string().optional().default(''),
  partner_id: z.string().optional().default(''),
  license_expiry: z.string().optional().default(''),
  license_package: z.string().optional().default(''),
  payment_lock_time: z.string().optional().default(''),

  store_id: z.string().optional(),
  no_kick_pda: z.boolean().optional(),
  device_receive_online: z.string().optional().default(''),
  active_devices: z.array(z.string()).default([]),

  time_after_lock: z.coerce.number().optional(),
  time_lock_data: z.coerce.number().optional(),

  is_delivery_direct: z.number().optional(),
  workstation_id: z.number().optional(),
  active: z.number().optional(),
  is_default: z.number().optional(),
  is_test: z.number().optional(),

  store_address: z.any().optional(),
  district: z.string().optional(),
  ward: z.string().optional(),

  pos_server_group_uid: z.string().optional(),
  brand_uid: z.string().optional(),
  company_uid: z.string().optional(),
  company_id: z.string().optional(),

  pos_type: z.string().optional(),
  operation_form: z.string().optional(),
  size: z.number().optional(),
  currency: z.string().optional(),

  delivery_services: z.string().optional(),
  email_delivery_service: z.string().optional(),

  is_fabi: z.number().optional(),
  partner: z.string().optional(),

  revision: z.number().optional(),
  expiry_date: z.number().optional(),
  sort: z.number().optional(),
  last_synced_transaction: z.number().optional(),
  created_at: z.coerce.number().optional(),

  source_ids_selected: z.array(z.string()).optional()
})

export type StoreFormValues = z.infer<typeof storeFormSchema>

/**
 * Validates only the required fields for enabling the Save button
 */
export const validateRequiredFields = (data: Partial<StoreFormValues>): boolean => {
  const requiredFields = {
    store_name: data.store_name?.trim(),
    address: data.address?.trim(),
    phone: data.phone?.trim(),
    city_uid: data.city_uid?.trim()
  }

  return Object.values(requiredFields).every(field => field && field.length > 0)
}
