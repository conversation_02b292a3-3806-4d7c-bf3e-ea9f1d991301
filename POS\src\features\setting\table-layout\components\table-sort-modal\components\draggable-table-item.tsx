import React from 'react'

import type { Table } from '@/lib/tables-api'

interface DraggableTableItemProps {
  table: Table
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
  isDragging: boolean
}

export const DraggableTableItem: React.FC<DraggableTableItemProps> = ({
  table,
  index,
  onDragStart,
  onDragOver,
  onDrop,
  isDragging
}) => {
  return (
    <div
      draggable
      onDragStart={e => onDragStart(e, index)}
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      className={`min-w-0 p-2 transition-all duration-200 ${isDragging ? 'scale-105 opacity-50' : 'cursor-move'}`}
    >
      <div
        className={`relative mx-auto h-16 w-20 rounded-[12px] bg-gray-300 shadow-md transition-all duration-200 ${
          isDragging ? 'shadow-xl' : 'hover:shadow-lg'
        }`}
      >
        <div className='pointer-events-none absolute -top-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-t-[8px] bg-gray-400'></div>
        <div className='pointer-events-none absolute top-1/2 -right-[8px] h-6 w-[8px] -translate-y-1/2 rounded-r-[8px] bg-gray-400'></div>
        <div className='pointer-events-none absolute -bottom-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-b-[8px] bg-gray-400'></div>
        <div className='pointer-events-none absolute top-1/2 -left-[8px] h-6 w-[8px] -translate-y-1/2 rounded-l-[8px] bg-gray-400'></div>
        <div className='pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2'>
          <span className='truncate text-center text-xs font-medium text-gray-800' title={table.table_name}>
            {table.table_name}
          </span>
        </div>
      </div>
    </div>
  )
}
