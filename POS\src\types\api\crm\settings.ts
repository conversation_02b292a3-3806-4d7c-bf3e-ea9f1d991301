export interface PosParentSettings {
  id: string
  description: string
  image: string
  name: string
  is_gift_point: number
  is_send_sms: number
  pos_type: number
  Brand_Name: string
  Sms_Partner: string
  Api_Key: string
  Secret_Key: string
  Direct_List: string
  Msg_Member_Bad_Rate: number
  Logo_Image: string
  Pos_Feature: number
  Manager_Phone: number
  Manager_Email_List: string
  Hotline: string
  Member_Parnter_Id: number
  Limit_Eat_Count_Per_Day: number
  Limit_Pay_Amount_Per_Day: number
  Company_Id: string
  Checkin_Time: number
  Estimate_Complete_Order_Time: number
  Using_Cloud_Loyalty: number
  Using_Ipos_Otp: number
  Booking_Type: number
}

export interface SettingConfigResponse {
  data: {
    id: string
    pos_parent: string
    created_at: string
    updated_at: string
    vat: number
    service_charge: number
    warning_cheat_mail_config: {
      mailType: string
    }
    alert_ws_mail_config: {
      mailType: string
      mailList?: string[]
    }
  }
}

export interface LoyaltySettingResponse {
  data: {
    company_id: string
    is_number_rounding: number
    reset_point_cycle: number
    membership_downgrade_cycle: number
    none_point_sources: string
    id: string
    created_at: string
  }
}
