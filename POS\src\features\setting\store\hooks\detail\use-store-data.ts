import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { apiClient } from '@/lib/api/pos/pos-api'
import type { ApiStore } from '@/lib/stores-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import { initialStoreFormData, type StoreFormValues } from '../../data'

interface UseCurrentStoreDataProps {
  storeId?: string
  enabled?: boolean
}

/**
 * Map API store data to form values
 */
export const mapApiStoreToFormValues = (apiStore: ApiStore): StoreFormValues => {
  const extraData = apiStore.extra_data || {}

  return {
    ...initialStoreFormData,
    store_name: apiStore.store_name || '',
    phone: apiStore.phone || '',
    description: (extraData.description as string) || '',
    sale_change_vat_enable: (extraData.sale_change_vat_enable as number) || 1,
    value_vat: (extraData.value_vat as string) || '',
    vat_discount_config: '',
    vat_discount_configs: [],
    print_bill_split: (extraData.print_bill_split as number) || 0,
    invoice_output: '',
    net_work: (extraData.net_work as number) === 1 ? '1' : '0',
    address: apiStore.address || '',
    city_uid: apiStore.city_uid || '',
    email: apiStore.email || '',
    facebook: (extraData.facebook as string) || '',
    website: (extraData.website as string) || '',
    logo: (extraData.logo as string) || '',
    background: apiStore.background || '',
    secondary_screen_image: (extraData.secondary_screen_image as string) || '',
    secondary_screen_video: (extraData.secondary_screen_video as string) || '',
    latitude: apiStore.latitude || 0,
    longitude: apiStore.longitude || 0,
    bank_id: (extraData.bank_id as string) || '',
    bank_acc: (extraData.bank_acc as string) || '',
    bank_acc_name: (extraData.bank_acc_name as string) || '',
    bank_name: (extraData.bank_name as string) || '',
    print_qrcode_pay: (extraData.print_qrcode_pay as number) || 0,
    group_item: (extraData.group_item as number) === 1,
    bill_template: (extraData.bill_template as number) || 0,
    prevent_cashier_edit_printer: (extraData.prevent_cashier_edit_printer as number) === 1,
    disable_print_button_in_payment_screen: (extraData.disable_print_button_in_payment_screen as number) || 0,
    is_show_logo_in_provisional_invoice: (extraData.is_show_logo_in_provisional_invoice as number) === 1,
    report_item_combo_split: (extraData.report_item_combo_split as number) || 0,
    tem_invoice_fake_bill: (extraData.tem_invoice_fake_bill as number) === 1,
    hideItemPriceAfterPrintBill: (extraData.hideItemPriceAfterPrintBill as number) === 1,
    hideItemPriceAfterPrintChecklist: (extraData.hideItemPriceAfterPrintChecklist as number) === 1,
    require_pin_reprint: (extraData.require_pin_reprint as number) === 1,
    prevent_print_order_transfer: (extraData.prevent_print_order_transfer as number) === 1,
    allway_show_tag_so: (extraData.allway_show_tag_so as number) === 1,
    use_shift_pos: (extraData.use_shift_pos as number) === 1,
    counter_code: (extraData.counter_code as string) || '',
    counter_mails: (extraData.counter_mails as string[]) || [],
    auto_check_momo_aio: (extraData.auto_check_momo_aio as number) || 0,
    print_type:
      (extraData.print_type as string) === 'PROVISIONAL_INVOICE'
        ? 'PROVISIONAL_INVOICE'
        : (extraData.print_type as string) === 'CHECK_LIST_AND_PROVISIONAL_INVOICE'
          ? 'CHECK_LIST_AND_PROVISIONAL_INVOICE'
          : (extraData.print_type as string) === 'CHECK_LIST'
            ? 'CHECK_LIST'
            : 'NO_PRINT',
    print_limit: (extraData.print_limit as number) ? (extraData.print_limit as number).toString() : '',
    sources_print: (extraData.sources_print as string[]) || [],
    print_bill_order_area: (extraData.print_bill_order_area as number) === 1,
    is_ahamove_active: apiStore.is_ahamove_active === 1,
    phone_manager: apiStore.phone_manager || '',
    ahamove_payment_method: (extraData.ahamove_payment_method as string) || '',

    ahamove_voucher_default: (extraData.ahamove_voucher_default as string) || '',
    operate_model: (extraData.operate_model as number) || 0,
    exchange_points_for_voucher: (extraData.exchange_points_for_voucher as number) === 1,
    view_voucher_of_member: (extraData.view_voucher_of_member as number) === 1,
    enable_checkin_by_phone_number: (extraData.enable_checkin_by_phone_number as number) === 1,
    multi_voucher: (extraData.multi_voucher as number) === 1,
    find_member: (extraData.find_member as number) === 1,
    is_run_buffet: (extraData.is_run_buffet as number) === 1,
    require_buffet_item: (extraData.require_buffet_item as number) === 1,
    enable_count_money: (extraData.enable_count_money as number) === 1,
    disable_shift_total_amount: (extraData.disable_shift_total_amount as number) || 0,
    allow_remove_shift_open: (extraData.allow_remove_shift_open as number) === 1,
    close_shift_auto_logout: (extraData.close_shift_auto_logout as number) === 1,
    require_close_shift_in_day: (extraData.require_close_shift_in_day as number) === 1,
    discount_reverse_on_price: (extraData.discount_reverse_on_price as number) === 1,
    inv_skip_item_no_price: (extraData.inv_skip_item_no_price as number) === 1,
    auto_export_vat: (extraData.auto_export_vat as number) === 1,
    bill_auto_export_vat: (extraData.bill_auto_export_vat as number) === 1,
    sorted_by_print: (extraData.sorted_by_print as number) === 1,
    export_time_vat: (extraData.export_time_vat as number) ? (extraData.export_time_vat as number).toString() : '',
    require_vat_info: (extraData.require_vat_info as number) === 1,
    pm_export_vat: (extraData.pm_export_vat as number) === 1,
    partner_id: (extraData.partner_id as string) || '',
    print_item_switch_table: (extraData.print_item_switch_table as number) === 1,
    time_after_lock: (extraData.time_after_lock as number) || 0,
    time_lock_data: (extraData.time_lock_data as number) || 0,
    source_ids_selected: (extraData.source_ids_selected as string[]) || [],
    enable_cash_drawer: (extraData.enable_cash_drawer as number) === 1,
    confirm_request: (extraData.confirm_request as number) === 1,
    use_order_control: (extraData.use_order_control as number) === 1,
    enable_note_delete_item: (extraData.enable_note_delete_item as number) === 1,
    service_charge_optional: (extraData.service_charge_optional as number) === 1,
    require_peo_count: (extraData.require_peo_count as number) === 1,
    enable_tab_delivery: (extraData.enable_tab_delivery as number) === 1,
    require_confirm_merge_table: (extraData.require_confirm_merge_table as number) === 1,
    hide_peo_count: (extraData.hide_peo_count as number) === 1,
    enable_change_item_in_store: (extraData.enable_change_item_in_store as number) === 1,
    enable_change_item_type_in_store: (extraData.enable_change_item_type_in_store as number) === 1,
    enable_change_printer_position_in_store: (extraData.enable_change_printer_position_in_store as number) === 1,
    prevent_create_custom_item: (extraData.prevent_create_custom_item as number) === 1,
    require_category_for_custom_item: (extraData.require_category_for_custom_item as number) === 1,
    is_menu_by_source: (extraData.is_menu_by_source as number) === 1,
    enable_tran_no_prefix: (extraData.enable_tran_no_prefix as number) === 1,
    tran_no_prefix: (extraData.tran_no_prefix as string) || '',
    sources_not_print: (extraData.sources_not_print as string[]) || [],
    allow_printer_for_invoice_by_location: (extraData.allow_printer_for_invoice_by_location as number) === 1,
    split_combo: (extraData.split_combo as number) === 1,
    ignore_combo_note: (extraData.ignore_combo_note as number) === 1,
    show_item_price_zero: (extraData.show_item_price_zero as number) === 1,
    enable_delete_order_bill: (extraData.enable_delete_order_bill as number) === 1,
    fb_store_id: apiStore.fb_store_id?.toString() || '',
    license_expiry: (extraData.license_expiry as string) || '',
    license_package: (extraData.license_package as string) || '',
    payment_lock_time:
      (extraData.time_lock_data as number) === 0
        ? 'NO_LOCK'
        : (extraData.time_lock_data as number) === 5
          ? 'LOCK_5_MIN'
          : (extraData.time_lock_data as number) === 10
            ? 'LOCK_10_MIN'
            : (extraData.time_lock_data as number) === 15
              ? 'LOCK_15_MIN'
              : (extraData.time_lock_data as number) === 30
                ? 'LOCK_30_MIN'
                : 'NO_LOCK',
    store_id: apiStore.store_id || '',
    no_kick_pda: (extraData.no_kick_pda as number) === 1,
    device_receive_online: (extraData.device_receive_online as string) || '',
    active_devices: (extraData.active_devices as string[]) || [],
    enable_edit_item_price_while_selling: (extraData.enable_edit_item_price_while_selling as number) || 0,
    role_quick_login: (extraData.role_quick_login as string) || '',
    auto_confirm_o2o_post_paid: (extraData.auto_confirm_o2o_post_paid as number) === 1,
    resetItemOutOfStockStatus: (extraData.resetItemOutOfStockStatus as number) === 1,
    resetItemQuantityNewDay: (extraData.resetItemQuantityNewDay as number) === 1,
    pin_code: (extraData.pin_code as string) || '',
    time_out_use_pin: (extraData.time_out_use_pin as number) || 0,
    open_at: (extraData.open_at as number) || 0,
    tracking_sale: (extraData.tracking_sale as number) || 0,
    enable_turn_order_report: (extraData.enable_turn_order_report as number) === 1,
    change_log_detail: (extraData.change_log_detail as number) === 1,
    require_custom_item_vat: (extraData.require_custom_item_vat as number) === 1,
    tran_no_syn_order: (extraData.tran_no_syn_order as number) || 0,
    print_order_at_checkout: (extraData.print_order_at_checkout as number) === 1,
    print_label_at_checkout: (extraData.print_label_at_checkout as number) === 1,
    reset_tran_no_period: (extraData.reset_tran_no_period as string) || 'DAILY',
    sources_label_print: (extraData.sources_label_print as string[]) || [],

    delivery_services: apiStore.delivery_services || '',
    email_delivery_service: apiStore.email_delivery_service || '',
    brand_uid: apiStore.brand_uid || '',
    company_uid: apiStore.company_uid || '',
    created_at: apiStore.created_at,
    expiry_date: apiStore.expiry_date,
    active: apiStore.active,
    is_fabi: apiStore.is_fabi
  }
}

export const useCurrentStoreData = ({ storeId, enabled = true }: UseCurrentStoreDataProps) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const storeQuery = useQuery({
    queryKey: [QUERY_KEYS.STORES_DETAIL, storeId, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<ApiStore | undefined> => {
      if (!company?.id || !selectedBrand?.id || !storeId) {
        return undefined
      }

      const queryParams = new URLSearchParams({
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: storeId
      })

      const response = await apiClient.get(`/mdata/v1/store?${queryParams.toString()}`)

      if (!response.data?.data) {
        return undefined
      }

      return response.data.data as ApiStore
    },
    enabled: enabled && !!storeId && !!company?.id && !!selectedBrand?.id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })

  const apiStoreData = storeQuery.data
  const isExpired = apiStoreData ? new Date(apiStoreData.expiry_date * 1000) < new Date() : false
  const isActive = apiStoreData?.active === 1
  const formValues = apiStoreData ? mapApiStoreToFormValues(apiStoreData) : undefined
  const { isLoading, isFetching, isError, error, refetch, status, fetchStatus } = storeQuery

  return {
    data: apiStoreData,
    formValues,
    isExpired,
    isActive,
    isLoading,
    isFetching,
    isError,
    error,
    refetch,
    status,
    fetchStatus
  }
}
