import type { SaveBillTemplateRequest, SaveBillTemplateResponse } from '@/features/setting/bill-template/types'

import { apiClient } from './api/pos/pos-api'

export interface SyncBillTemplateRequest {
  company_uid: string
  brand_uid: string
  store_uid: string
}

export interface GetBillTemplateRequest {
  company_uid: string
  brand_uid: string
  store_uid: string
}

export interface UseBillTemplateRequest {
  id: string
  store_id: string
  fb_store_id: number
  store_name: string
  is_delivery_direct: number
  latitude: number
  longitude: number
  email: string
  phone: string
  logo: string
  background: string
  facebook: string
  website: string
  address: string
  description: string
  workstation_id: number
  active: number
  is_default: number
  is_test: number
  store_address: Record<string, unknown>
  extra_data: {
    bill_template: number
    [key: string]: unknown
  }
  revision: number
  expiry_date: number
  sort: number
  last_synced_transaction: string | null
  city_uid: string
  district: string | null
  pos_server_group_uid: string | null
  brand_uid: string
  company_uid: string
  is_ahamove_active: number
  delivery_services: string
  email_delivery_service: string
  phone_manager: string
  is_fabi: number
  partner: string
  pos_type: string
  operation_form: string
  size: number
  currency: string
  ward: string | null
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  brand: Record<string, unknown>
  company: Record<string, unknown>
  city: Record<string, unknown>
}

export interface UseBillTemplateResponse {
  success: boolean
  message?: string
  data?: unknown
}

export interface BillTemplateApiResponse<T = unknown> {
  data: T
  success?: boolean
  message?: string
  track_id?: string
}

/**
 * Bill Template API Service
 */
export const billTemplateApi = {
  /**
   * Get bill template configuration
   */
  getBillTemplate: async (
    params: GetBillTemplateRequest
  ): Promise<BillTemplateApiResponse<SaveBillTemplateRequest>> => {
    const response = await apiClient.get<BillTemplateApiResponse<SaveBillTemplateRequest>>(
      `/v3/pos-cms/bill-template?store_uid=${params.store_uid}&brand_uid=${params.brand_uid}&company_uid=${params.company_uid}`
    )
    return response.data
  },

  /**
   * Sync bill template(s) for a specific store within a brand/company
   */
  syncBillTemplate: async <T = unknown>(payload: SyncBillTemplateRequest): Promise<BillTemplateApiResponse<T>> => {
    const response = await apiClient.post<BillTemplateApiResponse<T>>('/v3/pos-cms/bill-template', payload)

    return response.data
  },

  /**
   * Save bill template configuration
   */
  saveBillTemplate: async (payload: SaveBillTemplateRequest): Promise<SaveBillTemplateResponse> => {
    const response = await apiClient.post<SaveBillTemplateResponse>('/v3/pos-cms/bill-template', payload)
    return response.data
  },

  /**
   * Use bill template - Update store with bill template configuration
   */
  useBillTemplate: async (payload: UseBillTemplateRequest): Promise<UseBillTemplateResponse> => {
    console.log('📡 Using bill template with payload:', payload)

    try {
      const response = await apiClient.put<UseBillTemplateResponse>('/mdata/v1/store', payload)
      console.log('📨 Use bill template response:', response.data)
      return response.data
    } catch (error) {
      console.error('🔥 Use bill template failed:', error)
      throw error
    }
  }
}
