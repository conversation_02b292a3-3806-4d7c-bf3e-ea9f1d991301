import { z } from 'zod'

// Channel schema based on API response
const channelSchema = z.object({
  id: z.string(),
  created_at: z.number(),
  created_by: z.string(),
  updated_at: z.number(),
  updated_by: z.string(),
  deleted: z.boolean(),
  deleted_at: z.number().nullable(),
  deleted_by: z.string().nullable(),
  source_id: z.string(),
  source_name: z.string(),
  source_type: z.array(z.string()),
  description: z.string().nullable(),
  extra_data: z.object({
    commission: z.number(),
    exclude_ship: z.number(),
    payment_type: z.string(),
    deduct_tax_rate: z.number(),
    require_tran_no: z.number(),
    use_order_online: z.number(),
    payment_method_id: z.string(),
    payment_method_name: z.string(),
    voucher_run_partner: z.string().nullable(),
    not_show_partner_bill: z.number(),
    marketing_partner_cost: z.number(),
    marketing_partner_cost_type: z.string(),
    marketing_partner_cost_to_date: z.number(),
    marketing_partner_cost_hour_day: z.number(),
    marketing_partner_cost_date_week: z.number(),
    marketing_partner_cost_from_date: z.number()
  }),
  is_fb: z.number(),
  active: z.number(),
  revision: z.number().nullable(),
  brand_uid: z.string(),
  company_uid: z.string(),
  sort: z.number(),
  is_fabi: z.number(),
  store_uid: z.string(),
  partner_config: z.number(),
  stores: z.object({
    id: z.string(),
    created_at: z.number(),
    created_by: z.string(),
    updated_at: z.number(),
    updated_by: z.string(),
    deleted: z.boolean(),
    deleted_at: z.number().nullable(),
    deleted_by: z.string().nullable(),
    store_id: z.string(),
    fb_store_id: z.number(),
    store_name: z.string(),
    is_delivery_direct: z.number(),
    email: z.string(),
    phone: z.string(),
    logo: z.string(),
    background: z.string(),
    facebook: z.string(),
    website: z.string(),
    address: z.string(),
    description: z.string(),
    workstation_id: z.number(),
    active: z.number(),
    is_default: z.number(),
    is_test: z.number(),
    extra_data: z.any(),
    revision: z.number(),
    city_uid: z.string(),
    pos_server_group_uid: z.string().nullable(),
    brand_uid: z.string(),
    company_uid: z.string(),
    latitude: z.number(),
    longitude: z.number(),
    sort: z.number(),
    store_address: z.any(),
    last_synced_transaction: z.string().nullable(),
    delivery_services: z.string(),
    phone_manager: z.string(),
    is_ahamove_active: z.number(),
    expiry_date: z.number(),
    is_fabi: z.number(),
    email_delivery_service: z.string(),
    operation_form: z.string().nullable(),
    partner: z.string(),
    pos_type: z.string().nullable(),
    size: z.number(),
    district: z.string().nullable(),
    currency: z.string(),
    ward: z.string().nullable()
  })
})

// API response schema
const channelsApiResponseSchema = z.object({
  data: z.array(channelSchema),
  track_id: z.string().optional()
})

// Export types
export type Channel = z.infer<typeof channelSchema>
export type ChannelsApiResponse = z.infer<typeof channelsApiResponseSchema>

// Export schemas
export { channelSchema, channelsApiResponseSchema }

// API data interface (raw from API)
export interface ChannelApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  source_id: string
  source_name: string
  source_type: string[]
  description: string | null
  extra_data: {
    commission: number
    exclude_ship: number
    payment_type: string
    deduct_tax_rate: number
    require_tran_no: number
    use_order_online: number
    payment_method_id: string
    payment_method_name: string
    voucher_run_partner: string | null
    not_show_partner_bill: number
    marketing_partner_cost: number
    marketing_partner_cost_type: string
    marketing_partner_cost_to_date: number
    marketing_partner_cost_hour_day: number
    marketing_partner_cost_date_week: number
    marketing_partner_cost_from_date: number
  }
  is_fb: number
  active: number
  revision: number | null
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  store_uid: string
  partner_config: number
  stores: {
    id: string
    created_at: number
    created_by: string
    updated_at: number
    updated_by: string
    deleted: boolean
    deleted_at: number | null
    deleted_by: string | null
    store_id: string
    fb_store_id: number
    store_name: string
    is_delivery_direct: number
    email: string
    phone: string
    logo: string
    background: string
    facebook: string
    website: string
    address: string
    description: string
    workstation_id: number
    active: number
    is_default: number
    is_test: number
    extra_data: Record<string, unknown>
    revision: number
    city_uid: string
    pos_server_group_uid: string | null
    brand_uid: string
    company_uid: string
    latitude: number
    longitude: number
    sort: number
    store_address: Record<string, unknown>
    last_synced_transaction: string | null
    delivery_services: string
    phone_manager: string
    is_ahamove_active: number
    expiry_date: number
    is_fabi: number
    email_delivery_service: string
    operation_form: string | null
    partner: string
    pos_type: string | null
    size: number
    district: string | null
    currency: string
    ward: string | null
  }
}

// Conversion function from API data to Channel
export const convertApiChannelToChannel = (apiChannel: ChannelApiData): Channel => {
  return {
    ...apiChannel
  }
}
