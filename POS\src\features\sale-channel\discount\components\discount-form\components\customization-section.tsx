import { useState } from 'react'

import { <PERSON><PERSON>, Label } from '@/components/ui'

import { useDiscountFormData } from '../stores'
import { AddModal } from './add-modal'

export function CustomizationSection() {
  const { formData, updateFormData } = useDiscountFormData()
  const [modalOpen, setModalOpen] = useState(false)

  const handleAddItems = () => {
    setModalOpen(true)
  }

  const handleSaveItems = (selectedItems: string[], applyToAll: boolean, activeFilter: string | null) => {
    const newFilterState = {
      ...formData.filterState,
      is_all: (applyToAll ? 1 : 0) as 0 | 1,
      is_type: 0 as 0 | 1,
      is_item: 0 as 0 | 1,
      is_combo: 0 as 0 | 1,
      type_id: '',
      item_id: '',
      combo_id: ''
    }

    if (!applyToAll && selectedItems.length > 0 && activeFilter) {
      if (activeFilter === 'groups') {
        newFilterState.is_type = 1 as 0 | 1
        newFilterState.type_id = selectedItems.join(',')
      } else if (activeFilter === 'items') {
        newFilterState.is_item = 1 as 0 | 1
        newFilterState.item_id = selectedItems.join(',')
      } else if (activeFilter === 'packages') {
        newFilterState.is_combo = 1 as 0 | 1
        newFilterState.combo_id = selectedItems.join(',')
      }
    }

    updateFormData({ filterState: newFilterState })
  }

  const isApplyToAll = formData.filterState?.is_all === 1

  const getInitialActiveFilter = () => {
    if (formData.filterState?.is_type === 1) return 'groups'
    if (formData.filterState?.is_item === 1) return 'items'
    if (formData.filterState?.is_combo === 1) return 'packages'
    return null
  }

  const getInitialSelectedItems = () => {
    if (formData.filterState?.is_type === 1 && formData.filterState.type_id) {
      return formData.filterState.type_id.split(',').filter(id => id.trim())
    }
    if (formData.filterState?.is_item === 1 && formData.filterState.item_id) {
      return formData.filterState.item_id.split(',').filter(id => id.trim())
    }
    if (formData.filterState?.is_combo === 1 && formData.filterState.combo_id) {
      return formData.filterState.combo_id.split(',').filter(id => id.trim())
    }
    return []
  }

  const getDisplayText = () => {
    if (isApplyToAll) {
      return 'Áp dụng cho tất cả'
    }

    const selectedItems = getInitialSelectedItems()
    if (selectedItems.length > 0) {
      const activeFilter = getInitialActiveFilter()
      const filterText = activeFilter === 'groups' ? 'nhóm' : activeFilter === 'items' ? 'món' : 'combo'
      return `${selectedItems.length} ${filterText}`
    }

    return 'Thêm'
  }

  return (
    <div className='space-y-4'>
      <h2 className='text-lg font-medium text-gray-900'>Tuỳ chỉnh</h2>
      <div className='mb-4 text-sm text-gray-600'>Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể</div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Áp dụng cho <span className='text-red-500'>*</span>
        </Label>
        <Button
          type='button'
          variant='outline'
          onClick={handleAddItems}
          disabled={!formData.storeUid}
          className='flex-1 justify-start'
        >
          {getDisplayText()}
        </Button>
      </div>

      <AddModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        storeUid={formData.storeUid}
        onSave={handleSaveItems}
        initialApplyToAll={isApplyToAll}
        initialActiveFilter={getInitialActiveFilter()}
        initialSelectedItems={getInitialSelectedItems()}
      />
    </div>
  )
}
