import { api } from './api'

export interface Table {
  id: string
  table_id?: string
  table_name: string
  table_code?: string
  description?: string
  area_uid: string
  area_name?: string
  store_uid: string
  company_uid: string
  brand_uid: string
  source_id?: string
  sort: number
  active: number
  position_x?: number
  position_y?: number
  width?: number
  height?: number
  extra_data?: {
    color?: string
    font_size?: string
    position?: {
      x: number
      y: number
    }
    size?: {
      width: number
      height: number
    }
    order_list?: Array<{
      item_id: string
      quantity: number
    }>
  }
  area?: {
    id: string
    area_id: string
    area_name: string
    active: number
    sort: number
    extra_data?: any
  }
  created_at: string
  updated_at: string
}

export interface GetTablesParams {
  skip_limit?: boolean
  company_uid: string
  brand_uid: string
  store_uid: string
  area_uid: string
  order?: string
}

export interface UpdateTablePositionRequest {
  table_uid: string
  position_x: number
  position_y: number
  sort?: number
}

export interface TablesApiResponse {
  data: Table[]
  total: number
}

/**
 * Table Layout API client
 */
export const tableLayoutApi = {
  /**
   * Get tables for layout
   */
  getTables: async (params: GetTablesParams): Promise<TablesApiResponse> => {
    const searchParams = new URLSearchParams()

    if (params.skip_limit) {
      searchParams.append('skip_limit', 'true')
    }
    if (params.company_uid) {
      searchParams.append('company_uid', params.company_uid)
    }
    if (params.brand_uid) {
      searchParams.append('brand_uid', params.brand_uid)
    }
    if (params.store_uid) {
      searchParams.append('store_uid', params.store_uid)
    }
    if (params.area_uid) {
      searchParams.append('area_uid', params.area_uid)
    }
    if (params.order) {
      searchParams.append('order', params.order)
    }

    const url = `/pos/v1/table${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

    const response = await api.get<Table[]>(url)

    // Handle different response formats
    if (Array.isArray(response.data)) {
      return {
        data: response.data,
        total: response.data.length
      }
    }

    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data as TablesApiResponse
    }

    return {
      data: [],
      total: 0
    }
  },

  /**
   * Update table position
   */
  updateTablePosition: async (request: UpdateTablePositionRequest): Promise<void> => {
    await api.patch(`/pos/v1/table/${request.table_uid}`, {
      position_x: request.position_x,
      position_y: request.position_y,
      sort: request.sort
    })
  },

  /**
   * Bulk update tables
   */
  bulkUpdateTables: async (tables: Partial<Table>[]): Promise<void> => {
    await api.post('/pos/v1/table', tables)
  }
}
