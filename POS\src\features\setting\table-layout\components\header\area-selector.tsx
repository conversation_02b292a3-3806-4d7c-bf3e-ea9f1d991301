import React from 'react'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import type { AreaOption } from '../../data/table-layout-types'

interface AreaSelectorProps {
  areas: AreaOption[]
  selectedAreaId: string
  onAreaChange: (areaId: string) => void
}

export const AreaSelector: React.FC<AreaSelectorProps> = ({ areas, selectedAreaId, onAreaChange }) => {
  return (
    <div className='flex items-center gap-2'>
      <span className='text-sm font-bold text-gray-700'>Danh sách bàn</span>
      <Select value={selectedAreaId} onValueChange={onAreaChange}>
        <SelectTrigger className='w-48'>
          <SelectValue placeholder='Chọn khu vực' />
        </SelectTrigger>
        <SelectContent>
          {areas.map(area => (
            <SelectItem key={area.id} value={area.id}>
              {area.area_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
