import type {
  MembershipDiscountApiResponse,
  MembershipDiscount,
  GetMembershipDiscountsParams,
  MembershipDiscountApiData
} from '@/types/discounts'
import { convertMembershipDiscountApiData } from '@/types/discounts'

import { api } from '@/lib/api/pos/pos-api'

export async function getMembershipDiscounts(params: GetMembershipDiscountsParams = {}): Promise<MembershipDiscount[]> {
  try {
    const searchParams = new URLSearchParams()

    if (params.companyUid) {
      searchParams.append('company_uid', params.companyUid)
    }
    if (params.brandUid) {
      searchParams.append('brand_uid', params.brandUid)
    }
    if (params.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params.listStoreUid && params.listStoreUid.length > 0) {
      searchParams.append('list_store_uid', params.listStoreUid.join(','))
    }
    if (params.status) {
      searchParams.append('status', params.status)
    }
    if (params.active !== undefined) {
      searchParams.append('active', params.active.toString())
    }

    const response = await api.get(`/mdata/v1/discount-membership?${searchParams.toString()}`)

    const responseData = response.data as MembershipDiscountApiResponse

    if (!responseData?.data) {
      return []
    }

    const discounts = responseData.data.map((apiDiscount: MembershipDiscountApiData) => {
      const storeName = 'Store Name'
      return convertMembershipDiscountApiData(apiDiscount, storeName)
    })

    return discounts
  } catch (error) {
    console.error('Error fetching membership discounts:', error)
    return []
  }
}

export async function createMembershipDiscount(
  discountData: Omit<MembershipDiscountApiData, 'id'>
): Promise<MembershipDiscountApiData> {
  try {
    const response = await api.post('/mdata/v1/discount-membership', discountData)
    return response.data.data as MembershipDiscountApiData
  } catch (error) {
    console.error('Error creating membership discount:', error)
    throw error
  }
}

export async function updateMembershipDiscount(discountData: MembershipDiscountApiData): Promise<void> {
  try {
    await api.put('/mdata/v1/discount-membership', discountData)
  } catch (error) {
    console.error('Error updating membership discount:', error)
    throw error
  }
}

export async function deleteMembershipDiscount(discountId: string): Promise<void> {
  try {
    await api.delete(`/mdata/v1/discount-membership/${discountId}`)
  } catch (error) {
    console.error('Error deleting membership discount:', error)
    throw error
  }
}

/**
 * Get membership discount programs for a specific store
 */
export async function getMembershipDiscountPrograms(params: {
  companyUid: string
  brandUid: string
  storeUid: string
}): Promise<MembershipDiscountApiData[]> {
  try {
    const searchParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      store_uid: params.storeUid
    })

    const response = await api.get(`/mdata/v1/discount-membership?${searchParams.toString()}`)
    const responseData = response.data as MembershipDiscountApiResponse

    return responseData?.data || []
  } catch (error) {
    console.error('Error fetching membership discount programs:', error)
    return []
  }
}

/**
 * Get membership discount by ID only (simpler version)
 * API: /mdata/v1/discount-membership?company_uid=...&brand_uid=...&id=...
 */
export async function getMembershipDiscountByIdOnly(params: {
  companyUid: string
  brandUid: string
  id: string
}): Promise<MembershipDiscountApiData> {
  try {
    const queryParams = new URLSearchParams({
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      id: params.id
    })

    const url = `/mdata/v1/discount-membership?${queryParams.toString()}`
    console.log('🔥 Membership Discount Detail API Call (ID Only):', url)
    console.log('🔥 Params:', params)

    const response = await api.get(url)

    console.log('🔥 Membership Discount Detail Response:', response.data)

    // The API returns a single discount object in the data field (not an array)
    const responseData = response.data as { data: MembershipDiscountApiData; track_id: string }

    if (!responseData?.data) {
      throw new Error('Membership discount not found')
    }

    const discountData = responseData.data
    console.log('🔥 Parsed Membership Discount:', discountData)

    return discountData
  } catch (error) {
    console.error('Error fetching membership discount by ID:', error)
    throw error
  }
}

/**
 * Get membership discount by ID and store UID
 * API: /mdata/v1/discount-membership?company_uid=...&brand_uid=...&id=...&store_uid=...
 */
export async function getMembershipDiscountById(params: {
  companyUid: string
  brandUid: string
  id: string
  storeUid: string
}): Promise<MembershipDiscountApiData> {
  try {
    const queryParams = new URLSearchParams({
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      id: params.id,
      store_uid: params.storeUid
    })

    const url = `/mdata/v1/discount-membership?${queryParams.toString()}`
    console.log('🔥 Membership Discount Detail API Call:', url)
    console.log('🔥 Params:', params)

    const response = await api.get(url)

    console.log('🔥 Membership Discount Detail Response:', response.data)

    // The API returns a single discount object in the data field (not an array)
    const responseData = response.data as { data: MembershipDiscountApiData; track_id: string }

    if (!responseData?.data) {
      throw new Error('Membership discount not found')
    }

    const discountData = responseData.data
    console.log('🔥 Parsed Membership Discount:', discountData)

    return discountData
  } catch (error) {
    console.error('Error fetching membership discount by ID:', error)
    throw error
  }
}

/**
 * Get membership discount promotions for a specific store
 * Matches the exact API call: /mdata/v1/promotions?skip_limit=true&company_uid=...&brand_uid=...&list_store_uid=...&partner_auto_gen=0&active=1
 */
export async function getMembershipDiscountPromotions(params: {
  companyUid: string
  brandUid: string
  storeUid: string
}): Promise<{ promotion_uid: string; promotion_name: string }[]> {
  try {
    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      store_uid: params.storeUid,
      partner_auto_gen: '0',
      active: '1'
    })

    const url = `/mdata/v1/promotions?${queryParams.toString()}`
    console.log('🔥 Membership Discount Promotions API Call:', url)
    console.log('🔥 Params:', params)

    const response = await api.get(url)

    console.log('🔥 Membership Discount Promotions Response:', response.data)

    // Parse the nested structure: data[].list_data[]
    const promotionGroups = Array.isArray(response.data?.data) ? response.data.data : []
    const flatPromotions = promotionGroups.flatMap((group: any) =>
      Array.isArray(group.list_data)
        ? group.list_data.map((item: any) => ({
            promotion_uid: item.id, // The 'id' field is the promotion_uid we need
            promotion_name: item.promotion_name
          }))
        : []
    )

    console.log('🔥 Parsed Membership Promotions:', flatPromotions)
    return flatPromotions
  } catch (error) {
    console.error('Error fetching membership discount promotions:', error)
    return []
  }
}

/**
 * Clone membership discount programs to target store
 */
export async function cloneMembershipDiscounts(params: {
  companyUid: string
  brandUid: string
  listDiscountUid: string[]
  storeUidRoot: string
  storeUidTarget: string
}): Promise<{ success: boolean; message?: string; track_id: string }> {
  try {
    const payload = {
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      list_discount_uid: params.listDiscountUid,
      store_uid_root: params.storeUidRoot,
      store_uid_target: params.storeUidTarget
    }

    const response = await api.post<{ success: boolean; message?: string; track_id: string }>(
      '/mdata/v1/discount-membership/clone',
      payload
    )
    return response.data.data
  } catch (error) {
    console.error('Error cloning membership discounts:', error)
    throw error
  }
}
