// Sale Summary Overview API Types

export interface SaleSummaryOverviewPreviousPeriod {
  percentage: number
  revenue_net: number
  discount_amount: number
  commission_amount: number
  total_sales: number
  peo_count: number
  start_date: number
  end_date: number
}

export interface SaleSummaryOverviewData {
  revenue_net: number
  discount_amount: number
  discount_wo_commission_amount: number
  commission_amount: number
  total_sales: number
  peo_count: number
  previous_period: SaleSummaryOverviewPreviousPeriod
  sale_tracking: Record<string, unknown>
}

export interface SaleSummaryOverviewResponse {
  data: SaleSummaryOverviewData
  message: string | null
  track_id: string
}

export interface GetSaleSummaryOverviewParams {
  brand_uid: string
  company_uid: string
  list_store_uid: string
  start_date: number
  end_date: number
  store_open_at?: number
}
