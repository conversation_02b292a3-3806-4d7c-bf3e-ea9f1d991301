export interface ExtraPointItem {
  id: string
  company_id: string
  type_id: string
  type_name: string
  extra_rate_type: string
  start_date: string
  end_date: string
  extra_rate: number
  time_hour_day: number
  time_date_week: number
  active: number
  created_at: string
  updated_at: string
  day_before_birthday?: number
  day_after_birthday?: number
}

export interface ExtraPointResponse {
  count: number
  totalPage: number
  list_membership_type_extra_rate: ExtraPointItem[]
}

export interface ExtraPointParams {
  company_id: string
  pos_parent: string
}

export interface UpdateExtraPointRequest {
  id: string
  company_id: string
  type_id: string
  type_name: string
  extra_rate_type: string
  start_date: string
  end_date: string
  extra_rate: number
  time_hour_day?: number
  time_date_week?: number
  active: number
  created_at: string
  day_before_birthday?: number
  day_after_birthday?: number
}

export interface UpdateExtraPointParams {
  pos_parent: string
}

export interface CreateExtraPointRequest {
  company_id: string
  type_id: string
  type_name: string
  start_date: string
  end_date: string
  extra_rate: number
  active: number
  extra_rate_type: string
  time_date_week: number
  time_hour_day: number
  day_before_birthday?: number
  day_after_birthday?: number
}

export interface CreateExtraPointParams {
  pos_parent: string
}
