import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { convertApiStoreToStore, type Store } from '@/types/store'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { apiClient } from '@/lib/api/pos/pos-api'
import { getStores, createStore, type StoreListParams, ApiStore } from '@/lib/stores-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseStoresDataOptions {
  params?: Partial<StoreListParams>
  enabled?: boolean
}

export const useStoresData = (options: UseStoresDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const dynamicParams: StoreListParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    limit: 50
  }

  const finalParams = { ...dynamicParams, ...params }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.STORES_LIST, finalParams],
    queryFn: async (): Promise<Store[]> => {
      const response = await getStores(finalParams)
      // Map API data to Store format but preserve original API fields
      return (
        response.data?.map(apiStore => {
          const convertedStore = convertApiStoreToStore(apiStore)
          // Add raw API fields for backward compatibility
          return {
            ...convertedStore,
            fb_store_id: apiStore.fb_store_id,
            city_name: apiStore.city_name,
            expiry_date: apiStore.expiry_date,
            active: apiStore.active,
            latitude: apiStore.latitude,
            longitude: apiStore.longitude,
            address: apiStore.address
          } as Store & any
        }) || []
      )
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 10 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000
  })
}

export const useFilteredStoresData = (
  filters: {
    searchTerm?: string
    status?: string
    storeType?: string
  } = {}
) => {
  const storesQuery = useStoresData()

  const filteredData = storesQuery.data?.filter(store => {
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesSearch =
        store.name.toLowerCase().includes(searchLower) ||
        store.address.toLowerCase().includes(searchLower) ||
        store.phone.toLowerCase().includes(searchLower)

      if (!matchesSearch) return false
    }

    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active' && !store.isActive) return false
      if (filters.status === 'inactive' && store.isActive) return false
    }

    if (filters.storeType && filters.storeType !== 'all') {
      // This would depend on the actual API response structure
      // For now, we'll skip this filter since the current API doesn't seem to have store_type
    }

    return true
  })

  return {
    ...storesQuery,
    data: filteredData || []
  }
}

export const useStoreData = (storeId: string, enabled: boolean = true) => {
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.STORES_DETAIL, storeId, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<Store | undefined> => {
      if (!company?.id || !selectedBrand?.id || !storeId) {
        return undefined
      }

      const queryParams = new URLSearchParams({
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: storeId
      })

      const response = await apiClient.get(`/mdata/v1/store?${queryParams.toString()}`)

      if (!response.data?.data) {
        return undefined
      }

      const storeData = response.data.data

      return {
        id: storeData.id,
        name: storeData.store_name,
        code: storeData.store_id,
        status: storeData.active === 1 ? 'active' : 'inactive',
        address: storeData.address,
        phone: storeData.phone,
        email: storeData.email || '',
        companyId: storeData.company_uid,
        brandId: storeData.brand_uid,
        cityId: storeData.city_uid,
        cityName: storeData.city?.city_name || '',
        isActive: storeData.active === 1,
        isFabi: storeData.is_fabi === 1,
        isAhamoveActive: storeData.is_ahamove_active === 1,
        latitude: storeData.latitude || 0,
        longitude: storeData.longitude || 0,
        deliveryServices: storeData.delivery_services || '',
        createdAt: new Date(storeData.created_at * 1000),
        updatedAt: new Date(storeData.updated_at * 1000),
        expiryDate: new Date(storeData.expiry_date * 1000)
      } as Store
    },
    enabled: enabled && !!storeId && !!company?.id && !!selectedBrand?.id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

export const useStoresStats = () => {
  const storesQuery = useStoresData()

  const stats = {
    total: storesQuery.data?.length || 0,
    active: storesQuery.data?.filter(store => store.isActive).length || 0,
    inactive: storesQuery.data?.filter(store => !store.isActive).length || 0
  }

  return {
    ...storesQuery,
    stats
  }
}

export const useStoresForSort = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.STORES_LIST, 'sort'],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      const response = await apiClient.get('/mdata/v1/stores', {
        params: {
          company_uid: companyUid,
          brand_uid: brandUid,
          skip_limit: true
        }
      })

      return response.data
    },
    enabled: !!(company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000
  })
}

export const useUpdateStoresSort = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (stores: any[]) => {
      // Gửi toàn bộ payload store với field sort đã được cập nhật
      return await apiClient.put('/mdata/v1/sort-store', stores)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.STORES_LIST]
      })
      toast.success('Cập nhật thứ tự cửa hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thứ tự cửa hàng'
      toast.error(errorMessage)
    }
  })

  return {
    updateSort: mutate,
    isUpdating: isPending
  }
}

/**
 * Hook for creating a new store
 */
export const useCreateStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: Partial<ApiStore>) => {
      const response = await createStore(data)
      return response
    },
    onSuccess: () => {
      // Invalidate stores list to refresh data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.STORES_LIST],
        refetchType: 'none'
      })

      // Manually refetch in background to update data
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.STORES_LIST]
        })
      }, 100)

      toast.success('Tạo cửa hàng thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo cửa hàng: ${error.message}`)
    }
  })

  return { createStore: mutate, isCreating: isPending }
}
