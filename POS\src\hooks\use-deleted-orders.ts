/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useMemo, useCallback } from 'react'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import {
  deletedOrdersApi,
  type DeletedOrdersSummary,
  type DeletedOrdersResponse,
} from '@/lib/deleted-orders-api'

interface UseDeletedOrdersOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  autoFetch?: boolean
}

interface UseDeletedOrdersReturn {
  // Raw deleted orders data
  deletedOrders: any[]
  shiftData: any[]

  // Processed summary statistics
  summary: DeletedOrdersSummary | null

  // Store statistics (sorted by most deleted orders)
  topStoresByDeleted: Array<{
    storeUid: string
    storeName: string
    totalDeleted: number
    totalDeletedAmount: number
    deletedPercentage: number
    topEmployees: Array<{
      employeeUid: string
      employeeName: string
      deletedCount: number
      deletedAmount: number
    }>
    topReasons: Array<{
      reason: string
      count: number
      amount: number
    }>
  }>

  // Employee statistics (sorted by most deleted orders)
  topEmployeesByDeleted: Array<{
    employeeUid: string
    employeeName: string
    totalDeleted: number
    totalDeletedAmount: number
    storeCount: number
    stores: string[]
  }>

  // Reason statistics (sorted by most common)
  topReasonsByCount: Array<{
    reason: string
    count: number
    amount: number
    storeCount: number
  }>

  // Loading and error states
  isLoading: boolean
  error: string | null
  loadingMessage?: string

  // Summary totals
  totalDeleted: number
  totalDeletedAmount: number
  overallDeletedPercentage: number

  refetch: () => Promise<void>

  selectedBrand: any
  currentBrandStores: any[]
}

/**
 * Custom hook for deleted orders statistics
 * Provides comprehensive analytics on cancelled/deleted orders across all stores
 */
export function useDeletedOrders(
  options: UseDeletedOrdersOptions = {}
): UseDeletedOrdersReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    autoFetch = true,
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()

  const [summary, setSummary] = useState<DeletedOrdersSummary | null>(null)
  const [rawData, setRawData] = useState<DeletedOrdersResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(
    undefined
  )

  const startTime = useMemo(() => dateRange?.from.getTime(), [dateRange?.from])
  const endTime = useMemo(() => dateRange?.to.getTime(), [dateRange?.to])
  const brandId = selectedBrand?.id
  const companyId = company?.id

  const fetchDeletedOrders = useCallback(
    async (retryCount = 0) => {
      if (!brandId || !companyId) {
        setError('Brand or company not selected')
        return
      }

      if (!startTime || !endTime) {
        setError('Date range is required')
        return
      }

      // Check if we already have data for this exact request
      if (
        summary &&
        rawData &&
        Math.abs(startTime - (rawData as any)._requestStartTime) < 1000 &&
        Math.abs(endTime - (rawData as any)._requestEndTime) < 1000
      ) {
        // Same request, don't refetch
        return
      }

      setIsLoading(true)
      setError(null)

      // Set appropriate loading message
      const daysDiff = Math.ceil((endTime - startTime) / (1000 * 60 * 60 * 24))
      if (daysDiff > 90) {
        setLoadingMessage('Loading large dataset, this may take a moment...')
      } else if (retryCount > 0) {
        setLoadingMessage(`Retrying... (attempt ${retryCount + 1}/3)`)
      } else {
        setLoadingMessage('Loading deleted orders data...')
      }

      try {
        // If specific stores are selected, we need to fetch data for each store
        // since the API doesn't support multiple store_uid in one request
        let allDeletedOrders: any[] = []
        let allShiftData: any[] = []

        if (
          selectedStores &&
          selectedStores.length > 0 &&
          !selectedStores.includes('all-stores')
        ) {
          // Fetch for specific stores
          const storeUids = selectedStores.filter(
            (id) => id !== 'all-stores' && id !== 'no-stores'
          )

          for (const storeUid of storeUids) {
            const response = await deletedOrdersApi.getDeletedOrders({
              companyUid: companyId,
              brandUid: brandId,
              storeUid,
              startDate: startTime,
              endDate: endTime,
              page: 1,
            })

            allDeletedOrders = [...allDeletedOrders, ...response.data]
            allShiftData = [...allShiftData, ...response.data_shift]
          }
        } else {
          // Fetch for all stores (don't specify store_uid)
          const response = await deletedOrdersApi.getDeletedOrders({
            companyUid: companyId,
            brandUid: brandId,
            startDate: startTime,
            endDate: endTime,
            page: 1,
          })

          allDeletedOrders = response.data
          allShiftData = response.data_shift
        }

        // Create combined response
        const combinedResponse: DeletedOrdersResponse = {
          data: allDeletedOrders,
          data_shift: allShiftData,
          track_id: `combined-${Date.now()}`,
        }

        const processedSummary =
          deletedOrdersApi.processDeletedOrdersSummary(combinedResponse)

        // Add request timestamps for caching
        const responseWithTimestamp = {
          ...combinedResponse,
          _requestStartTime: startTime,
          _requestEndTime: endTime,
        }

        setRawData(responseWithTimestamp)
        setSummary(processedSummary)
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to fetch deleted orders data'

        // Handle timeout errors with retry
        if (
          (errorMessage.includes('timeout') || errorMessage.includes('504')) &&
          retryCount < 2
        ) {
          // eslint-disable-next-line no-console
          console.warn(
            `Deleted Orders API timeout, retrying... (attempt ${retryCount + 1}/3)`
          )
          // Wait before retry with exponential backoff
          await new Promise((resolve) =>
            setTimeout(resolve, (retryCount + 1) * 2000)
          )
          return fetchDeletedOrders(retryCount + 1)
        }

        // Handle 504 Gateway Timeout specifically
        if (errorMessage.includes('504')) {
          setError(
            'Server is taking too long to respond. Please try again with a smaller date range.'
          )
        } else {
          setError(errorMessage)
        }
      } finally {
        setIsLoading(false)
        setLoadingMessage(undefined)
      }
    },
    [
      brandId,
      companyId,
      startTime,
      endTime,
      currentBrandStores,
      selectedStores,
      rawData,
      summary,
    ]
  )

  useEffect(() => {
    if (autoFetch) {
      fetchDeletedOrders()
    }
  }, [autoFetch, fetchDeletedOrders])

  const deletedOrders = useMemo(() => {
    return rawData?.data || []
  }, [rawData?.data])

  const shiftData = useMemo(() => {
    return rawData?.data_shift || []
  }, [rawData?.data_shift])

  const topStoresByDeleted = useMemo(() => {
    return summary?.storeStats || []
  }, [summary?.storeStats])

  const topEmployeesByDeleted = useMemo(() => {
    return summary?.employeeStats || []
  }, [summary?.employeeStats])

  const topReasonsByCount = useMemo(() => {
    return summary?.reasonStats || []
  }, [summary?.reasonStats])

  const totalDeleted = useMemo(() => {
    return summary?.totalDeleted || 0
  }, [summary?.totalDeleted])

  const totalDeletedAmount = useMemo(() => {
    return summary?.totalDeletedAmount || 0
  }, [summary?.totalDeletedAmount])

  const overallDeletedPercentage = useMemo(() => {
    return summary?.overallDeletedPercentage || 0
  }, [summary?.overallDeletedPercentage])

  return {
    deletedOrders,
    shiftData,

    summary,

    topStoresByDeleted,
    topEmployeesByDeleted,
    topReasonsByCount,

    isLoading,
    error,
    loadingMessage,

    totalDeleted,
    totalDeletedAmount,
    overallDeletedPercentage,

    refetch: fetchDeletedOrders,

    selectedBrand,
    currentBrandStores,
  }
}

/**
 * Convenience hook to get deleted orders for current date range
 */
export function useCurrentBrandDeletedOrders(dateRange?: {
  from: Date
  to: Date
}) {
  return useDeletedOrders({
    dateRange,
    autoFetch: true,
  })
}

/**
 * Hook to get deleted orders with specific filters
 */
export function useDeletedOrdersWithFilters(
  dateRange: { from: Date; to: Date },
  selectedStores: string[]
) {
  return useDeletedOrders({
    dateRange,
    selectedStores,
    autoFetch: true,
  })
}

export default useDeletedOrders
