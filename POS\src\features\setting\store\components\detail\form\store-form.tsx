import type { UseFormReturn } from 'react-hook-form'

import { Form } from '@/components/ui'

import { type StoreFormValues } from '../../../data'
import {
  BasicInfoSection,
  OtherInfoSection,
  BrandBuildingSection,
  VietQrSection,
  MoMoQrSection,
  PrintConfigSection,
  OrderSourceSection,
  AhamoveSection,
  AdvancedConfigSection,
  DeviceSection,
  RevenueLockSection
} from '../sections'

export type FormMode = 'add' | 'edit'

interface StoreFormProps {
  form: UseFormReturn<StoreFormValues>
  mode: FormMode
  isLoading?: boolean
  storeId?: string
}

export function StoreForm({ form, isLoading = false, mode, storeId }: StoreFormProps) {
  return (
    <Form {...form}>
      <div className='space-y-6'>
        <div className='rounded-lg border bg-white p-6'>
          <BasicInfoSection form={form} isLoading={isLoading} mode={mode} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <VietQrSection form={form} isLoading={isLoading} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <MoMoQrSection form={form} isLoading={isLoading} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <PrintConfigSection form={form} isLoading={isLoading} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <OrderSourceSection form={form} isLoading={isLoading} storeUid={storeId} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <AhamoveSection form={form} isLoading={isLoading} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <RevenueLockSection form={form} isLoading={isLoading} />
        </div>

        <AdvancedConfigSection form={form} isLoading={isLoading} />

        {mode === 'edit' && (
          <div className='rounded-lg border bg-white p-6'>
            <DeviceSection form={form} isLoading={isLoading} storeUid={storeId} />
          </div>
        )}

        <div className='rounded-lg border bg-white p-6'>
          <OtherInfoSection form={form} isLoading={isLoading} />
        </div>

        <div className='rounded-lg border bg-white p-6'>
          <BrandBuildingSection form={form} isLoading={isLoading} />
        </div>
      </div>
    </Form>
  )
}
