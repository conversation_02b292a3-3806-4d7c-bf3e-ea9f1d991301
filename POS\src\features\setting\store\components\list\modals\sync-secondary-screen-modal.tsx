import { useState, useRef } from 'react'

import { ImageIcon, Upload } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useStoresData, useImageUpload, useSyncStoreBackground } from '@/hooks/api'

import { Button } from '@/components/ui/button'

import { PosModal, MultiSelectCombobox } from '@/components/pos'

interface SyncSecondaryScreenModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SyncSecondaryScreenModal({ open, onOpenChange }: SyncSecondaryScreenModalProps) {
  const { data: stores = [] } = useStoresData()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const [selectedStoreIds, setSelectedStoreIds] = useState<string[]>([])
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const uploadImageMutation = useImageUpload()
  const syncBackgroundMutation = useSyncStoreBackground()

  const handleCancel = () => {
    setSelectedStoreIds([])
    setSelectedImage(null)
    setImagePreview(null)
    onOpenChange(false)
  }

  const handleConfirm = async () => {
    if (selectedStoreIds.length === 0) {
      toast.error('Vui lòng chọn ít nhất một cửa hàng')
      return
    }

    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    try {
      let backgroundUrl = ''

      if (selectedImage) {
        const uploadResponse = await uploadImageMutation.mutateAsync(selectedImage)
        backgroundUrl = uploadResponse.data.image_url
      }

      await syncBackgroundMutation.mutateAsync({
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        list_store_uid: selectedStoreIds,
        background: backgroundUrl
      })

      setSelectedStoreIds([])
      setSelectedImage(null)
      setImagePreview(null)
      onOpenChange(false)
    } catch (error) {
      console.error('Sync failed:', error)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const maxSize = 2.5 * 1024 * 1024
    if (file.size > maxSize) {
      alert('Kích thước ảnh phải nhỏ hơn 2.5MB')
      return
    }

    if (!file.type.startsWith('image/')) {
      alert('Vui lòng chọn file ảnh')
      return
    }

    setSelectedImage(file)

    const reader = new FileReader()
    reader.onload = e => {
      setImagePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <PosModal
      open={open}
      onOpenChange={onOpenChange}
      title='Đồng bộ màn hình phụ'
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='Đồng bộ'
      confirmDisabled={
        selectedStoreIds.length === 0 || uploadImageMutation.isPending || syncBackgroundMutation.isPending
      }
      maxWidth='sm:max-w-[600px] sm:min-h-[500px]'
      disableCancelButton={true}
    >
      <div className='space-y-4'>
        <div className='space-y-2'>
          <label className='text-sm font-medium'>Chọn cửa hàng</label>
          <MultiSelectCombobox
            options={stores.map((store: any) => ({ value: store.id, label: store.name }))}
            value={selectedStoreIds}
            onValueChange={setSelectedStoreIds}
            placeholder='Chọn cửa hàng để đồng bộ'
            searchPlaceholder='Tìm kiếm cửa hàng...'
            emptyText='Không tìm thấy cửa hàng.'
            selectAllLabel='Chọn tất cả'
            allSelectedText='Tất cả cửa hàng'
            selectedCountText={(count: number) => `${count} cửa hàng đã chọn`}
            className='w-full justify-between'
          />
        </div>

        <div className='space-y-2'>
          <label className='text-sm font-medium'>Hình nền trên thiết bị bán hàng</label>
          <p className='text-xs text-gray-500'>
            Hình ảnh này xuất hiện trên màn hình 2 của các thiết bị pos 2 màn hình. Kích thước đề xuất 1920x1080 px
          </p>

          <input ref={fileInputRef} type='file' accept='image/*' onChange={handleImageUpload} className='hidden' />

          <div className='min-h-[200px] rounded-lg border-2 border-dashed border-gray-300 p-6'>
            {imagePreview && (
              <div className='space-y-3'>
                <img src={imagePreview} alt='Preview' className='h-48 w-full rounded-lg object-cover' />
                <Button type='button' variant='outline' size='sm' onClick={handleUploadClick} className='w-full'>
                  <Upload className='mr-2 h-4 w-4' />
                  Thay đổi ảnh
                </Button>
              </div>
            )}
            {!imagePreview && (
              <div className='space-y-3 text-center'>
                <ImageIcon className='mx-auto h-12 w-12 text-gray-400' />
                <p className='text-sm text-gray-600'>Tải lên một hình ảnh hồ sơ</p>
                <Button type='button' variant='outline' size='sm' onClick={handleUploadClick}>
                  <Upload className='mr-2 h-4 w-4' />
                  Chọn ảnh
                </Button>
                <p className='text-xs text-gray-500'>Kích thước ảnh nhỏ hơn 2.5MB</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PosModal>
  )
}
