// Types for Sales API based on actual response structure
export interface SaleData {
  tran_id: string
  tran_no: string
  store_uid: string
  store_name: string
  total_amount: number
  amount_origin: number
  discount_amount: number
  net_amount: number
  voucher_code: string
  employee_name: string
  employee_uid: string
  tran_date: number
  created_at: string
  updated_at: string
  table_name?: string
  source_voucher?: string
  source_deli?: string
  shift_id?: string
}

export interface VoucherSalesSummary {
  storeUid: string
  storeName: string
  totalAmountOrigin: number
  totalDiscountAmount: number
  totalNetAmount: number
  transactionCount: number
  voucherSales: SaleData[]
}

// Types for sale-not-sync-vat endpoint
export interface SaleNotSyncVatData {
  id: string
  created_at: string
  updated_at: string
  tran_id: string
  origin_tran_id: string
  tran_no: string
  tran_date: number
  currency: string
  discount_extra: number
  discount_extra_amount: number
  employee_id: string
  employee_name: string
  start_date: number
  start_hour: number
  start_minute: number
  end_hour: number
  end_minute: number
  sale_note: string
  sale_type: string
  sale_method: string | null
  service_charge: number
  service_charge_amount: number
  shift_id: string
  ship_fee_amount: number
  source_fb_id: string
  table_name: string
  total_amount: number
  voucher_amount: number
  voucher_code: string
  voucher_name: string
  foodbook_order_id: string
  source_voucher: string
  store_uid: string
  brand_uid: string
  company_uid: string
  table_id: string | null
  store_id: string | null
  brand_id: string
  company_id: string | null
  device_code: string
  order_type: string
  area_id: string | null
  source_deli: string
  sync_status: number
  discount_extra_name: string
  service_charge_name: string
  voucher_extra: number
  vat_amount: number
  vat_extra: number
  amount_discount_detail: number
  amount_discount_price: number
  amount_origin: number
  raw_data: unknown | null
  voucher_amount_paid: number
  state_action_bill: number
  version_app: string
  city_uid: string
  sale_updated_at: number
  extra_data: {
    Point: number
    mkt_max: number
    comm_max: number
    del_edit: number
    ship_fee: number
    ship_lat: number
    ship_lng: number
    peo_count: number
    tip_amount: number
    vat_option: number
    excess_cash: number
    payment_pda: number
    voucher_log: string
    process_time: number
    customer_name: string
    is_print_bill: number
    split_two_vat: number
    customer_email: string
    customer_phone: string
    deposit_amount: number
    money_received: number
    partner_config: number
    tran_no_partner: string
    customer_address: string
    print_price_temp: number
    customer_birthday: string
    Membership_Type_Id: string
    message_merge_table: string
    Membership_Type_Name: string
    call_voucher_partner: number
    message_modify_table: string
    not_show_partner_bill: number
    number_print_check_list: number
  }
  discount_extra_amount_campaign: number
  sale_by_source_payment_status: string
  commission: number
  commission_amount: number
  partner_marketing: number
  partner_marketing_amount: number
  is_sync_vat: number
  vat_invoice_number: string | null
  vat_invoice_date: string | null
  extra_sale: Record<string, unknown>
  merged_tran_id: string | null
  vat_invoice_series: string | null
  vat_tax_reverse_amount: number
  buffet_id: string | null
  discount_vat_amount: number
  is_fabi: number
  payment_method_id: string
  payment_method_name: string
  enable_vat_cms: number
  enable_vat_pos: number
  area_name: string | null
}

export interface SalesSummary {
  storeUid: string
  storeName: string
  totalTransactions: number
  totalAmount: number
  discountAmount: number
  netAmount: number
  discountPercentage: number
  salesData: SaleData[]
}

export interface SalesResponse {
  data: SaleData[]
  pagination?: {
    page: number
    totalPages: number
    totalRecords: number
  }
}

// API Parameter types
export interface GetSalesReportParams {
  companyUid: string
  brandUid: string
  listStoreUid: string
  startDate: number
  endDate: number
  page?: number
  sourceId?: number
  isDiscount?: number
}

export interface GetAllSalesDataParams {
  companyUid: string
  brandUid: string
  listStoreUid: string
  startDate: number
  endDate: number
  sourceId?: number
  isDiscount?: number
  maxPages?: number
}

export interface GetSalesSummaryParams {
  companyUid: string
  brandUid: string
  listStoreUid: string
  startDate: number
  endDate: number
  sourceId?: number
  isDiscount?: number
}

export interface GetSalesByVoucherCodesParams {
  companyUid: string
  brandUid: string
  listStoreUid: string
  startDate: number
  endDate: number
  voucherCodes: string[]
  sourceId?: number
}

export interface GetVoucherSummaryParams {
  companyUid: string
  brandUid: string
  listStoreUid: string
  startDate: number
  endDate: number
  voucherCodes: string[]
  sourceId?: number
}

export interface GetMultiStoreVoucherSummaryParams {
  companyUid: string
  brandUid: string
  storeUids: string[]
  startDate: number
  endDate: number
  voucherCodes: string[]
  sourceId?: number
}

export interface GetSaleNotSyncVatParams {
  companyUid: string
  brandUid: string
  storeUid: string
  startDate: number
  endDate: number
  sourceId?: number
  page?: number
  resultsPerPage?: number
}

export interface GetAllSaleNotSyncVatParams {
  companyUid: string
  brandUid: string
  storeUid: string
  startDate: number
  endDate: number
  sourceId?: number
  maxPages?: number
}

// Response types
export interface SaleNotSyncVatResponse {
  data: SaleNotSyncVatData[]
  pagination?: {
    page: number
    resultsPerPage: number
    hasMore: boolean
  }
}

export interface TotalVoucherAmountOriginResponse {
  totalAmountOrigin: number
  totalDiscountAmount: number
  totalNetAmount: number
  totalTransactions: number
  storeCount: number
  storeSummaries: VoucherSalesSummary[]
}
