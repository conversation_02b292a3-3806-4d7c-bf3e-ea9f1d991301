import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import type { ImportSaleChannelData } from '../../hooks/use-import-sale-channel'

interface ImportPreviewTableProps {
  data: ImportSaleChannelData[]
}

export function ImportPreviewTable({ data }: ImportPreviewTableProps) {
  if (data.length === 0) {
    return (
      <div className='flex h-32 items-center justify-center text-gray-500'>
        Không có dữ liệu để hiển thị
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <div className='text-lg font-medium'>
        Xem trước dữ liệu ({data.length} kênh bán hàng)
      </div>
      
      <div className='max-h-96 overflow-auto rounded-lg border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='min-w-[120px]'>Mã nguồn</TableHead>
              <TableHead className='min-w-[100px]'><PERSON><PERSON> hồng (%)</TableHead>
              <TableHead className='min-w-[120px]'>Hóa đơn khách</TableHead>
              <TableHead className='min-w-[100px]'>Đơn online</TableHead>
              <TableHead className='min-w-[120px]'>Loại thanh toán</TableHead>
              <TableHead className='min-w-[120px]'>Mã PTTT</TableHead>
              <TableHead className='min-w-[100px]'>Loại chi phí</TableHead>
              <TableHead className='min-w-[120px]'>Chi phí</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item, index) => (
              <TableRow key={index}>
                <TableCell className='font-medium'>{item.source_id}</TableCell>
                <TableCell>{item.commission}%</TableCell>
                <TableCell>
                  {item.not_show_partner_bill === 0
                    ? 'Không sử dụng'
                    : item.not_show_partner_bill === 1
                    ? 'Sử dụng'
                    : 'Chỉ giảm giá'}
                </TableCell>
                <TableCell>{item.use_order_online ? 'Có' : 'Không'}</TableCell>
                <TableCell>
                  {item.payment_type === 'PREPAID' ? 'Trả trước' : 'Trả sau'}
                </TableCell>
                <TableCell>{item.payment_method_id}</TableCell>
                <TableCell>
                  {item.marketing_partner_cost_type === 'AMOUNT' ? 'Số tiền' : 'Phần trăm'}
                </TableCell>
                <TableCell>
                  {item.marketing_partner_cost_type === 'AMOUNT'
                    ? `${item.marketing_partner_cost.toLocaleString()} VNĐ`
                    : `${item.marketing_partner_cost}%`}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
