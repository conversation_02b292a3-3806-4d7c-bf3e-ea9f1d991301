import React, { useCallback } from 'react'

import QRCode from 'react-qr-code'

import { useSaveBillTemplate } from '../../hooks'
import { BillData, SaveBillTemplateRequest } from '../../types'
import { EditableField } from '../editable-field'

interface BillTemplate2Props {
  billData: BillData
  billTemplateData?: SaveBillTemplateRequest
}

export function BillTemplate2({ billData, billTemplateData }: BillTemplate2Props) {
  const saveBillTemplate = useSaveBillTemplate()

  const data = (billTemplateData?.extra_pos_mini || {}) as any

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const displayGrandTotal = data.show_discount ? billData.grandTotal - billData.itemDiscountAmount : billData.grandTotal

  const handleFieldChange = useCallback(
    (fieldName: string, value: string) => {
      if (billTemplateData) {
        const updatedPayload: SaveBillTemplateRequest = {
          ...billTemplateData,
          revision: billTemplateData.revision + 1,
          updated_at: new Date().toISOString(),
          extra_pos_mini: {
            ...(billTemplateData.extra_pos_mini || {}),
            [fieldName]: value
          } as any
        }

        saveBillTemplate.mutate(updatedPayload)
      }
    },
    [billTemplateData, saveBillTemplate]
  )

  return (
    <div
      className={`relative mx-auto border border-gray-300 bg-white p-4 shadow-md ${saveBillTemplate.isPending ? 'opacity-75' : ''}`}
      style={{ width: '278px', fontSize: `${(13 * (data.font_size || 100)) / 100}px` }}
    >
      {saveBillTemplate.isPending && (
        <div className='bg-opacity-50 absolute inset-0 z-10 flex items-center justify-center bg-white'>
          <div className='text-sm text-gray-600'>Đang lưu...</div>
        </div>
      )}
      <div className='px-4'>
        <h5 className='mb-0 text-center font-bold text-gray-900'>
          <EditableField
            value={billTemplateData?.extra_pos_mini?.title_custom || 'HOÁ ĐƠN THANH TOÁN'}
            onSave={value => handleFieldChange('title_custom', value)}
          />
        </h5>

        <div className='text-center font-bold text-black' style={{ fontSize: '0.7rem' }}>
          {data.group_source_tranno
            ? `${billData.orderSource} ${billData.billNumber}`
            : `${billData.orderSource} ${billData.billNumber}`}
        </div>

        <p className='mb-0 text-center font-bold text-black'>{billData.restaurantName}</p>
        <p className='mb-0 text-center text-sm text-black'>Địa chỉ: {billData.restaurantAddress}</p>
        {data?.hotline && <p className='mb-0 text-center text-sm text-black'>Số Hotline: {data?.hotline}</p>}

        <div>
          <p className='mt-3 mb-0'>
            <EditableField
              value={billTemplateData?.extra_pos_mini?.title_code || 'Mã Hóa Đơn'}
              onSave={value => handleFieldChange('title_code', value)}
              className='font-bold text-gray-900'
            />
            <span className='mx-0 text-black'>: </span>
            {billData.billId}
          </p>
          <p className='mb-0'>
            <EditableField
              value={billTemplateData?.extra_pos_mini?.title_table || 'Số Bàn'}
              onSave={value => handleFieldChange('title_table', value)}
              className='font-bold text-gray-900'
            />
            <span className='mx-0 text-black'>: </span>
            {billData.table}
          </p>
          <p className='mb-0'>
            <EditableField
              value={billTemplateData?.extra_pos_mini?.title_cashier || 'Thu Ngân'}
              onSave={value => handleFieldChange('title_cashier', value)}
              className='font-bold text-gray-900'
            />
            <span className='mx-0 text-black'>: </span>
            {billData.cashier}{' '}
          </p>
          <p className='mb-0'>
            <EditableField
              value={billTemplateData?.extra_pos_mini?.title_date || 'Ngày Tháng'}
              onSave={value => handleFieldChange('title_date', value)}
              className='font-bold text-gray-900'
            />
            <span className='mx-0 text-black'>: </span>
            {billData.date}
          </p>
          <div className='flex justify-between'>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_pos_mini?.title_time_in || 'Giờ Vào'}
                onSave={value => handleFieldChange('title_time_in', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.checkInTime}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_pos_mini?.title_time_out || 'Giờ Ra'}
                onSave={value => handleFieldChange('title_time_out', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.checkOutTime}
            </p>
          </div>
        </div>

        <div className='border-b border-dashed border-gray-300 pb-3'>
          <table className='w-full'>
            <thead>
              <tr>
                <th className='text-left'>
                  <EditableField
                    value={(billTemplateData?.extra_pos_mini as any)?.title_name_item || 'Tên món'}
                    onSave={value => handleFieldChange('title_name_item', value)}
                  />
                </th>
                {data.show_discount && (
                  <th className='text-center'>
                    <EditableField
                      value={(billTemplateData?.extra_pos_mini as any)?.title_discount || 'GG'}
                      onSave={value => handleFieldChange('title_discount', value)}
                    />
                  </th>
                )}
                <th className='text-right'>
                  <EditableField
                    value={(billTemplateData?.extra_pos_mini as any)?.title_amount || 'Thành tiền'}
                    onSave={value => handleFieldChange('title_amount', value)}
                  />
                </th>
              </tr>
            </thead>
            <tbody>
              {(() => {
                if (data.show_item_class) {
                  // Group items by itemType
                  const groupedItems = billData.items.reduce(
                    (groups, item) => {
                      const type = item.itemType || 'Không có loại'
                      if (!groups[type]) {
                        groups[type] = []
                      }
                      groups[type].push(item)
                      return groups
                    },
                    {} as Record<string, typeof billData.items>
                  )

                  return Object.entries(groupedItems).map(([itemType, items]) => (
                    <React.Fragment key={itemType}>
                      <tr>
                        <td className='p-0 font-semibold text-gray-700'>{itemType}</td>
                        <td className='text-right font-bold text-black' colSpan={data.show_discount ? 2 : 1}>
                          {formatCurrency(
                            items.reduce((total, item) => {
                              const itemTotal = data.show_discount
                                ? item.subtotal - (item.discountAmount || 0)
                                : item.subtotal
                              const toppingTotal =
                                data.show_topping && item.toppings
                                  ? item.toppings.reduce((toppingSum, topping) => {
                                      const toppingAmount = data.show_discount
                                        ? topping.subtotal - (topping.discountAmount || 0)
                                        : topping.subtotal
                                      return toppingSum + toppingAmount
                                    }, 0)
                                  : 0
                              return total + itemTotal + toppingTotal
                            }, 0)
                          )}
                        </td>
                      </tr>
                      {items.map(item => (
                        <React.Fragment key={item.id}>
                          <tr>
                            <td className='p-0'>
                              <p className='mb-0'>{item.name}</p>
                              <small className='mb-0 text-black italic'>
                                {formatCurrency(item.unitPrice)} x {item.quantity}
                                {data.show_unit && <span> {item.unit}</span>}
                              </small>
                              {data.show_item_note && item.notes ? (
                                <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                                  <span className='underline'>Ghi chú</span>
                                  <span>: {item.notes}</span>
                                </div>
                              ) : null}
                            </td>
                            {data.show_discount && (
                              <td className='p-0 text-right'>
                                <p>{item.discountPercentage}%</p>
                              </td>
                            )}
                            <td className='p-0 text-right align-top'>
                              <p className='mb-0'>
                                {formatCurrency(
                                  data.enable_discount ? item.subtotal - (item.discountAmount || 0) : item.subtotal
                                )}
                              </p>
                            </td>
                          </tr>
                          {data.show_topping &&
                            item.toppings &&
                            item.toppings.map((topping, toppingIndex) => (
                              <tr key={`${item.id}-topping-${toppingIndex}`}>
                                <td className='p-0 align-top'>
                                  <p className='mb-0 ml-2'>-{topping.name}</p>
                                  <small className='mb-0 ml-2 text-black italic'>
                                    {formatCurrency(topping.unitPrice)} x {topping.quantity}
                                    {data.show_unit && <span> {item.unit}</span>}
                                  </small>
                                  {data.show_item_note && topping.notes ? (
                                    <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                                      <span className='underline'>Ghi chú</span>
                                      <span>: {topping.notes}</span>
                                    </div>
                                  ) : null}
                                </td>
                                {data.show_discount && (
                                  <td className='p-0 text-right'>
                                    <p>{topping.discountPercentage || 10}%</p>
                                  </td>
                                )}
                                <td className='text-right align-top'>
                                  {formatCurrency(
                                    data.show_discount
                                      ? topping.subtotal - (topping.discountAmount || 0)
                                      : topping.subtotal
                                  )}
                                </td>
                              </tr>
                            ))}
                        </React.Fragment>
                      ))}
                    </React.Fragment>
                  ))
                } else {
                  // Original logic when showItemType is false
                  return billData.items.map(item => (
                    <React.Fragment key={item.id}>
                      <tr>
                        <td className='p-0'>
                          <p className='mb-0'>{item.name}</p>
                          <small className='mb-0 text-black italic'>
                            {formatCurrency(item.unitPrice)} x {item.quantity}
                            {data.show_unit && <span> {item.unit}</span>}
                          </small>
                          {data.show_item_note && item.notes ? (
                            <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                              <span className='underline'>Ghi chú</span>
                              <span>: {item.notes}</span>
                            </div>
                          ) : null}
                        </td>
                        {data.show_discount && (
                          <td className='p-0 text-right'>
                            <p>{item.discountPercentage}%</p>
                          </td>
                        )}
                        <td className='p-0 text-right align-top'>
                          <p className='mb-0'>
                            {formatCurrency(
                              data.show_discount ? item.subtotal - (item.discountAmount || 0) : item.subtotal
                            )}
                          </p>
                        </td>
                      </tr>
                      {data.show_topping &&
                        item.toppings &&
                        item.toppings.map((topping, toppingIndex) => (
                          <tr key={`${item.id}-topping-${toppingIndex}`}>
                            <td className='p-0 align-top'>
                              <p className='mb-0 ml-2'>-{topping.name}</p>
                              <small className='mb-0 ml-2 text-black italic'>
                                {formatCurrency(topping.unitPrice)} x {topping.quantity}
                                {data.show_unit && <span> {item.unit}</span>}
                              </small>
                              {data.show_item_note && topping.notes ? (
                                <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                                  <span className='underline'>Ghi chú</span>
                                  <span>: {topping.notes}</span>
                                </div>
                              ) : null}
                            </td>
                            {data.show_discount && (
                              <td className='p-0 text-right'>
                                <p>{topping.discountPercentage}%</p>
                              </td>
                            )}
                            <td className='text-right align-top'>
                              {formatCurrency(
                                data.show_discount ? topping.subtotal - (topping.discountAmount || 0) : topping.subtotal
                              )}
                            </td>
                          </tr>
                        ))}
                    </React.Fragment>
                  ))
                }
              })()}
            </tbody>
          </table>
        </div>

        <div className='border-b border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 font-bold text-black'>Thành tiền:</p>
            <p className='mb-0 font-bold text-black'>
              {formatCurrency(data.show_discount ? billData.subtotal - billData.itemDiscountAmount : billData.subtotal)}
            </p>
          </div>

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-black'>Tiền chiết khấu:</p>
            <p className='mb-0 text-black'>- {formatCurrency(billData.discountAmount)}</p>
          </div>

          {!data.show_discount && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-black'>Tiền giảm giá món:</p>
              <p className='mb-0 text-black'>- {formatCurrency(billData.itemDiscountAmount)}</p>
            </div>
          )}

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-black'>Tiền thuế (VAT):</p>
            <p className='mb-0 text-black'>{formatCurrency(billData.vatAmount)}</p>
          </div>

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 font-bold text-black'>Thành tiền VAT:</p>
            <p className='mb-0 font-bold text-black'>{formatCurrency(billData.totalWithVat)}</p>
          </div>

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-black'>Phí vận chuyển:</p>
            <p className='mb-0 text-black'>{formatCurrency(billData.shippingFee)}</p>
          </div>

          <div className='flex items-center justify-between'>
            <p className='mb-0 text-black'>Phiếu giảm giá:</p>
            <p className='mb-0 text-black'>- {formatCurrency(billData.voucherDiscount)}</p>
          </div>
        </div>

        <div className='border-b border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 font-bold text-black'>Tổng cộng:</p>
            <p className='mb-0 font-bold text-black'>{formatCurrency(billData.grandTotal)} ₫</p>
          </div>

          {data.is_show_payment_fee && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-blue-600'>Phí cà thẻ:</p>
              <p className='mb-0 text-blue-600'>{formatCurrency(billData.cardFeeAmount)} ₫</p>
            </div>
          )}

          {data.show_cash_change && (
            <>
              <div className='mb-2 flex items-center justify-between'>
                <p className='mb-0 text-black'>Tiền nhận:</p>
                <p className='mb-0 text-black'>{formatCurrency(billData.amountReceived)} ₫</p>
              </div>
              <div className='flex items-center justify-between'>
                <p className='mb-0 text-black'>Tiền thừa:</p>
                <p className='mb-0 text-black'>{formatCurrency(billData.amountReceived - billData.grandTotal)} ₫</p>
              </div>
            </>
          )}
        </div>

        <div className='py-3'>
          {data.show_vat_reverse && (
            <div className='flex justify-between'>
              <p className='mb-0 text-center text-black'>+ Tổng tiền trên đã bao gồm</p>
              <p className='mb-0 font-bold text-black'>{formatCurrency(billData.vatAmount)} VAT</p>
            </div>
          )}

          <div className='flex justify-between'>
            <p className='mb-0 text-center text-black'>+ Thanh toán {billData.paymentMethod}</p>
            <p className='mb-0 text-black'>{formatCurrency(displayGrandTotal)} ₫</p>
          </div>
        </div>
      </div>

      <div className='pb-3'>
        <div className='mx-auto mb-1 h-px w-1/4 border border-black'></div>

        <div className='mx-4 mb-2'>
          <p className='mb-0 text-sm text-black'>
            Khách hàng: {billData.customerName} -{' '}
            <span>
              {data.hide_customer_phone
                ? billData.customerPhone.substring(0, 2) + '*'.repeat(billData.customerPhone.length - 2)
                : billData.customerPhone}
            </span>
          </p>
          {data?.show_points && (
            <p className='mb-0 text-sm text-black'>
              Số điểm đã tích: {new Intl.NumberFormat('vi-VN').format(billData.accumulatedPoints)}
            </p>
          )}
        </div>

        {data.show_voucher_gift && billData.voucherName ? (
          <div className='mt-2 text-center'>
            <p className='mb-0 text-sm font-bold text-black'>{billData.voucherName}</p>
          </div>
        ) : null}
        {data?.custom_text_1 && <p className='mt-2 mb-0 text-center text-sm text-black'>{data?.custom_text_1}</p>}

        {data.enable_qr_code && (
          <div className='mx-4 mt-3 text-center'>
            {data.qr_title && <p className='text-md mb-2 text-black'>{data.qr_title}</p>}
            {data.qr_content ? (
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={data.qr_content.trim()}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            ) : (
              <span className='text-gray-500'>Nhập nội dung để tạo mã QR</span>
            )}
          </div>
        )}

        {data.show_vat_info && (
          <div className='mx-4 mt-2'>
            <p className='mb-1 text-sm text-black'>Thông tin khách hàng</p>
            <p className='mb-1 text-sm text-black'>Tên khách hàng: Mr.Cường</p>
            <p className='mb-1 text-sm text-black'>Mã số thuế: 0101234567</p>
            <p className='mb-0 text-sm text-black'>Địa chỉ: 106 Hoàng Quốc Việt, Cầu Giấy, Hà Nội</p>
          </div>
        )}

        {data.show_vat_qr_code && (
          <div className='mx-5 mt-2 flex flex-col justify-between'>
            <p className='mx-auto mb-1 text-center text-sm text-black'>
              Quét mã QR dưới đây để cung cấp thông tin hoá đơn điện tử
            </p>
            <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
              <QRCode
                value={'https://fabi.ipos.vn/'}
                level='H'
                bgColor='#fff'
                fgColor='#000'
                size={150}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </div>
        )}
      </div>

      <div className='border-t border-gray-300 py-3'>
        <p className='mb-0 text-center text-sm font-bold text-black'>{billData.customText2}</p>
        <p className='mb-0 text-center text-black'>Powered by iPOS.vn</p>
      </div>
    </div>
  )
}
