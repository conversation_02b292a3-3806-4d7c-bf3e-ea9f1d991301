export interface DiscountPayment {
  id: string
  value: number
  type: 'AMOUNT' | 'PERCENT'
  from_amount: number
  to_amount: number
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  is_membership: number
  is_coupon: number
  description: string | null
  extra_data: {
    voucher_input_number: number
  }
  active: number
  revision: number
  sort: number
  membership_type_uid: string | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  discount_payment_clone_id: string | null
  deleted: boolean
  created_by: string
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number | null
  deleted_at: number | null
  store_uid: string
  promotion: {
    id: string
    company_uid: string
    brand_uid: string
    deleted: boolean
    promotion_id: string
    promotion_name: string
    active: number
    partner_auto_gen: number
    is_fabi: number
    source_uid: string
    description: string | null
  }
}

export interface DiscountPaymentApiResponse {
  data: DiscountPayment[]
  track_id: string
}

export interface GetDiscountPaymentParams {
  company_uid: string
  brand_uid: string
  page?: number
  list_store_uid?: string
  active?: number
  limit?: number
}
