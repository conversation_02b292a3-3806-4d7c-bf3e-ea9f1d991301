import { ConfirmDialog } from '@/components/confirm-dialog'

import { useDeletePrinterPosition } from '@/features/setting/printer-position/printer-position-in-brand/hooks'

import { usePrinterPositionInBrand } from '../context'
import { PrinterPositionInBrandMutate } from './printer-position-in-brand-mutate'
import { PrinterPositionInBrandBulkDelete } from './printer-position-in-brand-bulk-delete'

export function PrinterPositionInBrandDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = usePrinterPositionInBrand()
  const deletePrinterPosition = useDeletePrinterPosition()

  return (
    <>
      <PrinterPositionInBrandMutate
        key='printer-position-create'
        open={open === 'create'}
        onOpenChange={() => setOpen(null)}
      />

      {currentRow && (
        <>
          <PrinterPositionInBrandMutate
            key={`printer-position-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={() => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='printer-position-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            handleConfirm={async () => {
              if (currentRow) {
                try {
                  await deletePrinterPosition.mutateAsync(currentRow.id)
                  setOpen(null)
                  setTimeout(() => {
                    setCurrentRow(null)
                  }, 500)
                } catch (_error) {
                  // Error is handled by the mutation's onError callback
                }
              }
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá vị trí máy in "${currentRow.printerPositionName}"?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}

      <PrinterPositionInBrandBulkDelete />
    </>
  )
}
