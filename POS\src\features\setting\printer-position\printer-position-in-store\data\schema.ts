import { z } from 'zod'

// Schema for printer position in store table
export const printerPositionInStoreSchema = z.object({
  id: z.string(),
  printerPositionId: z.string(),
  printerPositionName: z.string(),
  listItemTypeId: z.string(),
  listItemId: z.string(),
  storeUid: z.string(),
  areaIds: z.string(),
  sources: z.string(),
  applyWithStore: z.number(),
  sort: z.number(),
  brandUid: z.string(),
  companyUid: z.string(),
  revision: z.number(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  createdBy: z.string(),
  updatedBy: z.string(),
  deleted: z.boolean(),
  deletedBy: z.string().nullable(),
  deletedAt: z.date().nullable(),
  originalData: z.any().optional(), // Raw API data
})

export type PrinterPositionInStore = z.infer<typeof printerPositionInStoreSchema>
