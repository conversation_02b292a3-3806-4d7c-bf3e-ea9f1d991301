import { useQuery } from '@tanstack/react-query'

import type { 
  ItemSaleSummaryParams, 
  ItemSaleSummaryResponse 
} from '@/types/api/revenue-promotion'

import { getItemSaleSummary } from '@/lib/api/pos/item-sale-summary-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UseItemSaleSummaryOptions {
  params: ItemSaleSummaryParams
  enabled?: boolean
}

export const useItemSaleSummary = ({ params, enabled = true }: UseItemSaleSummaryOptions) => {
  const queryKey = [
    QUERY_KEYS.ITEMS,
    'sale-summary-top',
    params.company_uid,
    params.brand_uid,
    params.start_date,
    params.end_date,
    params.list_store_uid,
    params.limit,
    params.order_by
  ]

  const queryFn = async (): Promise<ItemSaleSummaryResponse> => {
    if (!params.list_store_uid) {
      return { 
        data: { list_data_item_return: [] }, 
        message: 'No stores selected', 
        track_id: '' 
      }
    }

    return getItemSaleSummary(params)
  }

  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && !!params.list_store_uid,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })

  return {
    data: query.data?.data.list_data_item_return || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasData: (query.data?.data.list_data_item_return || []).length > 0,
    message: query.data?.message || '',
    trackId: query.data?.track_id || ''
  }
}
