import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface VoucherMemberSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function VoucherMemberSection({ form, isLoading = false }: VoucherMemberSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Cấu hình Voucher và Hội viên</h2>

      <div className='space-y-6'>
        {/* Đổi điểm lấy voucher */}
        <FormField
          control={form.control}
          name='exchange_points_for_voucher'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Đổi điểm lấy voucher</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Xem voucher của thành viên */}
        <FormField
          control={form.control}
          name='view_voucher_of_member'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Xem voucher của thành viên</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không checkin bằng SĐT */}
        <FormField
          control={form.control}
          name='enable_checkin_by_phone_number'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Không checkin bằng SĐT</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép sử dụng nhiều mã giảm giá trong 1 đơn hàng */}
        <FormField
          control={form.control}
          name='multi_voucher'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Cho phép sử dụng nhiều mã giảm giá trong 1 đơn hàng
                  </FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tìm kiếm hội viên CRM theo tên hoặc số điện thoại */}
        <FormField
          control={form.control}
          name='find_member'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed'>Tìm kiếm hội viên CRM theo tên hoặc số điện thoại</FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className='h-6 w-6 cursor-help text-gray-400 hover:text-gray-600' />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className='max-w-xs'>
                          Nếu muốn tìm kiếm theo tên bạn vào "Thiết lập nhà hàng" chọn cấu hình Mã check-in gồm cả chữ
                          và số
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
