import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetMembershipDiscountsParams, MembershipDiscountApiData } from '@/types/discounts'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'
import {
  getMembershipDiscounts,
  createMembershipDiscount,
  deleteMembershipDiscount,
  updateMembershipDiscount,
  getMembershipDiscountPrograms,
  getMembershipDiscountPromotions,
  getMembershipDiscountById,
  getMembershipDiscountByIdOnly,
  cloneMembershipDiscounts
} from '@/lib/membership-discounts-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export function useMembershipDiscounts(params: GetMembershipDiscountsParams = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'membership', params],
    queryFn: () => getMembershipDiscounts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

interface UseCreateMembershipDiscountOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useCreateMembershipDiscount(options?: UseCreateMembershipDiscountOptions) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (discountData: Omit<MembershipDiscountApiData, 'id'>) => createMembershipDiscount(discountData),
    onSuccess: () => {
      toast.success('Tạo membership discount thành công')

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNTS]
      })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi tạo membership discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

export function useDeleteMembershipDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMembershipDiscount,
    onSuccess: () => {
      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

interface UseUpdateMembershipDiscountOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useUpdateMembershipDiscount(options?: UseUpdateMembershipDiscountOptions) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMembershipDiscount,
    onSuccess: () => {
      toast.success('Cập nhật membership discount thành công')

      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật membership discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

/**
 * Hook to get membership discount programs for a specific store
 */
export function useMembershipDiscountPrograms(params: {
  companyUid?: string
  brandUid?: string
  storeUid?: string
  enabled?: boolean
}) {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'programs', params.companyUid, params.brandUid, params.storeUid],
    queryFn: async () => {
      if (!params.companyUid || !params.brandUid || !params.storeUid) {
        throw new Error('Missing required parameters')
      }
      return getMembershipDiscountPrograms({
        companyUid: params.companyUid,
        brandUid: params.brandUid,
        storeUid: params.storeUid
      })
    },
    enabled: params.enabled && !!params.companyUid && !!params.brandUid && !!params.storeUid,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to clone membership discount programs
 */
export function useCloneMembershipDiscounts() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: cloneMembershipDiscounts,
    onSuccess: () => {
      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

/**
 * Hook to fetch membership types for form
 */
export function useMembershipTypes() {
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'membership-types', selectedBrand?.brandId],
    queryFn: async () => {
      if (!selectedBrand?.brandId) {
        throw new Error('Brand ID is required')
      }

      const response = await api.get(
        `/v3/forward/food-book/pos-cms/membership-type?page=1&results_per_page=15000&pos_parent=${selectedBrand.brandId}`
      )

      return response.data.data || []
    },
    enabled: !!selectedBrand?.brandId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

interface UseMembershipDiscountPromotionsOptions {
  enabled?: boolean
}

/**
 * Hook to fetch membership discount promotions for a specific store
 * Uses the same API pattern as regular discounts
 */
export function useMembershipDiscountPromotions(
  storeUid: string,
  options: UseMembershipDiscountPromotionsOptions = {}
) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const { enabled = true } = options

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'membership-promotions', storeUid, company?.id, selectedBrand?.id],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết để lấy danh sách CTKM')
      }

      return getMembershipDiscountPromotions({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        storeUid: storeUid
      })
    },
    enabled: enabled && !!storeUid && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to fetch membership discount detail by ID only (simpler version)
 * Matches the flow used in regular discount detail page
 */
export function useMembershipDiscountDetailById(discountId: string) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'membership-detail-by-id', discountId, company?.id, selectedBrand?.id],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !discountId) {
        throw new Error('Thiếu thông tin cần thiết để lấy chi tiết membership discount')
      }

      return getMembershipDiscountByIdOnly({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId
      })
    },
    enabled: !!discountId && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to fetch membership discount detail by ID and store UID
 * Matches the flow used in regular discount detail page
 */
export function useMembershipDiscountDetail(discountId: string, storeUid: string) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'membership-detail', discountId, storeUid, company?.id, selectedBrand?.id],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !discountId || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết để lấy chi tiết membership discount')
      }

      return getMembershipDiscountById({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId,
        storeUid: storeUid
      })
    },
    enabled: !!discountId && !!storeUid && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}
