import { useCallback } from 'react'

import { useParams } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { Button } from '@/components/ui'

import { ChannelForm } from './components'
import { useChannelForm, useChannelSave, useChannelData } from './hooks'

// Export data types for external use
export * from './data'

export default function ChannelDetailPage() {
  // Get UUID from route params (undefined for add mode)
  let channelId: string | undefined

  // Try to get params from both possible routes
  try {
    const params = useParams({
      from: '/_authenticated/sale-channel/channel/detail/$uuid'
    })
    channelId = params?.uuid
  } catch (error) {
    // If we're on the non-parameterized route, channelId will be undefined (add mode)
    channelId = undefined
  }

  // Alternative: Try to get from URL directly
  if (!channelId) {
    const urlParts = window.location.pathname.split('/')
    const detailIndex = urlParts.findIndex(part => part === 'detail')
    if (detailIndex !== -1 && urlParts[detailIndex + 1]) {
      channelId = urlParts[detailIndex + 1]
    }
  }

  const isEditMode = !!channelId

  const { formData, updateFormData, isFormValid, resetForm } = useChannelForm()

  // Memoize the callback to prevent infinite re-renders
  const handleDataLoaded = useCallback(
    (data: any) => {
      updateFormData(data)
    },
    [updateFormData]
  )

  // Load channel data for edit mode
  const { isLoading: isLoadingChannelData, isDataLoaded } = useChannelData({
    channelId,
    onDataLoaded: handleDataLoaded
  })

  const { handleBack, handleSave, createChannelMutation } = useChannelSave({
    formData,
    isFormValid,
    channelId,
    onSuccess: resetForm
  })

  const isLoading = createChannelMutation.isPending || isLoadingChannelData

  // For edit mode, only show form when data is loaded and converted to form data
  // For add mode, show form immediately
  const isDataReady = !isEditMode || (isEditMode && isDataLoaded)

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={isLoading || !isFormValid}
            className='min-w-[100px]'
            onClick={handleSave}
          >
            {createChannelMutation.isPending
              ? isEditMode
                ? 'Đang cập nhật...'
                : 'Đang tạo...'
              : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>
            {isEditMode ? 'Chỉnh sửa kênh bán hàng' : 'Tạo kênh bán hàng mới'}
          </h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        {isDataReady ? (
          <ChannelForm
            formData={formData}
            onFormDataChange={updateFormData}
            mode={isEditMode ? 'edit' : 'add'}
            isLoading={isLoading}
          />
        ) : (
          <div className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600'></div>
              <p className='text-gray-600'>Đang tải dữ liệu kênh bán hàng...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
