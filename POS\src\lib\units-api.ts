import { api } from './api/pos/pos-api'

// Types for Units API
export interface Unit {
  id: string
  unit_id: string
  unit_name: string
  unit_name_en: string | null
  unit_symbol: string
  unit_symbol_en: string | null
  description: string | null
  sort: number
  extra_data: Record<string, unknown> | null
  active: number
  revision: number
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
}

export interface UnitsApiResponse {
  data: Unit[]
  track_id: string
}

export interface GetUnitsParams {
  page?: number
  limit?: number
  search?: string
  active?: number
  sort?: string
}

// Cache for units requests
const unitsCache = new Map<string, { data: UnitsApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<UnitsApiResponse>>()
const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes (units change very rarely)

// Units API Service
export const unitsApi = {
  /**
   * Get units data with request deduplication and caching
   */
  getUnits: async (params: GetUnitsParams = {}): Promise<UnitsApiResponse> => {
    const requestKey = `units-${params.page || 1}-${params.limit || 50}-${params.search || ''}-${params.active ?? 1}-${params.sort || 'sort'}`

    // Check cache first
    const cached = unitsCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    // Create new request
    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()

        if (params.page) {
          queryParams.append('page', params.page.toString())
        }

        if (params.limit) {
          queryParams.append('limit', params.limit.toString())
        }

        if (params.search) {
          queryParams.append('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.append('active', params.active.toString())
        }

        if (params.sort) {
          queryParams.append('sort', params.sort)
        }

        const queryString = queryParams.toString()
        const url = queryString ? `/mdata/v1/units?${queryString}` : '/mdata/v1/units'

        const response = await api.get(url)

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from units API')
        }

        const result = response.data as UnitsApiResponse

        // Cache the result
        unitsCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        // Remove from pending requests
        pendingRequests.delete(requestKey)
      }
    })()

    // Store the pending request
    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Clear cache for units
   */
  clearCache: () => {
    unitsCache.clear()
    pendingRequests.clear()
  },

  /**
   * Get cache stats
   */
  getCacheStats: () => ({
    cacheSize: unitsCache.size,
    pendingRequests: pendingRequests.size
  }),

  /**
   * Get unit by ID from cache
   */
  getUnitById: (id: string): Unit | undefined => {
    for (const cached of unitsCache.values()) {
      const unit = cached.data.data.find(item => item.id === id)
      if (unit) return unit
    }
    return undefined
  },

  /**
   * Get unit by unit_id from cache
   */
  getUnitByUnitId: (unitId: string): Unit | undefined => {
    for (const cached of unitsCache.values()) {
      const unit = cached.data.data.find(item => item.unit_id === unitId)
      if (unit) return unit
    }
    return undefined
  }
}
