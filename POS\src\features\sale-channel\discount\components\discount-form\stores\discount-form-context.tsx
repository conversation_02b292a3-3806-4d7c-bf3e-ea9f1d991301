import { createContext, useContext, ReactNode } from 'react'

import type { DiscountFormData } from '../hooks'

interface DiscountFormContextType {
  formData: DiscountFormData
  updateFormData: (updates: Partial<DiscountFormData>) => void
  handleBack: () => void
  handleSave: () => void
  isFormValid: boolean
  isLoading: boolean
  isEditMode: boolean
}

const DiscountFormContext = createContext<DiscountFormContextType | undefined>(undefined)

interface DiscountFormProviderProps {
  children: ReactNode
  value: DiscountFormContextType
}

export function DiscountFormProvider({ children, value }: DiscountFormProviderProps) {
  return <DiscountFormContext.Provider value={value}>{children}</DiscountFormContext.Provider>
}

export function useDiscountFormContext() {
  const context = useContext(DiscountFormContext)
  if (context === undefined) {
    throw new Error('useDiscountFormContext must be used within a DiscountFormProvider')
  }
  return context
}

export function useDiscountFormData() {
  const { formData, updateFormData } = useDiscountFormContext()
  return { formData, updateFormData }
}

export function useDiscountFormActions() {
  const { handleBack, handleSave } = useDiscountFormContext()
  return { handleBack, handleSave }
}

export function useDiscountFormStatus() {
  const { isFormValid, isLoading, isEditMode } = useDiscountFormContext()
  return { isFormValid, isLoading, isEditMode }
}
