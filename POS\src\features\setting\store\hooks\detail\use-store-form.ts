import { useEffect, useRef } from 'react'

import { useForm, type UseFormReturn } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { storeFormSchema, initialStoreFormData, validateRequiredFields, type StoreFormValues } from '../../data'
import { useCurrentStoreData } from './use-store-data'

interface UseStoreFormProps {
  storeId?: string
  initialData?: Partial<StoreFormValues>
}

export const useStoreForm = ({ storeId, initialData }: UseStoreFormProps = {}) => {
  const isEditMode = !!storeId

  const { formValues: apiFormValues, isLoading: isLoadingStoreData } = useCurrentStoreData({
    storeId,
    enabled: isEditMode
  })

  const hasPopulatedRef = useRef(false)

  const form = useForm({
    resolver: zodResolver(storeFormSchema),
    defaultValues: {
      ...initialStoreFormData,
      ...initialData
    },
    mode: 'onChange'
  }) as UseFormReturn<StoreFormValues>

  useEffect(() => {
    hasPopulatedRef.current = false
  }, [storeId])

  useEffect(() => {
    if (isEditMode && apiFormValues && !isLoadingStoreData && !hasPopulatedRef.current) {
      hasPopulatedRef.current = true
      form.reset({
        ...initialStoreFormData,
        ...apiFormValues,
        ...initialData
      })
    }
  }, [apiFormValues, isLoadingStoreData, isEditMode, initialData, form])

  const resetForm = () => {
    const resetValues =
      isEditMode && apiFormValues
        ? { ...initialStoreFormData, ...apiFormValues, ...initialData }
        : { ...initialStoreFormData, ...initialData }

    form.reset(resetValues)
  }

  const formValues = form.watch()
  const isRequiredFieldsValid = validateRequiredFields(formValues)

  const isFormValid = form.formState.isValid

  return {
    form,
    resetForm,
    isFormValid,
    isRequiredFieldsValid,
    isEditMode,
    isLoadingStoreData
  }
}
