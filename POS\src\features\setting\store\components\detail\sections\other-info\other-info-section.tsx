import type { UseFormReturn } from 'react-hook-form'

import { FormField, FormItem, FormControl, FormMessage, Input } from '@/components/ui'

import { type StoreFormValues } from '../../../../data'

interface OtherInfoSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function OtherInfoSection({ form, isLoading = false }: OtherInfoSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Thông tin khác</h2>

      <div className='space-y-4'>
        {/* Facebook */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Facebook</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='facebook'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder='Facebook' disabled={isLoading} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Website */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Website</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='website'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder='Website' disabled={isLoading} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
