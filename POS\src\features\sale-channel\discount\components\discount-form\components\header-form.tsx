import { useParams } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { useUpdateDiscountSimple, useDiscountDetail } from '@/hooks/api/use-discount'

import { DiscountStatusButton } from '@/components/pos/discount-toggle-button'
import { Button } from '@/components/ui'

import { useDiscountFormActions, useDiscountFormStatus } from '../stores'

export function HeaderForm() {
  const { handleBack, handleSave } = useDiscountFormActions()
  const { isEditMode, isLoading, isFormValid } = useDiscountFormStatus()

  const params = useParams({ strict: false })
  const discountId = params.id as string

  // Get original discount data for toggle functionality
  const { data: discountData } = useDiscountDetail(discountId || '')
  const updateDiscountMutation = useUpdateDiscountSimple()

  const handleToggleStatus = async () => {
    if (!isEditMode || !discountData) return

    // Use the same pattern as in channel discount index page
    const discountApiData = {
      id: discountData.id,
      created_at: discountData.created_at,
      created_by: discountData.created_by,
      updated_at: discountData.updated_at,
      updated_by: discountData.updated_by,
      deleted: discountData.deleted || false,
      deleted_at: discountData.deleted_at || null,
      deleted_by: discountData.deleted_by || null,
      ta_discount: discountData.ta_discount,
      ots_discount: discountData.ots_discount,
      is_all: discountData.is_all as 0 | 1,
      is_type: discountData.is_type as 0 | 1,
      is_item: discountData.is_item as 0 | 1,
      type_id: discountData.type_id,
      item_id: discountData.item_id,
      discount_type: discountData.discount_type as 'PERCENT' | 'AMOUNT',
      from_date: discountData.from_date,
      to_date: discountData.to_date,
      time_sale_hour_day: discountData.time_sale_hour_day,
      time_sale_date_week: discountData.time_sale_date_week,
      description: discountData.description,
      extra_data: discountData.extra_data,
      active: (discountData.active === 1 ? 0 : 1) as 0 | 1, // Toggle active status
      revision: discountData.revision || null,
      promotion_uid: discountData.promotion_uid,
      brand_uid: discountData.brand_uid || '',
      company_uid: discountData.company_uid || '',
      sort: discountData.sort || 1000,
      store_uid: discountData.store_uid,
      discount_clone_id: discountData.discount_clone_id || null,
      source_uid: discountData.source_uid || '',
      promotion: discountData.promotion,
      source: discountData.source
    }

    await updateDiscountMutation.mutateAsync(discountApiData)
  }

  return (
    <div className='mb-8 flex items-center justify-between'>
      <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
        <X className='h-4 w-4' />
      </Button>

      <h1 className='text-3xl font-bold'>
        {isEditMode && 'Chỉnh sửa chương trình giảm giá'}
        {!isEditMode && 'Tạo chương trình giảm giá'}
      </h1>

      <div className='flex items-center gap-2'>
        {isEditMode && discountData && (
          <DiscountStatusButton
            isActive={discountData.active === 1}
            onToggle={handleToggleStatus}
            disabled={isLoading || updateDiscountMutation.isPending}
          />
        )}
        <Button type='button' disabled={isLoading || !isFormValid} className='min-w-[100px]' onClick={handleSave}>
          {isLoading && isEditMode && 'Đang cập nhật...'}
          {isLoading && !isEditMode && 'Đang tạo...'}
          {!isLoading && isEditMode && 'Cập nhật'}
          {!isLoading && !isEditMode && 'Lưu'}
        </Button>
      </div>
    </div>
  )
}
