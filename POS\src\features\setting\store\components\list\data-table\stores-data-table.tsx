import { useNavigate } from '@tanstack/react-router'

import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'

import { Store } from '@/types'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

interface StoresDataTableProps {
  columns: ColumnDef<Store>[]
  data: Store[]
}

export function StoresDataTable({ columns, data }: StoresDataTableProps) {
  const navigate = useNavigate()

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel()
  })

  const handleRowClick = (store: Store) => {
    navigate({
      to: `/setting/store/detail/${store.id}`
    })
  }

  return (
    <div className='w-full overflow-auto'>
      <div className='min-w-full rounded-md border'>
        <Table className='w-full table-auto'>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className='h-6 sm:h-7'>
                {headerGroup.headers.map(header => (
                  <TableHead
                    key={header.id}
                    className='px-1 py-0.5 text-xs font-medium whitespace-nowrap sm:px-2 sm:py-1'
                  >
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='hover:bg-muted/50 h-8 cursor-pointer sm:h-10'
                  onClick={() => handleRowClick(row.original)}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} className='px-1 py-0.5 text-xs sm:px-2 sm:py-1 sm:text-sm'>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-12 text-center text-xs sm:h-16 sm:text-sm'>
                  Không có dữ liệu cửa hàng.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
