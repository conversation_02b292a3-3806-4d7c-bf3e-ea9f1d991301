/**
 * Helper functions for mapping form fields to API format
 */

/**
 * Maps VAT configuration to API format
 */
export const mapVatValue = (vatType?: string, vatPercentage?: number): string => {
  if (!vatType || vatType === 'NO_VAT') return '0'
  return vatPercentage?.toString() || '0'
}

/**
 * Maps print type configuration to API format
 */
export const mapPrintType = (printType?: string): string => {
  if (!printType) return 'PROVISIONAL_INVOICE'
  switch (printType) {
    case 'TEMP_CALC':
      return 'PROVISIONAL_INVOICE'
    case 'FINALIZE':
      return 'INVOICE'
    case 'BOTH':
      return 'BOTH'
    case 'NONE':
      return 'NONE'
    default:
      return 'PROVISIONAL_INVOICE'
  }
}

/**
 * Maps operation model to API format
 */
export const mapOperationModel = (operationModel: string): number => {
  switch (operationModel) {
    case 'allow_cashier_remove':
      return 0
    case 'limit_cashier_remove':
      return 1
    case 'no_cashier_remove':
      return 2
    default:
      return 0
  }
}

/**
 * Maps PIN timeout configuration to API format
 */
export const mapPinTimeout = (autoChangePinConfig: string): number => {
  switch (autoChangePinConfig) {
    case 'one_time_use':
      return 0.01
    case 'change_after_1_min':
      return 0.02
    case 'change_after_2_min':
      return 0.03
    case 'change_after_3_min':
      return 0.04
    case 'change_after_4_min':
      return 0.05
    case 'change_after_5_min':
      return 0.06
    case 'change_after_6_min':
      return 0.07
    case 'change_after_7_min':
      return 0.08
    case 'change_after_8_min':
      return 0.09
    case 'change_after_9_min':
      return 0.1
    case 'change_after_10_min':
      return 0.11
    default:
      return 0.02
  }
}

/**
 * Maps invoice reset schedule to API format
 */
export const mapInvoiceResetPeriod = (invoiceResetSchedule: string): string => {
  switch (invoiceResetSchedule) {
    case 'daily':
      return 'DAILY'
    case 'monthly':
      return 'MONTHLY'
    case 'yearly':
      return 'YEARLY'
    case 'never':
      return 'NEVER'
    default:
      return 'DAILY'
  }
}

/**
 * Maps invoice configuration to API format
 */
export const mapInvoiceConfig = (invoiceConfig: string): number => {
  switch (invoiceConfig) {
    case 'PRINT_BOTH_TOTAL_AND_SEPARATE':
      return 0
    case 'PRINT_TOTAL_ONLY':
      return 1
    case 'PRINT_SEPARATE_ONLY':
      return 2
    case 'NO_AUTO_INVOICE':
      return 3
    default:
      return 3
  }
}

/**
 * Maps Ahamove payment methods to API format
 */
export const mapAhamovePaymentMethods = (paymentMethods: string[]): string => {
  if (!paymentMethods || paymentMethods.length === 0) {
    return 'AHAMOVE'
  }

  const hasCash = paymentMethods.includes('CASH')
  const hasPrepaid = paymentMethods.includes('PREPAID')

  if (hasCash && hasPrepaid) {
    return 'AHAMOVE_PREPAID,AHAMOVE'
  } else if (hasPrepaid) {
    return 'AHAMOVE_PREPAID'
  } else {
    return 'AHAMOVE'
  }
}

/**
 * Converts boolean to number for API
 */
export const booleanToNumber = (value: boolean | undefined): number => {
  return value ? 1 : 0
}

/**
 * Converts inverted boolean to number for API (true becomes 0, false becomes 1)
 */
export const invertedBooleanToNumber = (value: boolean | undefined): number => {
  return value ? 0 : 1
}
