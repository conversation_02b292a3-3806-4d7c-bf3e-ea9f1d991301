import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import type { CreateMembershipTypeParams, MembershipTypeItem } from '@/types/api/crm'

import { membershipTypeApi } from '@/lib/api/crm'

import { CRM_QUERY_KEYS } from '@/constants/crm'

import { usePosCompanyData, useBrandsData } from '@/hooks/local-storage'

/**
 * Hook to fetch the list of membership types.
 */
export const useMembershipType = () => {
  const posCompanyData = usePosCompanyData()
  const brandsData = useBrandsData()

  const params =
    posCompanyData && brandsData
      ? {
          company_id: posCompanyData.company_id,
          pos_parent: brandsData[0]?.brand_id || ''
        }
      : {
          company_id: '',
          pos_parent: ''
        }

  return useQuery({
    queryKey: ['crm', CRM_QUERY_KEYS.MEMBERSHIP_TYPE, params],
    queryFn: () => membershipTypeApi.getList(params),
    enabled: !!params.company_id && !!params.pos_parent
  })
}

/**
 * Hook to update a membership type.
 */
export const useUpdateMembershipTypeMutation = () => {
  const queryClient = useQueryClient()
  const brandsData = useBrandsData()

  return useMutation({
    mutationFn: async (data: MembershipTypeItem) => {
      const pos_parent = brandsData?.[0]?.brand_id || ''
      return membershipTypeApi.update(data, { pos_parent })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm', CRM_QUERY_KEYS.MEMBERSHIP_TYPE] })
    },
    onError: error => {
      console.error('Error updating membership type:', error)
    }
  })
}

/**
 * Hook to create a new membership type.
 */
export const useCreateMembershipTypeMutation = () => {
  const queryClient = useQueryClient()
  const brandsData = useBrandsData()

  return useMutation({
    mutationFn: async (data: CreateMembershipTypeParams) => {
      const pos_parent = brandsData?.[0]?.brand_id || ''
      return membershipTypeApi.create(data, { pos_parent })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm', CRM_QUERY_KEYS.MEMBERSHIP_TYPE] })
    },
    onError: error => {
      console.error('Error creating membership type:', error)
    }
  })
}
