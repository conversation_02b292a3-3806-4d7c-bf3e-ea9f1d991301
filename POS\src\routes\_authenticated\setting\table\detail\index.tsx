import { createFileRoute } from '@tanstack/react-router'

import { CreateTableForm } from '@/features/setting/tables'

interface TableDetailSearch {
  store_uid?: string
  area_uid?: string
  tableLayout?: boolean
}

export const Route = createFileRoute('/_authenticated/setting/table/detail/')({
  validateSearch: (search: Record<string, unknown>): TableDetailSearch => {
    return {
      store_uid: search.store_uid as string,
      area_uid: search.area_uid as string,
      tableLayout: search.tableLayout as boolean
    }
  },
  component: CreateTableIndexPage
})

function CreateTableIndexPage() {
  const { store_uid, area_uid, tableLayout } = Route.useSearch()

  return <CreateTableForm storeUid={store_uid} areaId={area_uid} fromTableLayout={tableLayout} />
}
