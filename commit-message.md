# Git Commit Message

```
feat(menu/items): implement complete price by source configuration with Excel preview

- Add PriceBySourceConfigModal component with 4-step workflow
- Integrate modal into ItemsInStoreDialogs with stores data
- Update context to include 'price-by-source-config' dialog type
- Connect modal trigger in ItemsInStoreButtons dropdown
- Implement Excel template download with fixed source mapping
- Map price_by_source data from API to Excel columns
- Group and sort data by item type (Nhóm món)
- Add Excel file upload with validation and preview
- Add comprehensive documentation in README.md
- Remove unused footer buttons (Cancel/Confirm) as requested

Features:
- Step 1: Store selection dropdown ✅
- Step 2: Download Excel template with existing items and price mapping ✅
- Step 3: Configuration instructions with validation rules ✅
- Step 4: File upload with validation and preview ✅
- Clean modal interface without footer buttons

Fixed Source Mapping:
- ✅ ZALO [10000045], FACEBOOK [10000049], SO [10000134]
- ✅ CRM [10000162], VNPAY [10000165], GOJEK (GOVIET) [10000168]
- ✅ ShopeeFood [10000169], MANG VỀ [10000171], TẠI CHỖ [10000172]
- ✅ CALL CENTER [10000176], O2O [10000216], BEFOOD [10000253]

API Integration:
- ✅ Fetch items using /mdata/v1/items?list_store_uid={storeId}
- ✅ Map price_by_source data from item.extra_data.price_by_source
- ✅ Use source_id to match with fixed source headers
- ✅ Added PriceBySource interface for proper typing

Data Organization:
- ✅ Group items by item type (Nhóm món) alphabetically
- ✅ Sort items within each group by item name
- ✅ Maintain logical data structure in Excel output

Excel Upload & Preview:
- ✅ File validation (type, size limits)
- ✅ Excel parsing with XLSX library
- ✅ Data validation and error handling
- ✅ Preview dialog with formatted table display
- ✅ Price formatting and source column highlighting
- ✅ Comprehensive error messages in Vietnamese

Technical changes:
- Created price-by-source-config-modal.tsx
- Created price-by-source-preview-dialog.tsx for Excel preview
- Created parse-price-by-source-excel.ts for file parsing
- Created use-price-by-source-data.ts hooks for API integration
- Created price-by-source-excel-utils.ts with fixed source mapping
- Added FIXED_SOURCE_MAPPING constant for consistent headers
- Added PriceBySource interface in item-api.ts
- Added PriceBySourcePreviewItem interface for preview data
- Updated Excel generation to group by item type and map price_by_source data
- Updated modals/index.ts and components/index.ts exports
- Updated utils/index.ts to export new utilities
- Updated context dialog types
- Updated ItemsInStoreDialogs with useStoresData hook
- Updated ItemsInStoreButtons handler logic
- Removed sources parameter dependency
- Added comprehensive README documentation
- Added debug logging for troubleshooting

Excel Template Features:
- Fixed headers for 12 predefined sources (complete mapping)
- Pre-filled item data (uid, id, name, type, price, vat)
- Mapped prices from item.extra_data.price_by_source based on source_id
- Grouped by item type with alphabetical sorting
- Items sorted by name within each group
- Proper styling and formatting
- Read-only columns for item identification
- Auto-download with timestamped filename

Excel Upload Features:
- File type validation (.xlsx, .xls)
- File size validation (max 10MB)
- Header validation with expected columns
- Data parsing with error handling using ArrayBuffer (no deprecation warnings)
- Responsive preview modal (max-w-7xl sm:max-w-4xl) for optimal viewing
- Preview table with formatted display and horizontal scroll
- Delete row functionality with Trash2 icon in action column
- Price formatting in Vietnamese locale
- Source price highlighting in blue
- Interactive row management (add/remove items before saving)

TODO: Implement API call to save price configuration from preview data
```
