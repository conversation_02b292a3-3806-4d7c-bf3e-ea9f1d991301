import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { type UpdatePrinterPositionRequest, printerPositionApi } from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const useUpdatePrinterPositionInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdatePrinterPositionRequest) => printerPositionApi.updatePrinterPosition(data),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PRINTER_POSITIONS_IN_STORE] })
      toast.success(response.message || 'Cập nhật vị trí máy in trong cửa hàng thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'C<PERSON> lỗi xảy ra khi cập nhật vị trí máy in trong cửa hàng')
    }
  })
}
