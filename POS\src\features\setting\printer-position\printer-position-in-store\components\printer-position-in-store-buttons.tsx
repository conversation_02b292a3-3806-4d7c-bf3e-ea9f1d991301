'use client'

import { Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'

import { usePrinterPositionInStore } from '../context'

export function PrinterPositionInStoreButtons() {
  const { setOpen } = usePrinterPositionInStore()

  return (
    <div className='flex items-center space-x-2'>
      <Button size='sm' className='h-8' onClick={() => setOpen('create')}>
        <Plus className='mr-2 h-4 w-4' />
        Tạo vị trí máy in
      </Button>
    </div>
  )
}
