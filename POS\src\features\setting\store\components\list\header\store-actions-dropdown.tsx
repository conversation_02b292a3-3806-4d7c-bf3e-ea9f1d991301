import React from 'react'

import { ArrowUpDown, Monitor } from 'lucide-react'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, Button } from '@/components/ui'

import { StoresSortModal } from '../modals/stores-sort-modal'

interface StoreActionsDropdownProps {
  onSyncSecondaryScreen: () => void
}

export function StoreActionsDropdown({ onSyncSecondaryScreen }: StoreActionsDropdownProps) {
  const [sortModalOpen, setSortModalOpen] = React.useState(false)

  const handleSortStores = () => {
    setSortModalOpen(true)
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='outline' size='sm'>
            Tiện ích
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem onClick={handleSortStores}>
            <ArrowUpDown className='mr-2 h-4 w-4' />
            Sắp xếp cửa hàng
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onSyncSecondaryScreen}>
            <Monitor className='mr-2 h-4 w-4' />
            Đồng bộ màn hình phụ
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {sortModalOpen && <StoresSortModal open={sortModalOpen} onOpenChange={setSortModalOpen} />}
    </>
  )
}
