import { useNavigate } from '@tanstack/react-router'

import { ChevronDown } from 'lucide-react'

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

interface Store {
  id: string
  store_name: string
}

interface SalesChannel {
  id: string
  sourceName: string
}

interface DiscountActionBarProps {
  title: string
  selectedStoreId: string
  selectedChannelId: string
  selectedStatus: string
  selectedExpiry: string
  stores: Store[]
  salesChannels: SalesChannel[]
  onStoreChange: (value: string) => void
  onChannelChange: (value: string) => void
  onStatusChange: (value: string) => void
  onExpiryChange: (value: string) => void
  onCopyDiscount: () => void
}

export function DiscountActionBar({
  title,
  selectedStoreId,
  selectedChannelId,
  selectedStatus,
  selectedExpiry,
  stores,
  salesChannels,
  onStoreChange,
  onChannelChange,
  onStatusChange,
  onExpiryChange,
  onCopyDiscount
}: DiscountActionBarProps) {
  const navigate = useNavigate()

  const handleCreateDiscount = () => {
    navigate({ to: '/sale-channel/discount/detail' })
  }
  return (
    <div className='flex flex-wrap items-center gap-4'>
      {/* Title */}
      <h2 className='text-xl font-semibold whitespace-nowrap'>{title}</h2>

      {/* Store Dropdown */}
      <div className='min-w-[160px]'>
        <Select value={selectedStoreId} onValueChange={onStoreChange}>
          <SelectTrigger>
            <SelectValue placeholder='Tất cả các điểm' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả các điểm</SelectItem>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.store_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Channel Dropdown */}
      <div className='min-w-[160px]'>
        <Select value={selectedChannelId} onValueChange={onChannelChange}>
          <SelectTrigger>
            <SelectValue placeholder='Tất cả kênh bán hàng' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả kênh bán hàng</SelectItem>
            {salesChannels.map(channel => (
              <SelectItem key={channel.id} value={channel.id}>
                {channel.sourceName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Status Dropdown */}
      <div className='min-w-[130px]'>
        <Select value={selectedStatus} onValueChange={onStatusChange}>
          <SelectTrigger>
            <SelectValue placeholder='Tất cả trạng thái' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả trạng thái</SelectItem>
            <SelectItem value='1'>Active</SelectItem>
            <SelectItem value='0'>Deactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Expiry Dropdown */}
      <div className='min-w-[150px]'>
        <Select value={selectedExpiry} onValueChange={onExpiryChange}>
          <SelectTrigger>
            <SelectValue placeholder='Tất cả ngày áp dụng' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả ngày áp dụng</SelectItem>
            <SelectItem value='expired'>Hết hạn</SelectItem>
            <SelectItem value='unexpired'>Chưa hết hạn</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Actions - Push to right */}
      <div className='ml-auto flex items-center gap-2'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' size='sm'>
              Tiện ích <ChevronDown className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={onCopyDiscount}>Sao chép CTGG</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button size='sm' onClick={handleCreateDiscount}>
          Tạo giảm giá
        </Button>
      </div>
    </div>
  )
}
