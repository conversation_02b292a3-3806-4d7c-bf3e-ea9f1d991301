import { Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

interface Store {
  id: string
  store_name: string
}

interface ActionBarProps {
  selectedStoreId: string
  setSelectedStoreId: (value: string) => void
  selectedStatus: string
  setSelectedStatus: (value: string) => void
  selectedExpiry: string
  setSelectedExpiry: (value: string) => void
  currentBrandStores: Store[]
  onCreateNew: () => void
  onCopy: () => void
}

const STORE_OPTIONS = {
  ALL: 'all',
  ALL_LABEL: 'Tất cả các điểm'
} as const

const STATUS_OPTIONS = {
  ALL: 'all',
  ACTIVE: '1',
  INACTIVE: '0',
  ALL_LABEL: 'Tất cả trạng thái',
  ACTIVE_LABEL: 'Active',
  INACTIVE_LABEL: 'Deactive'
} as const

const EXPIRY_OPTIONS = {
  ALL: 'all',
  EXPIRED: 'expired',
  UNEXPIRED: 'unexpired',
  ALL_LABEL: 'Tất cả ngày áp dụng',
  EXPIRED_LABEL: 'Hết hạn',
  UNEXPIRED_LABEL: 'Chưa hết hạn'
} as const

export function ActionBar({
  selectedStoreId,
  setSelectedStoreId,
  selectedStatus,
  setSelectedStatus,
  selectedExpiry,
  setSelectedExpiry,
  currentBrandStores,
  onCreateNew,
  onCopy
}: ActionBarProps) {
  return (
    <div className='flex flex-wrap items-center gap-4'>
      <h2 className='text-xl font-semibold whitespace-nowrap'>Phí dịch vụ</h2>

      <div className='min-w-[160px]'>
        <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
          <SelectTrigger>
            <SelectValue placeholder={STORE_OPTIONS.ALL_LABEL} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={STORE_OPTIONS.ALL}>{STORE_OPTIONS.ALL_LABEL}</SelectItem>
            {currentBrandStores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.store_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='min-w-[130px]'>
        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger>
            <SelectValue placeholder={STATUS_OPTIONS.ALL_LABEL} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={STATUS_OPTIONS.ALL}>{STATUS_OPTIONS.ALL_LABEL}</SelectItem>
            <SelectItem value={STATUS_OPTIONS.ACTIVE}>{STATUS_OPTIONS.ACTIVE_LABEL}</SelectItem>
            <SelectItem value={STATUS_OPTIONS.INACTIVE}>{STATUS_OPTIONS.INACTIVE_LABEL}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className='min-w-[150px]'>
        <Select value={selectedExpiry} onValueChange={setSelectedExpiry}>
          <SelectTrigger>
            <SelectValue placeholder={EXPIRY_OPTIONS.ALL_LABEL} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={EXPIRY_OPTIONS.ALL}>{EXPIRY_OPTIONS.ALL_LABEL}</SelectItem>
            <SelectItem value={EXPIRY_OPTIONS.EXPIRED}>{EXPIRY_OPTIONS.EXPIRED_LABEL}</SelectItem>
            <SelectItem value={EXPIRY_OPTIONS.UNEXPIRED}>{EXPIRY_OPTIONS.UNEXPIRED_LABEL}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className='ml-auto flex items-center gap-2'>
        <Button variant='outline' size='sm' onClick={onCopy}>
          Đồng bộ
        </Button>
        <Button size='sm' onClick={onCreateNew}>
          Tạo phí dịch vụ
        </Button>
      </div>
    </div>
  )
}
