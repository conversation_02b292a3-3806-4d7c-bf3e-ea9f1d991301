import React from 'react'

const COLOR_PRESETS = [
  '#1E40AF',
  '#EA580C',
  '#C026D3',
  '#FCD34D',
  '#6B7280',
  '#000000',
  '#FF4500',
  '#32CD32',
  '#00BFFF',
  '#4169E1',
  '#FF1493',
  '#8B4513',
  '#FFB6C1',
  '#DDA0DD',
  '#CD5C5C',
  '#A0522D',
  '#8B0000',
  '#2F4F4F'
]

interface ColorPickerProps {
  selectedColor: string
  onColorSelect: (color: string) => void
}

export const ColorPicker: React.FC<ColorPickerProps> = ({ selectedColor, onColorSelect }) => {
  return (
    <div className='absolute top-8 left-0 z-50 w-80 rounded-lg border bg-white p-4 shadow-lg'>
      <div className='mb-4'>
        <div className='grid grid-cols-6 gap-2'>
          {COLOR_PRESETS.map(color => (
            <div
              key={color}
              className={`h-8 w-8 cursor-pointer rounded border-2 ${
                selectedColor === color ? 'border-blue-500' : 'border-gray-300'
              }`}
              style={{ backgroundColor: color }}
              onClick={() => onColorSelect(color)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
