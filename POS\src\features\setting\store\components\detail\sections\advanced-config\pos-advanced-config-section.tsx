import type { UseFormReturn } from 'react-hook-form'

import { useRolesData } from '@/hooks/api/use-roles'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Checkbox } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface PosAdvancedConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function PosAdvancedConfigSection({ form, isLoading = false }: PosAdvancedConfigSectionProps) {
  const { data: roles = [], isLoading: rolesLoading } = useRolesData()

  const roleOptions = roles.map(role => ({
    value: role.id,
    label: role.role_name
  }))

  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>C<PERSON>u hình nâng cao POS</h2>

      <div className='space-y-6'>
        {/* Hiển thị nút mở két */}
        <FormField
          control={form.control}
          name='enable_cash_drawer'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị nút mở két</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hiển thị nút xác nhận yêu cầu */}
        <FormField
          control={form.control}
          name='confirm_request'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị nút xác nhận yêu cầu</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sử dụng KDS Order Control */}
        <FormField
          control={form.control}
          name='use_order_control'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sử dụng KDS Order Control</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hiển thị tab quản lý giao hàng */}
        <FormField
          control={form.control}
          name='enable_tab_delivery'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị tab quản lý giao hàng</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yêu cầu chọn lý do bỏ món */}
        <FormField
          control={form.control}
          name='enable_note_delete_item'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Yêu cầu chọn lý do bỏ món</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép tự nhập phí dịch vụ */}
        <FormField
          control={form.control}
          name='service_charge_optional'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cho phép tự nhập phí dịch vụ</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yêu cầu nhập số khách khi vào bàn */}
        <FormField
          control={form.control}
          name='require_peo_count'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Yêu cầu nhập số khách khi vào bàn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yêu cầu xác nhận khi gộp đơn chuyển bàn */}
        <FormField
          control={form.control}
          name='require_confirm_merge_table'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Yêu cầu xác nhận khi gộp đơn chuyển bàn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ẩn thông tin số khách */}
        <FormField
          control={form.control}
          name='hide_peo_count'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Ẩn thông tin số khách</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình sửa giá, chiết khấu trực tiếp khi bán */}
        <FormField
          control={form.control}
          name='enable_edit_item_price_while_selling'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Cấu hình sửa giá, chiết khấu trực tiếp khi bán
                  </FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value?.toString() || '0'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn cấu hình'
                    className='flex-1'
                    options={[
                      {
                        value: '0',
                        label: 'Không cho phép thay đổi'
                      },
                      {
                        value: '1',
                        label: 'Cho phép sửa giá khi bán hàng'
                      },
                      {
                        value: '2',
                        label: 'Cho phép thu ngân chiết khấu trực tiếp khi bán hàng'
                      },
                      {
                        value: '3',
                        label: 'Cho phép sửa giá khi bán hàng và thu ngân được phép chiết khấu trực tiếp khi bán hàng'
                      }
                    ]}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Quyền nhân viên được khởi tạo khi quét mã QR từ POS */}
        <FormField
          control={form.control}
          name='role_quick_login'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Quyền nhân viên được khởi tạo khi quét mã QR từ POS
                  </FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value?.toString()}
                    onValueChange={field.onChange}
                    disabled={isLoading || rolesLoading}
                    placeholder='Chọn quyền nhân viên'
                    className='flex-1'
                    options={roleOptions}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tự động xác nhận với đơn O2O trả sau */}
        <FormField
          control={form.control}
          name='auto_confirm_o2o_post_paid'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Tự động xác nhận với đơn O2O trả sau</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Trạng thái hết món không tự động làm mới hàng ngày */}
        <FormField
          control={form.control}
          name='resetItemOutOfStockStatus'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Trạng thái hết món không tự động làm mới hàng ngày
                  </FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không tự động đặt lại số lượng món qua ngày */}
        <FormField
          control={form.control}
          name='resetItemQuantityNewDay'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Không tự động đặt lại số lượng món qua ngày
                  </FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
