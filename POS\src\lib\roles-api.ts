import type { Role, RolesResponse, CreateRoleRequest, UpdateRoleRequest } from '@/types/role'

import { apiClient } from './api/pos/pos-api'

export interface GetRolesParams {
  company_uid?: string
  brand_uid?: string
  searchTerm?: string
  limit?: number
  offset?: number
}

class RolesApi {
  async getRoles(params: GetRolesParams = {}): Promise<Role[]> {
    const response = await apiClient.get<RolesResponse>('/accounts/v1/roles', {
      params
    })
    return response.data.data
  }

  async getRoleById(id: string): Promise<Role> {
    const response = await apiClient.get<{ data: Role }>(`/accounts/v1/role?role_uid=${id}`)
    return response.data.data
  }

  async createRole(role: CreateRoleRequest): Promise<Role> {
    const response = await apiClient.post<{ data: Role }>('/accounts/v1/roles', role)
    return response.data.data
  }

  async updateRole(role: UpdateRoleRequest): Promise<Role> {
    const response = await apiClient.post<{ data: Role }>('/accounts/v1/roles', role)
    return response.data.data
  }

  async deleteRole(params: { id: string; company_uid: string; brand_uid: string }): Promise<void> {
    await apiClient.delete('/accounts/v1/role', {
      data: {
        id: params.id,
        company_uid: params.company_uid,
        brand_uid: params.brand_uid
      }
    })
  }

  async bulkDeleteRoles(params: { ids: string[]; company_uid: string; brand_uid: string }): Promise<void> {
    await apiClient.delete('/accounts/v1/roles', {
      data: {
        list_role_uid: params.ids,
        company_uid: params.company_uid,
        brand_uid: params.brand_uid
      }
    })
  }

  async copyRole(sourceRoleId: string, newRoleName: string): Promise<Role> {
    const response = await apiClient.post<{ data: Role }>(`/accounts/v1/roles/${sourceRoleId}/copy`, {
      role_name: newRoleName
    })
    return response.data.data
  }
}

export const rolesApi = new RolesApi()
