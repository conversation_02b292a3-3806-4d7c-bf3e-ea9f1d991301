import { useState, useEffect } from 'react'

import type { Channel } from '@/types/channels'
import type { Store } from '@/types/store'
import { ArrowRight } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useChannelsData, useCopyChannels } from '@/hooks/api/use-channels'
import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'

import { FilterDropdown } from '@/components/filter-dropdown'
import { MultiSelectDropdown } from '@/components/multi-select-dropdown'

import { getApiErrorMessage } from '../detail/utils'

interface CopyChannelModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CopyChannelModal({ open, onOpenChange }: CopyChannelModalProps) {
  // Auth state
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // State for selected source store, channels, and target stores
  const [sourceStoreId, setSourceStoreId] = useState<string>('')
  const [selectedChannelIds, setSelectedChannelIds] = useState<string[]>([])
  const [targetStoreIds, setTargetStoreIds] = useState<string[]>([])

  // Fetch stores data
  const { data: stores, isLoading: storesLoading } = useStoresData({
    params: {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || ''
    },
    enabled: !!company?.id && !!selectedBrand?.id
  })

  // Fetch channels data for selected source store
  const { data: channels, isLoading: channelsLoading } = useChannelsData({
    storeId: sourceStoreId,
    enabled: !!sourceStoreId
  })

  // Copy channels mutation
  const copyChannelsMutation = useCopyChannels({
    onSuccess: () => {
      toast.success('Sao chép kênh bán hàng thành công')
      handleClose()
    },
    onError: error => {
      const errorMessage = getApiErrorMessage(error)
      toast.error(errorMessage)
    }
  })

  // Reset selections when modal opens/closes
  useEffect(() => {
    if (!open) {
      setSourceStoreId('')
      setSelectedChannelIds([])
      setTargetStoreIds([])
    }
  }, [open])

  // Format stores for dropdown
  const storeOptions = !stores
    ? []
    : stores.map((store: Store) => ({
        value: store.id,
        label: store.name
      }))

  // Format target store options (exclude source store)
  const targetStoreOptions = !stores
    ? []
    : stores
        .filter((store: Store) => store.id !== sourceStoreId)
        .map((store: Store) => ({
          value: store.id,
          label: store.name
        }))

  // Handle source store change
  const handleSourceStoreChange = (value: string) => {
    setSourceStoreId(value)
    setSelectedChannelIds([]) // Reset selected channels when source store changes
  }

  // Handle channel selection
  const handleChannelToggle = (channelId: string) => {
    setSelectedChannelIds(prev => {
      if (prev.includes(channelId)) {
        return prev.filter(id => id !== channelId)
      } else {
        return [...prev, channelId]
      }
    })
  }

  // Handle target stores change
  const handleTargetStoresChange = (values: string[]) => {
    setTargetStoreIds(values)
  }

  // Handle copy action
  const handleCopy = async () => {
    if (!channels) return

    try {
      await copyChannelsMutation.mutateAsync({
        sourceStoreId,
        channelIds: selectedChannelIds,
        targetStoreIds,
        channels
      })
    } catch (error) {
      console.error('Error copying channels:', error)
    }
  }

  // Handle close
  const handleClose = () => {
    onOpenChange(false)
  }

  // Check if copy button should be disabled
  const isCopyDisabled =
    selectedChannelIds.length === 0 || targetStoreIds.length === 0 || copyChannelsMutation.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle className='text-center text-lg font-medium'>
            Sao chép kênh bán hàng
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-6 py-4'>
          {/* Account selection row */}
          <div className='flex items-center gap-4'>
            <div className='flex-1'>
              <FilterDropdown
                value={sourceStoreId}
                onValueChange={handleSourceStoreChange}
                options={storeOptions}
                isLoading={storesLoading}
                placeholder='Chọn cửa hàng nguồn'
                className='w-full'
                showAllOption={false}
              />
            </div>
            <div className='flex-1'>
              <MultiSelectDropdown
                options={targetStoreOptions}
                value={targetStoreIds}
                onValueChange={handleTargetStoresChange}
                placeholder='Chọn cửa hàng đích'
                searchPlaceholder='Tìm kiếm'
                isLoading={storesLoading}
                disabled={!sourceStoreId}
                showSelectAll
                selectAllLabel='Chọn tất cả'
              />
            </div>
          </div>

          {/* Platform selection section */}
          <div className='flex items-start gap-6'>
            {/* Source channels */}
            <div className='flex-1'>
              <h3 className='text-sm font-medium mb-2'>
                Kênh bán hàng nguồn {channels?.length ? `(${channels.length})` : ''}
              </h3>
              <div className='min-h-[200px] space-y-3'>
                {channelsLoading ? (
                  <div className='flex items-center justify-center py-8'>
                    <p className='text-muted-foreground text-sm'>Đang tải...</p>
                  </div>
                ) : !sourceStoreId ? (
                  <div className='flex items-center justify-center py-8'>
                    <p className='text-muted-foreground text-sm'>
                      Chọn cửa hàng để thực hiện sao chép
                    </p>
                  </div>
                ) : channels?.length === 0 ? (
                  <div className='flex items-center justify-center py-8'>
                    <p className='text-muted-foreground text-sm'>Không có kênh bán hàng</p>
                  </div>
                ) : (
                  channels?.map((channel: Channel) => (
                    <div
                      key={channel.id}
                      className='flex cursor-pointer items-center space-x-3 hover:bg-gray-50 hover:shadow-sm p-2 rounded border border-transparent hover:border-gray-200 transition-all duration-150'
                      onClick={() => handleChannelToggle(channel.id)}
                    >
                      <Checkbox
                        checked={selectedChannelIds.includes(channel.id)}
                        className='h-5 w-5 pointer-events-none'
                      />
                      <span className='text-sm font-medium select-none'>{channel.source_name}</span>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Arrow */}
            <div className='flex items-center justify-center pt-8'>
              <ArrowRight className='text-muted-foreground h-8 w-8' />
            </div>

            {/* Target channels preview */}
            <div className='flex-1'>
              <h3 className='text-sm font-medium mb-2'>
                Kênh sẽ được sao chép {selectedChannelIds.length ? `(${selectedChannelIds.length})` : ''}
              </h3>
              <div className='min-h-[200px] space-y-3'>
                {selectedChannelIds.length === 0 ? (
                  <div className='flex items-center justify-center py-8'>
                    <p className='text-muted-foreground text-sm'>
                      Chọn kênh bán hàng từ cửa hàng nguồn
                    </p>
                  </div>
                ) : (
                  channels
                    ?.filter((channel: Channel) => selectedChannelIds.includes(channel.id))
                    .map((channel: Channel) => (
                      <div key={channel.id} className='text-sm font-medium'>
                        {channel.source_name}
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <div className='flex w-full justify-between'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={copyChannelsMutation.isPending}
            >
              Huỷ
            </Button>
            <Button
              type='button'
              onClick={handleCopy}
              disabled={isCopyDisabled}
              className='bg-blue-600 text-white hover:bg-blue-700'
            >
              {copyChannelsMutation.isPending ? 'Đang xử lý...' : 'Sao chép'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
