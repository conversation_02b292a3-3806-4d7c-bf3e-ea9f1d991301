import { useState } from 'react'

import { useMutation } from '@tanstack/react-query'

import { Store } from '@/types'
import { Home, Handshake, Edit } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { updateStoreFranchiseStatus } from '@/lib/stores-api'

import { ConfirmModal } from '@/components/pos'
import { Button } from '@/components/ui'

interface StoreTypeCellProps {
  store: Store
}

export const StoreTypeCell = ({ store }: StoreTypeCellProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { auth } = useAuthStore()

  const isFranchise = store.extraData?.is_franchise === 1

  const updateFranchiseMutation = useMutation({
    mutationFn: updateStoreFranchiseStatus,
    onSuccess: () => {
      toast.success(`Đã cập nhật loại hình nhà hàng thành ${!isFranchise ? 'nhượng quyền' : 'thuộc chuỗi thương hiệu'}`)
      setIsModalOpen(false)
      window.location.reload()
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật loại hình nhà hàng')
    }
  })

  const handleConfirm = () => {
    const company = auth.company
    const selectedBrand = auth.brands?.[0]

    if (!company?.id || !selectedBrand?.id) {
      toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
      return
    }

    updateFranchiseMutation.mutate({
      company_uid: company.id,
      brand_uid: selectedBrand.id,
      id: store.id,
      is_franchise: isFranchise ? 0 : 1 // Đảo ngược trạng thái hiện tại
    })
  }

  const getModalContent = () => {
    if (isFranchise) {
      return {
        title: `Bạn xác nhận thay đổi ${store.name}`,
        content: 'Từ điểm nhượng quyền thành điểm thuộc chuỗi thương hiệu',
        confirmText: 'Xác nhận',
        cancelText: 'Hủy'
      }
    } else {
      return {
        title: `Bạn xác nhận thay đổi ${store.name}`,
        content: 'Từ điểm thuộc chuỗi thương hiệu thành điểm nhượng quyền',
        confirmText: 'Xác nhận',
        cancelText: 'Hủy'
      }
    }
  }

  const modalContent = getModalContent()

  return (
    <div onClick={e => e.stopPropagation()}>
      <Button
        variant='ghost'
        size='sm'
        className={`flex w-fit items-center justify-center gap-2 rounded px-2 py-1 text-white ${
          isFranchise ? 'bg-orange-500 hover:bg-orange-600' : 'bg-blue-500 hover:bg-blue-600'
        }`}
        onClick={() => setIsModalOpen(true)}
        disabled={updateFranchiseMutation.isPending}
      >
        {isFranchise ? <Handshake className='h-4 w-4' /> : <Home className='h-4 w-4' />}
        <Edit className='h-4 w-4' />
      </Button>

      <ConfirmModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        title={modalContent.title}
        content={modalContent.content}
        confirmText={modalContent.confirmText}
        cancelText={modalContent.cancelText}
        onConfirm={handleConfirm}
        isLoading={updateFranchiseMutation.isPending}
      />
    </div>
  )
}
