import type { AxiosResponse } from 'axios'

import type { UsingDailyResponse, UsingDailyParams } from '@/types/api/crm/using-daily'

import { crmApi } from './crm-api'

export const getUsingDailyApi = async (params: UsingDailyParams): Promise<AxiosResponse<UsingDailyResponse>> => {
  const queryParams = new URLSearchParams({
    date_start: params.date_start,
    date_end: params.date_end,
    number_per_page: params.number_per_page.toString(),
    pos_parent: params.pos_parent
  })

  return crmApi.get(`/billing/get-using-daily?${queryParams.toString()}`)
}
