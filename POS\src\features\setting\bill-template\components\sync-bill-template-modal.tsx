import { useState } from 'react'

import { useMutation } from '@tanstack/react-query'

import { toast } from 'sonner'

import { billTemplateApi } from '@/lib/bill-template-api'

import { ConfirmModal, PosModal } from '@/components/pos'
import { MultiSelectCombobox } from '@/components/pos'
import { Button, Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import type { SyncBillTemplateModalProps, SaveBillTemplateRequest } from '../types'

export function SyncBillTemplateModal({ open, onOpenChange, stores }: SyncBillTemplateModalProps) {
  const [sourceStoreId, setSourceStoreId] = useState<string>('')
  const [targetStoreIds, setTargetStoreIds] = useState<string[]>([])
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false)
  const syncBillTemplateMutation = useMutation({
    mutationFn: (payload: SaveBillTemplateRequest) => billTemplateApi.saveBillTemplate(payload),
    onSuccess: () => {
      toast.success('Đồng bộ mẫu hoá đơn thành công')
      onOpenChange(false)
    },
    onError: () => {
      toast.error('Đồng bộ mẫu hoá đơn thất bại')
      onOpenChange(false)
    }
  })
  const handleSync = () => {
    setIsConfirmModalOpen(true)
  }

  const handleConfirm = async () => {
    try {
      const sourceStore = stores.find(store => store.id === sourceStoreId)
      if (!sourceStore) {
        toast.error('Không tìm thấy cửa hàng nguồn')
        return
      }

      const sourceBillTemplate = await billTemplateApi.getBillTemplate({
        company_uid: '595e8cb4-674c-49f7-adec-826b211a7ce3',
        brand_uid: 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e',
        store_uid: sourceStoreId
      })

      if (!sourceBillTemplate.data) {
        toast.error('Không tìm thấy dữ liệu mẫu hoá đơn từ cửa hàng nguồn')
        return
      }

      const promises = targetStoreIds.map(targetStoreId => {
        const targetStore = stores.find(store => store.id === targetStoreId)
        if (!targetStore) return Promise.resolve()

        const payload: SaveBillTemplateRequest = {
          ...sourceBillTemplate.data,
          store_uid: targetStoreId,
          revision: Date.now(),
          updated_at: new Date().toISOString()
        }

        return syncBillTemplateMutation.mutateAsync(payload)
      })

      await Promise.all(promises.filter(Boolean))
      setIsConfirmModalOpen(false)
    } catch (error) {
      console.error('Sync failed:', error)
      toast.error('Đồng bộ mẫu hoá đơn thất bại')
    }
  }

  return (
    <>
      <PosModal
        open={open}
        onOpenChange={onOpenChange}
        title='Đồng bộ mẫu hoá đơn'
        maxWidth='sm:max-w-5xl'
        hideButtons={true}
        onCancel={() => {}}
        onConfirm={() => {}}
      >
        <div className='grid grid-cols-2 gap-8'>
          <div className='space-y-3'>
            <Label className='text-sm font-medium'>Cửa hàng nguồn</Label>
            <Select value={sourceStoreId} onValueChange={setSourceStoreId}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Chọn cửa hàng nguồn' />
              </SelectTrigger>
              <SelectContent>
                {stores.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-3'>
            <Label className='text-sm font-medium'>Cửa hàng đích</Label>
            <MultiSelectCombobox
              options={stores.map(store => ({ value: store.id, label: store.name }))}
              value={targetStoreIds}
              onValueChange={setTargetStoreIds}
              placeholder='Chọn cửa hàng đích'
              searchPlaceholder='Tìm kiếm cửa hàng...'
              emptyText='Không có cửa hàng nào khả dụng'
              selectAllLabel='Chọn tất cả'
              allSelectedText='Tất cả cửa hàng'
              selectedCountText={count => `${count} cửa hàng đã chọn`}
              className='w-full'
            />
          </div>
        </div>

        <div className='flex justify-start pt-3'>
          <Button onClick={handleSync} disabled={!sourceStoreId || targetStoreIds.length === 0}>
            Đồng bộ
          </Button>
        </div>
      </PosModal>

      <ConfirmModal
        open={isConfirmModalOpen}
        onOpenChange={setIsConfirmModalOpen}
        onConfirm={handleConfirm}
        title='Đồng bộ mẫu hoá đơn'
        content='Bạn có chắc chắn muốn đồng bộ mẫu hoá đơn không?'
        confirmText='Đồng bộ'
        cancelText='Hủy'
      />
    </>
  )
}
