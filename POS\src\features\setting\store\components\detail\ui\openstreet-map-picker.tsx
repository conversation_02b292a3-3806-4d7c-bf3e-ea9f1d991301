import { useEffect, useState } from 'react'

import L, { LeafletMouseEvent } from 'leaflet'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet'

import { Button } from '@/components/ui'

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png'
})

interface OpenStreetMapPickerProps {
  defaultAddress?: string
  onAddressSelect: (address: string, lat: number, lng: number) => void
  onCancel: () => void
}

// Component to handle map clicks
function MapClickHandler({ onLocationSelect }: { onLocationSelect: (lat: number, lng: number) => void }) {
  useMapEvents({
    click: (e: LeafletMouseEvent) => {
      onLocationSelect(e.latlng.lat, e.latlng.lng)
    }
  })
  return null
}

export function OpenStreetMapPicker({ defaultAddress, onAddressSelect, onCancel }: OpenStreetMapPickerProps) {
  // Default to Hanoi coordinates
  const defaultCenter: [number, number] = [21.0285, 105.8542]
  const [selectedCoords, setSelectedCoords] = useState<[number, number]>(defaultCenter)
  const [selectedAddress, setSelectedAddress] = useState(defaultAddress || '')
  const [isLoading, setIsLoading] = useState(false)
  const [_currentLocation, setCurrentLocation] = useState<[number, number] | null>(null)

  // Reverse geocoding using Nominatim API
  const reverseGeocode = async (lat: number, lng: number) => {
    setIsLoading(true)
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=vi`
      )
      const data = await response.json()
      if (data.display_name) {
        setSelectedAddress(data.display_name)
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error)
      setSelectedAddress(`${lat.toFixed(6)}, ${lng.toFixed(6)}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Forward geocoding using Nominatim API
  const forwardGeocode = async (address: string) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&accept-language=vi&limit=1`
      )
      const data = await response.json()
      if (data.length > 0) {
        const lat = parseFloat(data[0].lat)
        const lng = parseFloat(data[0].lon)
        setSelectedCoords([lat, lng])
        setSelectedAddress(data[0].display_name)
      }
    } catch (error) {
      console.error('Error forward geocoding:', error)
    }
  }

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          const coords: [number, number] = [lat, lng]
          setCurrentLocation(coords)
          setSelectedCoords(coords)
          reverseGeocode(lat, lng)
        },
        error => {
          console.error('Error getting current location:', error)
          // Fallback to default address or Hanoi
          if (defaultAddress) {
            forwardGeocode(defaultAddress)
          }
        }
      )
    } else {
      console.error('Geolocation is not supported by this browser')
      if (defaultAddress) {
        forwardGeocode(defaultAddress)
      }
    }
  }

  useEffect(() => {
    if (defaultAddress) {
      forwardGeocode(defaultAddress)
    } else {
      // Get current location if no default address
      getCurrentLocation()
    }
  }, [defaultAddress])

  const handleLocationSelect = (lat: number, lng: number) => {
    setSelectedCoords([lat, lng])
    reverseGeocode(lat, lng)
  }

  const handleMarkerDrag = (e: L.DragEndEvent) => {
    const marker = e.target as L.Marker
    const position = marker.getLatLng()
    handleLocationSelect(position.lat, position.lng)
  }

  const handleConfirm = () => {
    if (selectedCoords && selectedAddress) {
      onAddressSelect(selectedAddress, selectedCoords[0], selectedCoords[1])
    }
  }

  return (
    <div className='flex h-full w-full flex-col gap-4'>
      <div className='flex-shrink-0'>
        {selectedAddress && (
          <div className='mt-2 rounded-md bg-gray-50 p-2 text-sm'>
            {selectedAddress}
            {isLoading && <span className='ml-2 text-blue-500'>Đang tải...</span>}
          </div>
        )}
      </div>

      <div className='flex-1 overflow-hidden rounded-md'>
        <MapContainer
          center={selectedCoords}
          zoom={16}
          style={{ height: '100%', width: '100%' }}
          key={`${selectedCoords[0]}-${selectedCoords[1]}`}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
          />
          <Marker
            position={selectedCoords}
            draggable={true}
            eventHandlers={{
              dragend: handleMarkerDrag
            }}
          />
          <MapClickHandler onLocationSelect={handleLocationSelect} />
        </MapContainer>
      </div>

      <div className='flex flex-shrink-0 justify-end gap-2'>
        <Button variant='outline' onClick={onCancel}>
          Hủy
        </Button>
        <Button onClick={handleConfirm} disabled={!selectedCoords || !selectedAddress || isLoading}>
          Xác nhận
        </Button>
      </div>
    </div>
  )
}
