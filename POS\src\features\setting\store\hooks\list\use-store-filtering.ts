import type { Store } from '@/types/store'

export interface UseStoreFilteringParams {
  stores: Store[]
  searchTerm: string
  selectedCity: string
}

export interface UseStoreFilteringReturn {
  filteredStores: Store[]
}

/**
 * Custom hook for filtering stores based on search term and selected city
 */
export function useStoreFiltering({
  stores,
  searchTerm,
  selectedCity
}: UseStoreFilteringParams): UseStoreFilteringReturn {
  const filteredStores = stores.filter(store => {
    // Search matching logic
    const matchesSearch =
      store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.email.toLowerCase().includes(searchTerm.toLowerCase())

    // City matching logic
    const storeCityId = store.cityId
    const matchesCity = selectedCity === 'all' || storeCityId === selectedCity

    return matchesSearch && matchesCity
  })

  return {
    filteredStores
  }
}
