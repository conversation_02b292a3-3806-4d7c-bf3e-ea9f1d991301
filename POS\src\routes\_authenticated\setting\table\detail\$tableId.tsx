import { createFileRoute } from '@tanstack/react-router'

import { CreateTableForm } from '@/features/setting/tables'

interface TableDetailSearch {
  store_uid?: string
  area_uid?: string
}

export const Route = createFileRoute('/_authenticated/setting/table/detail/$tableId')({
  component: TableDetailPage,
  validateSearch: (search: Record<string, unknown>): TableDetailSearch => ({
    store_uid: search.store_uid as string,
    area_uid: search.area_uid as string
  })
})

function TableDetailPage() {
  const { tableId } = Route.useParams()
  const { store_uid } = Route.useSearch()

  return <CreateTableForm areaId={tableId} storeUid={store_uid} fromTableLayout={true} />
}
