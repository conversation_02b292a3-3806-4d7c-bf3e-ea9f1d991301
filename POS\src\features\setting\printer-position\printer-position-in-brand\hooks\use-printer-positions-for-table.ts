import { useMemo } from 'react'

import type { PrinterPosition } from '@/lib/printer-position-api'

import { PrinterPositionInBrand } from '../data'
import { usePrinterPositionsData } from './use-printer-positions-data'

export interface UsePrinterPositionsForTableOptions {
  params?: {
    company_uid?: string
    brand_uid?: string
  }
  enabled?: boolean
}

export const usePrinterPositionsForTable = (options: UsePrinterPositionsForTableOptions = {}) => {
  const {
    data: printerPositions,
    isLoading,
    error,
    refetch
  } = usePrinterPositionsData({
    params: options.params,
    enabled: options.enabled
  })

  const transformedData = useMemo(() => {
    if (!printerPositions || !Array.isArray(printerPositions)) return []

    return printerPositions.map(
      (item: PrinterPosition): PrinterPositionInBrand => ({
        id: item.id,
        printerPositionId: item.printer_position_id,
        printerPositionName: item.printer_position_name,
        listItemTypeId: item.list_item_type_id || '',
        listItemId: item.list_item_id || '',
        storeUid: item.store_uid ?? null,
        areaIds: item.area_ids || '',
        sources: item.sources || '',
        applyWithStore: item.apply_with_store || 0,
        sort: item.sort || 0,
        brandUid: item.brand_uid || '',
        companyUid: item.company_uid || '',
        revision: item.revision || 0,
        isActive: !item.deleted,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
        createdBy: item.created_by || '',
        updatedBy: item.updated_by || '',
        deleted: !!item.deleted,
        deletedBy: item.deleted_by || null,
        deletedAt: item.deleted_at ? new Date(item.deleted_at) : null,
        originalData: item
      })
    )
  }, [printerPositions])

  return {
    data: transformedData,
    isLoading,
    error,
    refetch
  }
}
