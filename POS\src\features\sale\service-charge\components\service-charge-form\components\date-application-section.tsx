import { format } from 'date-fns'

import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Button, Calendar, Label, Popover, PopoverContent, PopoverTrigger } from '@/components/ui'

import { useServiceChargeFormData } from '../stores'

export function DateApplicationSection() {
  const { formData, updateFormData } = useServiceChargeFormData()
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const startDate = formData.startDate ? new Date(formData.startDate) : undefined
  const endDate = formData.endDate ? new Date(formData.endDate) : undefined

  const handleStartDateSelect = (date: Date | undefined) => {
    if (date) {
      const dateString = format(date, 'yyyy-MM-dd')
      updateFormData({ startDate: dateString })
    }
  }

  const handleEndDateSelect = (date: Date | undefined) => {
    if (date) {
      const dateString = format(date, 'yyyy-MM-dd')
      updateFormData({ endDate: dateString })
    }
  }

  return (
    <div className='space-y-4'>
      <h2 className='text-lg font-medium text-gray-900'>Ngày áp dụng</h2>

      <div className='flex items-center gap-4'>
        <div className='flex flex-1 items-center gap-4'>
          <Label className='min-w-[120px] text-sm font-medium'>Ngày bắt đầu</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className={cn('flex-1 justify-start text-left font-normal', !startDate && 'text-muted-foreground')}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {startDate ? format(startDate, 'dd/MM/yyyy', { locale: vi }) : 'Chọn ngày bắt đầu'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={startDate}
                onSelect={handleStartDateSelect}
                disabled={date => date > today}
                initialFocus
                locale={vi}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className='flex flex-1 items-center gap-4'>
          <Label className='min-w-[120px] text-sm font-medium'>Ngày kết thúc</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className={cn('flex-1 justify-start text-left font-normal', !endDate && 'text-muted-foreground')}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {endDate ? format(endDate, 'dd/MM/yyyy', { locale: vi }) : 'Chọn ngày kết thúc'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={endDate}
                onSelect={handleEndDateSelect}
                disabled={date => date < today}
                initialFocus
                locale={vi}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  )
}
