import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox,
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface MenuEditSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function MenuEditSection({ form, isLoading = false }: MenuEditSectionProps) {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>C<PERSON>u hình sửa thực đơn tại cửa hàng</h2>
        <p className='text-sm text-gray-600'>
          Thay đổi chỉ áp dụng tại cửa hàng, không ảnh hưởng tới thực đơn của thương hiệu
        </p>
      </div>

      <div className='space-y-4'>
        {/* <PERSON><PERSON><PERSON> thực đơn tại cửa hàng */}
        <FormField
          control={form.control}
          name='enable_change_item_in_store'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sửa thực đơn tại cửa hàng</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sửa nhóm món tại cửa hàng */}
        <FormField
          control={form.control}
          name='enable_change_item_type_in_store'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sửa nhóm món tại cửa hàng</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sửa vị trí máy in tại cửa hàng */}
        <FormField
          control={form.control}
          name='enable_change_printer_position_in_store'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sửa vị trí máy in tại cửa hàng</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không được tạo món tùy chọn */}
        <FormField
          control={form.control}
          name='prevent_create_custom_item'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Không được tạo món tùy chọn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='require_custom_item_vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Yêu cầu món tùy chọn phải nhập VAT</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yêu cầu món tùy chọn phải chọn nhóm món */}
        <FormField
          control={form.control}
          name='require_category_for_custom_item'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Yêu cầu món tùy chọn phải chọn nhóm món</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hiển thị menu theo nguồn */}
        <FormField
          control={form.control}
          name='is_menu_by_source'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị menu theo nguồn</FormLabel>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 text-gray-400 hover:text-gray-600' />
                    </TooltipTrigger>
                    <TooltipContent className='max-w-xs'>
                      <p>
                        Nếu món ăn có khai báo giá theo nguồn sẽ chỉ hiển thị với nguồn đó, món ăn không khai báo theo
                        nguồn sẽ luôn hiển thị. Trường hợp bàn hoặc hoá đơn không có nguồn sẽ hiển thị tất.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
