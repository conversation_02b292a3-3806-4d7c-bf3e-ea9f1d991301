import type { UseFormReturn } from 'react-hook-form'

import { MapPin } from 'lucide-react'

import { useCities } from '@/hooks/api/use-cities'

import { Combobox } from '@/components/pos'
import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  Input,
  Button,
  Dialog,
  DialogContent,
  DialogTrigger
} from '@/components/ui'

import { type StoreFormValues } from '../../../../data'
import { OpenStreetMapPicker, AddressAutocomplete } from '../../ui'

interface LocationSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  isMapModalOpen: boolean
  setIsMapModalOpen: (open: boolean) => void
}

export function LocationSection({ form, isLoading = false, isMapModalOpen, setIsMapModalOpen }: LocationSectionProps) {
  const { data: cities = [], isLoading: isLoadingCities, error: citiesError } = useCities()

  const cityOptions = cities.map(city => ({
    value: city.id,
    label: city.city_name
  }))

  return (
    <>
      {/* Địa chỉ */}
      <div className='grid grid-cols-12 items-start gap-4'>
        <div className='col-span-3 pt-2'>
          <label className='text-sm font-medium text-gray-700'>
            Địa chỉ <span className='text-red-500'>*</span>
          </label>
        </div>
        <div className='col-span-9'>
          <FormField
            control={form.control}
            name='address'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className='flex gap-2'>
                    <AddressAutocomplete
                      value={field.value || ''}
                      onChange={field.onChange}
                      onAddressSelect={(address, lat, lng) => {
                        field.onChange(address)
                        // Update latitude and longitude fields
                        form.setValue('latitude', lat)
                        form.setValue('longitude', lng)
                      }}
                      placeholder='Nhập địa chỉ'
                      disabled={isLoading}
                      className='flex-1'
                    />
                    <Dialog open={isMapModalOpen} onOpenChange={setIsMapModalOpen}>
                      <DialogTrigger asChild>
                        <Button type='button' variant='outline' size='icon' disabled={isLoading} className='shrink-0'>
                          <MapPin className='h-4 w-4' />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className='flex h-[700px] flex-col' style={{ width: '80vw', maxWidth: '1000px' }}>
                        <div className='flex-1 overflow-hidden'>
                          <OpenStreetMapPicker
                            defaultAddress={field.value}
                            onAddressSelect={(address, lat, lng) => {
                              field.onChange(address)
                              // Update latitude and longitude fields if they exist
                              const latField = form.getValues('latitude')
                              const lngField = form.getValues('longitude')
                              if (latField !== undefined) {
                                form.setValue('latitude', lat)
                              }
                              if (lngField !== undefined) {
                                form.setValue('longitude', lng)
                              }
                              setIsMapModalOpen(false)
                            }}
                            onCancel={() => setIsMapModalOpen(false)}
                          />
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Latitude & Longitude */}
      <div className='grid grid-cols-12 items-start gap-4'>
        <div className='col-span-3 pt-2'>
          <label className='text-sm font-medium text-gray-700'>Latitude</label>
        </div>
        <div className='col-span-4'>
          <FormField
            control={form.control}
            name='latitude'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type='number'
                    step='any'
                    placeholder='0'
                    readOnly
                    disabled={isLoading}
                    {...field}
                    className='bg-gray-50'
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='col-span-1 pt-2 text-center'>
          <label className='text-sm font-medium text-gray-700'>Longitude</label>
        </div>
        <div className='col-span-4'>
          <FormField
            control={form.control}
            name='longitude'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type='number'
                    step='any'
                    placeholder='0'
                    readOnly
                    disabled={isLoading}
                    {...field}
                    className='bg-gray-50'
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Thành phố */}
      <div className='grid grid-cols-12 items-start gap-4'>
        <div className='col-span-3 pt-2'>
          <label className='text-sm font-medium text-gray-700'>
            Thành phố <span className='text-red-500'>*</span>
          </label>
        </div>
        <div className='col-span-9'>
          <FormField
            control={form.control}
            name='city_uid'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Combobox
                    options={cityOptions}
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder={isLoadingCities ? 'Đang tải thành phố...' : 'Chọn thành phố'}
                    searchPlaceholder='Tìm kiếm thành phố...'
                    emptyText={
                      isLoadingCities
                        ? 'Đang tải...'
                        : citiesError
                          ? 'Lỗi tải dữ liệu thành phố'
                          : 'Không tìm thấy thành phố nào.'
                    }
                    disabled={isLoading || isLoadingCities}
                    className='w-full'
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </>
  )
}
