import { Button } from '@/components/ui'

import { STORE_CONSTANTS } from '../../../data/constants'

interface ActionButtonsProps {
  isEditMode: boolean
  isExpired: boolean
  isActive: boolean
  isUpdating: boolean
  onToggleStatus: () => void
  isRequiredFieldsValid: boolean
  isCreating: boolean
  onSave: () => void
}

export function ActionButtons({
  isEditMode,
  isExpired,
  isActive,
  isUpdating,
  onToggleStatus,
  isRequiredFieldsValid,
  isCreating,
  onSave
}: ActionButtonsProps) {
  const shouldShowStatusButton = isEditMode
  const shouldShowSaveButton = !isEditMode || !isExpired

  if (!shouldShowStatusButton && !shouldShowSaveButton) {
    return null
  }

  const statusButtonVariant = isActive ? 'destructive' : 'default'
  const statusButtonText = isUpdating
    ? STORE_CONSTANTS.LOADING_UPDATING
    : isActive
      ? STORE_CONSTANTS.BUTTON_DEACTIVE
      : STORE_CONSTANTS.BUTTON_ACTIVE

  const statusButtonClassName = !isActive ? 'bg-green-600 text-white hover:bg-green-700' : ''

  const saveButtonText = isCreating ? STORE_CONSTANTS.LOADING_CREATING : STORE_CONSTANTS.BUTTON_SAVE

  return (
    <>
      {shouldShowStatusButton && (
        <Button
          type='button'
          variant={statusButtonVariant}
          disabled={isUpdating}
          className={`min-w-[${STORE_CONSTANTS.MIN_BUTTON_WIDTH}px] ${statusButtonClassName}`}
          onClick={onToggleStatus}
        >
          {statusButtonText}
        </Button>
      )}

      {shouldShowSaveButton && (
        <Button
          type='button'
          disabled={!isRequiredFieldsValid || isCreating}
          className={`min-w-[${STORE_CONSTANTS.MIN_BUTTON_WIDTH}px]`}
          onClick={onSave}
        >
          {saveButtonText}
        </Button>
      )}
    </>
  )
}
