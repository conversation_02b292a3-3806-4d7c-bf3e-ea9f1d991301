import { useState } from 'react'

import { useQuery } from '@tanstack/react-query'

import type { CrmServicesParams } from '@/types/api/crm'

import { getBillingServices } from '@/lib/api/crm/billing-services-api'

import { CRM_QUERY_KEYS } from '@/constants/crm'

export const useBillingServices = (initialParams: CrmServicesParams = {}) => {
  const [params, setParams] = useState<CrmServicesParams>({
    limit: 1000,
    ...initialParams
  })

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [CRM_QUERY_KEYS.BILLING_SERVICES, params],
    queryFn: () => getBillingServices(params)
  })

  const services = data?.services || []

  return {
    services,
    isLoading,
    error: error?.message || null,
    refetch,
    params,
    setParams
  }
}
