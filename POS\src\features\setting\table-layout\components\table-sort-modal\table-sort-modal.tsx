import React from 'react'

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui'

import type { TableLayoutItem } from '../../data/table-layout-types'
import { StoreSelector } from '../header/store-selector'
import { AreasList, TablesGrid } from './components'
import { useTableSortModal } from './hooks'

interface TableSortModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (sortedTables: TableLayoutItem[]) => Promise<void>
}

export const TableSortModal: React.FC<TableSortModalProps> = ({ open, onOpenChange, onSave }) => {
  const {
    selectedStoreId,
    sortedAreas,
    draggedIndex,
    selectedAreaId,
    draggedTableIndex,
    filteredTables,
    isSaving,
    areasLoading,
    tablesLoading,
    handleStoreChange,
    handleAreaClick,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleTableDragStart,
    handleTableDragOver,
    handleTableDrop,
    handleSave: handleSaveInternal
  } = useTableSortModal(open)

  const handleSaveClick = () => {
    const wrappedOnSave = async (_storeId: string, sortedAreas: any[]) => {
      const sortedTables: TableLayoutItem[] = sortedAreas.map(area => ({
        id: area.id,
        position: { x: 0, y: 0 },
        size: { width: 100, height: 100 },
        table_name: area.area_name || '',
        area_uid: area.id,
        store_uid: area.store_uid || '',
        company_uid: area.company_uid || '',
        brand_uid: area.brand_uid || '',
        sort: area.sort || 0,
        active: area.active || 1,
        created_at: area.created_at || new Date().toISOString(),
        updated_at: area.updated_at || new Date().toISOString()
      }))
      await onSave(sortedTables)
    }

    handleSaveInternal(wrappedOnSave, onOpenChange)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='h-[85vh] !w-[90vw] !max-w-none sm:!max-w-none'>
        <DialogHeader className='space-y-3'>
          <DialogTitle>Sắp xếp bàn</DialogTitle>
          <div className='w-full'>
            <StoreSelector
              value={selectedStoreId}
              onValueChange={handleStoreChange}
              placeholder='Chọn điểm áp dụng'
              className='w-80'
            />
          </div>
        </DialogHeader>

        <div className='flex h-[calc(85vh-120px)] flex-col'>
          <div className='min-h-0 flex-1'>
            {selectedStoreId && (
              <div className='flex h-full gap-4'>
                <AreasList
                  areas={sortedAreas}
                  isLoading={areasLoading}
                  selectedAreaId={selectedAreaId}
                  draggedIndex={draggedIndex}
                  onAreaClick={handleAreaClick}
                  onDragStart={handleDragStart}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                />
                <TablesGrid
                  areas={sortedAreas}
                  tables={filteredTables}
                  isLoading={tablesLoading}
                  selectedAreaId={selectedAreaId}
                  draggedTableIndex={draggedTableIndex}
                  onTableDragStart={handleTableDragStart}
                  onTableDragOver={handleTableDragOver}
                  onTableDrop={handleTableDrop}
                />
              </div>
            )}
          </div>

          <div className='flex justify-end gap-2 border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button
              onClick={handleSaveClick}
              disabled={!selectedStoreId || sortedAreas.length === 0 || isSaving}
              className='bg-blue-600 hover:bg-blue-700'
            >
              {isSaving ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
