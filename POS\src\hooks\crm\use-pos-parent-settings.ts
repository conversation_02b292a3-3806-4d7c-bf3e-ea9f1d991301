import { useQuery } from '@tanstack/react-query'

import { crmSettingsApi } from '@/lib/api/crm/settings-api'

import { CRM_QUERY_KEYS } from '@/constants/crm/query-keys'

export function usePosParentSettings(params: { pos_parent: string }) {
  return useQuery({
    queryKey: [CRM_QUERY_KEYS.POS_PARENT_SETTINGS, params],
    queryFn: () => crmSettingsApi.getCrmSettings(params.pos_parent),
    enabled: !!params.pos_parent
  })
}
