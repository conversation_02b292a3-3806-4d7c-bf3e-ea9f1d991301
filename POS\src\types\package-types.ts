export interface PackageItem {
  item_id: string
  ta_price: number
  item_name: string
  ots_price: number
  state_change_price: number
  discount_combo_item: number
}

export interface PackageOption {
  id: string
  Name: string
  LstItem: PackageItem[]
  Max_Permitted: number
  Min_Permitted: number
}

export interface PackageDetail {
  LstItem_Options: PackageOption[]
}

export interface PriceTime {
  price: number
  to_date: number
  from_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
}

export interface PriceBySource {
  price: number
  source_id: string
  price_times: PriceTime[]
  is_source_exist_in_city: boolean
}

export interface ExtraData {
  price_by_source: PriceBySource[]
}

export interface Package {
  package_id: string
  package_name: string
  use_same_data: number
  ots_value: number
  from_date: number
  to_date: number
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  ta_value: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string
  active: number
  extra_data: ExtraData
  package_detail: PackageDetail
  revision: any | null
  unit_uid: string | null
  promotion_uid: string | null
  brand_uid: string
  company_uid: string
  sort: number
  image_path: string
  image_path_thumb: string | null
  package_color: string | null
  is_fabi: number
  store_uid: string
  vat_tax_rate: number
  item_type_uid: string
  ps_store_uid: string
  row: number
  stores: number
  list_package_uid: string[]
  list_store_uid: string[]
}

export interface GetPackagesParams {
  company_uid: string
  brand_uid: string
  list_store_uid: string[]
  skip_limit?: boolean
}

export interface PackagesApiResponse {
  data: Package[]
  track_id: string
}

export interface PackageSortItem {
  list_package_uid: string[]
  sort: number
}

export interface UpdatePackagesSortRequest {
  company_uid: string
  brand_uid: string
  list_data: PackageSortItem[]
}
