export interface CreateComboRequest {
  store_uid: string
  promotion_uid: string
  combo_type: 'PERCENT' | 'AMOUNT'
  type_id?: string
  item_id?: string
  from_date: number
  to_date: number
  sale_channel_uid: string
  time_sale_date_week: number
  time_sale_hour_day: number
  company_uid: string
  brand_uid: string
  ta_combo: number
  ots_combo: number
  is_all: 0 | 1
  is_type: 0 | 1
  is_item: 0 | 1
  extra_data: {
    is_combo: 0 | 1
    combo_id: string
  }
  promotion_partner_auto_gen?: 0 | 1
  is_update_same_combos?: boolean
}

export interface CreateComboResponse {
  success: boolean
  data?: any
  message?: string
}

/**
 * Update Combo Request Interface
 * Based on the actual API structure from cURL
 */
export interface UpdateComboRequest {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  ta_combo: number
  ots_combo: number
  is_all: 0 | 1
  is_type: 0 | 1
  is_item: 0 | 1
  type_id: string
  item_id: string
  combo_type: 'PERCENT' | 'AMOUNT'
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: 0 | 1
  }
  active: 0 | 1
  revision: number | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  sort: number
  store_uid: string
  combo_clone_id: string | null
  source_uid: string
  promotion: {
    id: string
    sort: number
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number
    brand_uid: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    source_uid: string
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    promotion_id: string
    promotion_name: string
    partner_auto_gen: 0 | 1
  }
  source: {
    id: string
    sort: number
    is_fb: 0 | 1
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number | null
    brand_uid: string
    source_id: string
    store_uid: string
    created_at: number
    created_by: string | null
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    source_name: string
    source_type: any[]
    partner_config: 0 | 1
  }
  sale_channel_uid: string
  promotion_partner_auto_gen: 0 | 1
  is_update_same_combos: boolean
}

export interface FilterState {
  is_all: 0 | 1
  is_item: 0 | 1
  is_type: 0 | 1
  type_id: string
  item_id: string
  is_combo: 0 | 1
  combo_id: string
}

export interface ComboFormData {
  comboType: 'PERCENT' | 'AMOUNT'
  comboValue: number
  fromDate: Date
  toDate: Date
  selectedDays: number[]
  selectedHours: number[]
  saleChannelUid: string
  promotionUid: string
  filterState: FilterState
}
