import { apiClient } from '@/lib/api/pos/pos-api'

export interface ImageUploadResponse {
  data: {
    image_url: string
  }
  track_id: string
}

export interface SyncBackgroundRequest {
  company_uid: string
  brand_uid: string
  list_store_uid: string[]
  background: string
}

export interface SyncBackgroundResponse {
  success: boolean
  message?: string
  data?: unknown
}

/**
 * Common Images API for POS system
 */
export const imagesApi = {
  /**
   * Upload image to POS CMS
   * Used for various features: payment methods, areas, store backgrounds, etc.
   */
  uploadImage: async (file: File): Promise<ImageUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await apiClient.post<ImageUploadResponse>('/v3/pos-cms/image/upload', formData, {
      headers: {
        // Let axios set Content-Type automatically with boundary
        'Content-Type': undefined
      }
    })

    return response.data
  },

  /**
   * Sync background image to stores
   */
  syncStoreBackground: async (request: SyncBackgroundRequest): Promise<SyncBackgroundResponse> => {
    const response = await apiClient.post<SyncBackgroundResponse>('/mdata/v1/store/sync-background', request)

    return response.data
  }
}
