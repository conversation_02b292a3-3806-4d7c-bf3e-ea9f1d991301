'use client'

import * as React from 'react'

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { usePrinterPositionInStore } from '../context'
import { PrinterPositionInStore } from '../data'
import { PrinterPositionInStoreButtons } from './printer-position-in-store-buttons'
import { PrinterPositionInStoreTableToolbar } from './printer-position-in-store-table-toolbar'

interface PrinterPositionInStoreTableWrapperProps {
  columns: ColumnDef<PrinterPositionInStore, unknown>[]
  data: PrinterPositionInStore[]
  onStoreFilterChange?: (storeId: string | null) => void
  onScopeTypeChange?: (scopeType: string) => void
  hasStoreSelected?: boolean
  currentScopeType?: string
  currentStoreId?: string | null
}

export function PrinterPositionInStoreTableWrapper({
  columns,
  data,
  onStoreFilterChange,
  onScopeTypeChange,
  hasStoreSelected = false,
  currentScopeType = '0',
  currentStoreId = null
}: PrinterPositionInStoreTableWrapperProps) {
  const { setOpen, setCurrentRow, open } = usePrinterPositionInStore()
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [storeFilter, setStoreFilter] = React.useState<string | null>(null)

  React.useEffect(() => {
    if (open !== 'bulk-delete') {
      setRowSelection({})
    }
  }, [open])

  const filteredData = React.useMemo(() => {
    return data
  }, [data, storeFilter])

  const handleRowClick = (row: PrinterPositionInStore) => {
    setCurrentRow(row)
    setOpen('update')
  }

  const handleStoreFilter = (storeId: string | null) => {
    setStoreFilter(storeId)
    if (onStoreFilterChange) {
      onStoreFilterChange(storeId)
    }
  }

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  })

  return (
    <div className='space-y-4'>
      {/* Header with buttons */}
      <div className='flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
        <h2 className='text-2xl font-bold tracking-tight'>Vị trí máy in tại cửa hàng</h2>
        <PrinterPositionInStoreButtons />
      </div>

      {/* Table toolbar */}
      <PrinterPositionInStoreTableToolbar
        table={table}
        onStoreFilter={handleStoreFilter}
        onScopeTypeChange={onScopeTypeChange}
        currentScopeType={currentScopeType}
        currentStoreId={currentStoreId}
      />

      {/* Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='cursor-pointer'
                  onClick={e => {
                    const target = e.target as HTMLElement
                    if (target.closest('[data-actions]') || target.closest('[role="checkbox"]')) {
                      return
                    }
                    handleRowClick(row.original)
                  }}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} {...(cell.column.id === 'actions' ? { 'data-actions': true } : {})}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  {!hasStoreSelected ? (
                    <div className='text-center'>
                      <p className='text-muted-foreground mb-2 text-sm'>
                        Vui lòng chọn cửa hàng để xem dữ liệu vị trí máy in
                      </p>
                    </div>
                  ) : (
                    'Không có dữ liệu vị trí máy in cho cửa hàng này.'
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
