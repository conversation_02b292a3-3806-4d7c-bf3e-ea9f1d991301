import { useMemo } from 'react'

import { useUserStore } from '@/stores/user'

import { POS_CITIES_DATA } from '@/constants/local-storage'

import { usePosStoresData } from './use-pos-stores-data'

export interface PosCity {
  id: string
  city_id: string
  city_name: string
  active: number
}

/**
 * Hook to get POS cities data from localStorage with utility functions
 * Filters cities based on current selected brand
 */
export const usePosCitiesData = () => {
  const { currentBrandId } = useUserStore()
  const { stores } = usePosStoresData()

  const cities = useMemo(() => {
    try {
      const storedData = localStorage.getItem(POS_CITIES_DATA)
      if (storedData) {
        const parsed = JSON.parse(storedData)
        return Array.isArray(parsed) ? parsed : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_cities_data from localStorage:', error)
      return []
    }
  }, [])

  const filteredCities = useMemo(() => {
    if (!currentBrandId) {
      return cities
    }

    const citiesWithBrandStores = cities.filter(city => {
      const cityId = city.id || city.city_id
      const cityStores = stores.filter(store => store.city_uid === cityId && store.brand_uid === currentBrandId)
      return cityStores.length > 0
    })

    return citiesWithBrandStores
  }, [cities, stores, currentBrandId])

  const activeCities = useMemo(() => {
    return filteredCities.filter(city => city.active === 1)
  }, [filteredCities])

  const sortedCities = useMemo(() => {
    return [...activeCities].sort((a, b) => {
      return a.city_name.localeCompare(b.city_name, 'vi')
    })
  }, [activeCities])

  const getCityById = (cityId: string): PosCity | undefined => {
    return filteredCities.find(city => city.id === cityId || city.city_id === cityId)
  }

  const getCityByName = (cityName: string): PosCity | undefined => {
    return filteredCities.find(city => city.city_name === cityName)
  }

  const getAllCityIds = (): string[] => {
    return sortedCities.map(city => city.id || city.city_id)
  }

  return {
    cities: sortedCities,
    activeCities,
    filteredCities,
    currentBrandId,

    getCityById,
    getCityByName,
    getAllCityIds,

    hasCities: sortedCities.length > 0,
    isEmpty: sortedCities.length === 0,
    totalCities: sortedCities.length
  }
}
