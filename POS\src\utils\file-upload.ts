import { FILE_TYPES } from '@/constants/crm/file-types'

export interface FileUploadOptions {
  accept?: string
  multiple?: boolean
  onSuccess?: (file: File, url: string) => void
  onError?: (error: string) => void
}

export const createFileUploader = (options: FileUploadOptions = {}) => {
  const { accept = FILE_TYPES.IMAGES, multiple = false, onSuccess, onError } = options

  return (): Promise<{ file: File; url: string } | null> => {
    return new Promise(resolve => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = accept
      input.multiple = multiple

      input.onchange = event => {
        const file = (event.target as HTMLInputElement).files?.[0]
        if (!file) {
          resolve(null)
          return
        }

        try {
          const url = URL.createObjectURL(file)
          const result = { file, url }
          onSuccess?.(file, url)
          resolve(result)
        } catch (error) {
          const errorMessage = 'Failed to process file'
          onError?.(errorMessage)
          resolve(null)
        }
      }

      input.click()
    })
  }
}
