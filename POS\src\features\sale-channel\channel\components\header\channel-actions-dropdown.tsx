import { IconCopy, IconSettings } from '@tabler/icons-react'

import { But<PERSON> } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

interface ChannelActionsDropdownProps {
  onCopyChannels: () => void
  onImportFromFile: () => void
}

export function ChannelActionsDropdown({
  onCopyChannels,
  onImportFromFile
}: ChannelActionsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='sm'>
          Tiện ích
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem onClick={onCopyChannels}>
          <IconCopy className='mr-2 h-4 w-4' />
          Sao chép KBH
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onImportFromFile}>
          <IconSettings className='mr-2 h-4 w-4' />
          Thê<PERSON> kênh bán hàng từ file
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
