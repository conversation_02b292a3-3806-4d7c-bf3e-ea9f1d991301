import { useMutation, useQueryClient } from '@tanstack/react-query'

import type { BulkCreateTablesRequest } from '@/types'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { tablesApi } from '@/lib/table-api'

import { useStoresData } from '@/hooks/api/use-stores'
import { areasApi } from '@/lib/api'

interface TableData {
  tenBan: string
  khuVuc: string
  nguon: string
  monDatTruoc: string
  moTa: string
}

interface ImportTablesParams {
  storeUid: string
  tableData: TableData[]
}

export const useImportTables = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const { data: stores } = useStoresData()

  const importTablesMutation = useMutation({
    mutationFn: async ({ storeUid, tableData }: ImportTablesParams) => {
      // Lấy thông tin company và brand từ auth store
      const company_uid = company?.id || ''
      const brand_uid = brands?.[0]?.id || '' // Lấy brand đầu tiên

      // Step 1: Tạo areas trước dựa vào khu vực trong Excel
      const uniqueAreaNames = Array.from(new Set(tableData.map(table => table.khuVuc)))
      const areasToCreate = uniqueAreaNames.map((areaName, index) => ({
        company_uid,
        brand_uid,
        area_name: areaName,
        area_id: `AREA-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
        store_uid: storeUid,
        sort: index + 1
      }))

      // Gọi API tạo areas
      const createdAreas = await areasApi.bulkImportAreas(areasToCreate)

      // Tạo mapping để tìm area theo tên
      const areaMap = new Map(createdAreas.map(area => [area.area_name, area]))

      // Step 2: Tạo tables dựa vào area vừa tạo
      const tablesToCreate: BulkCreateTablesRequest = tableData.map((table, index) => {
        const area = areaMap.get(table.khuVuc)
        if (!area) {
          throw new Error(`Không tìm thấy khu vực: ${table.khuVuc}`)
        }

        return {
          company_uid,
          brand_uid,
          table_name: table.tenBan,
          table_id: `TABLE-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
          extra_data: {
            order_list: []
          },
          description: table.moTa || `Khu vực ${table.khuVuc}`,
          store_uid: storeUid,
          source_id: table.nguon || '10000171',
          area_uid: area.id, // Lấy từ area vừa tạo
          area_id: area.area_id, // Lấy từ area vừa tạo
          sort: index + 1
        }
      })

      // Gọi API tạo tables
      const createdTables = await tablesApi.createTables(tablesToCreate)

      return {
        areas: createdAreas,
        tables: createdTables,
        storeUid
      }
    },
    onSuccess: data => {
      // Tìm tên cửa hàng để hiển thị trong toast
      const selectedStore = stores?.find(store => store.id === data.storeUid)
      const storeName = selectedStore?.name || data.storeUid

      toast.success(`Đã tạo ${data.areas.length} khu vực và ${data.tables.length} bàn cho cửa hàng: ${storeName}`)

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['areas'] })
      queryClient.invalidateQueries({ queryKey: ['tables'] })
      queryClient.invalidateQueries({ queryKey: ['table-layout'] })
    },
    onError: (error: any) => {
      console.error('Import tables error:', error)
      toast.error(error.message || 'Có lỗi xảy ra khi import bàn')
    }
  })

  return {
    importTables: importTablesMutation.mutate,
    isImporting: importTablesMutation.isPending,
    error: importTablesMutation.error
  }
}
