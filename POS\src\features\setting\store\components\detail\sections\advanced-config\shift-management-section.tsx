import type { UseFormReturn } from 'react-hook-form'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Checkbox } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface ShiftManagementSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function ShiftManagementSection({ form, isLoading = false }: ShiftManagementSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Quản lý ca</h2>

      <div className='space-y-6'>
        {/* Đếm tờ tiền khi chốt ca */}
        <FormField
          control={form.control}
          name='enable_count_money'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'><PERSON><PERSON><PERSON> tờ tiền khi chốt ca</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình thu ngân và doanh thu trong ca */}
        <FormField
          control={form.control}
          name='disable_shift_total_amount'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Cấu hình thu ngân và doanh thu trong ca</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value?.toString() || '0'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn cấu hình thu ngân'
                    className='flex-1'
                    options={[
                      {
                        value: '0',
                        label: 'Cho phép thu ngân thấy doanh thu trong ca'
                      },
                      {
                        value: '1',
                        label: 'Thu ngân không được thấy doanh thu trong ca, nhưng vẫn thấy danh sách đơn hàng'
                      },
                      {
                        value: '2',
                        label: 'Thu ngân không được thấy doanh thu trong ca và danh sách đơn hàng'
                      }
                    ]}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép xóa hóa đơn trên CMS khi ca đang mở */}
        <FormField
          control={form.control}
          name='allow_remove_shift_open'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <FormLabel className='w-[240px] leading-relaxed'>
                  Cho phép xóa hóa đơn trên CMS khi ca đang mở
                </FormLabel>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tự động đăng xuất khi chốt ca */}
        <FormField
          control={form.control}
          name='close_shift_auto_logout'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Tự động đăng xuất khi chốt ca</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yêu cầu phải đóng ca trong ngày */}
        <FormField
          control={form.control}
          name='require_close_shift_in_day'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Yêu cầu phải đóng ca trong ngày</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
