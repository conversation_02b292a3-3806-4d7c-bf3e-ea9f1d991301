interface UseSeparatedSourcesProps {
  sources: any[]
  selectedSourceIds: string[]
}

interface UseSeparatedSourcesReturn {
  selectedSources: any[]
  unselectedSources: any[]
}

export function useSeparatedSources({
  sources,
  selectedSourceIds
}: UseSeparatedSourcesProps): UseSeparatedSourcesReturn {
  // Separate selected and unselected sources
  const selectedSources = sources.filter((source: any) => 
    selectedSourceIds.includes(source.id)
  )

  const unselectedSources = sources.filter((source: any) => 
    !selectedSourceIds.includes(source.id)
  )

  return {
    selectedSources,
    unselectedSources
  }
}
