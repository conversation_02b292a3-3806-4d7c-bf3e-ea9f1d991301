import { useQuery } from '@tanstack/react-query'

import { QUERY_KEYS } from '@/constants'
import { useAuthStore } from '@/stores'
import { SaleByDateResponse, GetSaleByDateParams } from '@/types/api'

import { saleByDateApi } from '@/lib/api'

export interface UseSaleByDateOptions {
  params?: Partial<GetSaleByDateParams>
  enabled?: boolean
}

/**
 * Hook to get sale by date report data
 */
export const useSaleByDate = (options: UseSaleByDateOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetSaleByDateParams = {
    company_uid: params.company_uid || company?.id || '',
    brand_uid: params.brand_uid || selectedBrand?.id || '',
    list_store_uid: params.list_store_uid || '',
    start_date: params.start_date ?? Date.now() - 24 * 60 * 60 * 1000, // Default to yesterday
    end_date: params.end_date ?? Date.now(),
    csv: params.csv ?? 0,
    store_open_at: params.store_open_at ?? 0,
    results_per_page: params.results_per_page ?? 200,
    page: params.page ?? 1
  }

  const hasRequiredAuth = !!(dynamicParams.company_uid && dynamicParams.brand_uid && dynamicParams.list_store_uid)

  return useQuery({
    queryKey: [QUERY_KEYS.REPORTS_SALE_BY_DATE, dynamicParams],
    queryFn: async (): Promise<SaleByDateResponse> => {
      return await saleByDateApi.getSaleByDate(dynamicParams)
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })
}
