import React from 'react'

import { HeaderActions } from './header-actions'
import { StoreSelector } from './store-selector'

interface TableLayoutHeaderProps {
  selectedStoreId?: string
  onStoreChange?: (storeId: string) => void
  onCreateNew?: () => void
  onSaveLayout?: () => void
  isUpdating?: boolean
  onImportTables?: () => void
  onEditTableInfo?: () => void
  onSortTables?: () => void
  onCustomizeTable?: () => void
}

export const TableLayoutHeader: React.FC<TableLayoutHeaderProps> = ({
  selectedStoreId = 'all',
  onStoreChange,
  onCreateNew,
  onSaveLayout,
  isUpdating = false,
  onImportTables,
  onEditTableInfo,
  onSortTables,
  onCustomizeTable
}) => {
  return (
    <div className='flex items-center justify-between border-b bg-white p-4'>
      <div className='flex items-center gap-4'>
        <span className='text-sm font-bold text-gray-700'>Danh sách bàn</span>
        <StoreSelector value={selectedStoreId} onValueChange={onStoreChange || (() => {})} />
      </div>
      <HeaderActions
        onCreateNew={onCreateNew}
        onSaveLayout={onSaveLayout}
        isUpdating={isUpdating}
        onImportTables={onImportTables}
        onEditTableInfo={onEditTableInfo}
        onSortTables={onSortTables}
        onCustomizeTable={onCustomizeTable}
      />
    </div>
  )
}
