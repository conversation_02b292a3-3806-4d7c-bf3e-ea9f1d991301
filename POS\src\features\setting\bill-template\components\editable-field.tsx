import React, { useState } from 'react'

interface EditableFieldProps {
  value: string | number
  onSave: (value: string) => void
  className?: string
  children?: React.ReactNode
  placeholder?: string
  type?: 'text' | 'number'
  inputClassName?: string
}

export function EditableField({
  value,
  onSave,
  className,
  children,
  placeholder,
  type = 'text',
  inputClassName
}: EditableFieldProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value.toString())

  React.useEffect(() => {
    setEditValue(value.toString())
  }, [value])

  const handleSave = () => {
    if (editValue.trim() !== value.toString()) {
      onSave(editValue.trim())
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditValue(value.toString())
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (isEditing) {
    return (
      <input
        type={type}
        value={editValue}
        onChange={e => setEditValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        className={`font-inherit rounded border-2 border-blue-500 bg-white px-1 py-0.5 text-sm outline-none focus:border-blue-700 focus:shadow-[0_0_0_2px_rgba(0,123,255,0.25)] ${inputClassName || ''}`}
        style={{ width: `${Math.max(editValue.length + 1, 6)}ch` }}
        placeholder={placeholder}
        autoFocus
      />
    )
  }

  return (
    <span
      className={`inline-flex cursor-pointer items-center gap-1 rounded py-0.5 transition-colors hover:bg-gray-100 ${className || ''}`}
      onClick={() => setIsEditing(true)}
      title='Click to edit'
    >
      {children || value}
    </span>
  )
}
