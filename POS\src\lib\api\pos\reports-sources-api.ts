import type { GetReportsSourcesParams, ReportsSourcesResponse } from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Get reports sources data
 */
export const getReportsSources = async (
  params: GetReportsSourcesParams
): Promise<ReportsSourcesResponse> => {
  const response = await apiClient.get<ReportsSourcesResponse>('/v1/reports/sale-summary/sources', {
    params: {
      brand_uid: params.brand_uid,
      company_uid: params.company_uid,
      list_store_uid: params.list_store_uid,
      start_date: params.start_date,
      end_date: params.end_date,
      store_open_at: params.store_open_at ?? 0,
      limit: params.limit ?? 5
    }
  })

  return response.data
}
