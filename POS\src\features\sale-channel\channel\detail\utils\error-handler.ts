/**
 * Error handling utilities for sales channel operations
 */

export interface ApiErrorResponse {
  error: {
    code: number
    message: string
  }
  track_id: string
}

export interface ApiError {
  response?: {
    status?: number
    data?: ApiErrorResponse | { message?: string }
  }
  message?: string
}

/**
 * Extract error message from API error response
 * Handles the specific format: { error: { code: 409, message: "..." }, track_id: "..." }
 * @param error The error object from API call
 * @returns User-friendly error message in Vietnamese
 */
export const getApiErrorMessage = (error: unknown): string => {
  const apiError = error as ApiError

  // Check for the specific API error format
  if (apiError?.response?.data && typeof apiError.response.data === 'object') {
    const errorData = apiError.response.data as ApiErrorResponse

    // Handle the format: { error: { code: 409, message: "..." }, track_id: "..." }
    if (errorData.error?.message) {
      return errorData.error.message
    }

    // Handle alternative format: { message: "..." }
    if ('message' in errorData && typeof errorData.message === 'string' && errorData.message) {
      return errorData.message
    }
  }

  // Fallback to generic error message
  if (apiError?.message) {
    return apiError.message
  }

  // Default error message
  return 'Đã xảy ra lỗi không xác định'
}

/**
 * Check if error is a conflict error (409)
 * @param error The error object from API call
 * @returns True if error is a conflict error
 */
export const isConflictError = (error: unknown): boolean => {
  const apiError = error as ApiError

  if (apiError?.response?.data && typeof apiError.response.data === 'object') {
    const errorData = apiError.response.data as ApiErrorResponse
    return errorData.error?.code === 409
  }

  return apiError?.response?.status === 409
}

/**
 * Check if error is a validation error (400)
 * @param error The error object from API call
 * @returns True if error is a validation error
 */
export const isValidationError = (error: unknown): boolean => {
  const apiError = error as ApiError

  if (apiError?.response?.data && typeof apiError.response.data === 'object') {
    const errorData = apiError.response.data as ApiErrorResponse
    return errorData.error?.code === 400
  }

  return apiError?.response?.status === 400
}

/**
 * Check if error is a server error (5xx)
 * @param error The error object from API call
 * @returns True if error is a server error
 */
export const isServerError = (error: unknown): boolean => {
  const apiError = error as ApiError

  if (apiError?.response?.data && typeof apiError.response.data === 'object') {
    const errorData = apiError.response.data as ApiErrorResponse
    return errorData.error?.code >= 500
  }

  return (apiError?.response?.status || 0) >= 500
}

/**
 * Get error type for better error handling
 * @param error The error object from API call
 * @returns Error type string
 */
export const getErrorType = (
  error: unknown
): 'conflict' | 'validation' | 'server' | 'network' | 'unknown' => {
  if (isConflictError(error)) return 'conflict'
  if (isValidationError(error)) return 'validation'
  if (isServerError(error)) return 'server'

  const apiError = error as ApiError
  if (!apiError?.response) return 'network'

  return 'unknown'
}

/**
 * Get track ID from error response for debugging
 * @param error The error object from API call
 * @returns Track ID if available
 */
export const getErrorTrackId = (error: unknown): string | null => {
  const apiError = error as ApiError

  if (apiError?.response?.data && typeof apiError.response.data === 'object') {
    const errorData = apiError.response.data as ApiErrorResponse
    return errorData.track_id || null
  }

  return null
}
