export interface ServiceCharge {
  id: string
  from_date: number
  to_date: number
  from_amount: number
  to_amount: number
  service_charge: number
  charge_type: 'AMOUNT' | 'PERCENT'
  time_sale_hour_day: number
  time_sale_date_week: number
  extra_data: {
    is_area: number
    area_ids: string[]
    is_table: number
    is_source: number
    table_ids: string[]
    desciption: string
    source_ids: string[]
  }
  description: string | null
  active: number
  sort: number
  brand_uid: string
  company_uid: string
  service_charge_clone_id: string | null
  membership_type_uid: string | null
  is_membership: string
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  store_uid: string
}

export interface ServiceChargeApiResponse {
  data: ServiceCharge[]
  track_id: string
}

export interface GetServiceChargeParams {
  company_uid: string
  brand_uid: string
  page?: number
  list_store_uid?: string
  active?: number
  limit?: number
  status?: 'expired' | 'unexpired'
}
