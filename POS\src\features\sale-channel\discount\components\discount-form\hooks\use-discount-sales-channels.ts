import { useSalesChannels } from '@/hooks/api'
import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

interface UseDiscountSalesChannelsProps {
  storeUid: string
}

export function useDiscountSalesChannels({ storeUid }: UseDiscountSalesChannelsProps) {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const { data: salesChannels = [], isLoading: isLoadingChannels } = useSalesChannels({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    storeUid: storeUid,
    partnerConfig: 1,
    skipLimit: true
  })

  return {
    salesChannels,
    isLoadingChannels
  }
}
