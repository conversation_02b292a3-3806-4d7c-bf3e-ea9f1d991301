import { useQuery } from '@tanstack/react-query'

import { apiClient } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface City {
  id: string
  city_id: string
  city_name: string
  fb_city_id: string
  active: number
  sort: number
}

// Remove unused interface since we're handling multiple response structures

const fetchCities = async (): Promise<City[]> => {
  try {
    const response = await apiClient.get('/v1/mdata/cities')

    // Check if response.data is an array directly
    if (Array.isArray(response.data)) {
      const filteredCities = response.data.filter((city: any) => city.active === 1)
      return filteredCities
    }

    // Check if response has success field
    if (response.data.success === false) {
      throw new Error(response.data.message || 'Failed to fetch cities')
    }

    // Check if response.data.data exists
    if (response.data.data && Array.isArray(response.data.data)) {
      const filteredCities = response.data.data.filter((city: any) => city.active === 1)
      return filteredCities
    }

    throw new Error('Unexpected response structure')
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to fetch cities: ${error.message}`)
    }
    throw new Error('Failed to fetch cities')
  }
}

export function useCities() {
  return useQuery({
    queryKey: [QUERY_KEYS.CITIES],
    queryFn: fetchCities,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export type { City }
