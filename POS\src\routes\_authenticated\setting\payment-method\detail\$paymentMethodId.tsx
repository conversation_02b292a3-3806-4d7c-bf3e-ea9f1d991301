import { createFileRoute } from '@tanstack/react-router'
import { PaymentMethodDetailForm } from '@/features/payment-methods'

interface PaymentMethodDetailSearch {
  company_uid: string
  brand_uid: string
  payment_method_id: string
}

export const Route = createFileRoute('/_authenticated/setting/payment-method/detail/$paymentMethodId')({
  component: PaymentMethodDetailPage,
  validateSearch: (search: Record<string, unknown>): PaymentMethodDetailSearch => ({
    company_uid: search.company_uid as string,
    brand_uid: search.brand_uid as string,
    payment_method_id: search.payment_method_id as string,
  }),
})

function PaymentMethodDetailPage() {
  const { paymentMethodId } = Route.useParams()
  const { company_uid, brand_uid, payment_method_id } = Route.useSearch()

  return (
    <PaymentMethodDetailForm 
      paymentMethodId={paymentMethodId}
      companyUid={company_uid}
      brandUid={brand_uid}
      paymentMethodCode={payment_method_id}
    />
  )
}
