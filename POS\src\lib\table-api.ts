import type { Table, GetTablesParams, TablesApiResponse, BulkCreateTablesRequest } from '@/types'

import { api } from './api/pos/pos-api'

/**
 * Tables API Service
 */
export const tablesApi = {
  /**
   * Get list of tables
   */
  getTables: async (params: GetTablesParams = {}): Promise<TablesApiResponse> => {
    const searchParams = new URLSearchParams()

    if (params.skip_limit) {
      searchParams.append('skip_limit', 'true')
    }
    if (params.brand_uid) {
      searchParams.append('brand_uid', params.brand_uid)
    }
    if (params.company_uid) {
      searchParams.append('company_uid', params.company_uid)
    }
    if (params.list_store_uid) {
      searchParams.append('list_store_uid', params.list_store_uid)
    }
    if (params.store_uid) {
      searchParams.append('store_uid', params.store_uid)
    }

    const url = `/pos/v1/table${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

    const response = await api.get<Table[]>(url)

    // Handle different response formats
    if (Array.isArray(response.data)) {
      return {
        data: response.data,
        total: response.data.length
      }
    }

    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data as TablesApiResponse
    }

    return {
      data: [],
      total: 0
    }
  },

  /**
   * Create multiple tables
   */
  createTables: async (tables: BulkCreateTablesRequest): Promise<Table[]> => {
    const response = await api.post('/pos/v1/table', tables)
    return (response.data.data || response.data || []) as Table[]
  }
}
