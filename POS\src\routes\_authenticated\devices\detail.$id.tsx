import { createFileRoute } from '@tanstack/react-router'

import DeviceDetailPage from '@/features/devices/detail'

interface DeviceDetailSearch {
  store_uid?: string
}

export const Route = createFileRoute('/_authenticated/devices/detail/$id')({
  component: DeviceDetailPage,
  validateSearch: (search: Record<string, unknown>): DeviceDetailSearch => ({
    store_uid: search.store_uid as string
  })
})
