import type { ServiceChargeApiResponse, GetServiceChargeParams, ServiceCharge } from '@/types/service-charge'

import { api } from './api/pos/pos-api'

// Cache for service charge requests
const serviceChargeCache = new Map<string, { data: ServiceChargeApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ServiceChargeApiResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export const serviceChargeApi = {
  /**
   * Get service charges with request deduplication and caching
   */
  getServiceCharges: async (params: GetServiceChargeParams): Promise<ServiceChargeApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.page || 1}-${params.list_store_uid || 'all'}-${params.active ?? 'all'}-${params.status || 'all'}`

    // Check cache first
    const cached = serviceChargeCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request for this key
    const pendingRequest = pendingRequests.get(requestKey)
    if (pendingRequest) {
      return pendingRequest
    }

    // Create new request
    const requestPromise = (async (): Promise<ServiceChargeApiResponse> => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.set('company_uid', params.company_uid)
        queryParams.set('brand_uid', params.brand_uid)

        if (params.page) {
          queryParams.set('page', params.page.toString())
        }

        if (params.list_store_uid) {
          queryParams.set('list_store_uid', params.list_store_uid)
        }

        if (params.active !== undefined) {
          queryParams.set('active', params.active.toString())
        }

        if (params.limit) {
          queryParams.set('limit', params.limit.toString())
        }

        if (params.status) {
          queryParams.set('status', params.status)
        }

        const response = await api.get(`/mdata/v1/service-charges?${queryParams.toString()}`)

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from service charge API')
        }

        const result = response.data as ServiceChargeApiResponse

        // Cache the result
        serviceChargeCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } catch (error: any) {
        console.error('Error fetching service charges:', error)

        if (error.response?.status === 429) {
          throw new Error('Too many requests - please wait a moment before trying again.')
        }

        if (error.response?.status === 401) {
          throw new Error('Unauthorized - please check your authentication.')
        }

        if (error.response?.status === 403) {
          throw new Error('Forbidden - you do not have permission to access this resource.')
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Get service charge detail by ID
   */
  getServiceChargeDetail: async (params: {
    company_uid: string
    brand_uid: string
    id: string
  }): Promise<ServiceCharge> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('id', params.id)

      const response = await api.get(`/mdata/v1/service-charge?${queryParams.toString()}`)

      return response.data.data as ServiceCharge
    } catch (error: any) {
      console.error('Error fetching service charge detail:', error)
      throw error
    }
  },

  /**
   * Create service charge
   */
  createServiceCharge: async (serviceChargeData: any): Promise<void> => {
    try {
      await api.post('/mdata/v1/service-charge', serviceChargeData)

      // Clear cache after successful creation
      serviceChargeCache.clear()
    } catch (error: any) {
      console.error('Error creating service charge:', error)
      throw error
    }
  },

  /**
   * Update service charge
   */
  updateServiceCharge: async (serviceChargeData: any): Promise<void> => {
    try {
      await api.put('/mdata/v1/service-charge', serviceChargeData)

      // Clear cache after successful update
      serviceChargeCache.clear()
    } catch (error: any) {
      console.error('Error updating service charge:', error)
      throw error
    }
  },

  /**
   * Delete service charge
   */
  deleteServiceCharge: async (params: { id: string; company_uid: string; brand_uid: string }): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('id', params.id)

      await api.delete(`/mdata/v1/service-charges?${queryParams.toString()}`)

      // Clear cache after successful deletion
      serviceChargeCache.clear()
    } catch (error: any) {
      console.error('Error deleting service charge:', error)
      throw error
    }
  },

  /**
   * Get area names by area IDs
   */
  getAreaNames: async (params: {
    company_uid: string
    brand_uid: string
    store_uid: string
    list_area_id: string
  }): Promise<{ data: Array<{ area_id: string; area_name: string }> }> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('skip_limit', 'true')
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('store_uid', params.store_uid)
      queryParams.set('list_area_id', params.list_area_id)

      const response = await api.get(`/pos/v1/area/get-name?${queryParams.toString()}`)

      return response.data as { data: Array<{ area_id: string; area_name: string }> }
    } catch (error: any) {
      console.error('Error fetching area names:', error)
      throw error
    }
  },

  /**
   * Get source names by source IDs
   */
  getSourceNames: async (params: {
    company_uid: string
    brand_uid: string
    store_uid: string
    list_source_id: string
  }): Promise<{ data: Array<{ source_id: string; source_name: string }> }> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('skip_limit', 'true')
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('store_uid', params.store_uid)
      queryParams.set('list_source_id', params.list_source_id)

      const response = await api.get(`/mdata/v1/sources?${queryParams.toString()}`)

      return response.data as { data: Array<{ source_id: string; source_name: string }> }
    } catch (error: any) {
      console.error('Error fetching source names:', error)
      throw error
    }
  },

  /**
   * Get service charge programs for a specific store (for copying)
   */
  getServiceChargePrograms: async (params: {
    company_uid: string
    brand_uid: string
    store_uid: string
  }): Promise<ServiceCharge[]> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('skip_limit', 'true')
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('store_uid', params.store_uid)

      const response = await api.get(`/mdata/v1/service-charges?${queryParams.toString()}`)

      return (response.data as ServiceChargeApiResponse)?.data || []
    } catch (error: any) {
      console.error('Error fetching service charge programs:', error)
      throw error
    }
  },

  /**
   * Clone service charge programs to target stores
   */
  cloneServiceCharges: async (params: {
    company_uid: string
    brand_uid: string
    list_service_charge_uid: string[]
    list_store_uid_target: string[]
  }): Promise<{ success: boolean; message?: string; track_id: string }> => {
    try {
      const payload = {
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        list_service_charge_uid: params.list_service_charge_uid,
        list_store_uid_target: params.list_store_uid_target
      }

      const response = await api.post('/mdata/v1/clone-service-charge', payload)

      return response.data as unknown as { success: boolean; message?: string; track_id: string }
    } catch (error: any) {
      console.error('Error cloning service charges:', error)
      throw error
    }
  },

  /**
   * Clear cache manually
   */
  clearCache: () => {
    serviceChargeCache.clear()
    pendingRequests.clear()
  }
}
