import type {
  GetRegisterPageConfigResponse,
  SaveRegisterPageConfigRequest,
  SaveRegisterPageConfigResponse
} from '@/types/api/crm'

import { crmApi } from './crm-api'

export const marketingApi = {
  getRegisterPageConfig: async (pos_parent: string): Promise<GetRegisterPageConfigResponse> => {
    const response = await crmApi.get('/marketing/get-config-register-page', {
      params: { pos_parent }
    })
    return response.data
  },

  saveRegisterPageConfig: async (data: SaveRegisterPageConfigRequest): Promise<SaveRegisterPageConfigResponse> => {
    const response = await crmApi.post('/marketing/save-config-register-page', data)
    return response.data
  }
}
