import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { discountPaymentApi } from '@/lib/discount-payment-api'

import { QUERY_KEYS } from '@/constants/query-keys'

// Hook for fetching payment discount detail
export const usePaymentDiscountDetail = (discountId: string) => {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT, 'detail', discountId, company?.id, selectedBrand?.id],
    queryFn: async () => {
      if (!discountId || !company?.id || !selectedBrand?.id) {
        return null
      }

      return discountPaymentApi.getDiscountPaymentById({
        id: discountId,
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })
    },
    enabled: !!discountId && !!company?.id && !!selectedBrand?.id
  })
}

interface UsePaymentDiscountPromotionsOptions {
  enabled?: boolean
}

// Hook for fetching payment discount promotions
export const usePaymentDiscountPromotions = (storeUid: string, options: UsePaymentDiscountPromotionsOptions = {}) => {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const { enabled = true } = options

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT, 'promotions', storeUid, company?.id, selectedBrand?.id],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết để lấy danh sách khuyến mãi')
      }

      return discountPaymentApi.getPaymentDiscountPromotions({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        storeUid: storeUid
      })
    },
    enabled: enabled && !!storeUid && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

// Hook for creating payment discount
export const useCreatePaymentDiscount = (options: { onSuccess?: () => void } = {}) => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  return useMutation({
    mutationFn: async (data: any) => {
      console.log('Creating payment discount:', data)
      return discountPaymentApi.createPaymentDiscount(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT] })
      toast.success('Đã tạo chiết khấu thanh toán thành công!')

      if (options.onSuccess) {
        options.onSuccess()
      } else {
        navigate({ to: '/sale/discount-payment' })
      }
    },
    onError: (error: any) => {
      console.error('Error creating payment discount:', error)
      toast.error(error.message || 'Lỗi khi tạo chiết khấu thanh toán')
    }
  })
}

// Hook for updating payment discount
export const useUpdatePaymentDiscount = (options: { onSuccess?: () => void } = {}) => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  return useMutation({
    mutationFn: async (data: any) => {
      console.log('Updating payment discount:', data)
      return discountPaymentApi.updateDiscountPayment(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNT_PAYMENT] })
      toast.success('Đã cập nhật chiết khấu thanh toán thành công!')

      if (options.onSuccess) {
        options.onSuccess()
      } else {
        navigate({ to: '/sale/discount-payment' })
      }
    },
    onError: (error: any) => {
      console.error('Error updating payment discount:', error)
      toast.error(error.message || 'Lỗi khi cập nhật chiết khấu thanh toán')
    }
  })
}
