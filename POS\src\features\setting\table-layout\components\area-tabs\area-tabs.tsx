import React, { useRef, useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { ChevronLeft, ChevronRight, Plus, Settings, Trash2 } from 'lucide-react'

import { useDeleteAreas } from '@/hooks/api/use-areas'

import { Button } from '@/components/ui'

import type { AreaOption } from '../../data/table-layout-types'

interface AreaTabsProps {
  areas: AreaOption[]
  selectedAreaId: string
  onAreaChange: (areaId: string) => void
  onCreateNew?: () => void
  storeUid?: string
}

export const AreaTabs: React.FC<AreaTabsProps> = ({ areas, selectedAreaId, onAreaChange, onCreateNew, storeUid }) => {
  const navigate = useNavigate()
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)
  const [hoveredAreaId, setHoveredAreaId] = useState<string | null>(null)

  const { deleteAreas, isDeleting } = useDeleteAreas()

  const checkScrollState = () => {
    const container = scrollContainerRef.current
    if (!container) return

    setCanScrollLeft(container.scrollLeft > 0)
    setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth)
  }

  useEffect(() => {
    checkScrollState()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollState)
      return () => container.removeEventListener('scroll', checkScrollState)
    }
  }, [areas])

  const scrollLeft = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  const handleSettingsClick = (areaId: string, event: React.MouseEvent) => {
    event.stopPropagation()

    if (!storeUid) {
      return
    }

    navigate({
      to: `/setting/area/detail/${areaId}`,
      search: {
        store_uid: storeUid
      }
    })
  }

  const handleDeleteArea = (areaId: string, event: React.MouseEvent) => {
    event.stopPropagation()

    if (!storeUid) {
      return
    }

    deleteAreas({ areaIds: [areaId], storeUid })
  }

  return (
    <div className='flex items-center gap-2 border-b bg-white px-4 py-3'>
      <div className='mr-4 flex items-center gap-2'>
        <span className='text-sm font-medium whitespace-nowrap text-gray-700'>Khu vực</span>
      </div>
      <Button
        variant='ghost'
        size='sm'
        onClick={scrollLeft}
        disabled={!canScrollLeft}
        className='h-8 w-8 flex-shrink-0 p-1'
      >
        <ChevronLeft className='h-4 w-4' />
      </Button>

      <div
        ref={scrollContainerRef}
        className='flex-1 overflow-x-auto'
        style={
          {
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          } as React.CSSProperties
        }
      >
        <div className='flex min-w-max gap-1'>
          {areas.map(area => (
            <div
              key={area.id}
              className='relative'
              onMouseEnter={() => setHoveredAreaId(area.id)}
              onMouseLeave={() => setHoveredAreaId(null)}
            >
              <button
                onClick={() => onAreaChange(area.id)}
                className={`rounded-md px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedAreaId === area.id
                    ? 'border border-blue-200 bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                } ${hoveredAreaId === area.id ? 'pr-16' : ''}`}
              >
                {area.area_name}
              </button>

              {hoveredAreaId === area.id && (
                <div className='absolute top-1/2 right-2 flex -translate-y-1/2 items-center gap-1'>
                  <button
                    onClick={e => handleSettingsClick(area.id, e)}
                    className='rounded p-1 transition-colors hover:bg-gray-200'
                    title='Cài đặt'
                  >
                    <Settings className='h-3 w-3 text-gray-500' />
                  </button>
                  <button
                    onClick={e => handleDeleteArea(area.id, e)}
                    disabled={isDeleting}
                    className='rounded p-1 transition-colors hover:bg-red-100'
                    title='Xóa khu vực'
                  >
                    <Trash2 className='h-3 w-3 text-red-500' />
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <Button
        variant='ghost'
        size='sm'
        onClick={scrollRight}
        disabled={!canScrollRight}
        className='h-8 w-8 flex-shrink-0 p-1'
      >
        <ChevronRight className='h-4 w-4' />
      </Button>
      <Button
        variant='outline'
        size='sm'
        onClick={onCreateNew}
        className='flex flex-shrink-0 items-center gap-1 border-blue-200 text-blue-600 hover:bg-blue-50'
      >
        <Plus className='h-4 w-4' />
        Tạo khu vực mới
      </Button>
    </div>
  )
}
