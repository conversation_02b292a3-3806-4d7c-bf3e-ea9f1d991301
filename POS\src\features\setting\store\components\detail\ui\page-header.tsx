import { X } from 'lucide-react'

import { But<PERSON> } from '@/components/ui'

interface PageHeaderProps {
  title: string
  onBack: () => void
  children?: React.ReactNode
}

export function PageHeader({ title, onBack, children }: PageHeaderProps) {
  return (
    <div className='mb-8'>
      <div className='mb-4 flex items-center justify-between'>
        <Button variant='ghost' size='sm' onClick={onBack} className='flex items-center'>
          <X className='h-4 w-4' />
        </Button>
        {children && (
          <div className='flex items-center gap-3'>
            {children}
          </div>
        )}
      </div>

      <div className='text-center'>
        <h1 className='mb-2 text-3xl font-bold'>{title}</h1>
      </div>
    </div>
  )
}
