import { useMemo } from 'react'

import type { PrinterPosition } from '@/lib/printer-position-api'

import { PrinterPositionInStore } from '../data'
import { usePrinterPositionsInStoreData } from './use-printer-positions-in-store-data'

export interface UsePrinterPositionsInStoreForTableOptions {
  params?: {
    company_uid?: string
    brand_uid?: string
    store_uid?: string
  }
  enabled?: boolean
}

export const usePrinterPositionsInStoreForTable = (options: UsePrinterPositionsInStoreForTableOptions = {}) => {
  const {
    data: printerPositionsResponse,
    isLoading,
    error,
    refetch
  } = usePrinterPositionsInStoreData({
    params: options.params,
    enabled: options.enabled
  })

  const transformedData = useMemo(() => {
    if (!printerPositionsResponse?.data || !Array.isArray(printerPositionsResponse.data)) return []

    const transformed = printerPositionsResponse.data.map((item: PrinterPosition): PrinterPositionInStore => {
      const transformedItem: PrinterPositionInStore = {
        id: item.id,
        printerPositionId: item.printer_position_id,
        printerPositionName: item.printer_position_name,
        listItemTypeId: item.list_item_type_id || '',
        listItemId: item.list_item_id || '',
        storeUid: item.store_uid || '', // Handle null as empty string
        areaIds: item.area_ids || '',
        sources: item.sources || '',
        applyWithStore: item.apply_with_store || 0,
        sort: item.sort || 0,
        brandUid: item.brand_uid || '',
        companyUid: item.company_uid || '',
        revision: item.revision || 0,
        isActive: !item.deleted,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
        createdBy: item.created_by || '',
        updatedBy: item.updated_by || '',
        deleted: item.deleted || false,
        deletedBy: item.deleted_by || null,
        deletedAt: item.deleted_at ? new Date(item.deleted_at) : null,
        originalData: item
      }

      return transformedItem
    })

    return transformed
  }, [printerPositionsResponse?.data])

  return {
    data: transformedData,
    total: printerPositionsResponse?.total || 0,
    hasNextPage: printerPositionsResponse?.hasNextPage || false,
    isLoading,
    error,
    refetch
  }
}
