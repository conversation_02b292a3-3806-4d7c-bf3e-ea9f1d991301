import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { itemTypesApi, type GetItemTypesParams, type ItemType } from '@/lib/item-types-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseItemTypesDataOptions extends Partial<GetItemTypesParams> {
  enabled?: boolean
}

export function useItemTypesData(params: UseItemTypesDataOptions = {}) {
  const { enabled = true, ...apiParams } = params
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const finalParams: GetItemTypesParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    skip_limit: true,
    active: 1,
    ...apiParams
  }

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, finalParams],
    queryFn: async () => {
      const response = await itemTypesApi.getItemTypes(finalParams)
      return response.data || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export const useItemTypeData = (itemTypeId: string, enabled: boolean = true) => {
  const itemTypesQuery = useItemTypesData()

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, 'detail', itemTypeId],
    queryFn: async (): Promise<ItemType | undefined> => {
      // First check cache
      const cachedItemType = itemTypesApi.getItemTypeById(itemTypeId)
      if (cachedItemType) {
        return cachedItemType
      }

      // Then check loaded data
      const loadedItemType = itemTypesQuery.data?.find(itemType => itemType.id === itemTypeId)
      if (loadedItemType) {
        return loadedItemType
      }

      // Finally, fetch from server if not found
      try {
        return await itemTypesApi.getItemTypeByIdFromServer(itemTypeId)
      } catch (error) {
        console.error('Failed to fetch item type by ID:', error)
        return undefined
      }
    },
    enabled: enabled && !!itemTypeId,
    staleTime: 15 * 60 * 1000 // 15 minutes
  })
}

export const useItemTypeByTypeId = (itemTypeId: string, enabled: boolean = true) => {
  const itemTypesQuery = useItemTypesData()

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, 'by-type-id', itemTypeId],
    queryFn: async (): Promise<ItemType | undefined> => {
      const cachedItemType = itemTypesApi.getItemTypeByTypeId(itemTypeId)
      if (cachedItemType) {
        return cachedItemType
      }

      return itemTypesQuery.data?.find(itemType => itemType.item_type_id === itemTypeId)
    },
    enabled: enabled && !!itemTypeId,
    staleTime: 15 * 60 * 1000 // 15 minutes
  })
}

export const useItemTypeById = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, 'server-detail', id],
    queryFn: () => itemTypesApi.getItemTypeByIdFromServer(id),
    enabled: enabled && !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

export const useItemTypesStats = () => {
  const itemTypesQuery = useItemTypesData()

  const stats = {
    total: itemTypesQuery.data?.length || 0,
    active: itemTypesQuery.data?.filter(itemType => itemType.active === 1).length || 0,
    inactive: itemTypesQuery.data?.filter(itemType => itemType.active === 0).length || 0
  }

  return {
    ...itemTypesQuery,
    stats
  }
}

export const useItemTypesOptions = () => {
  const itemTypesQuery = useItemTypesData()

  const options =
    itemTypesQuery.data?.map(itemType => ({
      label: itemType.item_type_name,
      value: itemType.id,
      itemTypeId: itemType.item_type_id,
      active: itemType.active === 1
    })) || []

  return {
    ...itemTypesQuery,
    options,
    activeOptions: options.filter(option => option.active)
  }
}

export const useUpdateItemTypeStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemType: ItemType) => itemTypesApi.updateItemTypeStatus(itemType),
    onSuccess: (_, variables) => {
      // Invalidate and refetch item types queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })

      // Also invalidate the single item category query
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES, 'single', variables.id]
      })

      // Invalidate all item categories list
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
    }
  })
}

export const useUpdateItemType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemType: ItemType) => itemTypesApi.updateItemTypeStatus(itemType),
    onSuccess: (_, variables) => {
      // Invalidate and refetch item types queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })

      // Also invalidate the single item category query
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES, 'single', variables.id]
      })
    }
  })
}

export const useDeleteItemType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemTypeId: string) => itemTypesApi.deleteItemType(itemTypeId),
    onSuccess: () => {
      // Invalidate and refetch item types queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export const useBulkCreateItemTypes = (store_uid?: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (
      itemTypes: Array<{
        item_type_name: string
        item_type_id: string
        sort: number
      }>
    ) => itemTypesApi.bulkCreateItemTypes(itemTypes, store_uid),
    onSuccess: () => {
      // Invalidate and refetch item types queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}
