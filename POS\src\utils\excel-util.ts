import * as XLSX from 'xlsx'
import type { WorkBook } from 'xlsx'

/**
 * Download Excel file from workbook
 * @param workbook - XLSX workbook object
 * @param filename - Name of the file to download (without extension)
 */
export const downloadExcel = (workbook: WorkBook, filename: string): void => {
  try {
    const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}.xlsx`
    link.click()

    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading Excel file:', error)
    throw new Error('Failed to download Excel file')
  }
}
