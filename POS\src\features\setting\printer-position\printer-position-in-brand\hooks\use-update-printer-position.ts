import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { printerPositionApi, type UpdatePrinterPositionRequest } from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const useUpdatePrinterPosition = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdatePrinterPositionRequest) => 
      printerPositionApi.updatePrinterPosition(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PRINTER_POSITIONS] })
      toast.success(response.message || 'Cập nhật vị trí máy in thành công')
    },
    onError: (error: Error) => {
      console.error('Error updating printer position:', error)
      toast.error(error.message || 'C<PERSON> lỗi xảy ra khi cập nhật vị trí máy in')
    }
  })
}
