import { useState } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import { Button, Checkbox, Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui'

interface ItemType {
  id: string
  item_type_id?: string
  name?: string
  item_type_name?: string
  active?: number
}

interface GroupsSectionProps {
  itemTypes: ItemType[]
  selectedItems: string[]
  searchTerm: string
  onItemToggle: (itemId: string) => void
}

export function GroupsSection({ itemTypes, selectedItems, searchTerm, onItemToggle }: GroupsSectionProps) {
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)

  const filteredItemTypes = (Array.isArray(itemTypes) ? itemTypes : []).filter((item: ItemType) => {
    const name = item.item_type_name || item.name || ''
    return name.toLowerCase() !== 'uncategory' && name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const selectedItemTypes = filteredItemTypes.filter(item => selectedItems.includes(item.item_type_id || item.id))
  const remainingItemTypes = filteredItemTypes.filter(item => !selectedItems.includes(item.item_type_id || item.id))

  return (
    <div className='space-y-4'>
      <Collapsible open={!selectedCollapsed} onOpenChange={open => setSelectedCollapsed(!open)}>
        <CollapsibleTrigger asChild>
          <Button variant='ghost' className='flex w-full items-center justify-between p-2 text-left'>
            <span>Đã chọn ({selectedItemTypes.length})</span>
            {selectedCollapsed && <ChevronRight className='h-4 w-4' />}
            {!selectedCollapsed && <ChevronDown className='h-4 w-4' />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className='max-h-40 space-y-2 overflow-y-auto'>
          {selectedItemTypes.map((item: ItemType) => (
            <div key={item.id} className={`flex items-center space-x-2 p-2 ${item.active === 0 ? 'opacity-50' : ''}`}>
              <Checkbox
                checked={true}
                onCheckedChange={() => onItemToggle(item.item_type_id || item.id)}
                disabled={item.active === 0}
              />
              <span className='text-sm'>{item.item_type_name || item.name || 'Không có tên'}</span>
            </div>
          ))}
          {selectedItemTypes.length === 0 && <div className='p-2 text-sm text-gray-500'>Chưa chọn nhóm nào</div>}
        </CollapsibleContent>
      </Collapsible>

      <Collapsible open={!remainingCollapsed} onOpenChange={open => setRemainingCollapsed(!open)}>
        <CollapsibleTrigger asChild>
          <Button variant='ghost' className='flex w-full items-center justify-between p-2 text-left'>
            <span>Còn lại ({remainingItemTypes.length})</span>
            {remainingCollapsed && <ChevronRight className='h-4 w-4' />}
            {!remainingCollapsed && <ChevronDown className='h-4 w-4' />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className='max-h-40 space-y-2 overflow-y-auto'>
          {remainingItemTypes.map((item: ItemType) => (
            <div key={item.id} className={`flex items-center space-x-2 p-2 ${item.active === 0 ? 'opacity-50' : ''}`}>
              <Checkbox
                checked={false}
                onCheckedChange={() => onItemToggle(item.item_type_id || item.id)}
                disabled={item.active === 0}
              />
              <span className='text-sm'>{item.item_type_name || item.name || 'Không có tên'}</span>
            </div>
          ))}
          {remainingItemTypes.length === 0 && <div className='p-2 text-sm text-gray-500'>Không có nhóm nào</div>}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
