import React, { useCallback } from 'react'

import QRCode from 'react-qr-code'

import { useSaveBillTemplate } from '../../hooks'
import { BillData, SaveBillTemplateRequest } from '../../types'
import { EditableField } from '../editable-field'

interface BillTemplate4Props {
  billData: BillData
  billTemplateData?: SaveBillTemplateRequest
}

export function BillTemplate4({ billData, billTemplateData }: BillTemplate4Props) {
  const saveBillTemplate = useSaveBillTemplate()

  // Get config from extra_bill_template_4
  const data = (billTemplateData?.extra_bill_template_4 || {}) as any

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const displaySubtotal = data.show_discount ? billData.totalWithVat - billData.itemDiscountAmount : billData.subtotal
  const displayTotalWithVat = data.show_discount
    ? billData.totalWithVat - billData.itemDiscountAmount
    : billData.totalWithVat
  const displayGrandTotal = data.show_discount ? billData.grandTotal - billData.itemDiscountAmount : billData.grandTotal

  // Group items by type
  const groupedItems = billData.items.reduce(
    (acc, item) => {
      const type = item.itemType || 'No type'
      if (!acc[type]) {
        acc[type] = []
      }
      acc[type].push(item)
      return acc
    },
    {} as Record<string, typeof billData.items>
  )

  const handleFieldChange = useCallback(
    (fieldName: string, value: string) => {
      if (billTemplateData) {
        const updatedPayload: SaveBillTemplateRequest = {
          ...billTemplateData,
          revision: billTemplateData.revision + 1,
          updated_at: new Date().toISOString(),
          extra_bill_template_4: {
            ...(billTemplateData.extra_bill_template_4 || {}),
            [fieldName]: value
          } as any
        }

        saveBillTemplate.mutate(updatedPayload)
      }
    },
    [billTemplateData, saveBillTemplate]
  )

  return (
    <div
      className={`relative border border-gray-300 bg-white font-sans text-sm shadow-md md:min-w-[415px] md:text-base ${saveBillTemplate.isPending ? 'opacity-75' : ''}`}
      style={{
        fontSize: `${(16 * (data.font_size || 100)) / 100}px`
      }}
    >
      {saveBillTemplate.isPending && (
        <div className='bg-opacity-50 absolute inset-0 z-10 flex items-center justify-center bg-white'>
          <div className='text-sm text-black'>Đang lưu...</div>
        </div>
      )}

      <div className='px-4'>
        {/* Bill title */}
        <h3 className='mb-0 pt-3 text-center font-bold'>
          <EditableField
            value={billTemplateData?.extra_bill_template_4?.custom_text_1 || 'INVOICE'}
            onSave={value => handleFieldChange('custom_text_1', value)}
            className='text-2xl text-black'
          />
        </h3>

        {/* Bill number */}
        <div className='mb-2 text-center font-bold text-black' style={{ fontSize: '1.3rem' }}>
          {data.group_source_tranno
            ? `${billData.orderSource} ${billData.billNumber}`
            : `${billData.orderSource} ${billData.billNumber}`}
        </div>

        {/* Transaction details */}
        <div className='flex justify-between'>
          <div>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_code || 'Tran ID'}
                onSave={value => handleFieldChange('title_code', value)}
                className='font-bold'
              />
              : {billData.billId}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_table || 'Table'}
                onSave={value => handleFieldChange('title_table', value)}
                className='font-bold'
              />
              : {billData.table}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_time_in || 'In'}
                onSave={value => handleFieldChange('title_time_in', value)}
                className='font-bold'
              />
              : {billData.checkInTime}
            </p>
          </div>
          <div>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_cashier || 'Cashier'}
                onSave={value => handleFieldChange('title_cashier', value)}
                className='font-bold'
              />
              : {billData.cashier}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_date || 'Date'}
                onSave={value => handleFieldChange('title_date', value)}
                className='font-bold'
              />
              : {billData.date}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_4?.title_time_out || 'Out'}
                onSave={value => handleFieldChange('title_time_out', value)}
                className='font-bold'
              />
              : {billData.checkOutTime}
            </p>
          </div>
        </div>

        {/* Items table */}
        <div className='border-b-2 border-dashed border-gray-300 pb-3'>
          <table className='w-full border-collapse border border-gray-700'>
            <thead>
              <tr>
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_4?.title_stt || 'Order'}
                    onSave={value => handleFieldChange('title_stt', value)}
                    className='font-bold text-black'
                  />
                </th>
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_4?.title_name_item || 'Item name'}
                    onSave={value => handleFieldChange('title_name_item', value)}
                    className='font-bold text-black'
                  />
                </th>
                <th className='border-collapse border border-gray-700 px-1 text-center'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_4?.title_quantity || 'Quantity'}
                    onSave={value => handleFieldChange('title_quantity', value)}
                    className='font-bold text-black'
                  />
                </th>
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_4?.title_price || 'Price'}
                    onSave={value => handleFieldChange('title_price', value)}
                    className='font-bold text-black'
                  />
                </th>
                {data.show_vat_rate && (
                  <th className='border-collapse border border-gray-700 px-1'>
                    <EditableField
                      value={billTemplateData?.extra_bill_template_4?.title_vat_rate || 'VAT'}
                      onSave={value => handleFieldChange('title_vat_rate', value)}
                      className='font-bold text-black'
                    />
                  </th>
                )}
                {data.show_vat_amount && (
                  <th className='border-collapse border border-gray-700 px-1'>
                    <EditableField
                      value={billTemplateData?.extra_bill_template_4?.title_vat_amount || 'Tiền VAT'}
                      onSave={value => handleFieldChange('title_vat_amount', value)}
                      className='font-bold text-black'
                    />
                  </th>
                )}
                {data.show_discount && (
                  <th className='border-collapse border border-gray-700 px-1'>
                    <EditableField
                      value={billTemplateData?.extra_bill_template_4?.title_discount || 'DC'}
                      onSave={value => handleFieldChange('title_discount', value)}
                      className='font-bold text-black'
                    />
                  </th>
                )}
                <th className='border-collapse border border-gray-700 px-1 text-right'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_4?.title_amount || 'Amount'}
                    onSave={value => handleFieldChange('title_amount', value)}
                    className='font-bold text-black'
                  />
                </th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(groupedItems).map(([itemType, items]) => (
                <React.Fragment key={itemType}>
                  {/* Item type header */}
                  {data.show_item_class && (
                    <tr>
                      <td
                        valign='top'
                        colSpan={2}
                        className='border-collapse border border-gray-700 px-1 font-semibold text-black'
                      >
                        {itemType}
                      </td>
                      <td
                        colSpan={
                          3 +
                          (data.show_vat_rate ? 1 : 0) +
                          (data.show_vat_amount ? 1 : 0) +
                          (data.show_discount ? 1 : 0)
                        }
                        className='border-collapse border border-gray-700 text-right text-sm font-bold text-black'
                      >
                        {formatCurrency(items.reduce((sum, item) => sum + item.subtotal, 0))}
                      </td>
                    </tr>
                  )}

                  {/* Items in this type */}
                  {items.map(item => (
                    <tr key={item.id}>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        {items.indexOf(item) + 1}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        <div>{item.name}</div>
                        {data.show_topping && item.toppings && item.toppings.length > 0 && (
                          <small className='mb-0 ml-1'>+ {item.toppings.map(t => t.name).join(', ')}</small>
                        )}
                        {data.show_item_note && item.notes ? (
                          <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                            <span style={{ textDecoration: 'underline' }}>Ghi chú</span>: {item.notes}
                          </div>
                        ) : null}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-center text-black'>
                        <div>
                          {item.quantity}
                          {data.show_unit && <span>{item.unit}</span>}
                        </div>
                        {data.show_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>
                              {topping.quantity}
                              {data.show_unit && <small>{topping.unit}</small>}
                            </div>
                          ))}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        <div>{formatCurrency(item.unitPrice)}</div>
                        {data.show_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>{formatCurrency(topping.unitPrice)}</div>
                          ))}
                      </td>
                      {data.show_vat_rate && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{item.vatPercentage || 10}%</div>
                          {data.show_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{topping.vatPercentage || 10}%</div>
                            ))}
                        </td>
                      )}
                      {data.show_vat_amount && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{formatCurrency(item.vatAmount || item.subtotal * 0.1)} ₫</div>
                          {data.show_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{formatCurrency(topping.vatAmount || topping.subtotal * 0.1)} ₫</div>
                            ))}
                        </td>
                      )}
                      {data.show_discount && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{item.discountPercentage || 0}%</div>
                          {data.show_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{topping.discountPercentage || 0}%</div>
                            ))}
                        </td>
                      )}
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-right text-black'>
                        <div>{formatCurrency(item.subtotal)}</div>
                        {data.show_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>{formatCurrency(topping.subtotal)}</div>
                          ))}
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary section */}
        <div className='border-b-2 border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Amount:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displaySubtotal)} ₫</p>
          </div>

          {billData.discountAmount > 0 && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Discount payment:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.discountAmount)} ₫</p>
            </div>
          )}

          {!data.show_discount && billData.itemDiscountAmount > 0 && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Discount item:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.itemDiscountAmount)} ₫</p>
            </div>
          )}

          {data.show_vat_amount && billData.vatAmount > 0 && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Tax (VAT):</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.vatAmount)} ₫</p>
            </div>
          )}

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Amount after VAT:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displayTotalWithVat)} ₫</p>
          </div>

          {billData.shippingFee > 0 && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Ship fee amount:</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.shippingFee)} ₫</p>
            </div>
          )}

          {billData.voucherDiscount > 0 && (
            <div className='flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Voucher amount:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.voucherDiscount)} ₫</p>
            </div>
          )}
        </div>

        {/* Total section */}
        <div className='border-b-2 border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Total amount:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displayGrandTotal)} ₫</p>
          </div>

          {/* Amount Received and Change Section */}
          {data.show_cash_change && (
            <>
              <div className='mb-2 flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Receive:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(displayGrandTotal)} ₫</p>
              </div>
              <div className='flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Change:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(billData.changeAmount)} ₫</p>
              </div>
            </>
          )}
        </div>

        {/* Payment Section */}
        <div className='py-3'>
          {data.show_vat_reverse && (
            <div className='mb-2 flex justify-between'>
              <p className='mb-0 text-sm text-black'>+ Show total amount including VAT</p>
              <p className='mb-0 font-bold text-black'>{formatCurrency(billData.vatAmount)} ₫ VAT</p>
            </div>
          )}
          {data.is_show_payment_fee && billData.cardFeeAmount > 0 && (
            <div className='mb-2 flex justify-between'>
              <p className='mb-0 text-sm text-black'>+ Payment fee</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.cardFeeAmount)} ₫</p>
            </div>
          )}
          <div className='flex justify-between'>
            <p className='mb-0 text-center text-sm text-black'>+ Thanh toán {billData.paymentMethod}</p>
            <p className='mb-0 text-sm text-black'>{formatCurrency(displayGrandTotal)} ₫</p>
          </div>
        </div>

        {/* Restaurant Info */}
        <div className='pb-3'>
          <p className='mb-0 text-center text-sm font-bold text-black'>{billData.restaurantName}</p>
          <p className='mb-0 text-center text-sm text-black'>Address: {billData.restaurantAddress}</p>
          {data.hotline_number && <p className='mb-0 text-center text-sm text-black'>Hotline: {data.hotline_number}</p>}
          <div className='mx-auto mt-3 mb-1 h-px w-1/4 border border-black bg-black'></div>
          <div className='mx-4 mt-2'>
            <p className='mb-0 text-sm text-black'>
              Customer: {billData.customerName} -{' '}
              <span>
                {data.hide_customer_phone
                  ? billData.customerPhone.substring(0, 2) + '*'.repeat(billData.customerPhone.length - 2)
                  : billData.customerPhone}
              </span>
            </p>
            {data.show_points && (
              <p className='mb-0 text-sm text-black'>
                Số điểm đã tích: {new Intl.NumberFormat('vi-VN').format(billData.accumulatedPoints)}
              </p>
            )}
          </div>

          {/* Voucher Name Section */}
          {data.show_voucher_gift && billData.voucherName && (
            <div className='mt-2 text-center'>
              <p className='mb-0 text-sm font-bold text-black'>{billData.voucherName}</p>
            </div>
          )}
          {billData.customText1 && (
            <p className='mt-2 mb-0 text-center text-sm text-gray-400'>{billData.customText1}</p>
          )}

          {/* VAT Information Section */}
          {data.show_vat_info && (
            <div className='mx-4 mt-2'>
              <p className='mb-0 text-sm text-black'>Thông tin khách hàng</p>
              <p className='mb-0 text-sm text-black'>Tên khách hàng: {billData.customerName}</p>
              <p className='mb-0 text-sm text-black'>Mã số thuế: {billData.customerTaxId}</p>
              <p className='mb-0 text-sm text-black'>Địa chỉ: {billData.customerAddress}</p>
            </div>
          )}

          {/* Custom Scan QR Section */}
          {data.enable_qr_code && data.qr_content && data.qr_content.trim() && (
            <div className='mx-4 mt-3 text-center'>
              {data.qr_title && <p className='text-md mb-2 text-black'>{data.qr_title}</p>}
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={data.qr_content.trim()}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          )}

          {/* VAT QR Code Section */}
          {data.show_vat_qr_code && (
            <div className='mx-4 mt-3 text-center'>
              <p className='mb-2 text-sm text-black'>Quét mã QR dưới đây để cung cấp thông tin hoá đơn điện tử</p>
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={'https://fabi.ipos.vn/'}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='border-t border-gray-300 py-3'>
          <p className='mb-0 text-center text-sm font-bold text-black'>
            {billTemplateData?.extra_bill_template_4?.custom_text_2 || 'Thank you for your visit!'}
          </p>
          <p className='mb-0 text-center text-sm text-black'>Powered by iPOS.vn</p>
        </div>
      </div>
    </div>
  )
}
