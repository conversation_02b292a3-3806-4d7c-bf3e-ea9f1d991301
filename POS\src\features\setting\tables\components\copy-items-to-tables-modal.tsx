import { useState, useMemo } from 'react'

import { Copy, Search } from 'lucide-react'

import { useTablesData, useBulkUpdateTables } from '@/hooks/api/use-tables'

import {
  Button,
  Input,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Checkbox
} from '@/components/ui'

interface SelectedItem {
  item_id: string
  item_name: string
  quantity: number
}

interface CopyItemsToTablesModalProps {
  selectedItems: SelectedItem[]
  storeUid: string
}

export function CopyItemsToTablesModal({ selectedItems, storeUid }: CopyItemsToTablesModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [tableSearchTerm, setTableSearchTerm] = useState('')
  const [selectedTables, setSelectedTables] = useState<Set<string>>(new Set())
  const [copiedTables, setCopiedTables] = useState<Set<string>>(new Set())

  // Get tables data for copy functionality
  const { data: tables = [], isLoading: isLoadingTables } = useTablesData({
    storeUid: storeUid,
    enabled: !!storeUid && isOpen
  })

  // Debug log
  console.log('CopyItemsToTablesModal - storeUid:', storeUid, 'isOpen:', isOpen, 'tables:', tables)

  // Bulk update tables hook
  const { mutateAsync: bulkUpdateTables, isPending: isBulkUpdating } = useBulkUpdateTables()

  // Filter tables based on search term
  const filteredTables = useMemo(() => {
    if (!tableSearchTerm) return tables
    return tables.filter(table =>
      table.table_name?.toLowerCase().includes(tableSearchTerm.toLowerCase()) ||
      table.id?.toLowerCase().includes(tableSearchTerm.toLowerCase())
    )
  }, [tables, tableSearchTerm])

  const handleTableToggle = (tableId: string) => {
    setSelectedTables(prev => {
      const newSet = new Set(prev)
      if (newSet.has(tableId)) {
        newSet.delete(tableId)
      } else {
        newSet.add(tableId)
      }
      return newSet
    })
  }

  const handleCopyToTables = async () => {
    if (selectedTables.size === 0) {
      return
    }

    try {
      const tablesToUpdate = tables.filter(table => selectedTables.has(table.id))
      const updatedTables = tablesToUpdate.map(table => ({
        ...table,
        extra_data: {
          ...table.extra_data,
          order_list: selectedItems.length > 0 ? selectedItems.map(item => ({
            item_id: item.item_id,
            quantity: item.quantity
          })) : []
        }
      }))

      await bulkUpdateTables({
        storeUid: storeUid,
        tables: updatedTables
      })

      setCopiedTables(prev => {
        const newSet = new Set(prev)
        selectedTables.forEach(tableId => newSet.add(tableId))
        return newSet
      })

      setIsOpen(false)
      setSelectedTables(new Set())
      setTableSearchTerm('')
    } catch (error) {
      console.error('Error copying items to tables:', error)
    }
  }

  const handleClose = () => {
    setIsOpen(false)
    setSelectedTables(new Set())
    setTableSearchTerm('')
  }


  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          size='sm'
        >
          <Copy className='h-4 w-4 mr-2' />
          Sao chép tạo món mới
        </Button>
      </DialogTrigger>
      <DialogContent className='max-w-2xl max-h-[80vh] overflow-hidden flex flex-col'>
        <DialogHeader>
          <DialogTitle>Sao chép món đặt trước</DialogTitle>
        </DialogHeader>

        <div className='text-sm text-gray-600 mb-4'>
          Chọn bàn cùng cửa hàng để đồng bộ món đặt trước
        </div>

        {/* Warning when no items selected */}
        {selectedItems.length === 0 && (
          <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4'>
            <div className='text-sm text-yellow-700'>
              ⚠️ Chưa có món nào được chọn. Việc sao chép sẽ xóa tất cả món đặt trước của các bàn được chọn.
            </div>
          </div>
        )}

        {/* Search */}
        <div className='relative mb-4'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
          <Input
            placeholder='Tìm kiếm'
            value={tableSearchTerm}
            onChange={e => setTableSearchTerm(e.target.value)}
            className='pl-10'
          />
        </div>

        {/* Select all checkbox */}
        <div className='flex items-center justify-between mb-4'>
          <div className='flex items-center space-x-2'>
            <Checkbox
              checked={filteredTables.length > 0 && filteredTables.every(table => selectedTables.has(table.id))}
              onCheckedChange={(checked) => {
                if (checked) {
                  setSelectedTables(prev => {
                    const newSet = new Set(prev)
                    filteredTables.forEach(table => newSet.add(table.id))
                    return newSet
                  })
                } else {
                  setSelectedTables(prev => {
                    const newSet = new Set(prev)
                    filteredTables.forEach(table => newSet.delete(table.id))
                    return newSet
                  })
                }
              }}
            />
            <span className='text-sm font-medium text-blue-600'>Chọn tất cả</span>
          </div>
          <div className='flex items-center gap-4'>
            {copiedTables.size > 0 && (
              <span className='text-sm text-green-600 font-medium'>
                ✓ Đã sao chép: {copiedTables.size} bàn
              </span>
            )}
            {selectedTables.size > 0 && (
              <span className='text-sm text-blue-600 font-medium'>
                Đang chọn: {selectedTables.size} bàn
              </span>
            )}
          </div>
        </div>

        {/* Tables list */}
        <div className='flex-1 overflow-y-auto space-y-2 max-h-96 border rounded-lg p-3'>
          {isLoadingTables ? (
            <div className='text-center py-8 text-gray-500'>
              Đang tải danh sách bàn...
            </div>
          ) : (
            <>
              {filteredTables.map(table => {
                const isSelected = selectedTables.has(table.id)
                const isCopied = copiedTables.has(table.id)
                return (
                  <div key={table.id} className={`flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 ${isCopied ? 'bg-green-50 border-green-200' : ''}`}>
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={() => handleTableToggle(table.id)}
                    />
                    <div className='flex-1'>
                      <div className='font-medium flex items-center gap-2'>
                        {table.table_name}
                        {isCopied && (
                          <span className='text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full'>
                            ✓ Đã sao chép
                          </span>
                        )}
                      </div>
                      <div className='text-sm text-gray-500'>{table.id}</div>
                    </div>
                  </div>
                )
              })}
              {filteredTables.length === 0 && !isLoadingTables && (
                <div className='text-center py-8 text-gray-500'>
                  {tableSearchTerm ? 'Không tìm thấy bàn nào' : 'Không có bàn nào'}
                </div>
              )}
            </>
          )}
        </div>

        {/* Dialog actions */}
        <div className='flex justify-between pt-4 border-t'>
          <Button
            type='button'
            variant='outline'
            onClick={handleClose}
          >
            Hủy
          </Button>
          <Button
            type='button'
            onClick={handleCopyToTables}
            disabled={selectedTables.size === 0 || isBulkUpdating}
          >
            {isBulkUpdating ? 'Đang xử lý...' : selectedItems.length === 0 ? 'Xóa món đặt trước' : 'Xác nhận'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}