import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { tablesApi, type UpdateTablePositionsRequest } from '@/lib/tables-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import type { TableLayoutItem } from '../data/table-layout-types'

export const useSaveTablePositions = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: { tables: TableLayoutItem[]; storeUid: string }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !data.storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      const sortedTables = [...data.tables].sort((a, b) => {
        const yDiff = a.position.y - b.position.y
        if (Math.abs(yDiff) > 50) {
          return yDiff
        }
        return a.position.x - b.position.x
      })
      const positions: UpdateTablePositionsRequest = sortedTables.map((table, index) => ({
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: data.storeUid,
        id: table.id,
        sort: index
      }))

      return await tablesApi.updateTablePositions(positions)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      toast.success('Lưu vị trí bàn thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi lưu vị trí bàn'
      toast.error(errorMessage)
    }
  })

  return { saveTablePositions: mutate, isSaving: isPending }
}
