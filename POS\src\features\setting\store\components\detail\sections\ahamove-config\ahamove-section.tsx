import type { UseFormReturn } from 'react-hook-form'

import { toast } from 'sonner'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Input, Checkbox, Label } from '@/components/ui'

import { AHAMOVE_PAYMENT_OPTIONS, type StoreFormValues } from '../../../../data'

interface AhamoveSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function AhamoveSection({ form, isLoading = false }: AhamoveSectionProps) {
  return (
    <div className='space-y-6'>
      <div>
        <div className='mb-2 flex items-center gap-2'>
          <h2 className='text-xl font-semibold'>Cấu hình <PERSON>amove</h2>
        </div>
        <p className='mb-6 text-sm text-gray-600'>Kết nối Ahamove và cài đặt cấu hình cho cửa hàng</p>
      </div>

      <div className='space-y-6'>
        {/* Kích hoạt <PERSON>amove */}
        <FormField
          control={form.control}
          name='is_ahamove_active'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center'>
                <FormLabel className='w-[220px]'>Kích hoạt Ahamove</FormLabel>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={checked => {
                      field.onChange(checked)
                      if (checked) {
                        toast.success('Kết nối Ahamove tại Ứng dụng để kích hoạt cấu hình')
                      }
                    }}
                    disabled={isLoading}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Số điện thoại liên hệ */}
        <FormField
          control={form.control}
          name='phone_manager'
          render={({ field }) => {
            const isAhamoveActive = form.watch('is_ahamove_active')
            return (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-[270px]'>
                    Số điện thoại liên hệ <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Nhập số điện thoại cho tài xế liên hệ'
                      disabled={isLoading || !isAhamoveActive}
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        {/* Phương thức thanh toán */}
        <FormField
          control={form.control}
          name='ahamove_payment_method'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <FormLabel className='w-[200px]'>Phương thức thanh toán</FormLabel>
                <FormControl>
                  <div className='flex gap-6'>
                    {AHAMOVE_PAYMENT_OPTIONS.map(option => (
                      <div key={option.value} className='flex items-center space-x-2'>
                        <Checkbox
                          id={option.value}
                          checked={typeof field.value === 'string' && field.value.includes(option.value)}
                          onCheckedChange={checked => {
                            const currentValues =
                              typeof field.value === 'string' ? field.value.split(',').filter(Boolean) : []
                            const optionValue = option.value as 'AHAMOVE' | 'AHAMOVE_PREPAID'
                            if (checked) {
                              const newValues = [...currentValues, optionValue]
                              field.onChange(newValues.join(','))
                            } else {
                              const newValues = currentValues.filter(val => val !== optionValue)
                              field.onChange(newValues.join(','))
                            }
                          }}
                          disabled={isLoading}
                        />
                        <Label htmlFor={option.value}>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ahamove voucher */}
        <FormField
          control={form.control}
          name='ahamove_voucher_default'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <FormLabel className='w-[270px]'>Ahamove voucher</FormLabel>
                <FormControl>
                  <Input placeholder='Nhập Ahamove voucher (nếu có)' disabled={isLoading} {...field} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
