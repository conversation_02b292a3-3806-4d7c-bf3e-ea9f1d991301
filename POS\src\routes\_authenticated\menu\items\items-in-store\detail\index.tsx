import { useEffect, useState } from 'react'

import { createFileRoute, useSearch } from '@tanstack/react-router'

import { ItemDetailForm } from '@/features/menu/items/items-in-store/detail'
import { ItemInStore, useItemInStoreDetail } from '@/features/menu/items/items-in-store/hooks'

export const Route = createFileRoute('/_authenticated/menu/items/items-in-store/detail/')({
  component: ItemCreatePage
})

function ItemCreatePage() {
  const search = useSearch({ from: '/_authenticated/menu/items/items-in-store/detail' })
  const [currentRow, setCurrentRow] = useState<ItemInStore | null>(null)

  const id = (search as any)?.id
  const { data: itemDetail, isLoading } = useItemInStoreDetail(id, !!id)

  if (!id) {
    return <ItemDetailForm />
  }

  useEffect(() => {
    if (itemDetail) {
      setCurrentRow(itemDetail.data as ItemInStore)
    }
  }, [itemDetail])

  if (isLoading || !currentRow) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center'>
          <div className='text-lg'>Đang tải...</div>
        </div>
      </div>
    )
  }

  return <ItemDetailForm currentRow={{ ...currentRow, item_id: '', item_id_barcode: '' }} isCopyMode={!!currentRow} />
}
