import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { DiscountFormData } from '@/types/api/discount-types'
import type { GetDiscountsParams, DiscountApiData } from '@/types/discounts'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import {
  getRegularDiscounts,
  deleteRegularDiscount,
  updateRegularDiscount,
  getRegularDiscountById,
  createRegularDiscountEnhanced,
  updateRegularDiscountEnhanced,
  transformFormDataToRegularDiscountRequest,
  transformFormDataToRegularDiscountUpdateRequest,
  getRegularDiscountPromotions
} from '@/lib/regular-discounts-api'

import { QUERY_KEYS } from '@/constants/query-keys'

/**
 * Hook for toggling regular discount active status in detail/edit pages
 */
export function useToggleRegularDiscountStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (discountData: DiscountApiData) => {
      await updateRegularDiscount(discountData)
    },
    onSuccess: () => {
      // Invalidate all discount queries to refresh both list and detail views
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

export function useRegularDiscounts(params: GetDiscountsParams = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'regular', params],
    queryFn: () => getRegularDiscounts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

export function useDeleteRegularDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteRegularDiscount,
    onSuccess: () => {
      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

export function useUpdateRegularDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateRegularDiscount,
    onSuccess: () => {
      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

interface UseCreateRegularDiscountOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useCreateRegularDiscount(storeUid: string, options?: UseCreateRegularDiscountOptions) {
  const queryClient = useQueryClient()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useMutation({
    mutationFn: async (formData: DiscountFormData) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Thiếu thông tin company hoặc brand')
      }

      const request = transformFormDataToRegularDiscountRequest(formData, storeUid, company.id, selectedBrand.id)

      const response = await createRegularDiscountEnhanced(request)

      if (!response.success) {
        throw new Error(response.message || 'Có lỗi xảy ra')
      }

      return response.data
    },
    onSuccess: () => {
      toast.success('Tạo regular discount thành công')

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNTS]
      })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi tạo regular discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

export function useEditRegularDiscount(discountId?: string, options?: UseCreateRegularDiscountOptions) {
  const queryClient = useQueryClient()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useMutation({
    mutationFn: async (formData: DiscountFormData) => {
      if (!company?.id || !selectedBrand?.id || !discountId) {
        throw new Error('Thiếu thông tin cần thiết để cập nhật')
      }

      // Get original discount data
      const originalDiscount = await getRegularDiscountById({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId
      })

      const request = transformFormDataToRegularDiscountUpdateRequest(formData, originalDiscount)

      const response = await updateRegularDiscountEnhanced(request)

      if (!response.success) {
        throw new Error(response.message || 'Có lỗi xảy ra')
      }

      return response.data
    },
    onSuccess: () => {
      toast.success('Cập nhật regular discount thành công')

      // Invalidate all discount queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNTS]
      })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật regular discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

export function useRegularDiscountDetail(discountId: string) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, discountId],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !discountId) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      return getRegularDiscountById({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId
      })
    },
    enabled: !!discountId && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

interface UseRegularDiscountPromotionsOptions {
  enabled?: boolean
}

export function useRegularDiscountPromotions(storeUid: string, options: UseRegularDiscountPromotionsOptions = {}) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const { enabled = true } = options

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'promotions', storeUid, company?.id, selectedBrand?.id],
    queryFn: () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết để lấy danh sách khuyến mãi')
      }

      return getRegularDiscountPromotions({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        storeUid: storeUid
      })
    },
    enabled: enabled && !!storeUid && !!company?.id && !!selectedBrand?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}
