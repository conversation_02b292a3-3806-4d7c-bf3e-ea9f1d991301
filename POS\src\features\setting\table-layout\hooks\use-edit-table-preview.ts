import { useState } from 'react'

import ExcelJS from 'exceljs'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { sourcesApi } from '@/lib/sources-api'
import { tableLayoutApi } from '@/lib/table-layout-api'

interface TablePreviewData {
  id: string
  tenBan: string
  nguon: string
  monDatTruoc: string
  moTa: string
}

interface UseEditTablePreviewProps {
  onSuccess?: () => void
}

export const useEditTablePreview = ({ onSuccess }: UseEditTablePreviewProps = {}) => {
  const { auth } = useAuthStore()
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false)
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false)
  const [previewData, setPreviewData] = useState<TablePreviewData[]>([])
  const [currentStoreId, setCurrentStoreId] = useState<string>('')

  const handleUploadFile = (storeId: string) => {
    setCurrentStoreId(storeId)
    if (!storeId) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.csv,.xlsx,.xls'
    input.onchange = async event => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          toast.info('Đang đọc file...')
          const data = await parseEditedTableFile(file)
          setPreviewData(data)
          setIsPreviewModalOpen(true)
        } catch (error) {
          console.error('Error parsing file:', error)
          toast.error('Không thể đọc file. Vui lòng kiểm tra định dạng file.')
        }
      }
    }
    input.click()
  }

  const handleRemoveRow = (index: number) => {
    setPreviewData(prev => prev.filter((_, i) => i !== index))
  }

  const handleConfirmImport = async () => {
    try {
      toast.info('Đang cập nhật thông tin bàn...')

      // Get sources mapping for source_id conversion
      const sources = await sourcesApi.getSources({
        skip_limit: true,
        company_uid: auth.company?.id || '',
        brand_uid: auth.brands?.[0]?.id || '',
        store_uid: currentStoreId
      })

      const sourcesMap = new Map()
      sources.forEach((source: any) => {
        sourcesMap.set(source.sourceName, source.sourceId)
      })

      // Transform preview data to API format
      const tablesForUpdate = previewData.map(row => ({
        id: row.id,
        table_name: row.tenBan,
        store_uid: currentStoreId,
        company_uid: auth.company?.id || '',
        brand_uid: auth.brands?.[0]?.id || '',
        source_id: sourcesMap.get(row.nguon) || '',
        extra_data: {
          order_list: row.monDatTruoc ? parseOrderList(row.monDatTruoc) : []
        },
        description: row.moTa
      }))

      // Call API to update tables
      await tableLayoutApi.bulkUpdateTables(tablesForUpdate)

      // Close preview modal and show success modal
      setIsPreviewModalOpen(false)
      setIsSuccessModalOpen(true)

      onSuccess?.()
    } catch (error) {
      console.error('Error updating tables:', error)
      toast.error('Không thể cập nhật thông tin bàn. Vui lòng thử lại.')
    }
  }

  const handleClosePreview = () => {
    setIsPreviewModalOpen(false)
    setPreviewData([])
  }

  const handleCloseSuccess = () => {
    setIsSuccessModalOpen(false)
    setPreviewData([])
    setCurrentStoreId('')
  }

  return {
    isPreviewModalOpen,
    isSuccessModalOpen,
    previewData,
    handleUploadFile,
    handleRemoveRow,
    handleConfirmImport,
    handleClosePreview,
    handleCloseSuccess
  }
}

// Helper function to parse order list from string format "ITEM001x2, ITEM002x1"
const parseOrderList = (orderListStr: string): Array<{ item_id: string; quantity: number }> => {
  if (!orderListStr.trim()) return []

  return orderListStr
    .split(',')
    .map(item => {
      const trimmed = item.trim()
      const parts = trimmed.split('x')
      if (parts.length === 2) {
        return {
          item_id: parts[0].trim(),
          quantity: parseInt(parts[1].trim()) || 1
        }
      }
      return {
        item_id: trimmed,
        quantity: 1
      }
    })
    .filter(item => item.item_id)
}

const parseEditedTableFile = async (file: File): Promise<TablePreviewData[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async e => {
      try {
        const data = e.target?.result
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(data as ArrayBuffer)

        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          throw new Error('Không thể đọc worksheet từ file Excel')
        }

        const tableData: TablePreviewData[] = []

        // Skip header row (row 1) and start from row 2
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            const rowData: TablePreviewData = {
              id: row.getCell(1).value?.toString() || '',
              tenBan: row.getCell(2).value?.toString() || '',
              nguon: row.getCell(3).value?.toString() || '',
              monDatTruoc: row.getCell(4).value?.toString() || '',
              moTa: row.getCell(5).value?.toString() || ''
            }

            // Only add row if it has ID (required for editing)
            if (rowData.id.trim()) {
              tableData.push(rowData)
            }
          }
        })

        resolve(tableData)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}
