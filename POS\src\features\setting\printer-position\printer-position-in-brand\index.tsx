import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  PrinterPositionInBrandDialogs,
  PrinterPositionInBrandTableSkeleton,
  PrinterPositionInBrandTableWrapper
} from './components'
import PrinterPositionInBrandProvider, { usePrinterPositionInBrand } from './context'
import { usePrinterPositionsForTable } from './hooks/use-printer-positions-for-table'

function PrinterPositionInBrandContent() {
  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()
  const { open } = usePrinterPositionInBrand()

  const {
    data: printerPositionsData,
    isLoading,
    error,
    refetch
  } = usePrinterPositionsForTable({
    params: {
      company_uid: companyUid || '',
      brand_uid: selectedBrand?.id || ''
    },
    enabled: true // Force enable for debugging
  })

  const showDataTable = !open || (open !== 'create' && open !== 'update')

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu vị trí máy in</p>
          <button onClick={() => refetch()} className='text-primary text-sm hover:underline'>
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {showDataTable && (
        <>
          <Header>
            <div className='ml-auto flex items-center space-x-4'>
              <Search />
              <ThemeSwitch />
              <ProfileDropdown />
            </div>
          </Header>

          <Main>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
              {isLoading && <PrinterPositionInBrandTableSkeleton />}
              {!isLoading && <PrinterPositionInBrandTableWrapper columns={columns} data={printerPositionsData} />}
            </div>
          </Main>
        </>
      )}

      <PrinterPositionInBrandDialogs />
    </>
  )
}

export default function PrinterPositionInBrandPage() {
  return (
    <PrinterPositionInBrandProvider>
      <PrinterPositionInBrandContent />
    </PrinterPositionInBrandProvider>
  )
}
