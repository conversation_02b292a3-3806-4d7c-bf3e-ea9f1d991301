/* eslint-disable @typescript-eslint/no-explicit-any */
import ExcelJS from 'exceljs'
import * as XLSX from 'xlsx'

import { SaleNotSyncVatData } from '@/lib/sales-api'

function formatCurrencyForExcel(amount: number): number {
  return amount
}

function formatDateForExcel(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatPercentageForExcel(value: number): string {
  return `${value.toFixed(1)}%`
}

export function exportSaleNotSyncVatToExcel(
  data: (SaleNotSyncVatData & { storeName: string })[],
  options: {
    filename?: string
    sheetName?: string
    showStoreInfo?: boolean
    showEmployeeInfo?: boolean
    showPaymentMethod?: boolean
  } = {}
) {
  const {
    filename = `sale-not-sync-vat-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName = 'Giao dịch chưa đồng bộ VAT',
    showStoreInfo = true,
    showEmployeeInfo = true,
    showPaymentMethod = true
  } = options

  const excelData = data.map(sale => {
    const discountPercentage =
      sale.amount_origin > 0 ? ((sale.amount_origin - sale.total_amount) / sale.amount_origin) * 100 : 0

    const row: Record<string, any> = {
      'Mã giao dịch': sale.tran_no,
      'Thời gian': formatDateForExcel(sale.tran_date)
    }

    if (showStoreInfo) {
      row['Cửa hàng'] = sale.storeName
    }

    if (showEmployeeInfo) {
      row['Nhân viên'] = sale.employee_name
    }

    row['Loại bàn'] = sale.table_name

    if (showPaymentMethod) {
      row['Phương thức thanh toán'] = sale.payment_method_name
    }

    row['Mã voucher'] = sale.voucher_code || '-'
    row['Số tiền gốc (VNĐ)'] = formatCurrencyForExcel(sale.amount_origin)
    row['Thành tiền (VNĐ)'] = formatCurrencyForExcel(sale.total_amount)
    row['Giảm giá'] = formatPercentageForExcel(discountPercentage)
    row['Số tiền giảm (VNĐ)'] = formatCurrencyForExcel(sale.amount_origin - sale.total_amount)

    return row
  })

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  const columnWidths = [
    { wch: 15 }, // Mã giao dịch
    { wch: 20 } // Thời gian
  ]

  if (showStoreInfo) columnWidths.push({ wch: 25 })
  if (showEmployeeInfo) columnWidths.push({ wch: 20 })

  columnWidths.push({ wch: 15 })

  if (showPaymentMethod) columnWidths.push({ wch: 20 })

  columnWidths.push({ wch: 15 }, { wch: 18 }, { wch: 18 }, { wch: 12 }, { wch: 18 })

  worksheet['!cols'] = columnWidths

  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

  XLSX.writeFile(workbook, filename)
}

export function exportToExcel<T extends Record<string, any>>(
  data: T[],
  options: {
    filename?: string
    sheetName?: string
    columnMapping?: Record<keyof T, string>
  } = {}
) {
  const {
    filename = `export-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName = 'Data',
    columnMapping
  } = options

  const excelData = columnMapping
    ? data.map(row => {
        const transformedRow: Record<string, any> = {}
        Object.entries(columnMapping).forEach(([key, header]) => {
          transformedRow[header] = row[key]
        })
        return transformedRow
      })
    : data

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
  const columnWidths: { wch: number }[] = []

  for (let col = range.s.c; col <= range.e.c; col++) {
    let maxWidth = 10
    for (let row = range.s.r; row <= range.e.r; row++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
      const cell = worksheet[cellAddress]
      if (cell && cell.v) {
        const cellLength = cell.v.toString().length
        maxWidth = Math.max(maxWidth, cellLength)
      }
    }
    columnWidths.push({ wch: Math.min(maxWidth + 2, 50) })
  }

  worksheet['!cols'] = columnWidths

  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

  XLSX.writeFile(workbook, filename)
}

export function exportMultipleSheetsToExcel(
  sheets: Array<{
    data: Record<string, any>[]
    sheetName: string
    columnMapping?: Record<string, string>
  }>,
  filename: string = `multi-sheet-export-${new Date().toISOString().split('T')[0]}.xlsx`
) {
  const workbook = XLSX.utils.book_new()

  sheets.forEach(({ data, sheetName, columnMapping }) => {
    const excelData = columnMapping
      ? data.map(row => {
          const transformedRow: Record<string, any> = {}
          Object.entries(columnMapping).forEach(([key, header]) => {
            transformedRow[header] = row[key]
          })
          return transformedRow
        })
      : data

    const worksheet = XLSX.utils.json_to_sheet(excelData)

    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
    const columnWidths: { wch: number }[] = []

    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 10
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
        const cell = worksheet[cellAddress]
        if (cell && cell.v) {
          const cellLength = cell.v.toString().length
          maxWidth = Math.max(maxWidth, cellLength)
        }
      }
      columnWidths.push({ wch: Math.min(maxWidth + 2, 50) })
    }

    worksheet['!cols'] = columnWidths
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
  })

  XLSX.writeFile(workbook, filename)
}

interface ComboExportData {
  id: string
  name: string
  store_name?: string
  price: number
  promotion_name?: string
  combo_code?: string
  vat_percent?: number
  start_date?: string
  end_date?: string
  apply_date?: string
  apply_time?: string
  image?: string
  group_name?: string
  require_selection?: string
  selection_limit?: number
  group_item_codes?: string
  item_price?: number
}

export async function exportCombosToExcel(data: ComboExportData[], filename: string = 'update-combo.xlsx') {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Combos')

  worksheet.columns = [
    { header: 'ID', key: 'id', width: 15 },
    { header: 'Tên', key: 'name', width: 30 },
    { header: 'Cửa hàng áp dụng', key: 'store_name', width: 25 },
    { header: 'Giá', key: 'price', width: 15 },
    { header: 'CTKM', key: 'promotion_name', width: 25 },
    { header: 'Mã Combo', key: 'combo_code', width: 15 },
    { header: 'VAT (%)', key: 'vat_percent', width: 12 },
    { header: 'Ngày bắt đầu', key: 'start_date', width: 15 },
    { header: 'Ngày kết thúc', key: 'end_date', width: 15 },
    { header: 'Ngày áp dụng', key: 'apply_date', width: 15 },
    { header: 'Giờ áp dụng', key: 'apply_time', width: 15 },
    { header: 'Ảnh', key: 'image', width: 20 },
    { header: 'Tên nhóm', key: 'group_name', width: 20 },
    { header: 'Yêu cầu chọn', key: 'require_selection', width: 15 },
    { header: 'Giới hạn chọn', key: 'selection_limit', width: 15 },
    { header: 'Mã món theo nhóm', key: 'group_item_codes', width: 20 },
    { header: 'Giá món con', key: 'item_price', width: 15 }
  ]

  const headerRow = worksheet.getRow(1)
  headerRow.font = {
    bold: true,
    color: { argb: 'FFFFFF' }
  }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '4472C4' }
  }
  headerRow.alignment = {
    horizontal: 'center',
    vertical: 'middle'
  }
  headerRow.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  }

  data.forEach((combo, index) => {
    const row = worksheet.addRow({
      id: combo.id,
      name: combo.name,
      store_name: combo.store_name,
      price: combo.price,
      promotion_name: combo.promotion_name || '',
      combo_code: combo.combo_code || '',
      vat_percent: combo.vat_percent || 0,
      start_date: combo.start_date,
      end_date: combo.end_date,
      apply_date: combo.apply_date || '',
      apply_time: combo.apply_time || '',
      image: combo.image || '',
      group_name: combo.group_name || '',
      require_selection: combo.require_selection || '',
      selection_limit: combo.selection_limit || 0,
      group_item_codes: combo.group_item_codes || '',
      item_price: combo.item_price || 0
    })

    row.alignment = {
      horizontal: 'left',
      vertical: 'middle'
    }

    if (index % 2 === 0) {
      row.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F8F9FA' }
      }
    }

    row.eachCell(cell => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })

    const priceCell = row.getCell('price')
    const itemPriceCell = row.getCell('item_price')

    priceCell.numFmt = '#,##0'
    if (combo.item_price) {
      itemPriceCell.numFmt = '#,##0'
    }
  })

  worksheet.addRow([])

  const noteRow = worksheet.getRow(worksheet.rowCount)
  noteRow.font = {
    italic: true,
    color: { argb: 'FF0000' }
  }

  const buffer = await workbook.xlsx.writeBuffer()

  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.click()

  window.URL.revokeObjectURL(url)
}

export async function readExcelFile(file: File): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        const worksheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[worksheetName]

        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        const headers = jsonData[0] as string[]
        const rows = jsonData.slice(1) as any[][]

        const parsedData = rows.map(row => {
          const obj: any = {}
          headers.forEach((header, index) => {
            obj[header] = row[index] || ''
          })
          return obj
        })

        resolve(parsedData)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}
