import { createFileRoute, useParams } from '@tanstack/react-router'

import CustomizationInCityDetailPage from '@/features/menu/customization/customization-in-city/detail'

function CustomizationInCityEditPageWrapper() {
  const params = useParams({
    from: '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
  })

  return <CustomizationInCityDetailPage customizationId={params.customizationId} />
}

export const Route = createFileRoute(
  '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
)({
  component: CustomizationInCityEditPageWrapper
})
