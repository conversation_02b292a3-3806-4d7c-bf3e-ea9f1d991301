import { useQuery } from '@tanstack/react-query'

import type { UsingDailyResponse, UsingDailyParams } from '@/types/api/crm/using-daily'

import { getUsingDailyApi } from '@/lib/api/crm/using-daily'

import { CRM_QUERY_KEYS } from '@/constants/crm/query-keys'

export function useUsingDaily(params: UsingDailyParams | null) {
  return useQuery<UsingDailyResponse, Error>({
    queryKey: [CRM_QUERY_KEYS.USING_DAILY, params],
    queryFn: async () => {
      if (!params) {
        throw new Error('Params are required')
      }
      const response = await getUsingDailyApi(params)
      return response.data
    },
    enabled: !!params
  })
}
