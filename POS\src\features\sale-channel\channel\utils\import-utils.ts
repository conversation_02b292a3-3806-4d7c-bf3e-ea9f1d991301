import type { ImportSaleChannelData } from '../hooks/use-import-sale-channel'

export interface ImportApiPayload {
  source_id: string
  source_name: string
  description: null
  active: number
  sort: number
  is_fabi: number
  source_type: string[]
  company_uid: string
  brand_uid: string
  store_uid: string
  partner_config: number
  extra_data: {
    require_tran_no: number
    commission: number
    deduct_tax_rate: number
    payment_method_id: string
    payment_method_name: string
    payment_type: string
    marketing_partner_cost_type: string
    marketing_partner_cost: number
    voucher: string
    not_show_partner_bill: number
    use_order_online: number
    exclude_ship: number
    marketing_partner_cost_from_date: number
    marketing_partner_cost_to_date: number
    marketing_partner_cost_date_week: number
    marketing_partner_cost_hour_day: number
  }
}

/**
 * Transform import data to API format
 */
export const transformImportDataToApiFormat = (
  parsedData: ImportSaleChannelData[],
  companyId: string,
  brandId: string,
  storeId: string
): ImportApiPayload[] => {
  return parsedData.map(item => ({
    source_id: item.source_id,
    source_name: item.source_id, // Use source_id as name for now
    description: null,
    active: 1,
    sort: 0,
    is_fabi: 1,
    source_type: ['ONLINE'],
    company_uid: companyId,
    brand_uid: brandId,
    store_uid: storeId,
    partner_config: 1,
    extra_data: {
      require_tran_no: item.require_tran_no,
      commission: item.commission / 100, // Convert percentage to decimal
      deduct_tax_rate: 0,
      payment_method_id: item.payment_method_id,
      payment_method_name: item.payment_method_id, // Use payment_method_id as name for now
      payment_type: item.payment_type,
      marketing_partner_cost_type: item.marketing_partner_cost_type,
      marketing_partner_cost: item.marketing_partner_cost,
      voucher: item.voucher_run_partner || '',
      not_show_partner_bill: item.not_show_partner_bill,
      use_order_online: item.use_order_online,
      exclude_ship: item.exclude_ship,
      marketing_partner_cost_from_date:
        new Date(item.marketing_partner_cost_from_date).getTime() * 1000, // Convert to microseconds
      marketing_partner_cost_to_date:
        new Date(item.marketing_partner_cost_to_date).getTime() * 1000, // Convert to microseconds
      marketing_partner_cost_date_week: item.marketing_partner_cost_date_week,
      marketing_partner_cost_hour_day: item.marketing_partner_cost_hour_day
    }
  }))
}
