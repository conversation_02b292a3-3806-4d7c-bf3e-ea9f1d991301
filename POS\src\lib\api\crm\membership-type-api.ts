import type {
  MembershipTypeResponse,
  MembershipTypeParams,
  MembershipTypeItem,
  CreateMembershipTypeParams,
  CreateMembershipTypeResponse,
  UpdateMembershipTypeParams,
  UpdateMembershipTypeResponse
} from '@/types/api/crm'

import { crmApi } from './crm-api'

export const membershipTypeApi = {
  getList: async (params: MembershipTypeParams): Promise<MembershipTypeResponse> => {
    const response = await crmApi.get('/loyalty/get-list-membership-type', {
      params
    })
    return response.data
  },

  create: async (
    data: CreateMembershipTypeParams,
    params: UpdateMembershipTypeParams
  ): Promise<CreateMembershipTypeResponse> => {
    const response = await crmApi.post('/loyalty/create-membership-type', data, {
      params
    })
    return response.data
  },

  update: async (
    data: MembershipTypeItem,
    params: UpdateMembershipTypeParams
  ): Promise<UpdateMembershipTypeResponse> => {
    const response = await crmApi.post('/loyalty/update-membership-type', data, {
      params
    })
    return response.data
  }
}
