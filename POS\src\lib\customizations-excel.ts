import type { Customization, ParsedCustomizationData } from '@/types/customizations'

import { apiClient } from './api/pos/pos-api'

// Import payload interface
interface ImportPayload {
  name: string
  list_item: string[]
  data: {
    LstItem_Options: Array<{
      Name: string
      Min_Permitted: number
      Max_Permitted: number
      LstItem_Id: string[]
    }>
  }
  company_uid: string
  brand_uid: string
  store_uid?: string
  city_uid?: string
}

/**
 * Create Excel file for customizations export
 */
export const createCustomizationsExcelFile = async (customizations: Customization[]): Promise<void> => {
  const ExcelJS = await import('exceljs')

  // Check if this is store-based customizations (has storeUid or storeName)
  // Also check if any customization has isCustomizationInStore = 1
  const isStoreBased = customizations.some(
    c => c.storeUid || (c as Customization & { storeName?: string }).storeName || c.isCustomizationInStore === 1
  )

  const excelData = [
    isStoreBased
      ? ['Tên', '<PERSON><PERSON><PERSON> hàng', '<PERSON>ã món áp dụng', 'Tên nhóm', '<PERSON>êu cầu chọn', 'Giới hạn chọn', 'Mã món theo nhóm']
      : ['ID', 'Tên', 'Thành phố', 'Mã món áp dụng', 'Tên nhóm', 'Yêu cầu chọn', 'Giới hạn chọn', 'Mã món theo nhóm']
  ]

  customizations.forEach(customization => {
    const firstOption = customization.data.LstItem_Options?.[0]

    if (isStoreBased) {
      excelData.push([
        customization.name,
        (customization as Customization & { storeName?: string }).storeName || 'Cửa hàng hiện tại',
        customization.listItem.join(','), // Mã món áp dụng
        firstOption?.Name || '',
        String(firstOption?.Min_Permitted || ''),
        String(firstOption?.Max_Permitted || ''),
        firstOption?.LstItem_Id?.join(',') || '' // Mã món theo nhóm
      ])
    } else {
      excelData.push([
        customization.id,
        customization.name,
        customization.cityName,
        customization.listItem.join(','), // Mã món áp dụng
        firstOption?.Name || '',
        String(firstOption?.Min_Permitted || ''),
        String(firstOption?.Max_Permitted || ''),
        firstOption?.LstItem_Id?.join(',') || '' // Mã món theo nhóm
      ])
    }
  })

  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Customizations')

  // Add data to worksheet
  excelData.forEach((row, index) => {
    const addedRow = worksheet.addRow(row)

    // Style header row
    if (index === 0) {
      addedRow.eachCell(cell => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF79abe3' }
        }
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFFFF' }
        }
        cell.alignment = {
          horizontal: 'center',
          vertical: 'middle'
        }
      })
    }
  })

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column) {
      column.width = 15
    }
  })

  const now = new Date()
  const dateStr = now.toISOString().split('T')[0]
  const filename = `customizations_export_${dateStr}.xlsx`

  // Write file
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * Bulk import customizations from parsed Excel data
 */
export const bulkImportCustomizations = async (
  parsedData: ParsedCustomizationData[],
  storeUid?: string,
  cityUid?: string
): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const payload = parsedData.map(item => {
    const groupItemCodes = item.groupItemCodes
      ? item.groupItemCodes
          .split(',')
          .map(code => code.trim())
          .filter(Boolean)
      : []

    const appliedItemCodes = item.appliedItemCodes
      ? item.appliedItemCodes
          .split(',')
          .map(code => code.trim())
          .filter(Boolean)
      : []

    const payload: ImportPayload = {
      name: item.name,
      list_item: appliedItemCodes,
      data: {
        LstItem_Options: [
          {
            Name: item.groupName,
            Min_Permitted: item.minRequired,
            Max_Permitted: item.maxAllowed,
            LstItem_Id: groupItemCodes
          }
        ]
      },
      company_uid: companyUid,
      brand_uid: brandUid
    }

    // Add store_uid if provided (for store-based customizations)
    if (storeUid) {
      payload.store_uid = storeUid
    } else if (cityUid) {
      // Add city_uid if provided (for city-based customizations)
      payload.city_uid = cityUid
    }

    return payload
  })

  const apiUrl = `/mdata/v1/customizations/import`

  await apiClient.post(apiUrl, payload)
}
