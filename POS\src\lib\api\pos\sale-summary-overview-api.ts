import type { GetSaleSummaryOverviewParams, SaleSummaryOverviewResponse } from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Get sale summary overview data
 */
export const getSaleSummaryOverview = async (
  params: GetSaleSummaryOverviewParams
): Promise<SaleSummaryOverviewResponse> => {
  const response = await apiClient.get<SaleSummaryOverviewResponse>('/v1/reports/sale-summary/overview', {
    params: {
      brand_uid: params.brand_uid,
      company_uid: params.company_uid,
      list_store_uid: params.list_store_uid,
      start_date: params.start_date,
      end_date: params.end_date,
      store_open_at: params.store_open_at ?? 0
    }
  })

  return response.data
}
