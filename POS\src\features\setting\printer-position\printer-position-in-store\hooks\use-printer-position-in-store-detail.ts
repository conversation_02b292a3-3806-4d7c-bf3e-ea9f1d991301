import { useQuery } from '@tanstack/react-query'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { apiClient } from '@/lib/api'
import type { PrinterPosition } from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const usePrinterPositionInStoreDetail = (id: string, enabled = true) => {
  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()

  return useQuery({
    queryKey: [QUERY_KEYS.PRINTER_POSITIONS, 'detail', id, companyUid, selectedBrand?.id],
    queryFn: async (): Promise<PrinterPosition | null> => {
      if (!companyUid || !selectedBrand?.id || !id) {
        return null
      }

      const queryParams = new URLSearchParams({
        company_uid: companyUid,
        brand_uid: selectedBrand.id,
        id: id
      })

      const response = await apiClient.get(`/pos/v1/printer-position/detail?${queryParams.toString()}`)

      // Extract data from API response
      if (response?.data?.data) {
        return response.data.data as PrinterPosition
      }

      if (response?.data) {
        return response.data as PrinterPosition
      }

      return null
    },
    enabled: enabled && !!id && !!companyUid && !!selectedBrand?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
