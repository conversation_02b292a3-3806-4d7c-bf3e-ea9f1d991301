// VietQR Bank API Types
export interface VietQRBank {
  id: number
  name: string
  code: string
  bin: string
  shortName: string
  logo: string
  transferSupported: number
  lookupSupported: number
  short_name: string
  support: number
  isTransfer: number
  swift_code: string
}

export interface VietQRBanksResponse {
  code: string
  desc: string
  data: VietQRBank[]
}

// VietQR Quick Link API Types
export interface VietQRQuickLinkRequest {
  bank_id: string
  bank_acc: string
  bank_acc_name: string
}

export interface VietQRQuickLinkResponse {
  data: {
    imageURL: string
  }
  track_id: string
}

// Form state types
export interface VietQRFormState {
  selectedBank: VietQRBank | null
  accountNumber: string
  accountName: string
  qrImageUrl: string | null
  isGenerating: boolean
}
