import React, { useState } from 'react'

import useDialogState from '@/hooks/ui/use-dialog-state'

import { PrinterPositionInBrand } from '../data'

type PrinterPositionInBrandDialogType = 'create' | 'update' | 'delete' | 'bulk-delete'

interface PrinterPositionInBrandContextType {
  open: PrinterPositionInBrandDialogType | null
  setOpen: (str: PrinterPositionInBrandDialogType | null) => void
  currentRow: PrinterPositionInBrand | null
  setCurrentRow: React.Dispatch<React.SetStateAction<PrinterPositionInBrand | null>>
  selectedRows: PrinterPositionInBrand[]
  setSelectedRows: React.Dispatch<React.SetStateAction<PrinterPositionInBrand[]>>
}

const PrinterPositionInBrandContext = React.createContext<PrinterPositionInBrandContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function PrinterPositionInBrandProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<PrinterPositionInBrandDialogType>(null)
  const [currentRow, setCurrentRow] = useState<PrinterPositionInBrand | null>(null)
  const [selectedRows, setSelectedRows] = useState<PrinterPositionInBrand[]>([])

  return (
    <PrinterPositionInBrandContext
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow,
        selectedRows,
        setSelectedRows
      }}
    >
      {children}
    </PrinterPositionInBrandContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const usePrinterPositionInBrand = () => {
  const printerPositionInBrandContext = React.useContext(PrinterPositionInBrandContext)

  if (!printerPositionInBrandContext) {
    throw new Error('usePrinterPositionInBrand has to be used within <PrinterPositionInBrandContext>')
  }

  return printerPositionInBrandContext
}
