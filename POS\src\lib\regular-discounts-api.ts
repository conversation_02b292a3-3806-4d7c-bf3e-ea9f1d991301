import type {
  DiscountFormData,
  CreateDiscountRequest,
  CreateDiscountResponse,
  UpdateDiscountRequest
} from '@/types/api/discount-types'
import type { GetDiscountsParams, DiscountsApiResponse, Discount, DiscountApiData } from '@/types/discounts'

import { dateUtils } from '@/utils/date-utils'

import { apiClient } from './api/pos/pos-api'

/**
 * Regular discount update request (without sale_channel_uid and promotion_partner_auto_gen)
 */
export type RegularDiscountUpdateRequest = Omit<
  UpdateDiscountRequest,
  'sale_channel_uid' | 'promotion_partner_auto_gen'
>

/**
 * Regular discount promotion interface (from list_data)
 */
export interface RegularDiscountPromotion {
  id: string // This is the promotion_uid we need
  promotion_id: string
  promotion_name: string
  brand_uid: string
  company_uid: string
  store_uid: string
  active: number
  created_at: number
  updated_at: number
}

/**
 * Regular discount promotions API response structure
 */
export interface RegularDiscountPromotionGroup {
  promotion_id: string
  promotion_name: string
  list_data: RegularDiscountPromotion[]
}

export interface RegularDiscountPromotionsResponse {
  data: RegularDiscountPromotionGroup[]
  track_id: string
}

/**
 * Fetch regular discounts from API using the new endpoint
 */
export const getRegularDiscounts = async (params: GetDiscountsParams = {}): Promise<Discount[]> => {
  const queryParams = new URLSearchParams()

  // Add company and brand UIDs
  if (params.companyUid) {
    queryParams.append('company_uid', params.companyUid)
  }
  if (params.brandUid) {
    queryParams.append('brand_uid', params.brandUid)
  }

  // Add pagination
  if (params.page) {
    queryParams.append('page', params.page.toString())
  }

  // Add store UIDs
  if (params.listStoreUid && params.listStoreUid.length > 0) {
    queryParams.append('list_store_uid', params.listStoreUid.join(','))
  }

  // Add status filter
  if (params.status) {
    queryParams.append('status', params.status)
  }

  // Add active filter
  if (params.active !== undefined) {
    queryParams.append('active', params.active.toString())
  }

  // Add search term
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  const response = await apiClient.get<DiscountsApiResponse>(`/mdata/v1/discounts?${queryParams.toString()}`)

  if (response.data?.data) {
    // Import the conversion function dynamically to avoid circular dependency
    const { convertApiDiscountToDiscount } = await import('@/types/discounts')
    return response.data.data.map(convertApiDiscountToDiscount)
  }

  return []
}

/**
 * Delete regular discount by ID
 */
export const deleteRegularDiscount = async (params: {
  companyUid: string
  brandUid: string
  id: string
}): Promise<void> => {
  const queryParams = new URLSearchParams()
  queryParams.append('company_uid', params.companyUid)
  queryParams.append('brand_uid', params.brandUid)
  queryParams.append('id', params.id)

  await apiClient.delete(`/mdata/v1/discount?${queryParams.toString()}`)
}

/**
 * Get regular discount by ID
 */
export const getRegularDiscountById = async (params: {
  companyUid: string
  brandUid: string
  id: string
}): Promise<DiscountApiData> => {
  const queryParams = new URLSearchParams()
  queryParams.append('company_uid', params.companyUid)
  queryParams.append('brand_uid', params.brandUid)
  queryParams.append('id', params.id)

  const response = await apiClient.get<{ data: DiscountApiData; track_id: string }>(
    `/mdata/v1/discount?${queryParams.toString()}`
  )

  if (response.data?.data) {
    return response.data.data
  }

  throw new Error('Regular discount not found')
}

/**
 * Transform form data to regular discount API request (without sale_channel_uid)
 */
export const transformFormDataToRegularDiscountRequest = (
  formData: DiscountFormData,
  storeUid: string,
  companyUid: string,
  brandUid: string
): Omit<CreateDiscountRequest, 'sale_channel_uid'> => {
  const timeSaleDateWeek = dateUtils.convertDaysToBitFlags(formData.selectedDays)
  const timeSaleHourDay = dateUtils.convertHoursToBitFlags(formData.selectedHours)

  console.log('Transform Regular - formData.filterState:', formData.filterState)

  // Use filterState directly - no need for complex logic
  const { is_all, is_item, is_type, type_id, item_id, is_combo, combo_id } = formData.filterState

  return {
    store_uid: storeUid,
    promotion_uid: formData.promotionUid,
    discount_type: formData.discountType,
    type_id: type_id,
    item_id: item_id,
    from_date: formData.fromDate.getTime(),
    to_date: formData.toDate.getTime(),
    time_sale_date_week: timeSaleDateWeek,
    time_sale_hour_day: timeSaleHourDay,
    company_uid: companyUid,
    brand_uid: brandUid,
    ta_discount: formData.discountValue,
    ots_discount: formData.discountValue,
    is_all: is_all,
    is_type: is_type,
    is_item: is_item,
    extra_data: {
      is_combo: is_combo,
      combo_id: combo_id
    },
    is_update_same_discounts: false
  }
}

/**
 * Create new regular discount
 */
export const createRegularDiscountEnhanced = async (
  request: Omit<CreateDiscountRequest, 'sale_channel_uid'>
): Promise<CreateDiscountResponse> => {
  try {
    console.log('🔥 CREATE Regular Discount API Call:')
    console.log('🔥 URL: POST /mdata/v1/discount')
    console.log('🔥 Payload:', JSON.stringify(request, null, 2))

    // Compare with expected cURL payload
    const expectedPayload = {
      store_uid: 'e20d55dd-6dcc-4238-a32e-42f8ae6abaeb',
      promotion_uid: '82939bd6-**************-8ec3a0caaf7b',
      discount_type: 'AMOUNT',
      type_id: '',
      item_id: '',
      from_date: 1754499600000,
      to_date: 1754585999000,
      time_sale_date_week: 32,
      time_sale_hour_day: 28,
      company_uid: '595e8cb4-674c-49f7-adec-826b211a7ce3',
      brand_uid: 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e',
      ta_discount: 100,
      ots_discount: 100,
      is_all: 1,
      is_type: 0,
      is_item: 0,
      extra_data: {
        is_combo: 0,
        combo_id: ''
      },
      is_update_same_discounts: false
    }
    console.log('🔥 Expected cURL Payload:', JSON.stringify(expectedPayload, null, 2))

    const response = await apiClient.post('/mdata/v1/discount', request)

    console.log('🔥 CREATE Regular Discount Response:', response.data)
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    console.error('🔥 CREATE Regular Discount Error:', error)
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tạo regular discount'
    }
  }
}

/**
 * Transform form data to full regular discount object for update
 */
export const transformFormDataToRegularDiscountUpdateRequest = (
  formData: DiscountFormData,
  originalDiscount: DiscountApiData
): RegularDiscountUpdateRequest => {
  const timeSaleDateWeek = dateUtils.convertDaysToBitFlags(formData.selectedDays)
  const timeSaleHourDay = dateUtils.convertHoursToBitFlags(formData.selectedHours)

  const { is_all, is_item, is_type, type_id, item_id, is_combo, combo_id } = formData.filterState

  // Merge formData with originalDiscount, only updating fields that exist in formData
  return {
    // Keep all original fields
    ...originalDiscount,

    // Update only the fields that can be modified in the form
    ta_discount: formData.discountValue,
    ots_discount: formData.discountValue,
    is_all: is_all,
    is_type: is_type,
    is_item: is_item,
    type_id: type_id,
    item_id: item_id,
    discount_type: formData.discountType,
    from_date: formData.fromDate.getTime(),
    to_date: formData.toDate.getTime(),
    time_sale_hour_day: timeSaleHourDay,
    time_sale_date_week: timeSaleDateWeek,
    extra_data: {
      is_combo: is_combo,
      combo_id: combo_id
    },

    // Regular discounts don't have sale_channel_uid
    is_update_same_discounts: false
  }
}

/**
 * Update regular discount with enhanced API
 */
export const updateRegularDiscountEnhanced = async (
  request: RegularDiscountUpdateRequest
): Promise<CreateDiscountResponse> => {
  try {
    const response = await apiClient.put('/mdata/v1/discount', request)
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật regular discount'
    }
  }
}

/**
 * Get regular discount promotions for a specific store
 * Matches the exact API call: /mdata/v1/promotions?skip_limit=true&company_uid=...&brand_uid=...&store_uid=...&partner_auto_gen=0&active=1
 */
export const getRegularDiscountPromotions = async (params: {
  companyUid: string
  brandUid: string
  storeUid: string
}): Promise<{ promotion_uid: string; promotion_name: string }[]> => {
  const queryParams = new URLSearchParams({
    skip_limit: 'true',
    company_uid: params.companyUid,
    brand_uid: params.brandUid,
    store_uid: params.storeUid,
    partner_auto_gen: '0',
    active: '1'
  })

  const url = `/mdata/v1/promotions?${queryParams.toString()}`
  console.log('🔥 Regular Discount Promotions API Call:', url)
  console.log('🔥 Params:', params)

  const response = await apiClient.get<RegularDiscountPromotionsResponse>(url)

  console.log('🔥 Regular Discount Promotions Response:', response.data)

  // Parse the nested structure: data[].list_data[]
  const promotionGroups = response.data?.data || []
  const flatPromotions = promotionGroups.flatMap(group =>
    group.list_data.map(item => ({
      promotion_uid: item.id, // The 'id' field is the promotion_uid we need
      promotion_name: item.promotion_name
    }))
  )

  console.log('🔥 Parsed Promotions:', flatPromotions)
  return flatPromotions
}

/**
 * Update regular discount (toggle active status)
 */
export const updateRegularDiscount = async (discountData: DiscountApiData): Promise<void> => {
  await apiClient.put('/mdata/v1/discount', discountData)
}
