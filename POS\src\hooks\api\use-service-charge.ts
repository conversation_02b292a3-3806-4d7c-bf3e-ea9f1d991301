import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetServiceChargeParams } from '@/types/service-charge'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'
import { serviceChargeApi } from '@/lib/service-charge-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseServiceChargeDataOptions {
  params?: Partial<GetServiceChargeParams>
  enabled?: boolean
}

export const useServiceChargeData = (options: UseServiceChargeDataOptions = {}) => {
  const { params = {}, enabled = true } = options

  return useQuery({
    queryKey: [QUERY_KEYS.SERVICE_CHARGE, params],
    queryFn: async () => {
      if (!params.company_uid || !params.brand_uid) {
        throw new Error('Company UID and Brand UID are required')
      }

      const response = await serviceChargeApi.getServiceCharges(params as GetServiceChargeParams)
      return response.data || []
    },
    enabled: enabled && !!(params.company_uid && params.brand_uid),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })
}

export const useServiceChargeDetail = (params: {
  company_uid?: string
  brand_uid?: string
  id?: string
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SERVICE_CHARGE, 'detail', params.company_uid, params.brand_uid, params.id],
    queryFn: async () => {
      if (!params.company_uid || !params.brand_uid || !params.id) {
        throw new Error('Missing required parameters for service charge detail')
      }
      return serviceChargeApi.getServiceChargeDetail({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        id: params.id
      })
    },
    enabled: params.enabled && !!(params.company_uid && params.brand_uid && params.id),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

export const useCreateServiceCharge = (options?: { onSuccess?: () => void }) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (serviceChargeData: any) => serviceChargeApi.createServiceCharge(serviceChargeData),
    onSuccess: () => {
      // Invalidate and refetch service charge queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SERVICE_CHARGE] })
      toast.success('Đã tạo phí dịch vụ thành công!')
      options?.onSuccess?.()
    },
    onError: (error: any) => {
      console.error('Error creating service charge:', error)
      toast.error(error.message || 'Lỗi khi tạo phí dịch vụ')
    }
  })
}

export const useUpdateServiceCharge = (options?: { onSuccess?: () => void }) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (serviceChargeData: any) => serviceChargeApi.updateServiceCharge(serviceChargeData),
    onSuccess: () => {
      // Invalidate and refetch service charge queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SERVICE_CHARGE] })
      toast.success('Đã cập nhật phí dịch vụ thành công!')
      options?.onSuccess?.()
    },
    onError: (error: any) => {
      console.error('Error updating service charge:', error)
      toast.error(error.message || 'Lỗi khi cập nhật phí dịch vụ')
    }
  })
}

export const useDeleteServiceCharge = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: { id: string; company_uid: string; brand_uid: string }) =>
      serviceChargeApi.deleteServiceCharge(params),
    onSuccess: () => {
      // Invalidate and refetch service charge queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SERVICE_CHARGE] })
      toast.success('Đã xóa phí dịch vụ thành công!')
    },
    onError: (error: any) => {
      console.error('Error deleting service charge:', error)
      toast.error(error.message || 'Lỗi khi xóa phí dịch vụ')
    }
  })
}

export const useAreaNames = (
  params: {
    company_uid: string
    brand_uid: string
    store_uid: string
    list_area_id: string
  },
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SERVICE_CHARGE, 'area-names', params],
    queryFn: async () => {
      if (!params.list_area_id) {
        return { data: [] }
      }
      return await serviceChargeApi.getAreaNames(params)
    },
    enabled: enabled && !!(params.company_uid && params.brand_uid && params.store_uid && params.list_area_id),
    staleTime: 10 * 60 * 1000 // 10 minutes
  })
}

export const useSourceNames = (
  params: {
    company_uid: string
    brand_uid: string
    store_uid: string
    list_source_id: string
  },
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SERVICE_CHARGE, 'source-names', params],
    queryFn: async () => {
      if (!params.list_source_id) {
        return { data: [] }
      }
      return await serviceChargeApi.getSourceNames(params)
    },
    enabled: enabled && !!(params.company_uid && params.brand_uid && params.store_uid && params.list_source_id),
    staleTime: 10 * 60 * 1000 // 10 minutes
  })
}

/**
 * Hook to get service charge programs for a specific store
 */
export const useServiceChargePrograms = (params: {
  company_uid?: string
  brand_uid?: string
  store_uid?: string
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SERVICE_CHARGE, 'programs', params.company_uid, params.brand_uid, params.store_uid],
    queryFn: async () => {
      if (!params.company_uid || !params.brand_uid || !params.store_uid) {
        throw new Error('Missing required parameters')
      }
      return serviceChargeApi.getServiceChargePrograms({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        store_uid: params.store_uid
      })
    },
    enabled: params.enabled && !!(params.company_uid && params.brand_uid && params.store_uid),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to get promotions for service charge forms
 */
export const useServiceChargePromotions = (storeUid: string, options?: { enabled?: boolean }) => {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.PROMOTIONS, company?.id, selectedBrand?.brandId, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.brandId || !storeUid) {
        throw new Error('Missing required parameters for promotions')
      }

      const queryParams = new URLSearchParams()
      queryParams.set('company_uid', company.id)
      queryParams.set('brand_uid', selectedBrand.brandId)
      queryParams.set('store_uid', storeUid)
      queryParams.set('skip_limit', 'true')

      const response = await api.get(`/mdata/v1/promotions?${queryParams.toString()}`)

      return response.data?.data || []
    },
    enabled: options?.enabled && !!(company?.id && selectedBrand?.brandId && storeUid),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

/**
 * Hook to clone service charge programs
 */
export const useCloneServiceCharges = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: serviceChargeApi.cloneServiceCharges,
    onSuccess: () => {
      // Invalidate and refetch service charge queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SERVICE_CHARGE] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SERVICE_CHARGE, 'programs'] })
      toast.success('Đã sao chép phí dịch vụ thành công!')
    },
    onError: (error: any) => {
      console.error('Error cloning service charges:', error)
      toast.error(error.message || 'Lỗi khi sao chép phí dịch vụ')
    }
  })
}
