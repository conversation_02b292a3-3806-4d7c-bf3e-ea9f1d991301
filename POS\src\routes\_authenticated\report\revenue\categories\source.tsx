import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import SourceRevenueReport from '@/features/reports/revenue/categories/source'

export const Route = createFileRoute('/_authenticated/report/revenue/categories/source')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: SourceRevenueReport
})
