import ExcelJS from 'exceljs'

import type { Item } from '@/lib/item-api'
import type { ItemType } from '@/lib/item-types-api'

/**
 * Fixed source mapping for price by source configuration
 */
const FIXED_SOURCE_MAPPING = [
  { sourceId: '10000045', name: '<PERSON><PERSON><PERSON>' },
  { sourceId: '10000049', name: 'FACEBOOK' },
  { sourceId: '10000134', name: 'SO' },
  { sourceId: '10000162', name: 'CRM' },
  { sourceId: '10000165', name: 'VNPAY' },
  { sourceId: '10000168', name: 'GOJE<PERSON> (GOVIET)' },
  { sourceId: '10000169', name: 'ShopeeFood' },
  { sourceId: '10000171', name: 'MANG VỀ' },
  { sourceId: '10000172', name: 'TẠI CHỖ' },
  { sourceId: '10000176', name: 'CALL CENTER' },
  { sourceId: '10000216', name: 'O2<PERSON>' },
  { sourceId: '10000253', name: 'BEFOOD' }
]

/**
 * Create a new Excel workbook for price by source configuration
 */
export const createPriceBySourceWorkbook = (): ExcelJS.Workbook => {
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'POS System'
  workbook.lastModifiedBy = 'POS System'
  workbook.created = new Date()
  workbook.modified = new Date()
  return workbook
}

/**
 * Generate headers for price by source template
 * Using fixed source mapping instead of dynamic sources
 */
export const getPriceBySourceHeaders = (): string[] => {
  const baseHeaders = ['item_uid', 'item_id', 'item_name', 'Nhóm món', 'Giá gốc', 'Vat (%)']

  // Add fixed source columns
  const sourceHeaders = FIXED_SOURCE_MAPPING.map(source => `${source.name} [${source.sourceId}]`)

  return [...baseHeaders, ...sourceHeaders]
}

/**
 * Generate data rows for price by source template
 * Map prices from item.price_by_source data and group by item type
 */
export const generatePriceBySourceData = (items: Item[], itemTypes: ItemType[]): any[][] => {
  const itemsWithType = items.map(item => {
    const itemType = itemTypes.find(type => type.id === item.item_type_uid)
    const itemTypeName = itemType?.item_type_name || 'Uncategory'

    return {
      item,
      itemTypeName
    }
  })

  // Group items by item type name
  const groupedItems = itemsWithType.reduce(
    (groups, { item, itemTypeName }) => {
      if (!groups[itemTypeName]) {
        groups[itemTypeName] = []
      }
      groups[itemTypeName].push({ item, itemTypeName })
      return groups
    },
    {} as Record<string, Array<{ item: Item; itemTypeName: string }>>
  )

  const sortedGroupNames = Object.keys(groupedItems).sort()

  const data: any[][] = []

  sortedGroupNames.forEach(groupName => {
    const groupItems = groupedItems[groupName]

    groupItems.sort((a, b) => a.item.item_name.localeCompare(b.item.item_name))

    groupItems.forEach(({ item, itemTypeName }) => {
      const row = [item.id, item.item_id, item.item_name, itemTypeName, item.ots_price || 0, (item.ots_tax || 0) * 100]

      FIXED_SOURCE_MAPPING.forEach(sourceMapping => {
        const priceBySource = item.extra_data?.price_by_source?.find(pbs => pbs.source_id === sourceMapping.sourceId)
        row.push(priceBySource?.price || '')
      })

      data.push(row)
    })
  })

  return data
}

/**
 * Create price by source worksheet
 */
export const createPriceBySourceWorksheet = (
  workbook: ExcelJS.Workbook,
  items: Item[],
  itemTypes: ItemType[]
): ExcelJS.Worksheet => {
  const worksheet = workbook.addWorksheet('Sheet')

  const headers = getPriceBySourceHeaders()
  const data = generatePriceBySourceData(items, itemTypes)

  const headerRow = worksheet.addRow(headers)

  headerRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0560A6' }
    }
    cell.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      size: 11
    }
    cell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  data.forEach(rowData => {
    const dataRow = worksheet.addRow(rowData)

    dataRow.eachCell((cell: any) => {
      cell.font = { size: 10 }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
      }
    })
  })

  const columnWidths = [40, 15, 25, 15, 12, 10, ...FIXED_SOURCE_MAPPING.map(() => 15)]

  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width
  })

  return worksheet
}

/**
 * Generate and download Excel file for price by source configuration
 */
export const generatePriceBySourceExcelFile = async (
  items: Item[],
  itemTypes: ItemType[],
  storeName: string
): Promise<void> => {
  try {
    const workbook = createPriceBySourceWorkbook()

    createPriceBySourceWorksheet(workbook, items, itemTypes)

    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `import-price-by-source_${storeName}_${timestamp}.xlsx`

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    return Promise.resolve()
  } catch (error) {
    console.error('Error creating price by source Excel file:', error)
    return Promise.reject(error)
  }
}
