import { useState, useEffect } from 'react'

import { Input } from '@/components/ui'

interface AddressSuggestion {
  display_name: string
  lat: string
  lon: string
}

interface AddressAutocompleteProps {
  value: string
  onChange: (value: string) => void
  onAddressSelect?: (address: string, lat: number, lng: number) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function AddressAutocomplete({
  value,
  onChange,
  onAddressSelect,
  placeholder = 'Nhập địa chỉ',
  disabled = false,
  className
}: AddressAutocompleteProps) {
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [debounceTimeout, setDebounceTimeout] = useState<NodeJS.Timeout | null>(null)

  // Debounced search function
  const searchAddresses = async (query: string) => {
    if (query.length < 3) {
      setSuggestions([])
      setShowSuggestions(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          query
        )}&accept-language=vi&limit=5&countrycodes=VN`
      )
      const data = await response.json()
      setSuggestions(data)
      setShowSuggestions(true)
    } catch (error) {
      console.error('Error searching addresses:', error)
      setSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }

  // Handle input change with debounce
  const handleInputChange = (newValue: string) => {
    onChange(newValue)

    // Clear previous timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout)
    }

    // Set new timeout for search
    const newTimeout = setTimeout(() => {
      searchAddresses(newValue)
    }, 500)
    setDebounceTimeout(newTimeout)
  }

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    const lat = parseFloat(suggestion.lat)
    const lng = parseFloat(suggestion.lon)

    onChange(suggestion.display_name)
    setShowSuggestions(false)
    setSuggestions([])

    if (onAddressSelect) {
      onAddressSelect(suggestion.display_name, lat, lng)
    }
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout)
      }
    }
  }, [debounceTimeout])

  return (
    <div
      className={`relative ${className || ''}`}
      onBlur={() => {
        // Delay hiding suggestions to allow click on suggestion
        setTimeout(() => setShowSuggestions(false), 150)
      }}
    >
      <Input
        value={value}
        onChange={e => handleInputChange(e.target.value)}
        onFocus={() => {
          if (suggestions.length > 0) {
            setShowSuggestions(true)
          }
        }}
        placeholder={placeholder}
        disabled={disabled}
        className='w-full'
        autoComplete='off'
      />

      {isLoading && (
        <div className='absolute top-1/2 right-3 -translate-y-1/2'>
          <div className='h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600'></div>
        </div>
      )}

      {showSuggestions && suggestions.length > 0 && (
        <div className='absolute top-full z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-200 bg-white shadow-lg'>
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className='cursor-pointer px-3 py-2 text-sm hover:bg-gray-100'
              onClick={() => handleSuggestionSelect(suggestion)}
            >
              {suggestion.display_name}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
