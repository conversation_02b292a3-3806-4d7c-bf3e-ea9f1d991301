'use client'

import { Table } from '@tanstack/react-table'

import { X, Trash2 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import { DataTableViewOptions } from '@/components/data-table'
import { usePrinterPositionInBrand } from '../context'
import { PrinterPositionInBrand } from '../data'

interface PrinterPositionInBrandTableToolbarProps<TData> {
  table: Table<TData>
}

export function PrinterPositionInBrandTableToolbar<TData>({ table }: PrinterPositionInBrandTableToolbarProps<TData>) {
  const { setOpen, setSelectedRows } = usePrinterPositionInBrand()
  const isFiltered = table.getState().columnFilters.length > 0
  const selectedRows = table.getFilteredSelectedRowModel().rows
  const hasSelectedRows = selectedRows.length > 0

  const handleBulkDelete = () => {
    const selectedData = selectedRows.map(row => row.original as PrinterPositionInBrand)
    setSelectedRows(selectedData)
    setOpen('bulk-delete')
  }

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Tìm kiếm vị trí máy in...'
          value={(table.getColumn('printerPositionName')?.getFilterValue() as string) ?? ''}
          onChange={event => table.getColumn('printerPositionName')?.setFilterValue(event.target.value)}
          className='h-8 w-[150px] lg:w-[250px]'
        />

        {isFiltered && (
          <Button variant='ghost' onClick={() => table.resetColumnFilters()} className='h-8 px-2 lg:px-3'>
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}

        {hasSelectedRows && (
          <div className='flex items-center space-x-2 ml-4'>
            <span className='text-sm text-muted-foreground'>
              {selectedRows.length} hàng được chọn
            </span>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => table.resetRowSelection()}
              className='h-8'
            >
              Hủy chọn
            </Button>
          </div>
        )}
      </div>

      <div className='flex items-center gap-2'>
        {hasSelectedRows && (
          <Button
            variant='destructive'
            size='sm'
            onClick={handleBulkDelete}
            className='h-8'
          >
            <Trash2 className='mr-2 h-4 w-4' />
            Xóa ({selectedRows.length})
          </Button>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}
