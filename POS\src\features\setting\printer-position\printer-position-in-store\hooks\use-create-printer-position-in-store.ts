import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { type CreatePrinterPositionRequest, printerPositionApi } from '@/lib/printer-position-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const useCreatePrinterPositionInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreatePrinterPositionRequest) => printerPositionApi.createPrinterPosition(data),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PRINTER_POSITIONS_IN_STORE] })
      toast.success(response.message || 'Tạo vị trí máy in trong cửa hàng thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'C<PERSON> lỗi x<PERSON>y ra khi tạo vị trí máy in trong cửa hàng')
    }
  })
}
