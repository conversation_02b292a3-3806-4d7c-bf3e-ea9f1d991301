# API cURL Examples for Testing

Dựa trên cấu trúc API từ `POS/src/lib/api.ts` và cURL mẫu được cung cấp.

## 1. CRM API - Get POS Settings (Từ mẫu của bạn)

```bash
curl 'https://crm.ipos.vn/settings/get-pos?pos_parent=BRAND-953H' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: en,vi;q=0.9,en-GB;q=0.8,en-US;q=0.7' \
  -H 'Authorization: 0d3806afc35ac01a13ea07dff13e3c22e72763f0' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://crm.ipos.vn/vi/general-setups/store-menu' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'Server: LIVE' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"'
```

## 2. POS API - GET Request (Dựa trên api.ts)

```bash
curl 'https://posapi.ipos.vn/api/products' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: vi' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'Origin: https://fabi.ipos.vn' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://fabi.ipos.vn/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'access_token: 5c885b2ef8c34fb7b1d1fad11eef7bec' \
  -H 'fabi_type: pos-cms' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'x-client-timezone: 25200000'
```

## 3. POS API - POST Request

```bash
curl 'https://posapi.ipos.vn/api/products' \
  -X POST \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: vi' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'Origin: https://fabi.ipos.vn' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://fabi.ipos.vn/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'access_token: 5c885b2ef8c34fb7b1d1fad11eef7bec' \
  -H 'fabi_type: pos-cms' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'x-client-timezone: 25200000' \
  -d '{
    "name": "Sản phẩm mới",
    "price": 100000,
    "category": "food",
    "description": "Mô tả sản phẩm"
  }'
```

## 4. POS API - PUT Request

```bash
curl 'https://posapi.ipos.vn/api/products/123' \
  -X PUT \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: vi' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'Origin: https://fabi.ipos.vn' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://fabi.ipos.vn/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'access_token: 5c885b2ef8c34fb7b1d1fad11eef7bec' \
  -H 'fabi_type: pos-cms' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'x-client-timezone: 25200000' \
  -d '{
    "name": "Sản phẩm đã cập nhật",
    "price": 150000,
    "category": "food",
    "description": "Mô tả đã cập nhật"
  }'
```

## 5. POS API - DELETE Request

```bash
curl 'https://posapi.ipos.vn/api/products/123' \
  -X DELETE \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: vi' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'Origin: https://fabi.ipos.vn' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://fabi.ipos.vn/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'access_token: 5c885b2ef8c34fb7b1d1fad11eef7bec' \
  -H 'fabi_type: pos-cms' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'x-client-timezone: 25200000'
```

## Thông tin quan trọng:

### API Endpoints:
- **CRM API**: `https://crm.ipos.vn`
- **POS API**: `https://posapi.ipos.vn/api`

### Authentication:
- **CRM API**: Sử dụng `Authorization` header với token: `0d3806afc35ac01a13ea07dff13e3c22e72763f0`
- **POS API**: Sử dụng `access_token` header với token: `5c885b2ef8c34fb7b1d1fad11eef7bec`

### Headers quan trọng:
- `Content-Type: application/json;charset=UTF-8` (cho POST/PUT)
- `fabi_type: pos-cms` (cho POS API)
- `x-client-timezone: 25200000` (cho POS API)

## Hướng dẫn sử dụng trong Postman:

1. **Import cURL**: Copy cURL command → Postman → Import → Raw text → Paste
2. **Thay đổi endpoint**: Sửa URL theo endpoint cần test
3. **Thay đổi data**: Sửa JSON data trong Body tab (cho POST/PUT)
4. **Kiểm tra headers**: Đảm bảo các headers quan trọng đã được set
5. **Test**: Click Send để test API

## Các endpoint thường dùng (ví dụ):
- `/products` - Quản lý sản phẩm
- `/orders` - Quản lý đơn hàng  
- `/customers` - Quản lý khách hàng
- `/categories` - Quản lý danh mục
- `/settings` - Cài đặt hệ thống
