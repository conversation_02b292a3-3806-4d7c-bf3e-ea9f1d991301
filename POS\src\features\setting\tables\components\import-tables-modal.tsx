import { useState, useMemo, useRef } from 'react'

import { Download, Upload } from 'lucide-react'
import { toast } from 'sonner'

import { useBulkImportTables } from '@/hooks/api/use-tables'
import { useAreasData } from '@/hooks/api/use-areas'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { useTablesImportExcelParser, type ParsedTableData } from '../hooks/use-tables-import-excel-parser'
import { useAutoCreateAreas } from '../hooks/use-auto-create-areas'



interface Store {
  id: string
  store_name: string
  active: number
}

interface ImportTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onSuccess: () => void
}

export function ImportTablesModal({ open, onOpenChange, onCancel, onSuccess }: ImportTablesModalProps) {
  // Import state
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedTableData[]>([])
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  // Store selection state for Step 2
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')

  // Hooks
  const { parseExcelFile, downloadTemplate } = useTablesImportExcelParser()
  const bulkImportTablesMutation = useBulkImportTables()
  const { checkAndCreateAreas, getAreasToCreate, isCreatingAreas } = useAutoCreateAreas()

  // Fetch areas data for selected store
  const { data: existingAreas = [] } = useAreasData({
    storeUid: selectedStoreId,
    page: 1,
    results_per_page: 15000
  })

  // Get stores from localStorage (same as TablesList component)
  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData) ? storesData.filter((store: Store) => store.active === 1) : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  // Set default store when stores are loaded
  useMemo(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores, selectedStoreId])

  // Reset import state when modal closes
  useMemo(() => {
    if (!open) {
      setCurrentStep(1)
      setImportParsedData([])
      setImportSelectedFile(null)
      setSelectedStoreId(stores[0]?.id || '')
    }
  }, [open, stores])

  const handleDownloadTemplate = () => {
    downloadTemplate()
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedTables = await parseExcelFile(file)
      setImportParsedData(parsedTables)
      setCurrentStep(2)
      toast.success(`Đã phân tích ${parsedTables.length} bàn từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleConfirm = async () => {
    if (currentStep === 2 && selectedStoreId && importParsedData.length > 0) {
      try {
        // Step 1: Tự động tạo các khu vực chưa tồn tại
        const areaMapping = await checkAndCreateAreas(
          selectedStoreId,
          importParsedData,
          existingAreas
        )

        // Step 2: Convert parsed data to API format với area_uid
        const tablesData = importParsedData.map(table => {
          const areaUid = areaMapping[table.area_name.trim()]
          if (!areaUid) {
            throw new Error(`Không tìm thấy khu vực: ${table.area_name}`)
          }

          return {
            table_name: table.table_name,
            area_uid: areaUid,
            description: table.description || ''
          }
        })

        // Step 3: Tạo bàn
        await bulkImportTablesMutation.mutateAsync({
          storeUid: selectedStoreId,
          tables: tablesData
        })

        toast.success(`Đã tạo thành công ${importParsedData.length} bàn!`)
        onSuccess()
      } catch (error) {
        if (error instanceof Error) {
          toast.error(error.message)
        } else {
          toast.error('Lỗi khi tạo bàn. Vui lòng thử lại.')
        }
      }
    }
  }

  return (
    <PosModal
      title='Thêm bàn'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={currentStep === 2 ? handleConfirm : () => {}}
      confirmText={currentStep === 2 ? 'Tạo bàn' : undefined}
      cancelText='Đóng'
      centerTitle={true}
      maxWidth='sm:max-w-4xl'
      isLoading={bulkImportTablesMutation.isPending || isCreatingAreas}
      hideButtons={currentStep === 1}
      confirmDisabled={currentStep === 2 && (!selectedStoreId || importParsedData.length === 0)}
    >
      <div className='max-h-[70vh] space-y-6 overflow-y-auto'>
        {/* Step 1: Tải file mẫu */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>Bước 1. Tải file mẫu</h3>
            <Button variant='outline' size='sm' onClick={handleDownloadTemplate} className='flex items-center gap-2'>
              <Download className='h-4 w-4' />
              Tải xuống
            </Button>
          </div>
        </div>

        {/* Step 2: Thêm bàn vào file */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Bước 2. Thêm bàn vào file</h3>
          <div className='space-y-2 text-sm text-gray-600'>
            <p>- Không sửa lại tên dòng tiêu đề</p>
            <p>- Nhập thông tin bàn vào các dòng sau đó</p>
          </div>
        </div>

        {/* Step 3: Tải file bàn lên */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>Bước 3. Tải file bàn lên</h3>
            <Button variant='outline' size='sm' onClick={handleImportFileUpload} className='flex items-center gap-2'>
              <Upload className='h-4 w-4' />
              Tải file lên
            </Button>
          </div>
          <p className='text-sm text-gray-600'>Sau khi đã điền đầy đủ ban bạn có thể tải file lên</p>

          {importSelectedFile && (
            <div className='rounded-lg border bg-gray-50 p-3'>
              <p className='text-sm text-gray-700'>
                File đã chọn: <span className='font-medium'>{importSelectedFile.name}</span>
              </p>
            </div>
          )}
        </div>

        {/* Step 2 Content: Show parsed data and store selection */}
        {currentStep === 2 && importParsedData.length > 0 && (
          <div className='space-y-4 border-t pt-4'>
            <div className='flex items-center justify-between'>
              <h4 className='text-md font-medium'>Chọn cửa hàng áp dụng</h4>
              <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
                <SelectTrigger className='w-[200px]'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {stores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Areas to be created warning */}
            {(() => {
              const areasToCreate = getAreasToCreate(importParsedData, existingAreas)
              return areasToCreate.length > 0 ? (
                <div className='rounded-lg bg-yellow-50 border border-yellow-200 p-3'>
                  <p className='text-sm text-yellow-800 text-center'>
                    Các khu vực chưa tồn tại sẽ được tạo trước khi thêm bàn
                  </p>
                </div>
              ) : null
            })()}

            {/* Data Preview Table with fixed height */}
            <div className='h-64 overflow-y-auto rounded-lg border'>
              <table className='w-full text-sm'>
                <thead className='sticky top-0 z-10 bg-gray-50'>
                  <tr>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Tên bàn</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Khu vực</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Nguồn</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Món đặt trước</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Mô tả</th>
                  </tr>
                </thead>
                <tbody>
                  {importParsedData.map((table, index) => (
                    <tr key={index} className='border-t hover:bg-gray-50'>
                      <td className='px-3 py-2 text-sm'>{table.table_name}</td>
                      <td className='px-3 py-2 text-sm'>{table.area_name}</td>
                      <td className='px-3 py-2 text-sm'>{table.source_name || '-'}</td>
                      <td className='px-3 py-2 text-sm'>{table.pre_order_items || '-'}</td>
                      <td className='px-3 py-2 text-sm'>{table.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Summary */}
            <div className='text-center text-sm text-gray-600'>
              Tổng cộng: <span className='font-medium'>{importParsedData.length}</span> bàn sẽ được tạo
            </div>
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={importFileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={handleImportFileChange}
        className='hidden'
      />
    </PosModal>
  )
}
