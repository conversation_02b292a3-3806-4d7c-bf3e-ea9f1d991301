import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import type { StoreFormValues } from '../../data'
import { syncStoreExpiryTimeProactive } from './license-api'

interface UseLicenseCheckParams {
  form: UseFormReturn<StoreFormValues>
  companyUid?: string
  brandUid?: string
  storeId?: string
}

export function useLicenseCheck({ form, companyUid, brandUid, storeId }: UseLicenseCheckParams) {
  const [isCheckingLicense, setIsCheckingLicense] = useState(false)

  const checkLicense = async () => {
    const defaultCompanyUid = companyUid || '595e8cb4-674c-49f7-adec-826b211a7ce3'
    const defaultBrandUid = brandUid || 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e'
    const defaultStoreId = storeId || 'ba6e0a44-080d-4ae4-aba0-c29b79e95ab3'

    try {
      setIsCheckingLicense(true)
      const result = await syncStoreExpiryTimeProactive({
        company_uid: defaultCompanyUid,
        brand_uid: defaultBrandUid,
        id: defaultStoreId
      })

      if (result && result.expiry_time) {
        form.setValue('license_expiry', result.expiry_time)
      }

      return result
    } finally {
      setIsCheckingLicense(false)
    }
  }

  return {
    checkLicense,
    isCheckingLicense
  }
}
