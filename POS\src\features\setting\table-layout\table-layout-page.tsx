import React from 'react'

import {
  TableLayoutCanvas,
  TableLayoutHeader,
  AreaTabs,
  TableSortModal,
  ImportTablesModal,
  ImportPreviewModal,
  EditTableInfoModal,
  ConfigureTablesModal
} from './components'
import { useTableLayoutPage } from './hooks'

export const TableLayoutPage: React.FC = () => {
  const {
    selectedAreaId,
    selectedStoreId,
    selectedTable,
    localTables,
    isSortModalOpen,
    isImportModalOpen,
    isPreviewModalOpen,
    isEditTableModalOpen,
    isConfigureTableModalOpen,
    importedTableData,
    allStores,
    isLoadingStores,
    areas,
    areasLoading,
    tablesLoading,
    handleAreaChange,
    handleStoreChange,
    handleCreateNewArea,
    handleTableSelect,
    handleTablePositionUpdate,
    handleCreateNew,
    handleImportTables,
    handleEditTableInfo,
    handleDownloadExistingTableData,
    handleUploadEditedTableFile,
    handleSortTables,
    handleSortModalSave,
    handleCustomizeTable,
    handleSaveLayout,
    handleDownloadTemplate,
    handleUploadFile,
    handlePreviewSave,
    handlePreviewCancel,
    setIsSortModalOpen,
    setIsImportModalOpen,
    setIsPreviewModalOpen,
    setIsEditTableModalOpen,
    setIsConfigureTableModalOpen,
    isSaving
  } = useTableLayoutPage()

  const layoutTables = localTables

  if (isLoadingStores || areasLoading) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='text-gray-500'>{isLoadingStores ? 'Đang tải cửa hàng...' : 'Đang tải khu vực...'}</div>
      </div>
    )
  }

  return (
    <div className='flex h-screen flex-col bg-gray-50'>
      <TableLayoutHeader
        selectedStoreId={selectedStoreId}
        onStoreChange={handleStoreChange}
        onCreateNew={handleCreateNew}
        onSaveLayout={handleSaveLayout}
        isUpdating={isSaving}
        onImportTables={handleImportTables}
        onEditTableInfo={handleEditTableInfo}
        onSortTables={handleSortTables}
        onCustomizeTable={handleCustomizeTable}
      />

      <AreaTabs
        areas={areas}
        selectedAreaId={selectedAreaId}
        onAreaChange={handleAreaChange}
        onCreateNew={handleCreateNewArea}
        storeUid={selectedStoreId}
      />

      <div className='flex-1 p-4'>
        {tablesLoading ? (
          <div className='flex h-full items-center justify-center'>
            <div className='text-gray-500'>Đang tải bàn...</div>
          </div>
        ) : layoutTables.length === 0 ? (
          <div className='flex h-full items-center justify-center'>
            <div className='text-center'>
              <div className='mb-2 text-lg text-gray-500'>Chưa có thông tin bàn</div>
              <div className='text-sm text-gray-400'>Vui lòng thêm bàn mới hoặc chọn khu vực khác</div>
            </div>
          </div>
        ) : (
          <TableLayoutCanvas
            tables={layoutTables}
            onTableSelect={handleTableSelect}
            selectedTableId={selectedTable?.id}
            onTablePositionUpdate={handleTablePositionUpdate}
            storeUid={selectedStoreId}
          />
        )}
      </div>

      <TableSortModal open={isSortModalOpen} onOpenChange={setIsSortModalOpen} onSave={handleSortModalSave} />

      <ImportTablesModal
        open={isImportModalOpen}
        onOpenChange={setIsImportModalOpen}
        onDownloadTemplate={handleDownloadTemplate}
        onUploadFile={handleUploadFile}
      />

      <ImportPreviewModal
        open={isPreviewModalOpen}
        onOpenChange={setIsPreviewModalOpen}
        tableData={importedTableData}
        onSave={handlePreviewSave}
        onCancel={handlePreviewCancel}
      />

      <EditTableInfoModal
        open={isEditTableModalOpen}
        onOpenChange={setIsEditTableModalOpen}
        onDownloadExistingData={handleDownloadExistingTableData}
        onUploadFile={handleUploadEditedTableFile}
        stores={
          allStores?.map(store => ({ id: store.id, name: store.name })) || [
            { id: 'test-store-1', name: 'Cửa hàng Test 1' },
            { id: 'test-store-2', name: 'Cửa hàng Test 2' }
          ]
        }
      />

      <ConfigureTablesModal
        open={isConfigureTableModalOpen}
        onOpenChange={setIsConfigureTableModalOpen}
        onCancel={() => setIsConfigureTableModalOpen(false)}
      />
    </div>
  )
}
