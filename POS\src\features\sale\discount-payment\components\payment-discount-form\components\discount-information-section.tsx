import { usePosStores } from '@/stores/posStore'

import {
  Checkbox,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsList,
  TabsTrigger
} from '@/components/ui'

import { usePaymentDiscountFormData, usePaymentDiscountFormStatus, usePaymentDiscountPromotions } from '../stores'
import { PromotionSelector } from './promotion-selector'

export function DiscountInformationSection() {
  const { formData, updateFormData } = usePaymentDiscountFormData()
  const { isEditMode } = usePaymentDiscountFormStatus()
  const { promotions, isLoadingPromotions } = usePaymentDiscountPromotions()
  const { currentBrandStores } = usePosStores()

  return (
    <div className='space-y-4'>
      <h2 className='text-lg font-medium text-gray-900'>Thông tin giảm giá</h2>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Áp dụng với hoá đơn từ <span className='text-red-500'>*</span>
        </Label>
        <div className='flex flex-1 items-center gap-2'>
          <Input
            type='number'
            min='0'
            value={formData.fromAmount || ''}
            onChange={e => updateFormData({ fromAmount: Number(e.target.value) })}
            placeholder='0'
            className='flex-1'
          />
          <Label className='text-sm font-medium'>
            Đến <span className='text-red-500'>*</span>
          </Label>
          <Input
            type='number'
            min='0'
            value={formData.toAmount || ''}
            onChange={e => updateFormData({ toAmount: Number(e.target.value) })}
            placeholder='0'
            className='flex-1'
          />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          {formData.discountType === 'PERCENT' ? 'Phần trăm giảm giá' : 'Số tiền giảm giá'}{' '}
          <span className='text-red-500'>*</span>
        </Label>
        <div className='flex flex-1 gap-2'>
          <Input
            type='number'
            min='0'
            max={formData.discountType === 'PERCENT' ? '100' : undefined}
            value={formData.discountValue || ''}
            onChange={e => {
              const value = Number(e.target.value)
              if (formData.discountType === 'PERCENT') {
                const cappedValue = value > 100 ? 100 : value
                updateFormData({ discountValue: cappedValue })
              } else {
                updateFormData({ discountValue: value })
              }
            }}
            placeholder='0'
            className='flex-1'
          />
          <Tabs
            value={formData.discountType}
            onValueChange={value => updateFormData({ discountType: value as 'PERCENT' | 'AMOUNT' })}
            className='w-auto'
          >
            <TabsList className='grid w-fit grid-cols-2'>
              <TabsTrigger value='PERCENT'>%</TabsTrigger>
              <TabsTrigger value='AMOUNT'>đ</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Cửa hàng <span className='text-red-500'>*</span>
        </Label>
        <Select
          value={formData.storeUid}
          onValueChange={value => {
            updateFormData({
              storeUid: value,
              promotionUid: '',
              promotionName: ''
            })
          }}
          disabled={isEditMode}
        >
          <SelectTrigger className='flex-1'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {currentBrandStores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.store_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <PromotionSelector promotions={promotions} isLoading={isLoadingPromotions} disabled={isLoadingPromotions} />

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>Là phiếu giảm giá (tính sau VAT)</Label>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='isDiscountVoucher'
            checked={formData.isDiscountVoucher}
            onCheckedChange={checked => {
              updateFormData({
                isDiscountVoucher: checked as boolean,
                // Reset quantity input when unchecking voucher
                requiresQuantityInput: checked ? formData.requiresQuantityInput : false
              })
            }}
          />
          <Label htmlFor='isDiscountVoucher' className='cursor-pointer text-sm font-normal'>
            Áp dụng phiếu giảm giá
          </Label>
        </div>
      </div>

      {formData.isDiscountVoucher && !isEditMode && (
        <div className='flex items-center gap-4'>
          <Label className='min-w-[200px] text-sm font-medium'>Phiếu giảm giá yêu cầu nhập số lượng</Label>
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='requiresQuantityInput'
              checked={formData.requiresQuantityInput}
              onCheckedChange={checked => updateFormData({ requiresQuantityInput: checked as boolean })}
            />
            <Label htmlFor='requiresQuantityInput' className='cursor-pointer text-sm font-normal'>
              Yêu cầu nhập số lượng
            </Label>
          </div>
        </div>
      )}
    </div>
  )
}
