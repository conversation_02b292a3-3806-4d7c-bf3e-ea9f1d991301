import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/crm/report/sale-manager')({
  component: SaleManagerReportPage,
})

function SaleManagerReportPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Bảng kê hóa đơn</h1>
        <p className="text-gray-600 mt-2">
          Tổng hợp và quản lý danh sách hóa đơn bán hàng
        </p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Bảng kê hóa đơn
          </h3>
          <p className="text-gray-500">
            Nội dung báo cáo sẽ được phát triển ở giai đoạn tiếp theo
          </p>
        </div>
      </div>
    </div>
  )
}
