import { z } from 'zod'

import { createFileRoute } from '@tanstack/react-router'

import { MembershipDiscountForm } from '@/features/sale/discount/membership/components/membership-discount-form'

const editMembershipDiscountSearchSchema = z.object({
  store_uid: z.string().optional()
})

export const Route = createFileRoute('/_authenticated/sale/discount/membership/detail/$id')({
  component: EditMembershipDiscountPage,
  validateSearch: editMembershipDiscountSearchSchema
})

function EditMembershipDiscountPage() {
  const { id } = Route.useParams()
  const { store_uid: storeUid } = Route.useSearch()

  console.log('🔥 Membership Discount Detail Page - URL Params:')
  console.log('🔥 discountId:', id)
  console.log('🔥 storeUid:', storeUid)

  return <MembershipDiscountForm discountId={id} storeUid={storeUid} />
}
