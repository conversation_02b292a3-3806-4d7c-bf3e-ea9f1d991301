import { useState } from 'react'

import type { DiscountPayment } from '@/types/discount-payment'

import { usePosStores, useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useDiscountPaymentPrograms, useCloneDiscountPayments } from '@/hooks/api/use-discount-payment'

import { PosModal } from '@/components/pos/modal'
import { Checkbox, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

interface CopyDiscountPaymentModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CopyDiscountPaymentModal({ isOpen, onClose }: CopyDiscountPaymentModalProps) {
  const [sourceStoreId, setSourceStoreId] = useState<string>('')
  const [selectedTargetStores, setSelectedTargetStores] = useState<Set<string>>(new Set())
  const [selectedPrograms, setSelectedPrograms] = useState<Set<string>>(new Set())

  const { currentBrandStores } = usePosStores()
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  // Clone discount payment mutation
  const cloneDiscountPaymentMutation = useCloneDiscountPayments()

  const { data: discountPaymentPrograms = [], isLoading: isLoadingPrograms } = useDiscountPaymentPrograms({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    store_uid: sourceStoreId
  })

  // Filter target stores (exclude source store)
  const targetStores = currentBrandStores.filter(store => store.id !== sourceStoreId)

  const handleProgramToggle = (programId: string) => {
    const newSelected = new Set(selectedPrograms)
    if (newSelected.has(programId)) {
      newSelected.delete(programId)
    } else {
      newSelected.add(programId)
    }
    setSelectedPrograms(newSelected)
  }

  const handleTargetStoreToggle = (storeId: string) => {
    const newSelected = new Set(selectedTargetStores)
    if (newSelected.has(storeId)) {
      newSelected.delete(storeId)
    } else {
      newSelected.add(storeId)
    }
    setSelectedTargetStores(newSelected)
  }

  const handleSelectAllTargetStores = () => {
    if (selectedTargetStores.size === targetStores.length) {
      // If all are selected, deselect all
      setSelectedTargetStores(new Set())
    } else {
      // Select all stores
      setSelectedTargetStores(new Set(targetStores.map(store => store.id)))
    }
  }

  const handleCopy = async () => {
    if (
      !company?.id ||
      !selectedBrand?.id ||
      !sourceStoreId ||
      selectedTargetStores.size === 0 ||
      selectedPrograms.size === 0
    ) {
      return
    }

    try {
      const selectedProgramIds = Array.from(selectedPrograms)
      const targetStoreIds = Array.from(selectedTargetStores)

      // Call API for each target store sequentially
      console.log('🎯 Starting copy operation:', {
        programs: selectedProgramIds.length,
        targetStores: targetStoreIds.length,
        sourceStore: sourceStoreId
      })

      for (const targetStoreId of targetStoreIds) {
        console.log(`📤 Copying ${selectedProgramIds.length} programs to store: ${targetStoreId}`)
        try {
          const result = await cloneDiscountPaymentMutation.mutateAsync({
            company_uid: company.id,
            brand_uid: selectedBrand.id,
            list_discount_payment_uid: selectedProgramIds,
            store_uid_root: sourceStoreId,
            store_uid_target: targetStoreId
          })
          console.log(`✅ Successfully copied to store: ${targetStoreId}`, result)
        } catch (error) {
          console.error(`❌ Failed to copy to store: ${targetStoreId}`, error)
          // Continue with other stores instead of throwing
          console.warn(`⚠️ Continuing with remaining stores...`)
        }
      }

      // Close modal after successful copy
      handleClose()
    } catch (error) {
      console.error('Error copying discount payment programs:', error)
      // Error handling will be managed by the mutation's onError if needed
    }
  }

  const handleClose = () => {
    setSourceStoreId('')
    setSelectedTargetStores(new Set())
    setSelectedPrograms(new Set())
    onClose()
  }

  const formatAmount = (discountPayment: DiscountPayment) => {
    if (discountPayment.type === 'PERCENT') {
      // Convert 0.25 to 25%
      return `${discountPayment.value * 100}%`
    }
    return `${discountPayment.value.toLocaleString('vi-VN')} ₫`
  }

  const selectedProgramsData = discountPaymentPrograms.filter(program => selectedPrograms.has(program.id))

  return (
    <PosModal
      title='Sao chép chiết khấu thanh toán'
      open={isOpen}
      onOpenChange={handleClose}
      onCancel={handleClose}
      onConfirm={handleCopy}
      confirmText={`Sao chép`}
      confirmDisabled={
        !sourceStoreId ||
        selectedTargetStores.size === 0 ||
        selectedPrograms.size === 0 ||
        cloneDiscountPaymentMutation.isPending
      }
      maxWidth='sm:max-w-6xl'
    >
      {/* Two Panel Layout */}
      <div className='grid grid-cols-2 gap-6'>
        {/* Left Panel - Source Store */}
        <div className='space-y-4'>
          <div>
            <label className='text-sm font-medium'>Cửa hàng nguồn</label>
            <Select value={sourceStoreId} onValueChange={setSourceStoreId}>
              <SelectTrigger className='mt-1'>
                <SelectValue placeholder='Chọn cửa hàng nguồn' />
              </SelectTrigger>
              <SelectContent>
                {currentBrandStores.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.store_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Discount Payment Programs List */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Chiết khấu thanh toán</label>
            <div className='max-h-64 overflow-y-auto rounded-md border p-3'>
              {isLoadingPrograms ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>Đang tải...</div>
              ) : discountPaymentPrograms.length === 0 ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>
                  {sourceStoreId ? 'Không có chiết khấu thanh toán' : 'Chọn cửa hàng nguồn'}
                </div>
              ) : (
                <div className='space-y-3'>
                  {discountPaymentPrograms.map(program => (
                    <div key={program.id} className='flex items-start space-x-3'>
                      <Checkbox
                        id={program.id}
                        checked={selectedPrograms.has(program.id)}
                        onCheckedChange={() => handleProgramToggle(program.id)}
                      />
                      <div className='min-w-0 flex-1'>
                        <div className='text-sm font-medium'>{program.promotion?.promotion_name || 'Chiết khấu'}</div>
                        <div className='text-xs text-gray-500'>Giảm {formatAmount(program)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Panel - Target Stores */}
        <div className='space-y-4'>
          <div>
            <label className='text-sm font-medium'>Cửa hàng đích</label>
            <Select>
              <SelectTrigger className='mt-1'>
                <SelectValue
                  placeholder={
                    selectedTargetStores.size === 0
                      ? 'Chọn cửa hàng đích'
                      : `Đã chọn ${selectedTargetStores.size} cửa hàng`
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {targetStores.length === 0 ? (
                  <div className='text-muted-foreground py-4 text-center text-sm'>Chọn cửa hàng nguồn trước</div>
                ) : (
                  <>
                    {/* Select All Option */}
                    <div className='flex items-center space-x-3 border-b px-2 py-1.5'>
                      <Checkbox
                        id='select-all-targets'
                        checked={selectedTargetStores.size === targetStores.length && targetStores.length > 0}
                        onCheckedChange={handleSelectAllTargetStores}
                        onClick={e => e.stopPropagation()}
                      />
                      <label
                        htmlFor='select-all-targets'
                        className='flex-1 cursor-pointer text-sm font-medium'
                        onClick={e => {
                          e.preventDefault()
                          handleSelectAllTargetStores()
                        }}
                      >
                        Chọn tất cả
                      </label>
                    </div>

                    {/* Individual Store Options */}
                    {targetStores.map(store => (
                      <div key={store.id} className='flex items-center space-x-3 px-2 py-1.5'>
                        <Checkbox
                          id={`target-${store.id}`}
                          checked={selectedTargetStores.has(store.id)}
                          onCheckedChange={() => handleTargetStoreToggle(store.id)}
                          onClick={e => e.stopPropagation()}
                        />
                        <label
                          htmlFor={`target-${store.id}`}
                          className='flex-1 cursor-pointer text-sm font-medium'
                          onClick={e => {
                            e.preventDefault()
                            handleTargetStoreToggle(store.id)
                          }}
                        >
                          {store.store_name}
                        </label>
                      </div>
                    ))}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Selected Programs Display */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Chiết khấu thanh toán được chọn</label>
            <div className='max-h-64 overflow-y-auto rounded-md border p-3'>
              {selectedProgramsData.length === 0 ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>
                  Chưa chọn chiết khấu thanh toán nào
                </div>
              ) : (
                <div className='space-y-2'>
                  {selectedProgramsData.map(program => (
                    <div key={program.id} className='bg-muted rounded-md p-2'>
                      <div className='text-sm font-medium'>{program.promotion?.promotion_name || 'Chiết khấu'}</div>
                      <div className='text-xs text-gray-500'>Giảm {formatAmount(program)}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </PosModal>
  )
}
