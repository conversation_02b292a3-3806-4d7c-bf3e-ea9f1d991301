import type { Table } from '@/lib/table-layout-api'

export interface TableLayoutItem extends Table {
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
}

export interface TableLayoutState {
  tables: TableLayoutItem[]
  selectedTable: TableLayoutItem | null
  isDragging: boolean
  draggedTable: TableLayoutItem | null
}

export interface DragEndEvent {
  active: {
    id: string
    data: {
      current: TableLayoutItem
    }
  }
  over?: {
    id: string
  } | null
  delta: {
    x: number
    y: number
  }
}

export interface TablePosition {
  id: string
  x: number
  y: number
  sort: number
}

export interface AreaOption {
  id: string
  area_name: string
  area_id: string
}
