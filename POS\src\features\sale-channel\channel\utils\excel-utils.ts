import type { Channel } from '@/types/channels'
import ExcelJS from 'exceljs'

/**
 * Create a new Excel workbook with ExcelJS
 */
export const createExcelWorkbook = (): ExcelJS.Workbook => {
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'POS System'
  workbook.lastModifiedBy = 'POS System'
  workbook.created = new Date()
  workbook.modified = new Date()
  return workbook
}

/**
 * Transform channel data to Excel row format
 */
export const transformChannelToExcelRow = (channel: Channel): any[] => {
  const extraData = channel.extra_data || {}
  return [
    channel.source_id || '',
    extraData.commission || 0,
    extraData.not_show_partner_bill || 0,
    extraData.use_order_online || 0,
    extraData.exclude_ship || 0,
    extraData.payment_type || 'POSTPAID',
    extraData.payment_method_id || '',
    extraData.require_tran_no || 0,
    extraData.marketing_partner_cost_type || 'AMOUNT',
    extraData.marketing_partner_cost || 0,
    extraData.voucher_run_partner || '',
    extraData.marketing_partner_cost_from_date
      ? new Date(extraData.marketing_partner_cost_from_date * 1000).toLocaleDateString('vi-VN')
      : '',
    extraData.marketing_partner_cost_to_date
      ? new Date(extraData.marketing_partner_cost_to_date * 1000).toLocaleDateString('vi-VN')
      : '',
    extraData.marketing_partner_cost_date_week || 0,
    extraData.marketing_partner_cost_hour_day || 0
  ]
}

/**
 * Create main worksheet with channel data using ExcelJS
 */
export const createMainWorksheet = (
  workbook: ExcelJS.Workbook,
  channels: Channel[]
): ExcelJS.Worksheet => {
  const worksheet = workbook.addWorksheet('Sheet')

  // Define headers
  const headers = [
    'Mã nguồn',
    'Phần trăm hoa hồng',
    'Sử dụng hóa đơn dành cho khách hàng khi in',
    'Áp dụng đơn online',
    'Không tính phí vận chuyển vào doanh thu',
    'Hình thức thanh toán',
    'Mã PTTT',
    'Nhập số hoá đơn đối tác',
    'Loại chi phí',
    'Số tiền|Phần trăm / đơn',
    'Voucher',
    'Ngày bắt đầu',
    'Ngày kết thúc',
    'Chọn ngày',
    'Chọn giờ'
  ]

  // Add header row
  const headerRow = worksheet.addRow(headers)

  // Style header row
  headerRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0560A6' }
    }
    cell.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      size: 11
    }
    cell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  // Add data rows
  channels.forEach((channel: Channel) => {
    const rowData = transformChannelToExcelRow(channel)
    const dataRow = worksheet.addRow(rowData)

    // Style data rows - only borders, no background color
    dataRow.eachCell((cell: any) => {
      cell.font = {
        size: 10
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
      }
    })
  })

  // Set column widths
  const columnWidths = [
    17.3, 20.6, 44.5, 19.3, 42.4, 21.7, 50.1, 24.2, 12.6, 23.6, 11.9, 15.0, 15.0, 11.3, 9.5
  ]
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width
  })

  return worksheet
}

/**
 * Create example worksheet with reference data using ExcelJS
 */
export const createExampleWorksheet = (
  workbook: ExcelJS.Workbook,
  channels: Channel[] = []
): ExcelJS.Worksheet => {
  const exampleWorksheet = workbook.addWorksheet('Example')

  // Add instruction row
  exampleWorksheet.addRow([
    'Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet 1 để nhập dữ liệu.'
  ])

  // Add header row
  const headers = [
    'Mã nguồn',
    'Phần trăm hoa hồng',
    'Sử dụng hóa đơn dành cho khách hàng khi in',
    'Áp dụng đơn online',
    'Không tính phí vận chuyển vào doanh thu',
    'Hình thức thanh toán',
    'Mã PTTT',
    'Nhập số hoá đơn đối tác',
    'Loại chi phí',
    'Số tiền|Phần trăm / đơn',
    'Voucher',
    'Ngày bắt đầu',
    'Ngày kết thúc',
    'Chọn ngày',
    'Chọn giờ'
  ]

  const headerRow = exampleWorksheet.addRow(headers)

  // Style header row
  headerRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF79ABE3' }
    }
    cell.font = { bold: true, size: 11 }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  // Add example data
  const exampleData = [
    [
      'SOURCE-AE3F',
      0,
      '0 (Không sử dụng hoá đơn dành cho khách khi in)',
      0,
      0,
      'PREPAID (Trả trước)',
      '(Không cần điền với trả trước)',
      0,
      'AMOUNT',
      10000,
      '',
      '01-02-2023​',
      '01-03-2023​',
      38,
      1324
    ],
    [
      'SOURCE-1XZ2',
      20,
      '1 (Sử dụng hoá đơn dành cho khách khi in)',
      1,
      1,
      'POSTPAID (Trả sau)',
      'MASTER',
      1,
      'PERCENT',
      25,
      '',
      '',
      '01-03-2023​',
      38,
      8264
    ],
    [
      'SOURCE-H250',
      40,
      '2 (In hóa đơn chỉ với phần giảm giá nhà hàng)',
      '',
      '',
      'POSTPAID (Trả sau)',
      'FABI_DEBT',
      '',
      'AMOUNT',
      '',
      '',
      '01-02-2023​',
      '',
      64,
      8193
    ]
  ]

  exampleData.forEach(rowData => {
    exampleWorksheet.addRow(rowData)
  })

  // Add empty row and reference headers
  exampleWorksheet.addRow([])

  // Add reference table headers with dark blue background
  const referenceHeaderRow = exampleWorksheet.addRow([
    'Bảng tham chiếu nguồn',
    '',
    '',
    'Bảng tham chiếu PTTT',
    '',
    '',
    'BẢNG THAM CHIẾU NGÀY',
    '',
    '',
    'BẢNG THAM CHIẾU GIỜ'
  ])

  // Style reference table headers (dark blue)
  referenceHeaderRow.eachCell((cell: any) => {
    if (cell.value && cell.value.toString().trim() !== '') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF0560A6' } // Dark blue
      }
      cell.font = {
        color: { argb: 'FFFFFFFF' },
        bold: true,
        size: 11
      }
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }
  })

  // Add column headers with light blue background
  const columnHeaderRow = exampleWorksheet.addRow([
    'Tên nguồn',
    'Mã nguồn',
    '',
    'Tên PTTT',
    'Mã PTTT',
    '',
    'Thời gian',
    'Giá trị',
    '',
    'Thời gian',
    'Giá trị'
  ])

  // Style column headers (light blue)
  columnHeaderRow.eachCell((cell: any) => {
    if (cell.value && cell.value.toString().trim() !== '') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF79ABE3' } // Light blue
      }
      cell.font = {
        bold: true,
        size: 10
      }
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }
  })

  // Generate reference data from actual channels
  const sourceReferenceData: string[][] = []
  const paymentReferenceData: string[][] = []

  // Extract unique sources and payment methods from channels
  const uniqueSources = new Map<string, string>()
  const uniquePaymentMethods = new Map<string, string>()

  channels.forEach((channel: Channel) => {
    // Add source data
    if (channel.source_id && channel.source_name) {
      uniqueSources.set(channel.source_id, channel.source_name)
    }

    // Add payment method data
    const extraData = channel.extra_data || {}
    if (extraData.payment_method_id && extraData.payment_method_name) {
      uniquePaymentMethods.set(extraData.payment_method_id, extraData.payment_method_name)
    }
  })

  // Convert to array format for sources
  Array.from(uniqueSources.entries()).forEach(([sourceId, sourceName]) => {
    sourceReferenceData.push([`${sourceName} (nguồn đã chọn)`, sourceId])
  })

  // Convert to array format for payment methods
  Array.from(uniquePaymentMethods.entries()).forEach(([methodId, methodName]) => {
    paymentReferenceData.push([methodName, methodId])
  })

  // Static reference data for days and hours
  // Day values use bit flags: CN=2, T2=4, T3=8, T4=16, T5=32, T6=64, T7=128
  // Example: CN + T2 + T5 = 2 + 4 + 32 = 38
  const dayReferenceData = [
    ['Chủ nhật', '2'],
    ['Thứ 2', '4'],
    ['Thứ 3', '8'],
    ['Thứ 4', '16'],
    ['Thứ 5', '32'],
    ['Thứ 6', '64'],
    ['Thứ 7', '128'],
    ['Ví dụ: CN, T2, T5 = 2 + 4 + 32', '38']
  ]

  // Hour values use bit flags: 0h=1, 1h=2, 2h=4, 3h=8, etc. (powers of 2)
  // Example: 0h + 1h + 3h = 1 + 2 + 8 = 11
  const hourReferenceData = [
    ['0h', '1'],
    ['1h', '2'],
    ['2h', '4'],
    ['3h', '8'],
    ['4h', '16'],
    ['5h', '32'],
    ['6h', '64'],
    ['7h', '128'],
    ['8h', '256'],
    ['9h', '512'],
    ['10h', '1024'],
    ['11h', '2048'],
    ['12h', '4096'],
    ['13h', '8192'],
    ['14h', '16384'],
    ['15h', '32768'],
    ['16h', '65536'],
    ['17h', '131072'],
    ['18h', '262144'],
    ['19h', '524288'],
    ['20h', '1048576'],
    ['21h', '2097152'],
    ['22h', '4194304'],
    ['23h', '8388608'],
    ['Ví dụ: 0h, 1h, 3h = 1 + 2 + 8', '11']
  ]

  // Combine all reference data into rows
  const maxRows = Math.max(
    sourceReferenceData.length,
    paymentReferenceData.length,
    dayReferenceData.length,
    hourReferenceData.length
  )

  const referenceData: string[][] = []
  for (let i = 0; i < maxRows; i++) {
    const row = [
      sourceReferenceData[i]?.[0] || '', // Source name
      sourceReferenceData[i]?.[1] || '', // Source ID
      '', // Empty column
      paymentReferenceData[i]?.[0] || '', // Payment method name
      paymentReferenceData[i]?.[1] || '', // Payment method ID
      '', // Empty column
      dayReferenceData[i]?.[0] || '', // Day name
      dayReferenceData[i]?.[1] || '', // Day value
      '', // Empty column
      hourReferenceData[i]?.[0] || '', // Hour name
      hourReferenceData[i]?.[1] || '' // Hour value
    ]
    referenceData.push(row)
  }

  referenceData.forEach(rowData => {
    const dataRow = exampleWorksheet.addRow(rowData)

    // Style reference data rows
    dataRow.eachCell((cell: any) => {
      if (cell.value && cell.value.toString().trim() !== '') {
        cell.font = {
          size: 10
        }
        cell.alignment = {
          horizontal: 'left',
          vertical: 'middle'
        }
        cell.border = {
          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
        }
      }
    })
  })

  // Set column widths
  const columnWidths = [
    25.8, 20.6, 49.2, 19.3, 42.4, 21.7, 29.2, 24.2, 12.6, 23.6, 9.0, 16.1, 16.1, 11.3, 9.5
  ]
  columnWidths.forEach((width, index) => {
    exampleWorksheet.getColumn(index + 1).width = width
  })

  return exampleWorksheet
}
