export interface UsingDailyItem {
  id: string
  pos_parent: string
  date_hash: string
  year: number
  month: number
  day: number
  voucher_published: number
  used_voucher_amount: number
  voucher_many_time_used: number
  sms_message: number
  zalo_message: number
  facebook_message: number
  zns_message: number
  zalo_transaction_message: number
  zns_send_by_app_mc: number
  promo_send_by_app_mc: number
  trans_send_by_app_mc: number
  cs_send_by_app_mc: number
  updated_at: string
  created_at: string
}

export interface UsingDailyResponse {
  count: number
  totalPage: number
  list_rp_daily_billing: UsingDailyItem[]
}

export interface UsingDailyParams {
  date_start: string
  date_end: string
  number_per_page: number
  pos_parent: string
}
