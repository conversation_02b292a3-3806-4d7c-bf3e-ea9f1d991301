import React from 'react'

import { Download, Upload } from 'lucide-react'

import { Button, Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui'

interface ImportTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onDownloadTemplate: () => void
  onUploadFile: () => void
}

export const ImportTablesModal: React.FC<ImportTablesModalProps> = ({
  open,
  onOpenChange,
  onDownloadTemplate,
  onUploadFile
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='text-xl font-semibold'>Thêm bàn</DialogTitle>
        </DialogHeader>

        <div className='space-y-6 py-4'>
          {/* Bước 1: Tải file mẫu */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='font-medium text-gray-900'>Bước 1. Tải file mẫu</h3>
              </div>
              <Button variant='outline' size='sm' onClick={onDownloadTemplate} className='flex items-center gap-2'>
                <Download className='h-4 w-4' />
                Tải xuống
              </Button>
            </div>
          </div>

          {/* Bước 2: Thêm bàn vào file */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 font-medium text-gray-900'>Bước 2. Thêm bàn vào file</h3>
            <div className='space-y-2 text-sm text-gray-600'>
              <div>- Không sửa lại tên dòng tiêu đề</div>
              <div>- Nhập thông tin bàn vào các dòng sau đó</div>
            </div>
          </div>

          {/* Bước 3: Tải file bàn lên */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='mb-2 font-medium text-gray-900'>Bước 3. Tải file bàn lên</h3>
                <p className='text-sm text-gray-600'>Sau khi đã điền đầy đủ bàn bạn có thể tải file lên</p>
              </div>
              <Button variant='outline' size='sm' onClick={onUploadFile} className='flex items-center gap-2'>
                <Upload className='h-4 w-4' />
                Tải file lên
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
