import type { UseFormReturn } from 'react-hook-form'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  Label
} from '@/components/ui'

import { MOMO_AUTO_CHECK_OPTIONS, type StoreFormValues } from '../../../../data'

interface MoMoQrSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function MoMoQrSection({ form, isLoading = false }: MoMoQrSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'><PERSON><PERSON>u hình cho MoMo QR Đa Năng</h2>

      <FormField
        control={form.control}
        name='auto_check_momo_aio'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-start gap-4'>
              <FormLabel className='w-[200px] pt-2'>Tự động kiểm tra thanh toán từ MoMo QR Đa Năng</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={value => field.onChange(Number(value))}
                  value={field.value.toString()}
                  className='flex-1 space-y-4'
                  disabled={isLoading}
                >
                  {MOMO_AUTO_CHECK_OPTIONS.map(option => (
                    <div key={option.value} className='flex items-start space-x-3'>
                      <RadioGroupItem value={option.value.toString()} id={option.value.toString()} className='mt-1' />
                      <Label htmlFor={option.value.toString()} className='cursor-pointer text-sm leading-relaxed'>
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
