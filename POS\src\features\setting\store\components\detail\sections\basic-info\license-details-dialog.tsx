import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui'

interface LicenseDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  licenseData?: LicenseDetailsData
}

export interface LicenseDetailsData {
  storeName: string
  status: 'active' | 'inactive' | 'expired'
  startDate: string
  endDate: string
  type: string
  productCode: string
  serviceCode: string
}

export function LicenseDetailsDialog({ open, onOpenChange, licenseData }: LicenseDetailsDialogProps) {
  if (!licenseData) return null

  const formatStatus = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className='inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800'>
            ACTIVE
          </span>
        )
      case 'inactive':
        return (
          <span className='inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800'>
            INACTIVE
          </span>
        )
      case 'expired':
        return (
          <span className='inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800'>
            EXPIRED
          </span>
        )
      default:
        return status
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>Thông tin gói bản quyền</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <label className='text-sm font-medium text-gray-600'>Cửa hàng</label>
              <p className='text-sm text-gray-900'>{licenseData.storeName}</p>
            </div>
            <div>
              <label className='col-end-1 text-sm font-medium text-gray-600'>Trạng thái</label>
              <div className='mt-1'>{formatStatus(licenseData.status)}</div>
            </div>
          </div>

          <div className='grid grid-cols-2 gap-4'>
            <div>
              <label className='text-sm font-medium text-gray-600'>Ngày bắt đầu</label>
              <p className='text-sm text-gray-900'>{licenseData.startDate}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-600'>Ngày kết thúc</label>
              <p className='text-sm text-gray-900'>{licenseData.endDate}</p>
            </div>
          </div>

          <div className='grid grid-cols-2 gap-4'>
            <div>
              <label className='text-sm font-medium text-gray-600'>Loại</label>
              <p className='text-sm text-gray-900'>{licenseData.type}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-600'>Product Code</label>
              <p className='text-sm text-gray-900'>{licenseData.productCode}</p>
            </div>
          </div>

          <div>
            <label className='text-sm font-medium text-gray-600'>Service Code</label>
            <p className='text-sm text-gray-900'>{licenseData.serviceCode}</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
