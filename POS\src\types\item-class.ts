import { z } from 'zod'

// Item Class status schema
const itemClassStatusSchema = z.enum(['active', 'inactive'])

// Item Class schema based on API response
const itemClassSchema = z.object({
  id: z.string(),
  item_class_id: z.string(),
  item_class_name: z.string(),
  description: z.string().nullable(),
  sort: z.number(),
  extra_data: z.any().nullable(),
  active: z.number(),
  revision: z.number(),
  brand_uid: z.string(),
  company_uid: z.string(),
  created_by: z.string().nullable(),
  updated_by: z.string().nullable(),
  deleted_by: z.string().nullable(),
  created_at: z.number(),
  updated_at: z.number(),
  deleted_at: z.number().nullable(),
  deleted: z.boolean(),
  list_item: z.array(z.string()).optional(),
  // Computed fields
  status: itemClassStatusSchema.optional(),
  isActive: z.boolean().optional()
})

// API response schema
const itemClassApiResponseSchema = z.object({
  data: z.array(itemClassSchema),
  track_id: z.string()
})

// Export types
export type ItemClass = z.infer<typeof itemClassSchema>
export type ItemClassStatus = z.infer<typeof itemClassStatusSchema>
export type ItemClassApiResponse = z.infer<typeof itemClassApiResponseSchema>

// Export schemas
export { itemClassSchema, itemClassStatusSchema, itemClassApiResponseSchema }

// API data interface (raw from API)
export interface ItemClassApiData {
  id: string
  item_class_id: string
  item_class_name: string
  description: string | null
  sort: number
  extra_data: null
  active: number
  revision: number
  brand_uid: string
  company_uid: string
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  list_item?: string[]
}

// Conversion function from API data to ItemClass
export const convertApiItemClassToItemClass = (
  apiItemClass: ItemClassApiData & { list_item?: string[] }
): ItemClass => {
  return {
    ...apiItemClass,
    status: apiItemClass.active === 1 ? 'active' : 'inactive',
    isActive: apiItemClass.active === 1
  }
}
