import { useMemo } from 'react'

import { useAreasData } from '@/hooks/api/use-areas'
import { useTablesData, useBulkUpdateTables } from '@/hooks/api/use-tables'

interface UseConfigureTableActionsProps {
  selectedStoreId: string
  selectedAreaId: string
  searchTerm: string
  selectedTables: Set<string>
  config: {
    color: string
    fontSize: string
  }
  setSelectedStoreId: (id: string) => void
  setSelectedAreaId: (id: string) => void
  setSelectedTables: (tables: Set<string>) => void
  setSearchTerm: (term: string) => void
  setSelectAllInArea: (checked: boolean) => void
  setApplyToAllTables: (checked: boolean) => void
}

export const useConfigureTableActions = ({
  selectedStoreId,
  selectedAreaId,
  searchTerm,
  selectedTables,
  config,
  setSelectedStoreId,
  setSelectedAreaId,
  setSelectedTables,
  setSearchTerm,
  setSelectAllInArea,
  setApplyToAllTables
}: UseConfigureTableActionsProps) => {
  const { data: areas = [] } = useAreasData({
    storeUid: selectedStoreId,
    page: 1,
    results_per_page: 1000
  })

  const { data: tables = [], isLoading: isLoadingTables } = useTablesData({
    storeUid: selectedStoreId,
    page: 1,
    limit: 1000
  })

  const { mutateAsync: bulkUpdateTables, isPending: isUpdating } = useBulkUpdateTables()

  const filteredTables = useMemo(() => {
    if (!searchTerm) return tables
    return tables.filter(table => table.table_name.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [tables, searchTerm])

  const tablesByArea = useMemo(() => {
    const grouped: Record<string, typeof tables> = {}

    const tablesToShow = selectedAreaId
      ? filteredTables.filter(table => table.area_uid === selectedAreaId)
      : filteredTables

    tablesToShow.forEach(table => {
      const areaName = table.area?.area_name || 'Không có khu vực'
      if (!grouped[areaName]) {
        grouped[areaName] = []
      }
      grouped[areaName].push(table)
    })
    return grouped
  }, [filteredTables, selectedAreaId])

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('')
    setSelectedTables(new Set())
    setSearchTerm('')
    setSelectAllInArea(false)
    setApplyToAllTables(false)
  }

  const handleTableSelect = (tableId: string) => {
    const newSelected = new Set(selectedTables)
    if (newSelected.has(tableId)) {
      newSelected.delete(tableId)
    } else {
      newSelected.add(tableId)
    }
    setSelectedTables(newSelected)
  }

  const handleSelectAllInArea = (checked: boolean) => {
    setSelectAllInArea(checked)
    if (checked && selectedAreaId) {
      const areaName = areas.find(area => area.id === selectedAreaId)?.area_name
      if (areaName && tablesByArea[areaName]) {
        const areaTableIds = tablesByArea[areaName].map(table => table.id)
        setSelectedTables(new Set([...selectedTables, ...areaTableIds]))
      }
    } else if (!checked && selectedAreaId) {
      const areaName = areas.find(area => area.id === selectedAreaId)?.area_name
      if (areaName && tablesByArea[areaName]) {
        const areaTableIds = new Set(tablesByArea[areaName].map(table => table.id))
        const newSelected = new Set([...selectedTables].filter(id => !areaTableIds.has(id)))
        setSelectedTables(newSelected)
      }
    }
  }

  const handleApplyToAllTables = (checked: boolean) => {
    setApplyToAllTables(checked)
    if (checked) {
      const allTableIds = tables.map(table => table.id)
      setSelectedTables(new Set(allTableIds))
    } else {
      setSelectedTables(new Set())
    }
  }

  const handleSave = async () => {
    if (selectedTables.size === 0) {
      return
    }

    try {
      const selectedTableObjects = tables.filter(table => selectedTables.has(table.id))

      const updatedTables = selectedTableObjects.map(table => ({
        ...table,
        store_uid: table.store_uid || selectedStoreId,
        company_uid: table.company_uid || '',
        brand_uid: table.brand_uid || '',
        source_id: table.source_id || '',
        area_uid: table.area_uid || '',
        sort: table.sort || 1,
        description: table.description || '',
        extra_data: {
          ...table.extra_data,
          color: config.color,
          font_size: config.fontSize,
          order_list: table.extra_data?.order_list || []
        }
      }))

      await bulkUpdateTables({
        storeUid: selectedStoreId,
        tables: updatedTables
      })

      return true
    } catch (error) {
      console.error('Error updating tables:', error)
      return false
    }
  }

  return {
    // Data
    areas,
    tables,
    filteredTables,
    tablesByArea,
    isLoadingTables,
    isUpdating,

    // Actions
    handleStoreChange,
    handleTableSelect,
    handleSelectAllInArea,
    handleApplyToAllTables,
    handleSave
  }
}
