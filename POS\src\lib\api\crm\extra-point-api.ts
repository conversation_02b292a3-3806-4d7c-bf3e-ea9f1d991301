import type {
  ExtraPointResponse,
  ExtraPointParams,
  UpdateExtraPointRequest,
  UpdateExtraPointParams,
  CreateExtraPointRequest,
  CreateExtraPointParams
} from '@/types/api/crm'

import { crmApi } from './crm-api'

export const extraPointApi = {
  getList: async (params: ExtraPointParams): Promise<ExtraPointResponse> => {
    const response = await crmApi.get('/loyalty/get-list-extra-point', {
      params
    })
    return response.data
  },

  create: async (data: CreateExtraPointRequest, params: CreateExtraPointParams): Promise<any> => {
    const response = await crmApi.post('/loyalty/create-extra-point', data, {
      params
    })
    return response.data
  },

  update: async (data: UpdateExtraPointRequest, params: UpdateExtraPointParams): Promise<any> => {
    const response = await crmApi.post('/loyalty/update-extra-point', data, {
      params
    })
    return response.data
  }
}
