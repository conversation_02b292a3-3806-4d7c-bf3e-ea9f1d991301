import { z } from 'zod'

import { createFileRoute } from '@tanstack/react-router'

import { PaymentDiscountForm } from '@/features/sale/discount-payment/components/payment-discount-form'

const editPaymentDiscountSearchSchema = z.object({
  store_uid: z.string().optional()
})

export const Route = createFileRoute('/_authenticated/sale/discount-payment/detail/$id')({
  component: EditPaymentDiscountPage,
  validateSearch: editPaymentDiscountSearchSchema
})

function EditPaymentDiscountPage() {
  const { id } = Route.useParams()
  const { store_uid: storeUid } = Route.useSearch()

  console.log('🔥 Payment Discount Detail Page - URL Params:')
  console.log('🔥 discountId:', id)
  console.log('🔥 storeUid:', storeUid)

  return <PaymentDiscountForm discountId={id} storeUid={storeUid} />
}
