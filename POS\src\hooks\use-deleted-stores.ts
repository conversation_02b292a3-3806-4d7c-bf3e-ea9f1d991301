import { useMemo, useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { deletedOrdersApi } from '@/lib/deleted-orders-api'

// Generate cache key with date range
const generateCacheKey = (startTime: number, endTime: number) => {
  return `deleted-stores-${startTime}-${endTime}`
}

// Cache data structure
interface CachedDeletedStoresData {
  topStores: StoreDeletedStats[]
  totalStores: number
  totalDeleted: number
  timestamp: number
}

// Save data to localStorage
const saveToCache = (key: string, data: CachedDeletedStoresData) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (_error) {
    // Ignore localStorage errors
  }
}

// Load data from localStorage
const loadFromCache = (key: string): CachedDeletedStoresData | null => {
  try {
    const cached = localStorage.getItem(key)
    if (cached) {
      const parsed = JSON.parse(cached) as CachedDeletedStoresData
      // Check if cache is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      if (Date.now() - parsed.timestamp < maxAge) {
        return parsed
      }
    }
  } catch (_error) {
    // Ignore localStorage errors
  }
  return null
}

interface StoreDeletedStats {
  storeUid: string
  storeName: string
  totalDeleted: number
  totalDeletedAmount: number
}

interface UseDeletedStoresOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  limit?: number
  autoFetch?: boolean
}

interface UseDeletedStoresReturn {
  topStores: StoreDeletedStats[]
  isLoading: boolean
  error: string | null
  totalStores: number
  totalDeleted: number
  refetch: () => void
}

/**
 * Query keys for deleted stores-related queries
 */
export const deletedStoresKeys = {
  all: ['deleted-stores'] as const,
  lists: () => [...deletedStoresKeys.all, 'list'] as const,
  list: (filters: {
    companyUid?: string
    brandUid?: string
    startDate?: number
    endDate?: number
    storeUids?: string[]
    limit?: number
    cacheKey?: string | null
  }) => [...deletedStoresKeys.lists(), filters] as const,
}

/**
 * Hook to get top stores with most deleted orders using TanStack Query
 * Optimized for performance with intelligent caching and minimal API calls
 */
export function useDeletedStores(
  options: UseDeletedStoresOptions = {}
): UseDeletedStoresReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    limit = 5,
    autoFetch = true,
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()

  // State for cached data (stale-while-revalidate)
  const [cachedData, setCachedData] = useState<CachedDeletedStoresData | null>(
    null
  )
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true)

  // Calculate date range - normalize to start/end of day for consistent cache keys
  const { startTime, endTime } = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      return {
        startTime: null,
        endTime: null,
      }
    }

    // Normalize to start of day for 'from' and end of day for 'to'
    const startOfDay = new Date(dateRange.from)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(dateRange.to)
    endOfDay.setHours(23, 59, 59, 999)

    return {
      startTime: startOfDay.getTime(),
      endTime: endOfDay.getTime(),
    }
  }, [dateRange])

  const brandId = selectedBrand?.id
  const companyId = company?.id

  // Generate cache key with date range
  const cacheKey = useMemo(() => {
    if (!startTime || !endTime) {
      return null
    }
    return generateCacheKey(startTime, endTime)
  }, [startTime, endTime])

  // Load from cache when cache key changes
  useEffect(() => {
    if (cacheKey) {
      const cached = loadFromCache(cacheKey)
      if (cached) {
        setCachedData(cached)
      }
    }
    setIsLoadingFromCache(false)
  }, [cacheKey])

  // Prepare store UIDs for API call
  const storeUids = useMemo(() => {
    if (
      selectedStores &&
      selectedStores.length > 0 &&
      !selectedStores.includes('all-stores')
    ) {
      return selectedStores.filter(
        (id) => id !== 'all-stores' && id !== 'no-stores'
      )
    } else {
      const activeStores = currentBrandStores.filter(
        (store) => store.active === 1
      )
      return activeStores.length > 0
        ? activeStores.map((store) => store.id)
        : undefined
    }
  }, [selectedStores, currentBrandStores])

  // TanStack Query for fetching deleted stores data
  const {
    data: topStores = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: deletedStoresKeys.list({
      companyUid: companyId,
      brandUid: brandId,
      startDate: startTime || undefined,
      endDate: endTime || undefined,
      storeUids,
      limit,
      cacheKey,
    }),
    queryFn: async (): Promise<StoreDeletedStats[]> => {
      if (!brandId || !companyId || !startTime || !endTime) {
        throw new Error('Brand, company, or date range not selected')
      }

      const storesToFetch = storeUids
        ? currentBrandStores.filter(
            (store) => storeUids.includes(store.id) && store.active === 1
          )
        : currentBrandStores.filter((store) => store.active === 1)

      if (storesToFetch.length === 0) {
        return []
      }

      const storeStats: StoreDeletedStats[] = []

      // Process stores in batches to prevent overwhelming the API
      const batchSize = 3
      for (let i = 0; i < storesToFetch.length; i += batchSize) {
        const batch = storesToFetch.slice(i, i + batchSize)

        const batchPromises = batch.map(async (store) => {
          try {
            const response = await deletedOrdersApi.getDeletedOrders({
              companyUid: companyId,
              brandUid: brandId,
              storeUid: store.id,
              startDate: startTime,
              endDate: endTime,
              page: 1,
            })

            const deletedOrders = response.data.filter(
              (order) => order.status === 'DELETE'
            )
            const totalDeleted = deletedOrders.length
            const totalDeletedAmount = deletedOrders.reduce(
              (sum, order) => sum + order.total_amount_origin,
              0
            )

            return {
              storeUid: store.id,
              storeName: store.store_name,
              totalDeleted,
              totalDeletedAmount,
            }
          } catch {
            return {
              storeUid: store.id,
              storeName: store.store_name,
              totalDeleted: 0,
              totalDeletedAmount: 0,
            }
          }
        })

        const batchResults = await Promise.all(batchPromises)
        storeStats.push(...batchResults)

        // Small delay between batches to prevent overwhelming the API
        if (i + batchSize < storesToFetch.length) {
          await new Promise((resolve) => setTimeout(resolve, 300))
        }
      }

      // Sort by deleted count (descending) and take top N
      const sortedStores = storeStats
        .filter((store) => store.totalDeleted > 0)
        .sort((a, b) => b.totalDeleted - a.totalDeleted)
        .slice(0, limit)

      // Save to cache when data is successfully processed
      if (cacheKey && sortedStores.length > 0) {
        const cacheData: CachedDeletedStoresData = {
          topStores: sortedStores,
          totalStores: sortedStores.length,
          totalDeleted: sortedStores.reduce(
            (sum, store) => sum + store.totalDeleted,
            0
          ),
          timestamp: Date.now(),
        }
        saveToCache(cacheKey, cacheData)
      }

      return sortedStores
    },
    enabled: autoFetch && !!brandId && !!companyId && !!startTime && !!endTime,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 15 * 60 * 1000, // Auto-refetch every 15 minutes
    refetchIntervalInBackground: true, // Continue refetching in background
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 2 // Reduce retries for this API
    },
  })

  // Implement stale-while-revalidate pattern
  const hasFreshData = topStores.length > 0

  // Priority:
  // 1. If we have fresh data from API, use it
  // 2. If we have cached data and no fresh data, use cached data
  // 3. Otherwise use empty data
  const finalTopStores = hasFreshData ? topStores : cachedData?.topStores || []
  const finalTotalStores = hasFreshData
    ? topStores.length
    : cachedData?.totalStores || 0
  const finalTotalDeleted = hasFreshData
    ? topStores.reduce((sum, store) => sum + store.totalDeleted, 0)
    : cachedData?.totalDeleted || 0

  // Always show loading when date range changes or API is loading
  const finalIsLoading = isLoadingFromCache || isLoading

  return {
    topStores: finalTopStores,
    isLoading: finalIsLoading,
    error: error?.message || null,
    totalStores: finalTotalStores,
    totalDeleted: finalTotalDeleted,
    refetch: () => {
      refetch()
    },
  }
}

// Export with old name for backward compatibility
export const useSimpleDeletedStores = useDeletedStores
export default useDeletedStores
