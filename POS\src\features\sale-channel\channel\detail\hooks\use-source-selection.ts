import { useState, useEffect } from 'react'

import { useOrderSources } from '@/hooks/api/use-order-sources'

export function useSourceSelection(storeId?: string, initialSources: string[] = []) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedSources, setSelectedSources] = useState<string[]>(initialSources)
  const [showSelectedSources, setShowSelectedSources] = useState(true)

  const { data: orderSourcesData, isLoading } = useOrderSources(1, undefined, storeId)

  // Sync selectedSources with initialSources
  useEffect(() => {
    setSelectedSources(initialSources)
  }, [initialSources])

  // Always skip the first source and show from the second source onwards
  const filteredSources = !orderSourcesData?.data
    ? []
    : orderSourcesData.data
        .slice(1) // Skip the first source (index 0), start from second source (index 1)
        .filter(source => source.source_name.toLowerCase().includes(searchTerm.toLowerCase()))

  const selectedSourcesData = filteredSources.filter(source => selectedSources.includes(source.id))

  const unselectedSourcesData = filteredSources.filter(
    source => !selectedSources.includes(source.id)
  )

  const getSelectedSourceNames = () => {
    return (
      orderSourcesData?.data
        ?.filter(source => selectedSources.includes(source.id))
        ?.map(source => source.source_name)
        ?.join(', ') || ''
    )
  }

  const handleSourceToggle = (sourceId: string) => {
    const newSelectedSources = selectedSources.includes(sourceId)
      ? selectedSources.filter(id => id !== sourceId)
      : [...selectedSources, sourceId]

    setSelectedSources(newSelectedSources)
  }

  const handleDialogConfirm = (onConfirm: (sources: string[]) => void) => {
    onConfirm(selectedSources)
    setIsDialogOpen(false)
  }

  const handleDialogCancel = () => {
    setSelectedSources(initialSources)
    setIsDialogOpen(false)
  }

  const resetSources = () => {
    setSelectedSources([])
  }

  return {
    searchTerm,
    setSearchTerm,
    isDialogOpen,
    setIsDialogOpen,
    selectedSources,
    showSelectedSources,
    setShowSelectedSources,
    isLoading,
    filteredSources,
    selectedSourcesData,
    unselectedSourcesData,
    getSelectedSourceNames,
    handleSourceToggle,
    handleDialogConfirm,
    handleDialogCancel,
    resetSources
  }
}
