import { useQuery } from '@tanstack/react-query'

import { QUERY_KEYS } from '@/constants'
import { useAuthStore } from '@/stores'
import { ReportsWeekdaysResponse, GetReportsWeekdaysParams } from '@/types/api'

import { getReportsWeekdays } from '@/lib/api'

export interface UseReportsWeekdaysOptions {
  params?: Partial<GetReportsWeekdaysParams>
  enabled?: boolean
}

/**
 * Hook to get reports weekdays data
 */
export const useReportsWeekdays = (options: UseReportsWeekdaysOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetReportsWeekdaysParams = {
    company_uid: params.company_uid || company?.id || '',
    brand_uid: params.brand_uid || selectedBrand?.id || '',
    list_store_uid: params.list_store_uid || '',
    start_date: params.start_date ?? Date.now() - 7 * 24 * 60 * 60 * 1000, // Default to 7 days ago
    end_date: params.end_date ?? Date.now(),
    store_open_at: params.store_open_at ?? 0,
    limit: params.limit ?? 5
  }

  const hasRequiredAuth = !!(dynamicParams.company_uid && dynamicParams.brand_uid && dynamicParams.list_store_uid)

  return useQuery({
    queryKey: [QUERY_KEYS.REPORTS_WEEKDAYS, dynamicParams],
    queryFn: async (): Promise<ReportsWeekdaysResponse> => {
      return await getReportsWeekdays(dynamicParams)
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })
}
