import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetPackagesParams, PackagesApiResponse, UpdatePackagesSortRequest } from '@/types/package-types'
import { toast } from 'sonner'

import { packagesApi } from '@/lib/api/pos/packages-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const usePackagesData = (params: GetPackagesParams) => {
  return useQuery<PackagesApiResponse, Error>({
    queryKey: [QUERY_KEYS.PACKAGES_LIST, params],
    queryFn: () => packagesApi.getPackages(params)
  })
}

export const usePackagesForSort = (params: Omit<GetPackagesParams, 'skip_limit'>) => {
  return usePackagesData({ ...params, skip_limit: true })
}

export const useUpdatePackagesSort = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: UpdatePackagesSortRequest) => {
      return await packagesApi.updatePackagesSort(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PACKAGES_LIST]
      })
      toast.success('Cập nhật thứ tự gói thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thứ tự gói'
      toast.error(errorMessage)
    }
  })

  return {
    updateSort: mutate,
    isUpdating: isPending
  }
}
