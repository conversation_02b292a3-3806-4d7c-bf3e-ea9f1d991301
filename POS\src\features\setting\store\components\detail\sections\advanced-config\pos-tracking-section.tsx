import type { UseFormReturn } from 'react-hook-form'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface PosTrackingSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function PosTrackingSection({ form, isLoading = false }: PosTrackingSectionProps) {
  const trackingOptions = [
    {
      value: 0,
      label: 'Không theo dõi'
    },
    {
      value: 1,
      label: '<PERSON> dõi dữ liệu đơn hàng'
    },
    {
      value: 2,
      label: '<PERSON> dõi dữ liệu đơn hàng và thông tin món'
    }
  ]

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'><PERSON> dõi bán hàng dưới POS</h2>
        <div className='space-y-2 text-sm text-gray-600'>
          <p>T<PERSON>h năng chỉ áp dụng cho mô hình bán dine-in.</p>
          <p>Các trạng thái, báo cáo bán hàng dưới POS (số bàn, số hóa đơn) sẽ được ghi lại định kỳ từ 5 - 10 phút.</p>
        </div>
      </div>

      <div className='space-y-4'>
        {/* Theo dõi thông tin tại nhà hàng */}
        <FormField
          control={form.control}
          name='tracking_sale'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Theo dõi thông tin tại nhà hàng</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn cấu hình theo dõi'
                    className='flex-1'
                    options={trackingOptions}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
