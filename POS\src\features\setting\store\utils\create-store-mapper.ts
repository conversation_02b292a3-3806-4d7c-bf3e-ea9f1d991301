import type { StoreFormValues } from '../data'

// Field mappers removed - using direct mapping instead

/**
 * Maps form data to API request format for creating a new store
 * Note: formData.city_uid will be converted to UUID before API call
 */
export const mapFormDataToCreateStoreRequest = (
  formData: StoreFormValues,
  companyUid: string,
  brandUid: string,
  _sourceIds: string[] = []
): Record<string, any> => {
  return {
    extra_data: {
      prevent_print_order_transfer: formData.prevent_print_order_transfer ? 1 : 0,
      print_bill_order_area: formData.print_bill_order_area ? 1 : 0,
      value_vat: formData.value_vat || '0',
      sale_change_vat_enable: formData.sale_change_vat_enable || 0,
      device_receive_online: formData.device_receive_online || '',
      print_type: formData.print_type || 'PROVISIONAL_INVOICE',
      net_work: formData.net_work === '1' ? 1 : 0,
      enable_change_item_in_store: formData.enable_change_item_in_store ? 1 : 0,
      subscription_info: {},
      enable_checkin_by_phone_number: formData.enable_checkin_by_phone_number ? 1 : 0,
      ahamove_payment_method: formData.ahamove_payment_method || 'AHAMOVE',
      open_at: formData.open_at || 0,
      pin_code: formData.pin_code || '',
      operate_model: formData.operate_model || 0,
      require_number_peo: formData.require_peo_count ? 1 : 0,
      tracking_sale: formData.tracking_sale || 0,
      use_order_control: formData.use_order_control ? 1 : 0,
      split_combo: formData.split_combo ? 1 : 0,
      group_item_print: formData.group_item ? 1 : 0,
      bill_template: formData.bill_template || 0,
      show_item_price_zero: formData.show_item_price_zero ? 1 : 0,
      tran_no_syn_order: formData.tran_no_syn_order || 0,
      disable_shift_total_amount: formData.disable_shift_total_amount || 0,
      allow_remove_shift_open: formData.allow_remove_shift_open ? 1 : 0,
      require_close_shift_in_day: formData.require_close_shift_in_day ? 1 : 0,
      inv_skip_item_no_price: formData.inv_skip_item_no_price ? 1 : 0,
      bill_auto_export_vat: formData.bill_auto_export_vat ? 1 : 0,
      sorted_by_print: formData.sorted_by_print ? 1 : 0,
      confirm_request: formData.confirm_request ? 1 : 0,
      require_peo_count: formData.require_peo_count ? 1 : 0,
      require_confirm_merge_table: formData.require_confirm_merge_table ? 1 : 0,
      hide_peo_count: formData.hide_peo_count ? 1 : 0,
      resetItemOutOfStockStatus: formData.resetItemOutOfStockStatus ? 1 : 0,
      resetItemQuantityNewDay: formData.resetItemQuantityNewDay ? 1 : 0,
      change_log_detail: formData.change_log_detail ? 1 : 0,
      require_custom_item_vat: formData.require_custom_item_vat ? 1 : 0,
      require_category_for_custom_item: formData.require_category_for_custom_item ? 1 : 0,
      print_order_at_checkout: formData.print_order_at_checkout ? 1 : 0,
      print_label_at_checkout: formData.print_label_at_checkout ? 1 : 0,
      only_print_label_ta: formData.print_label_at_checkout ? 1 : 0,
      prevent_create_custom_item: formData.prevent_create_custom_item ? 1 : 0,
      is_menu_by_source: formData.is_menu_by_source ? 1 : 0,
      prevent_cashier_edit_printer: formData.prevent_cashier_edit_printer ? 1 : 0,
      exchange_points_for_voucher: formData.exchange_points_for_voucher ? 1 : 0,
      view_voucher_of_member: formData.view_voucher_of_member ? 1 : 0,
      enable_edit_item_price_while_selling: formData.enable_edit_item_price_while_selling || 0,
      discount_reverse_on_price: formData.discount_reverse_on_price ? 1 : 0,
      enable_cash_drawer: formData.enable_cash_drawer ? 1 : 0,
      auto_export_vat: formData.auto_export_vat ? 1 : 0,
      time_out_use_pin: formData.time_out_use_pin || 0,
      disable_print_button_in_payment_screen: formData.disable_print_button_in_payment_screen || 0,
      ahamove_voucher_default: formData.ahamove_voucher_default || '',
      enable_delete_order_bill: formData.enable_delete_order_bill ? 1 : 0,
      is_show_logo_in_provisional_invoice: formData.is_show_logo_in_provisional_invoice ? 1 : 0,
      enable_tab_delivery: formData.enable_tab_delivery ? 1 : 0,
      enable_turn_order_report: formData.enable_turn_order_report ? 1 : 0,
      report_item_combo_split: formData.report_item_combo_split || 0,
      print_bill_split: formData.print_bill_split || 3,
      enable_tran_no_prefix: formData.enable_tran_no_prefix ? 1 : 0,
      tran_no_prefix: formData.tran_no_prefix || '',
      reset_tran_no_period: formData.reset_tran_no_period || 'DAILY',
      enable_note_delete_item: formData.enable_note_delete_item ? 1 : 0,
      service_charge_optional: formData.service_charge_optional ? 1 : 0,
      enable_minvoice: 0,
      inv_buyerTaxCode: '',
      mau_hd: '',
      inv_invoiceSeries: '',
      inv_circulars: '',
      minvoice_link_api: '',
      minvoice_token: '',
      tem_invoice_fake_bill: formData.tem_invoice_fake_bill ? 1 : 0,
      bill_not_show_item_zero: formData.hideItemPriceAfterPrintBill ? 1 : 0,
      require_pin_reprint: formData.require_pin_reprint ? 1 : 0,
      allway_show_tag_so: formData.allway_show_tag_so ? 1 : 0,
      use_shift_pos: formData.use_shift_pos ? 1 : 0,
      enable_count_money: formData.enable_count_money ? 1 : 0,
      partner_id: formData.partner_id || '',
      payment_method_aeon_selected: ['ATM', 'VISA', 'MASTER'],
      has_aeon_export: 0,
      bank_id: formData.bank_id || '',
      bank_name: formData.bank_name || '',
      bank_acc: formData.bank_acc || '',
      bank_acc_name: formData.bank_acc_name || '',
      print_qrcode_pay: formData.print_qrcode_pay || 0,
      is_run_buffet: formData.is_run_buffet ? 1 : 0,
      require_buffet_item: formData.require_buffet_item ? 1 : 0,
      export_time_vat: parseInt(formData.export_time_vat || '0'),
      require_vat_info: formData.require_vat_info ? 1 : 0,
      sources_label_print: formData.sources_label_print || [],
      pm_export_vat: formData.pm_export_vat ? 1 : 0,
      counter_code: formData.counter_code || '',
      role_quick_login: formData.role_quick_login || '',
      time_after_lock: formData.time_after_lock || 0,
      time_lock_data: formData.time_lock_data || 0,
      print_item_switch_table: formData.print_item_switch_table ? 1 : 0,
      no_kick_pda: formData.no_kick_pda ? 1 : 0,
      close_shift_auto_logout: formData.close_shift_auto_logout ? 1 : 0,
      discounts_vat_hkd: [],
      multi_voucher: formData.multi_voucher ? 1 : 0,
      find_member: formData.find_member ? 1 : 0,
      sources_print: formData.sources_print || [],
      enable_change_item_type_in_store: formData.enable_change_item_type_in_store ? 1 : 0,
      enable_change_printer_position_in_store: formData.enable_change_printer_position_in_store ? 1 : 0,
      sources_not_print: formData.sources_not_print || [],
      auto_check_momo_aio: formData.auto_check_momo_aio || 0,
      auto_confirm_o2o_post_paid: formData.auto_confirm_o2o_post_paid ? 1 : 0,
      new_ui_payment_momo: 1,
      print_limit: parseInt(formData.print_limit || '0'),
      option_export_vat_pos_or_cms: 0,
      state_print_order: formData.print_order_at_checkout ? 1 : 0,
      last_confirm_use_multi_voucher_at: null,
      secondary_screen_video: formData.secondary_screen_video || '',
      secondary_screen_image: formData.secondary_screen_image || '',
      counter_mails: formData.counter_mails || []
    },
    source_ids_selected: formData.sources_print || [],
    store_name: formData.store_name,
    phone: formData.phone,
    city_uid: formData.city_uid,
    address: formData.address,
    email: formData.email || '',
    background: formData.background || '',
    phone_manager: formData.phone_manager || '',
    is_ahamove_active: formData.is_ahamove_active ? 1 : 0,
    company_uid: companyUid,
    brand_uid: brandUid,
    delivery_services: formData.is_ahamove_active ? 'AHAMOVE' : '',
    longitude: formData.longitude || 0,
    latitude: formData.latitude || 0
  }
}
