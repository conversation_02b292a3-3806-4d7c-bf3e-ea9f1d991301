export interface BillTemplate {
  id: string
  name: string
  description?: string
  storeId: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface BillTemplateActionBarProps {
  selectedStoreId: string
  onStoreChange: (value: string) => void
  onUseBillTemplate: () => void
  onCopyTemplates: () => void
  isUsingTemplate?: boolean
}

export interface SyncBillTemplateModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stores: Array<{
    id: string
    name: string
  }>
}

export interface BillItem {
  id: string
  name: string
  quantity: number
  unitPrice: number
  subtotal: number
  unit?: string
  itemType?: string
  notes?: string
  discountPercentage?: number
  discountAmount?: number
  vatPercentage?: number
  vatAmount?: number
  toppings?: Array<{
    name: string
    quantity: number
    unitPrice: number
    subtotal: number
    unit?: string
    notes?: string
    discountPercentage?: number
    discountAmount: number
    vatPercentage?: number
    vatAmount?: number
  }>
}

export interface BillData {
  billNumber: string
  billId: string
  orderSource: string
  table: string
  checkInTime: string
  checkOutTime: string
  cashier: string
  date: string
  items: BillItem[]
  subtotal: number
  discountAmount: number
  itemDiscountAmount: number
  vatAmount: number
  totalWithVat: number
  shippingFee: number
  voucherDiscount: number
  grandTotal: number
  paymentMethod: string
  paymentAmount: number
  amountReceived: number
  changeAmount: number
  restaurantName: string
  restaurantAddress: string
  customerName: string
  customerPhone: string
  customerTaxId: string
  customerAddress: string
  accumulatedPoints: number
  voucherName: string
  cardFeeAmount: number
  customText1: string
  customText2: string
  hotline: string
}

export interface BillTemplateConfig {
  showLogo: boolean
  logoUrl?: string
  customText1: string
  customText2: string
  hotlineNumber: string
  showAmountReceived: boolean
  showToppings: boolean
  showVoucherGift: boolean
  showBankCardCode: boolean
  showMainItemQuantity: boolean
  showBillFrame: boolean
  secureCustomerInfo: boolean
  showItemDiscount: boolean
  showVatPercentage: boolean
  showVatAmount: boolean
  showUnit: boolean
  mergeInvoiceAndOrderSource: boolean
  showAccumulatedPoints: boolean
  showVatInformation: boolean
  showTotalIncludingVat: boolean
  showVatQrCode: boolean
  showItemType: boolean
  showTotalItemType: boolean
  hideInvoiceCorrectionNotes: boolean
  showServiceItemTime: boolean
  showDebtInformation: boolean
  showAioMomoQr: boolean
  showVietQr: boolean
  fontSize: number
  showFoodItemNotes: boolean
  showCardFee: boolean
  currencyRates: Array<{
    id: string
    currencyCode: string
    rate: number
  }>
  showScanQrCode: boolean
  scanQrTitle: string
  scanQrContent: string
}

export interface CurrencyExchange {
  currency: string
  exchange: number
}

// Base interface chứa các field chung nhất
export interface BaseBillTemplateFields {
  momo_qr_aio: number
  is_show_unit: number
  enable_topping: boolean
  show_item_note: number
  enable_discount: boolean
  show_item_class: number
  enable_border_bill: number
  display_debt_amount: number
  number_table_column: number
  show_amount_with_vat: number
}

// Interface chứa các field title chung
export interface TitleFields {
  title_code?: string
  title_table?: string
  title_cashier?: string
  title_date?: string
  title_time_in?: string
  title_time_out?: string
  title_custom?: string
}

// Interface chứa các field chung cho bill template
export interface CommonBillTemplateFields extends BaseBillTemplateFields {
  custom_text_1: string
  custom_text_2: string
  enable_cash_change: boolean
  enable_qr_code: boolean
  show_voucher_gift: boolean
  show_payment_id: boolean
  show_count_item_bill: number
  show_customer_phone: number
  is_group_source_and_tranno: number
  show_points: number
  show_vat_info: number
  hide_note_sale_change: number
  show_vat_reverse: number
  show_qr_vat_info: number
  show_start_end_item_service: number
  currency_exchanges: CurrencyExchange[]
  show_total_item_class_amount: number
  apply_new_logo_template: boolean
  logo: string | null
}

export interface ExtraData extends CommonBillTemplateFields {
  qr_title: string
  qr_content: string
  font_size_rate: number
  hotline: string
  enable_vat_rate: boolean
  enable_vat_amount: boolean
  is_show_payment_fee: number
}

export interface ExtraBillTemplate3 extends CommonBillTemplateFields, TitleFields {
  logo: string | null
  hotline: string | null
  qr_title: string | null
  qr_content: string | null
  font_size_rate: string
  enable_vat_rate: boolean
  enable_vat_amount: boolean
  title_stt?: string
  title_name_item?: string
  title_quantity?: string
  title_price?: string
  title_vat_rate?: string
  title_vat_amount?: string
  title_discount?: string
  title_amount?: string
}

export interface ExtraBillTemplateCustom extends BaseBillTemplateFields, TitleFields {
  content: string
  title_stt: string
  title_price: string
  title_amount: string
  title_discount: string
  title_quantity: string
  title_vat_rate: string
  title_name_item: string
  title_vat_amount: string
}

export interface ExtraPosMini extends CommonBillTemplateFields {
  logo: string | null
  hotline: string
  qr_title: string
  title_stt: string
  qr_content: string
  title_code: string
  title_date: string
  title_price: string
  title_table: string
  title_amount: string
  title_custom: string
  bill_template: string
  title_cashier: string
  title_time_in: string
  font_size_rate: number
  title_discount: string
  title_quantity: string
  title_time_out: string
  title_vat_rate: string
  title_name_item: string
  title_vat_amount: string
  is_show_payment_fee: number
}

export interface ExtraBillTemplate4 extends CommonBillTemplateFields {
  logo: string | null
  hotline: string | null
  qr_title: string | null
  title_stt: string
  qr_content: string | null
  title_code: string
  title_date: string
  title_price: string
  title_table: string
  title_amount: string
  title_custom: string
  title_cashier: string
  title_time_in: string
  font_size_rate: number
  title_discount: string
  title_quantity: string
  title_time_out: string
  title_vat_rate: string
  title_name_item: string
  title_vat_amount: string
}

export interface ExtraDeposit {
  note: string | null
  address: string | null
  hotline: string | null
  title_text: string | null
  custom_text: string | null
  padding_top: number
  logo_deposit: string | null
  font_size_rate: number
  operating_time: string | null
  show_item_type: number
  show_raw_quanity: number
  item_font_size_rate: number
  font_size_title_text: number
  price_font_size_rate: number
  new_templace_order_bill: number
  quantity_font_size_rate: number
  disable_unit_in_order_note: number
  total_price_font_size_rate: number
  display_confirmation_signature: number
}

export interface SaveBillTemplateRequest {
  company_uid: string
  brand_uid: string
  store_uid: string
  id: string
  logo: string | null
  name: string | null
  address: string | null
  city: string | null
  phone: string | null
  facebook: string | null
  revision: number
  extra_data: ExtraData
  extra_config: unknown | null
  extra_pos_mini: ExtraPosMini | null
  extra_bill_template_3: ExtraBillTemplate3
  extra_bill_template_4: ExtraBillTemplate4 | null
  extra_bill_template_custom: ExtraBillTemplateCustom
  extra_order_note: unknown | null
  extra_label: unknown | null
  extra_provisional: unknown | null
  extra_deposit: ExtraDeposit
  store_id: string | null
  brand_id: string | null
  company_id: string | null
  is_fabi: number
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: string
  updated_at: string
  deleted_at: string | null
}

export interface SaveBillTemplateResponse {
  success: boolean
  message?: string
  data?: unknown
  track_id?: string
}
