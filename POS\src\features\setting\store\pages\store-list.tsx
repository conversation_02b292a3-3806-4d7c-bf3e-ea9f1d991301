import { getErrorMessage } from '@/utils/error-utils'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { storesColumns, StoresDataTable, StoreHeader, SyncSecondaryScreenModal } from '../components/list'
import { STORE_CONSTANTS } from '../data'
import { useStoreList } from '../hooks/list'

export default function StoreListPage() {
  const {
    filteredStores,
    cities,
    isLoading,
    citiesLoading,
    error,
    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity,
    handleCreateStore,
    handleSyncSecondaryScreen,
    syncModalOpen,
    setSyncModalOpen
  } = useStoreList()

  if (error) {
    return (
      <>
        <Header>
          <div className='ml-auto flex items-center space-x-4'>
            <Search />
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>

        <Main>
          <div className='container mx-auto px-4 py-8'>
            <StoreHeader
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              selectedCity={selectedCity}
              onCityChange={setSelectedCity}
              cities={cities}
              citiesLoading={citiesLoading}
              onCreateStore={handleCreateStore}
              onSyncSecondaryScreen={handleSyncSecondaryScreen}
            />
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          </div>
        </Main>
      </>
    )
  }

  if (isLoading) {
    return (
      <>
        <Header>
          <div className='ml-auto flex items-center space-x-4'>
            <Search />
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>

        <Main>
          <div className='container mx-auto px-4 py-8'>
            <StoreHeader
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              selectedCity={selectedCity}
              onCityChange={setSelectedCity}
              cities={cities}
              citiesLoading={citiesLoading}
              onCreateStore={handleCreateStore}
              onSyncSecondaryScreen={handleSyncSecondaryScreen}
            />
            <div className='py-8 text-center'>
              <p>{STORE_CONSTANTS.LOADING_STORES}</p>
            </div>
          </div>
        </Main>
      </>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <StoreHeader
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedCity={selectedCity}
            onCityChange={setSelectedCity}
            cities={cities}
            citiesLoading={citiesLoading}
            onCreateStore={handleCreateStore}
            onSyncSecondaryScreen={handleSyncSecondaryScreen}
          />
          <StoresDataTable columns={storesColumns} data={filteredStores} />
          <SyncSecondaryScreenModal open={syncModalOpen} onOpenChange={setSyncModalOpen} />
        </div>
      </Main>
    </>
  )
}
