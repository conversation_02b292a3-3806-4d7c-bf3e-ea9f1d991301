import { Brand, City, Store } from '@/types/auth'

export interface User {
  id: string
  full_name: string
  email: string
  phone: string
  role_uid: string
  active: number
  phone_verified_at: string | null
  role_name: string
  role_id: string
  role_description: string
  stores: Record<string, Record<string, unknown[]>>
  brand_access?: string[]
  user_permissions?: {
    id: string
    user_uid: string
    company_uid: string
    stores: Record<string, Record<string, unknown[]>>
    tables: Record<string, string[]>
  }
  brands?: Brand[]
  cities?: City[]
  storeDetails?: Store[]
}

export interface GetUsersParams {
  company_uid?: string
  active?: number
  page?: number
  limit?: number
  search?: string
  brand_uid?: string
  city_uid?: string
  store_uid?: string
}

export interface UsersApiResponse {
  data: User[]
  total?: number
  page?: number
  limit?: number
}

export interface CreateUserPermissions {
  company_uid: string
  tables: Record<string, string[]>
  stores: Record<string, Record<string, string[]>>
}

export interface CreateUserParams {
  email: string
  full_name: string
  phone?: string
  role_uid: string
  password: string
  confirm_password: string
  company_uid?: string
  permissions?: CreateUserPermissions
  brand_access?: string[]
}

export interface CreateUserResponse {
  user: User
  message?: string
}

export interface UpdateUserParams {
  email: string
  full_name: string
  phone?: string
  role_uid: string
  password?: string
  confirm_password?: string
  brand_access?: string[]
}

export interface UpdateUserProfileParams {
  id: string
  phone?: string
  full_name: string
  profile_image_path?: string | null
  role_uid: string
  last_login_at?: string | null
  is_fabi?: number
  permissions: {
    company_uid: string
    tables: Record<string, string[]>
    stores: Record<string, Record<string, string[]>>
  }
}

export interface UpdateUserResponse {
  user: User
  message?: string
}

export interface DeactivateUserResponse {
  message?: string
}

export interface ApiUser {
  id: string
  email: string
  phone: string
  full_name: string
  profile_image_path: string | null
  role_uid: string
  company_uid: string
  country_id: string
  active: number
  is_verified: number
  partner_company_uid: string | null
  fixed_account: boolean
  created_at: string
  updated_at: number
  last_login_at: number
  is_fabi: number | null
  phone_verified_at: string | null
}

export interface ApiUserRole {
  id: string
  role_id: string
  role_name: string
  description: string
  scope: string
  scope_value: string | null
  allow_access: string[]
  reject_permissions: string[]
  created_at: string
  updated_at: string
}

export interface ApiUserPermissions {
  id: string
  user_uid: string
  company_uid: string
  stores: Record<string, Record<string, unknown[]>>
  tables: Record<string, string[]>
}

export interface ApiCompany {
  id: string
  company_id: string
  company_name: string
  description: string | null
  extra_data: Record<string, unknown>
  active: number
  revision: string | null
}

export interface UserApiResponse {
  data?: {
    user?: ApiUser
    user_role?: ApiUserRole
    user_permissions?: ApiUserPermissions
    company?: ApiCompany
    brands?: Brand[]
    cities?: City[]
    stores?: Store[]
  }
  user?: ApiUser
  user_role?: ApiUserRole
  user_permissions?: ApiUserPermissions
  company?: ApiCompany
  brands?: Brand[]
  cities?: City[]
  stores?: Store[]
}
