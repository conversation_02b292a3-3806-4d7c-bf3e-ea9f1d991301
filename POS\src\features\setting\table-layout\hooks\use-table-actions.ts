import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import type { TableLayoutItem } from '../data/table-layout-types'

interface UseTableActionsProps {
  selectedStoreId: string
  selectedAreaId: string
  setSelectedAreaId: (areaId: string) => void
  setSelectedStoreId: (storeId: string) => void
  setSelectedTable: (table: TableLayoutItem | null) => void
  setLocalTables: React.Dispatch<React.SetStateAction<TableLayoutItem[]>>
  setIsSortModalOpen: (open: boolean) => void
  setIsConfigureTableModalOpen: (open: boolean) => void
}

export const useTableActions = ({
  selectedStoreId,
  selectedAreaId,
  setSelectedAreaId,
  setSelectedStoreId,
  setSelectedTable,
  setLocalTables,
  setIsSortModalOpen,
  setIsConfigureTableModalOpen
}: UseTableActionsProps) => {
  const navigate = useNavigate()

  const handleAreaChange = (areaId: string) => {
    setSelectedAreaId(areaId)
    setSelectedTable(null)
  }

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('')
    setSelectedTable(null)
  }

  const handleCreateNewArea = () => {
    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng trước')
      return
    }
    navigate({
      to: '/setting/area',
      search: { store_id: selectedStoreId }
    })
  }

  const handleTableSelect = (table: TableLayoutItem) => {
    setSelectedTable(table)
    navigate({
      to: '/setting/table/detail/$tableId',
      params: { tableId: table.id },
      search: { 
        store_uid: selectedStoreId,
        area_uid: selectedAreaId
      }
    })
  }

  const handleTablePositionUpdate = (tableId: string, newPosition: { x: number; y: number }) => {
    setLocalTables(prev => prev.map(table => (table.id === tableId ? { ...table, position: newPosition } : table)))
  }

  const handleCreateNew = () => {
    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng trước')
      return
    }
    if (!selectedAreaId) {
      toast.error('Vui lòng chọn khu vực trước')
      return
    }
    navigate({
      to: '/setting/table',
      search: { store_id: selectedStoreId, area_id: selectedAreaId }
    })
  }

  const handleSortTables = () => {
    if (!selectedAreaId) {
      toast.error('Vui lòng chọn khu vực để sắp xếp bàn')
      return
    }
    setIsSortModalOpen(true)
  }

  const handleCustomizeTable = () => {
    // Tạm thời bỏ qua validation để test modal
    // if (!selectedStoreId) {
    //   toast.error('Vui lòng chọn cửa hàng trước')
    //   return
    // }
    setIsConfigureTableModalOpen(true)
  }

  return {
    handleAreaChange,
    handleStoreChange,
    handleCreateNewArea,
    handleTableSelect,
    handleTablePositionUpdate,
    handleCreateNew,
    handleSortTables,
    handleCustomizeTable
  }
}
