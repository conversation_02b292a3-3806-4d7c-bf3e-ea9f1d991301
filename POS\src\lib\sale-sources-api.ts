import { api } from './api/pos/pos-api'

// Cache configuration
const SOURCES_CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
const sourcesCache = new Map<string, { data: SourcesResponse; timestamp: number }>()
const ongoingSourcesRequests = new Map<string, Promise<SourcesResponse>>()

// Sources API Types based on actual API response
export interface SourceDataItem {
  date: string
  tran_date: number
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  peo_count: number
  revenue_net: number
}

export interface SourceData {
  source_id: string
  source_name: string
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  revenue_net: number
  total_cost: number
  total_cost_rate: number
  peo_count: number
  list_data: SourceDataItem[]
}

export interface SourcesResponse {
  data: SourceData[]
  message: string
  track_id: string
}

export interface SourcesSummary {
  totalRevenue: number
  totalBills: number
  sourceCount: number
  dailyData: Array<{
    date: string
    revenue: number
    bills: number
  }>
  sourceData: Array<{
    sourceId: string
    sourceName: string
    revenue: number
    bills: number
    percentage: number
  }>
}

// Sources API Service
export const sourcesApi = {
  /**
   * Get sources summary with request deduplication
   */
  getSourcesSummary: async (params: {
    companyUid: string
    brandUid: string
    startDate: number // timestamp in milliseconds
    endDate: number // timestamp in milliseconds
    storeUids?: string[] // optional list of store UIDs
    byDays?: number // 1 for daily breakdown
    limit?: number
  }): Promise<SourcesResponse> => {
    // Create a unique key for this request
    const requestKey = `${params.companyUid}-${params.brandUid}-${params.startDate}-${params.endDate}-${params.storeUids?.join(',') || 'all'}-${params.byDays || 1}`

    // Check cache first
    const cached = sourcesCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < SOURCES_CACHE_DURATION) {
      return cached.data
    }

    // If there's already an ongoing request, return it
    if (ongoingSourcesRequests.has(requestKey)) {
      return ongoingSourcesRequests.get(requestKey)!
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          limit: (params.limit || 100).toString(),
          by_days: (params.byDays || 1).toString(),
          store_open_at: '0'
        })

        // Always add store UIDs - never use 'all-stores' text
        if (params.storeUids && params.storeUids.length > 0) {
          queryParams.set('list_store_uid', params.storeUids.join(','))
        }

        const response = await api.get(`/v1/reports/sale-summary/sources?${queryParams.toString()}`)

        // Cache the result
        sourcesCache.set(requestKey, {
          data: response.data as SourcesResponse,
          timestamp: Date.now()
        })

        return response.data as SourcesResponse
      } finally {
        // Clean up the ongoing request
        ongoingSourcesRequests.delete(requestKey)
      }
    })()

    // Store the promise to prevent duplicate requests
    ongoingSourcesRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Process sources data into summary format
   */
  processSourcesSummary: (sourcesData: SourceData[]): SourcesSummary => {
    const totalRevenue = sourcesData.reduce((sum, source) => sum + source.revenue_gross, 0)
    const totalBills = sourcesData.reduce((sum, source) => sum + source.total_bill, 0)
    const sourceCount = sourcesData.length

    // Aggregate daily data across all sources
    const dailyDataMap = new Map<string, { revenue: number; bills: number }>()

    sourcesData.forEach(source => {
      source.list_data.forEach(day => {
        const existing = dailyDataMap.get(day.date)
        if (existing) {
          existing.revenue += day.revenue_gross
          existing.bills += day.total_bill
        } else {
          dailyDataMap.set(day.date, {
            revenue: day.revenue_gross,
            bills: day.total_bill
          })
        }
      })
    })

    const dailyData = Array.from(dailyDataMap.entries())
      .map(([date, data]) => ({
        date,
        revenue: data.revenue,
        bills: data.bills
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    // Process source data with percentages
    const sourceData = sourcesData.map(source => ({
      sourceId: source.source_id,
      sourceName: source.source_name,
      revenue: source.revenue_gross,
      bills: source.total_bill,
      percentage: totalRevenue > 0 ? (source.revenue_gross / totalRevenue) * 100 : 0
    }))

    return {
      totalRevenue,
      totalBills,
      sourceCount,
      dailyData,
      sourceData
    }
  },

  /**
   * Format currency for display
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN').format(amount)
  },

  /**
   * Get date range for different periods
   */
  getDateRange: (period: string): { startDate: number; endDate: number } => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (period) {
      case 'today':
        return {
          startDate: today.getTime(),
          endDate: today.getTime() + 24 * 60 * 60 * 1000 - 1
        }

      case 'yesterday': {
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        return {
          startDate: yesterday.getTime(),
          endDate: yesterday.getTime() + 24 * 60 * 60 * 1000 - 1
        }
      }

      case 'this-week': {
        const startOfWeek = new Date(today)
        startOfWeek.setDate(today.getDate() - today.getDay())
        return {
          startDate: startOfWeek.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-week': {
        const startOfThisWeek = new Date(today)
        startOfThisWeek.setDate(today.getDate() - today.getDay())
        const startOfLastWeek = new Date(startOfThisWeek)
        startOfLastWeek.setDate(startOfThisWeek.getDate() - 7)
        const endOfLastWeek = new Date(startOfThisWeek)
        endOfLastWeek.setTime(endOfLastWeek.getTime() - 1)
        return {
          startDate: startOfLastWeek.getTime(),
          endDate: endOfLastWeek.getTime()
        }
      }

      case 'this-month': {
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        return {
          startDate: startOfMonth.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-month': {
        const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59, 999)
        return {
          startDate: startOfLastMonth.getTime(),
          endDate: endOfLastMonth.getTime()
        }
      }

      case 'this-quarter': {
        const currentQuarter = Math.floor(today.getMonth() / 3)
        const startOfQuarter = new Date(today.getFullYear(), currentQuarter * 3, 1)
        return {
          startDate: startOfQuarter.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-quarter': {
        const currentQuarter = Math.floor(today.getMonth() / 3)
        const lastQuarter = currentQuarter === 0 ? 3 : currentQuarter - 1
        const lastQuarterYear = currentQuarter === 0 ? today.getFullYear() - 1 : today.getFullYear()
        const startOfLastQuarter = new Date(lastQuarterYear, lastQuarter * 3, 1)
        const endOfLastQuarter = new Date(lastQuarterYear, (lastQuarter + 1) * 3, 0, 23, 59, 59, 999)
        return {
          startDate: startOfLastQuarter.getTime(),
          endDate: endOfLastQuarter.getTime()
        }
      }

      case 'this-year': {
        const startOfYear = new Date(today.getFullYear(), 0, 1)
        return {
          startDate: startOfYear.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-year': {
        const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1)
        const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31, 23, 59, 59, 999)
        return {
          startDate: startOfLastYear.getTime(),
          endDate: endOfLastYear.getTime()
        }
      }

      case 'last-7-days': {
        const sevenDaysAgo = new Date(today)
        sevenDaysAgo.setDate(today.getDate() - 7)
        return {
          startDate: sevenDaysAgo.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-30-days': {
        const thirtyDaysAgo = new Date(today)
        thirtyDaysAgo.setDate(today.getDate() - 30)
        return {
          startDate: thirtyDaysAgo.getTime(),
          endDate: now.getTime()
        }
      }

      case 'last-90-days': {
        const ninetyDaysAgo = new Date(today)
        ninetyDaysAgo.setDate(today.getDate() - 90)
        return {
          startDate: ninetyDaysAgo.getTime(),
          endDate: now.getTime()
        }
      }

      default: {
        // Default to this month
        const defaultStart = new Date(today.getFullYear(), today.getMonth(), 1)
        return {
          startDate: defaultStart.getTime(),
          endDate: now.getTime()
        }
      }
    }
  }
}

export default sourcesApi
