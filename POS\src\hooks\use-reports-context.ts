import { createContext, useContext } from 'react'

interface ReportsContextValue {
  dateRange: {
    from: Date
    to: Date
  }
  filterType: 'monthly' | 'daily'
  selectedStores: string[]
  selectedSources: string[]
}

export const ReportsContext = createContext<ReportsContextValue | null>(null)

// Create default values once to avoid recreating on every call
const today = new Date()
const startOfYear = new Date(today.getFullYear(), 0, 1)

const defaultContextValue: ReportsContextValue = {
  dateRange: {
    from: startOfYear,
    to: today,
  },
  filterType: 'monthly',
  selectedStores: ['all-stores'],
  selectedSources: ['all-sources'],
}

export function useReportsContext() {
  const context = useContext(ReportsContext)
  if (!context) {
    // Return memoized default values if no context is provided
    return defaultContextValue
  }
  return context
}

export default useReportsContext
