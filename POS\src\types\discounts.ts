// Discount API data structure
export interface DiscountApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  ta_discount: number
  ots_discount: number
  is_all: 0 | 1
  is_type: 0 | 1
  is_item: 0 | 1
  type_id: string
  item_id: string
  discount_type: 'PERCENT' | 'AMOUNT'
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: 0 | 1
  }
  active: 0 | 1
  revision: number | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  sort: number
  store_uid: string
  discount_clone_id: string | null
  source_uid: string
  promotion: {
    id: string
    sort: number
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number
    brand_uid: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    source_uid: string
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    promotion_id: string
    promotion_name: string
    partner_auto_gen: 0 | 1
  }
  store_name?: string
  source: {
    id: string
    sort: number
    is_fb: 0 | 1
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number | null
    brand_uid: string
    source_id: string
    store_uid: string
    created_at: number
    created_by: string | null
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    source_name: string
    source_type: any[]
    partner_config: 0 | 1
  }
}

// Converted discount data structure
export interface Discount {
  id: string
  taDiscount: number
  otsDiscount: number
  isAll: number
  isType: number
  isItem: number
  typeId: string
  itemId: string
  discountType: string
  fromDate: number
  toDate: number
  active: number
  storeUid: string
  storeName: string
  sourceUid: string
  promotionUid: string
  promotionId: string
  promotionName: string
  partnerAutoGen: number
  // Combo information
  comboId: string
  isCombo: number
  source: {
    id: string
    sourceId: string
    sourceName: string
    sourceType: string[]
    active: number
  }
  createdAt: number
  createdBy: string
  updatedAt: number
  updatedBy: string | null
  deleted?: boolean
  deletedAt?: number | null
  deletedBy?: string | null
}

// API response structure
export interface DiscountsApiResponse {
  data: DiscountApiData[]
  track_id: string
}

// Parameters for fetching discounts
export interface GetDiscountsParams {
  companyUid?: string
  brandUid?: string
  page?: number
  listStoreUid?: string[]
  promotionPartnerAutoGen?: number
  status?: 'expired' | 'unexpired'
  active?: number
  searchTerm?: string
}

// Convert API data to internal format
export function convertApiDiscountToDiscount(apiDiscount: DiscountApiData): Discount {
  return {
    id: apiDiscount.id,
    taDiscount: apiDiscount.ta_discount,
    otsDiscount: apiDiscount.ots_discount,
    isAll: apiDiscount.is_all,
    isType: apiDiscount.is_type,
    isItem: apiDiscount.is_item,
    typeId: apiDiscount.type_id,
    itemId: apiDiscount.item_id,
    discountType: apiDiscount.discount_type,
    fromDate: apiDiscount.from_date,
    toDate: apiDiscount.to_date,
    active: apiDiscount.active,
    storeUid: apiDiscount.store_uid,
    storeName: apiDiscount.store_name || '',
    sourceUid: apiDiscount.source_uid,
    promotionUid: apiDiscount.promotion_uid,
    promotionId: apiDiscount.promotion.promotion_id,
    promotionName: apiDiscount.promotion?.promotion_name || '',
    partnerAutoGen: apiDiscount.promotion.partner_auto_gen,
    // Combo information
    comboId: apiDiscount.extra_data.combo_id,
    isCombo: apiDiscount.extra_data.is_combo,
    source: {
      id: apiDiscount.source.id,
      sourceId: apiDiscount.source.source_id,
      sourceName: apiDiscount.source.source_name,
      sourceType: apiDiscount.source.source_type,
      active: apiDiscount.source.active
    },
    createdAt: apiDiscount.created_at,
    createdBy: apiDiscount.created_by,
    updatedAt: apiDiscount.updated_at,
    updatedBy: apiDiscount.updated_by,
    deleted: apiDiscount.deleted,
    deletedAt: apiDiscount.deleted_at,
    deletedBy: apiDiscount.deleted_by
  }
}

// Membership Discount Types
export interface MembershipDiscountApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number | null
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  membership_type_id: string
  membership_type_name: string
  birth_ta_discount: number
  birth_ots_discount: number
  birth_time_range_apply: number
  ta_discount: number
  ots_discount: number
  is_all: number
  is_type: number
  is_item: number
  type_id: string
  item_id: string
  discount_type: string
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: number
  }
  active: number
  revision: number
  sort: number
  promotion_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  discount_clone_id: string | null
  promotion: {
    id: string
    company_uid: string
    brand_uid: string
    deleted: boolean
    promotion_id: string
    promotion_name: string
    active: number
    partner_auto_gen: number
    is_fabi: number
    source_uid: string
    description: string | null
  }
}

export interface MembershipDiscountApiResponse {
  data: MembershipDiscountApiData[]
  track_id: string
}

// Converted membership discount data structure
export interface MembershipDiscount {
  id: string
  membershipTypeId: string
  membershipTypeName: string
  birthTaDiscount: number
  birthOtsDiscount: number
  birthTimeRangeApply: number
  taDiscount: number
  otsDiscount: number
  isAll: number
  isType: number
  isItem: number
  typeId: string
  itemId: string
  discountType: string
  fromDate: number
  toDate: number
  active: number
  storeUid: string
  storeName: string
  promotionUid: string
  promotionId: string
  promotionName: string
  createdAt: number
  createdBy: string
  updatedAt: number | null
  updatedBy: string | null
  deleted: boolean
  deletedAt: number | null
  deletedBy: string | null
  timeSaleHourDay: number
  timeSaleDateWeek: number
  description: string | null
  extraData: {
    combo_id: string
    is_combo: number
  }
  revision: number
  sort: number
  brandUid: string
  companyUid: string
  discountCloneId: string | null
  promotion: {
    id: string
    company_uid: string
    brand_uid: string
    deleted: boolean
    promotion_id: string
    promotion_name: string
    active: number
    partner_auto_gen: number
    is_fabi: number
    source_uid: string
    description: string | null
  }
}

export interface GetMembershipDiscountsParams {
  companyUid?: string
  brandUid?: string
  page?: number
  listStoreUid?: string[]
  status?: 'expired' | 'unexpired'
  active?: number
}

// Convert API data to frontend format for membership discounts
export function convertMembershipDiscountApiData(
  apiDiscount: MembershipDiscountApiData,
  storeName: string
): MembershipDiscount {
  return {
    id: apiDiscount.id,
    membershipTypeId: apiDiscount.membership_type_id,
    membershipTypeName: apiDiscount.membership_type_name,
    birthTaDiscount: apiDiscount.birth_ta_discount,
    birthOtsDiscount: apiDiscount.birth_ots_discount,
    birthTimeRangeApply: apiDiscount.birth_time_range_apply,
    taDiscount: apiDiscount.ta_discount,
    otsDiscount: apiDiscount.ots_discount,
    isAll: apiDiscount.is_all,
    isType: apiDiscount.is_type,
    isItem: apiDiscount.is_item,
    typeId: apiDiscount.type_id,
    itemId: apiDiscount.item_id,
    discountType: apiDiscount.discount_type,
    fromDate: apiDiscount.from_date,
    toDate: apiDiscount.to_date,
    active: apiDiscount.active,
    storeUid: apiDiscount.store_uid,
    storeName: storeName,
    promotionUid: apiDiscount.promotion_uid,
    promotionId: apiDiscount.promotion.promotion_id,
    promotionName: apiDiscount.promotion.promotion_name,
    createdAt: apiDiscount.created_at,
    createdBy: apiDiscount.created_by,
    updatedAt: apiDiscount.updated_at,
    updatedBy: apiDiscount.updated_by,
    deleted: apiDiscount.deleted,
    deletedAt: apiDiscount.deleted_at,
    deletedBy: apiDiscount.deleted_by,
    timeSaleHourDay: apiDiscount.time_sale_hour_day,
    timeSaleDateWeek: apiDiscount.time_sale_date_week,
    description: apiDiscount.description,
    extraData: apiDiscount.extra_data,
    revision: apiDiscount.revision,
    sort: apiDiscount.sort,
    brandUid: apiDiscount.brand_uid,
    companyUid: apiDiscount.company_uid,
    discountCloneId: apiDiscount.discount_clone_id,
    promotion: apiDiscount.promotion
  }
}

// Payment Discount Types
export interface PaymentDiscountApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number | null
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  payment_method_id: string
  discount_type: 'PERCENT' | 'AMOUNT'
  discount_value: number
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  active: 0 | 1
  revision: number | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  sort: number
  store_uid: string
  discount_clone_id: string | null
  source_uid: string
  promotion: {
    id: string
    sort: number
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number
    brand_uid: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    company_uid: string
    updated_at: number | null
    updated_by: string | null
    promotion_name: string
    description: string | null
  }
}

export interface PaymentDiscountApiResponse {
  data: PaymentDiscountApiData[]
  track_id: string
}
