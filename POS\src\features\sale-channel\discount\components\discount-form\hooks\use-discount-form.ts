import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { DiscountFormData as ApiDiscountFormData } from '@/types/api/discount-types'
import type { DiscountApiData } from '@/types/discounts'

import { transformFormDataToUpdateRequest } from '@/lib/discounts-api'

import { dateUtils } from '@/utils/date-utils'

import {
  useCreateDiscount,
  useEditDiscount,
  useDiscountFormValidation,
  useDiscountDetail
} from '@/hooks/api/use-discount'

export interface FilterState {
  is_all: 0 | 1
  is_item: 0 | 1
  is_type: 0 | 1
  type_id: string
  item_id: string
  is_combo: 0 | 1
  combo_id: string
}

export interface DiscountFormData {
  storeUid: string
  channelUid: string
  discountType: 'PERCENT' | 'AMOUNT'
  discountPercentage: number
  discountAmount: number
  startDate: string
  endDate: string
  marketingDays: string[]
  marketingHours: string[]
  promotionUid: string
  promotionName: string
  filterState: FilterState
}

interface UseDiscountFormProps {
  discountId?: string
  initialStoreUid?: string
}

export function useDiscountForm({ discountId, initialStoreUid }: UseDiscountFormProps = {}) {
  const navigate = useNavigate()
  const isEditMode = !!discountId

  const { data: discountData, isLoading: isLoadingDiscount } = useDiscountDetail(discountId || '')

  const [formData, setFormData] = useState<DiscountFormData>({
    storeUid: initialStoreUid || '',
    channelUid: '',
    discountType: 'PERCENT',
    discountPercentage: 0,
    discountAmount: 0,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    marketingDays: [],
    marketingHours: [],
    promotionUid: '',
    promotionName: '',
    filterState: {
      is_all: 0 as 0 | 1,
      is_item: 0 as 0 | 1,
      is_type: 0 as 0 | 1,
      type_id: '',
      item_id: '',
      is_combo: 0 as 0 | 1,
      combo_id: ''
    }
  })

  const timestampToVietnamDateString = (timestamp: number): string => {
    const date = new Date(timestamp)

    const vietnamDate = new Date(date.getTime() + 7 * 60 * 60 * 1000)
    return vietnamDate.toISOString().split('T')[0]
  }

  const vietnamDateStringToUTCDate = (dateString: string): Date => {
    const vietnamDate = new Date(dateString + 'T00:00:00+07:00')
    return vietnamDate
  }

  const transformApiDataToFormData = (apiData: DiscountApiData): DiscountFormData => {
    const startDate = timestampToVietnamDateString(apiData.from_date)
    const endDate = timestampToVietnamDateString(apiData.to_date)

    const marketingDays = dateUtils.convertNumbersToDayStrings(
      dateUtils.convertBitFlagsToDays(apiData.time_sale_date_week)
    )

    const marketingHours = dateUtils.convertNumbersToHourStrings(
      dateUtils.convertBitFlagsToHours(apiData.time_sale_hour_day)
    )

    return {
      storeUid: apiData.store_uid,
      channelUid: apiData.source_uid,
      discountType: apiData.discount_type,
      discountPercentage: apiData.discount_type === 'PERCENT' ? apiData.ta_discount * 100 : 0,
      discountAmount: apiData.discount_type === 'AMOUNT' ? apiData.ta_discount : 0,
      startDate,
      endDate,
      marketingDays,
      marketingHours,
      promotionUid: apiData.promotion_uid,
      promotionName: apiData.promotion?.promotion_name || '',
      filterState: {
        is_all: apiData.is_all as 0 | 1,
        is_item: apiData.is_item as 0 | 1,
        is_type: apiData.is_type as 0 | 1,
        type_id: apiData.type_id,
        item_id: apiData.item_id,
        is_combo: apiData.extra_data?.is_combo as 0 | 1,
        combo_id: apiData.extra_data?.combo_id || ''
      }
    }
  }

  useEffect(() => {
    if (discountData && isEditMode) {
      const transformedData = transformApiDataToFormData(discountData)
      setFormData(transformedData)
    }
  }, [discountData, isEditMode])

  const { validateFormData } = useDiscountFormValidation()
  const { mutate: createDiscount, isPending: isCreating } = useCreateDiscount(formData.storeUid || 'temp', {
    onSuccess: () => {
      navigate({ to: '/sale-channel/discount' })
    },
    onError: () => {}
  })

  const { mutate: editDiscount, isPending: isUpdating } = useEditDiscount({
    onSuccess: () => {
      navigate({ to: '/sale-channel/discount' })
    },
    onError: () => {}
  })

  const handleBack = () => {
    navigate({ to: '/sale-channel/discount' })
  }

  const transformToApiData = (): ApiDiscountFormData => {
    const selectedDays =
      formData.marketingDays.length > 0
        ? dateUtils.convertDayStringsToNumbers(formData.marketingDays)
        : [0, 1, 2, 3, 4, 5, 6]

    const selectedHours =
      formData.marketingHours.length > 0
        ? dateUtils.convertHourStringsToNumbers(formData.marketingHours)
        : dateUtils.getAllHours()

    return {
      discountType: formData.discountType,
      discountValue: formData.discountType === 'PERCENT' ? formData.discountPercentage / 100 : formData.discountAmount,
      fromDate: vietnamDateStringToUTCDate(formData.startDate),
      toDate: vietnamDateStringToUTCDate(formData.endDate),
      selectedDays,
      selectedHours,
      saleChannelUid: formData.channelUid,
      promotionUid: formData.promotionUid || '1576be99-992c-4085-a68c-105f7fbf0fff',
      filterState: formData.filterState
    }
  }

  const handleSave = async () => {
    if (!isFormValid) {
      return
    }

    if (isEditMode && discountData) {
      // Edit mode: merge formData with original discount data
      const apiFormData = transformToApiData()
      const updateRequest = transformFormDataToUpdateRequest(apiFormData, discountData)
      editDiscount(updateRequest)
    } else {
      // Create mode: use standard transformation
      const apiData = transformToApiData()

      const errors = validateFormData(apiData)

      if (errors.length > 0) {
        return
      }

      createDiscount(apiData)
    }
  }

  const updateFormData = (updates: Partial<DiscountFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
  }

  const isFormValid =
    formData.storeUid !== '' &&
    formData.channelUid !== '' &&
    (formData.discountType === 'PERCENT' ? formData.discountPercentage > 0 : formData.discountAmount > 0)

  return {
    formData,
    updateFormData,
    handleBack,
    handleSave,
    isFormValid,
    isLoading: isCreating || isUpdating || isLoadingDiscount,
    isEditMode
  }
}
