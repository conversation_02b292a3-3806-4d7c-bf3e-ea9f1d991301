import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import PromotionRevenueReport from '@/features/reports/revenue/categories/promotion'

export const Route = createFileRoute('/_authenticated/report/revenue/categories/promotion')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: PromotionRevenueReport
})
