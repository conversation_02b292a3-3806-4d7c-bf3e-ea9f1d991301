export interface PromotionRevenueListData {
  date: string
  tran_date?: number
  // Different endpoints may return either revenue_net or total_amount
  revenue_net?: number
  total_amount?: number
  total_bill?: number
  total_count?: number
  discount_amount?: number
}

export interface PromotionRevenueData {
  promotion_id: string
  promotion_name: string
  total_bill?: number
  // Totals at promotion level (optional since we aggregate by list_data)
  revenue_net?: number
  total_amount?: number
  total_count?: number
  discount_amount?: number
  list_data: Array<PromotionRevenueListData>
}

export interface PromotionRevenueParams {
  brand_uid: string
  company_uid: string
  start_date: number
  end_date: number
  list_store_uid: string
  store_open_at: number
  by_days: number
}

export interface PromotionRevenueResponse {
  data: PromotionRevenueData[]
  message: string
  track_id: string
}

// New types for promotion sale summary (without by_days breakdown)
export interface PromotionSaleSummaryData {
  promotion_id: string
  promotion_name: string
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  revenue_net: number
  deduct_tax_amount: number
  list_data: any[] // Empty array for summary mode
}

export interface PromotionSaleSummaryParams {
  brand_uid: string
  company_uid: string
  start_date: number
  end_date: number
  list_store_uid: string
  store_open_at: number
  limit?: number
}

export interface PromotionSaleSummaryResponse {
  data: PromotionSaleSummaryData[]
  message: string
  track_id: string
}

// New types for items sale summary
export interface ItemSaleSummaryData {
  group_id: string
  item_id: string
  item_name: string
  unit_id: string
  unit_name: string
  item_type_id: string
  item_type_name: string
  item_class_id: string
  item_class_name: string
  quantity_sold: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  revenue_net: number
  deduct_tax_amount: number
  list_data: any[] // Empty array for summary mode
}

export interface ItemSaleSummaryParams {
  brand_uid: string
  company_uid: string
  start_date: number
  end_date: number
  list_store_uid: string
  store_open_at: number
  limit?: number
  order_by?: 'revenue_net' | 'quantity_sold' | 'revenue_gross'
}

export interface ItemTypeData {
  item_type_id: string
  item_type_name: string
}

export interface ItemSaleSummaryResponse {
  data: {
    list_data_item_return: ItemSaleSummaryData[]
    other_data?: ItemTypeData[]
  }
  message: string
  track_id: string
}
