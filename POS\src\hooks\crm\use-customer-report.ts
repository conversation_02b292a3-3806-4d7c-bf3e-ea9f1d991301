import { useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'

import type {
  CustomerReportParams,
  CustomerReportData
} from '@/types/api/crm/customer-report'

import { customerReportApi } from '@/lib/api/crm/customer-report-api'
import { CRM_QUERY_KEYS } from '@/constants/crm'

export const useCustomerReport = (initialParams?: Partial<CustomerReportParams>) => {
  const [params, setParams] = useState<CustomerReportParams>({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end_date: new Date().toISOString().split('T')[0],
    pos_parent: 'BRAND-953H',
    ...initialParams
  })

  const isEnabled = !!(params.start_date && params.end_date)

  const registrationQuery = useQuery({
    queryKey: [CRM_QUERY_KEYS.CUSTOMER_REGISTRATION_REPORT, params],
    queryFn: () => customerReportApi.getCustomerRegistrationReport(params),
    enabled: isEnabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  })

  const membershipStatsQuery = useQuery({
    queryKey: [CRM_QUERY_KEYS.MEMBERSHIP_TYPE_CHANGE_STATS, params],
    queryFn: () => customerReportApi.getMembershipTypeChangeStats(params),
    enabled: isEnabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  })

  const orderByMembershipQuery = useQuery({
    queryKey: [CRM_QUERY_KEYS.ORDER_BY_MEMBERSHIP_TYPE, params],
    queryFn: () => customerReportApi.getOrderByMembershipType(params),
    enabled: isEnabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  })

  const isLoading = registrationQuery.isLoading || membershipStatsQuery.isLoading || orderByMembershipQuery.isLoading
  const error = registrationQuery.error?.message || membershipStatsQuery.error?.message || orderByMembershipQuery.error?.message || null

  const refetch = () => {
    registrationQuery.refetch()
    membershipStatsQuery.refetch()
    orderByMembershipQuery.refetch()
  }

  const updateParams = useCallback((newParams: Partial<CustomerReportParams>) => {
    setParams((prev: CustomerReportParams) => ({ ...prev, ...newParams }))
  }, [])

  const data: CustomerReportData = {
    registrationReport: registrationQuery.data || null,
    membershipTypeStats: membershipStatsQuery.data || null,
    orderByMembershipType: orderByMembershipQuery.data || null
  }

  return {
    data,
    isLoading,
    error,
    refetch,
    params,
    updateParams,
    queries: {
      registration: registrationQuery,
      membershipStats: membershipStatsQuery,
      orderByMembership: orderByMembershipQuery
    }
  }
}

export const useCustomerRegistrationReport = (params: CustomerReportParams) => {
  return useQuery({
    queryKey: [CRM_QUERY_KEYS.CUSTOMER_REGISTRATION_REPORT, params],
    queryFn: () => customerReportApi.getCustomerRegistrationReport(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 5 * 60 * 1000,
    retry: 2
  })
}

export const useMembershipTypeChangeStats = (params: CustomerReportParams) => {
  return useQuery({
    queryKey: [CRM_QUERY_KEYS.MEMBERSHIP_TYPE_CHANGE_STATS, params],
    queryFn: () => customerReportApi.getMembershipTypeChangeStats(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 5 * 60 * 1000,
    retry: 2
  })
}

export const useOrderByMembershipType = (params: CustomerReportParams) => {
  return useQuery({
    queryKey: [CRM_QUERY_KEYS.ORDER_BY_MEMBERSHIP_TYPE, params],
    queryFn: () => customerReportApi.getOrderByMembershipType(params),
    enabled: !!(params.start_date && params.end_date),
    staleTime: 5 * 60 * 1000,
    retry: 2
  })
}
