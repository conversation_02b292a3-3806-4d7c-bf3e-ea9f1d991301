import { z } from 'zod'

export const vatDiscountConfigSchema = z.object({
  discountPercentage: z
    .number()
    .min(0, 'Mức giảm phải lớn hơn hoặc bằng 0')
    .max(100, '<PERSON><PERSON><PERSON> giảm không được vượt quá 100%'),
  vatPercentage: z
    .number()
    .min(0, 'VAT phải lớn hơn hoặc bằng 0')
    .max(100, 'VAT không được vượt quá 100%'),
  programName: z.string().min(1, 'Tên chương trình chiến dịch là bắt buộc'),
  startDate: z.string().min(1, '<PERSON><PERSON>y bắt đầu là bắt buộc'),
  endDate: z.string().min(1, '<PERSON>ày kết thúc là bắt buộc')
})

export type VatDiscountConfigFormData = z.infer<typeof vatDiscountConfigSchema>
