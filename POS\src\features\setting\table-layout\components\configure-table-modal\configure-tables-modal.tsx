import React from 'react'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { ConfigureTableControls, ConfigureTableSidebar, ConfigureTableGrid } from './components'
import { useConfigureTableModal } from './hooks'

interface ConfigureTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
}

export const ConfigureTablesModal: React.FC<ConfigureTablesModalProps> = ({ open, onOpenChange, onCancel }) => {
  const {
    // State
    selectedStoreId,
    selectedAreaId,
    searchTerm,
    selectedTables,
    showColorPicker,
    config,
    selectAllInArea,
    applyToAllTables,
    stores,
    setShowColorPicker,
    setConfig,
    setSearchTerm,
    setSelectedAreaId,

    // Data
    areas,
    tablesByArea,
    isLoadingTables,
    isUpdating,

    // Actions
    handleStoreChange,
    handleTableSelect,
    handleSelectAllInArea,
    handleApplyToAllTables,
    handleClose: handleModalClose,
    handleSave: handleModalSave
  } = useConfigureTableModal()

  const handleClose = async () => {
    await handleModalClose()
    onOpenChange(false)
    onCancel()
  }

  const handleSave = async () => {
    const success = await handleModalSave()
    if (success) {
      onOpenChange(false)
      onCancel()
    }
  }

  const handleAreaSelect = (areaId: string) => {
    setSelectedAreaId(areaId)
  }

  const handleColorSelect = (color: string) => {
    setConfig({ ...config, color })
  }

  const handleConfigChange = (configUpdate: Partial<typeof config>) => {
    setConfig({ ...config, ...configUpdate })
  }

  return (
    <PosModal
      title='Cấu hình bàn'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleClose}
      onConfirm={() => {}}
      confirmText='Lưu'
      cancelText='Đóng'
      centerTitle={true}
      maxWidth='sm:max-w-7xl'
      isLoading={false}
      confirmDisabled={false}
      hideButtons={true}
    >
      <div className='flex h-[75vh] flex-col'>
        <ConfigureTableControls
          stores={stores}
          selectedStoreId={selectedStoreId}
          config={config}
          showColorPicker={showColorPicker}
          onStoreChange={handleStoreChange}
          onConfigChange={handleConfigChange}
          onColorPickerToggle={setShowColorPicker}
          onColorSelect={handleColorSelect}
        />

        {selectedStoreId ? (
          <div className='flex flex-1 overflow-hidden'>
            <ConfigureTableSidebar
              areas={areas}
              tables={Object.values(tablesByArea).flat()}
              selectedAreaId={selectedAreaId}
              isLoadingTables={isLoadingTables}
              onAreaSelect={handleAreaSelect}
            />

            <ConfigureTableGrid
              selectedStoreId={selectedStoreId}
              selectedAreaId={selectedAreaId}
              areas={areas}
              tablesByArea={tablesByArea}
              selectedTables={selectedTables}
              config={config}
              selectAllInArea={selectAllInArea}
              applyToAllTables={applyToAllTables}
              searchTerm={searchTerm}
              onTableSelect={handleTableSelect}
              onSelectAllInAreaChange={handleSelectAllInArea}
              onApplyToAllTablesChange={handleApplyToAllTables}
              onSearchChange={setSearchTerm}
            />
          </div>
        ) : (
          <div className='flex flex-1 items-center justify-center text-gray-500'>Vui lòng chọn cửa hàng</div>
        )}

        <div className='flex justify-end gap-3 border-t p-6'>
          <Button variant='outline' onClick={handleClose} disabled={isUpdating}>
            Đóng
          </Button>
          <Button onClick={handleSave} disabled={selectedTables.size === 0 || isUpdating}>
            {isUpdating ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </div>
    </PosModal>
  )
}
