import { api } from './api/pos/pos-api'
import {
  SaleNotSyncVatData,
  SaleNotSyncVatResponse,
  GetSaleNotSyncVatParams,
  GetAllSaleNotSyncVatParams
} from './sales-types'

/**
 * VAT-related Sales API functions
 */
export const salesApiVat = {
  /**
   * Get sale-not-sync-vat data (new endpoint)
   */
  getSaleNotSyncVat: async (params: GetSaleNotSyncVatParams): Promise<SaleNotSyncVatResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.companyUid,
      brand_uid: params.brandUid,
      store_uid: params.storeUid,
      start_date: params.startDate.toString(),
      end_date: params.endDate.toString(),
      page: (params.page || 1).toString(),
      results_per_page: (params.resultsPerPage || 50).toString()
    })

    // Add source_id if provided
    if (params.sourceId !== undefined) {
      queryParams.append('source_id', params.sourceId.toString())
    }

    const response = await api.get(`/v3/pos-client/sale-not-sync-vat?${queryParams.toString()}`)

    return response.data as SaleNotSyncVatResponse
  },

  /**
   * Get all sale-not-sync-vat data with pagination
   */
  getAllSaleNotSyncVat: async (params: GetAllSaleNotSyncVatParams): Promise<SaleNotSyncVatData[]> => {
    const allData: SaleNotSyncVatData[] = []
    let currentPage = 1
    const maxPages = params.maxPages || 100
    const resultsPerPage = 50

    while (currentPage <= maxPages) {
      const response = await salesApiVat.getSaleNotSyncVat({
        companyUid: params.companyUid,
        brandUid: params.brandUid,
        storeUid: params.storeUid,
        startDate: params.startDate,
        endDate: params.endDate,
        sourceId: params.sourceId,
        page: currentPage,
        resultsPerPage
      })

      if (!response.data || response.data.length === 0) {
        break
      }

      allData.push(...response.data)
      currentPage++
    }

    return allData
  }
}
