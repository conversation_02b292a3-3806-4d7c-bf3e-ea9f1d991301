/**
 * Utility functions for debugging localStorage data
 */
import {
  POS_USER_DATA,
  POS_JWT_TOKEN,
  POS_BRANDS_DATA,
  POS_STORES_DATA,
  POS_CITIES_DATA,
  POS_COMPANY_DATA,
  POS_USER_ROLE_DATA
} from '@/constants/local-storage'

// localStorage keys used by the application
export const STORAGE_KEYS = {
  POS_USER_DATA,
  POS_JWT_TOKEN,
  POS_BRANDS_DATA,
  POS_STORES_DATA,
  POS_CITIES_DATA,
  POS_COMPANY_DATA,
  POS_USER_ROLE_DATA,
  POS_SELECTED_BRAND: 'pos_selected_brand'
} as const

/**
 * Get all POS-related data from localStorage
 */
export const getAllPosData = () => {
  const data: Record<string, unknown> = {}

  Object.entries(STORAGE_KEYS).forEach(([key, storageKey]) => {
    try {
      const stored = localStorage.getItem(storageKey)
      data[key] = stored ? JSON.parse(stored) : null
    } catch (_error) {
      // Silent error handling - store raw value if J<PERSON><PERSON> parse fails
      data[key] = localStorage.getItem(storageKey)
    }
  })

  return data
}

/**
 * Log all POS data to console for debugging
 */
export const debugPosData = () => {
  // Debug function - console output removed for production
  const allData = getAllPosData()
  return allData
}

/**
 * Clear all POS-related localStorage data
 */
export const clearAllPosData = () => {
  // Clear all POS localStorage data
  Object.values(STORAGE_KEYS).forEach(storageKey => {
    localStorage.removeItem(storageKey)
  })
}

/**
 * Check if user is logged in based on localStorage
 */
export const isUserLoggedIn = () => {
  const userData = localStorage.getItem(STORAGE_KEYS.POS_USER_DATA)
  const jwtToken = localStorage.getItem(STORAGE_KEYS.POS_JWT_TOKEN)

  return !!(userData && jwtToken)
}

/**
 * Get stored brands count
 */
export const getStoredBrandsCount = () => {
  try {
    const brands = localStorage.getItem(STORAGE_KEYS.POS_BRANDS_DATA)
    return brands ? JSON.parse(brands).length : 0
  } catch {
    return 0
  }
}

/**
 * Get stored stores count
 */
export const getStoredStoresCount = () => {
  try {
    const stores = localStorage.getItem(STORAGE_KEYS.POS_STORES_DATA)
    return stores ? JSON.parse(stores).length : 0
  } catch {
    return 0
  }
}

/**
 * Get current selected brand
 */
export const getCurrentSelectedBrand = () => {
  try {
    const brand = localStorage.getItem(STORAGE_KEYS.POS_SELECTED_BRAND)
    return brand ? JSON.parse(brand) : null
  } catch {
    return null
  }
}

/**
 * Summary of localStorage state
 */
export const getStorageSummary = () => {
  return {
    isLoggedIn: isUserLoggedIn(),
    brandsCount: getStoredBrandsCount(),
    storesCount: getStoredStoresCount(),
    selectedBrand: getCurrentSelectedBrand(),
    hasUserData: !!localStorage.getItem(STORAGE_KEYS.POS_USER_DATA),
    hasJwtToken: !!localStorage.getItem(STORAGE_KEYS.POS_JWT_TOKEN),
    hasCompanyData: !!localStorage.getItem(STORAGE_KEYS.POS_COMPANY_DATA)
  }
}

// Make functions available globally for debugging in browser console
if (typeof window !== 'undefined') {
  ;(window as unknown as Record<string, unknown>).posDebug = {
    getAllData: getAllPosData,
    debug: debugPosData,
    clear: clearAllPosData,
    summary: getStorageSummary,
    isLoggedIn: isUserLoggedIn
  }
}
