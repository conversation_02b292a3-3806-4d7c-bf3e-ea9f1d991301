/**
 * Utility functions for handling days of week conversion
 * Each day has a value of 2^n where n is the day index (Monday = 0, Tuesday = 1, etc.)
 */

export interface DayOfWeek {
  value: string
  label: string
  numericValue: number
}

export const DAYS_OF_WEEK: DayOfWeek[] = [
  { value: 'monday', label: 'Thứ 2', numericValue: 4 }, // 2^2 = 4
  { value: 'tuesday', label: 'Thứ 3', numericValue: 8 }, // 2^3 = 8
  { value: 'wednesday', label: 'Thứ 4', numericValue: 16 }, // 2^4 = 16
  { value: 'thursday', label: 'Thứ 5', numericValue: 32 }, // 2^5 = 32
  { value: 'friday', label: 'Thứ 6', numericValue: 64 }, // 2^6 = 64
  { value: 'saturday', label: 'Thứ 7', numericValue: 128 }, // 2^7 = 128
  { value: 'sunday', label: '<PERSON><PERSON> nhật', numericValue: 256 } // 2^8 = 256
]

/**
 * Convert array of selected day values to numeric sum
 * @param selectedDays Array of day values (e.g., ['monday', 'tuesday'])
 * @returns Sum of numeric values for selected days
 */
export const convertDaysToNumeric = (selectedDays: string[]): number => {
  return selectedDays.reduce((sum, dayValue) => {
    const day = DAYS_OF_WEEK.find(d => d.value === dayValue)
    return sum + (day?.numericValue || 0)
  }, 0)
}

/**
 * Convert numeric sum back to array of selected day values
 * @param numericValue Sum of day values
 * @returns Array of selected day values
 */
export const convertNumericToDays = (numericValue: number): string[] => {
  const selectedDays: string[] = []

  // Check each day from highest to lowest value
  const sortedDays = [...DAYS_OF_WEEK].sort((a, b) => b.numericValue - a.numericValue)

  let remainingValue = numericValue

  for (const day of sortedDays) {
    if (remainingValue >= day.numericValue) {
      selectedDays.push(day.value)
      remainingValue -= day.numericValue
    }
  }

  // Return in original order (Monday to Sunday)
  return selectedDays.sort((a, b) => {
    const indexA = DAYS_OF_WEEK.findIndex(d => d.value === a)
    const indexB = DAYS_OF_WEEK.findIndex(d => d.value === b)
    return indexA - indexB
  })
}

/**
 * Get day label by value
 * @param dayValue Day value (e.g., 'monday')
 * @returns Day label (e.g., 'Thứ 2')
 */
export const getDayLabel = (dayValue: string): string => {
  return DAYS_OF_WEEK.find(d => d.value === dayValue)?.label || dayValue
}

/**
 * Get day numeric value by day value
 * @param dayValue Day value (e.g., 'monday')
 * @returns Numeric value (e.g., 4)
 */
export const getDayNumericValue = (dayValue: string): number => {
  return DAYS_OF_WEEK.find(d => d.value === dayValue)?.numericValue || 0
}

/**
 * Check if a specific day is selected in the numeric value
 * @param numericValue Sum of day values
 * @param dayValue Day value to check
 * @returns True if day is selected
 */
export const isDaySelected = (numericValue: number, dayValue: string): boolean => {
  const dayNumericValue = getDayNumericValue(dayValue)
  return (numericValue & dayNumericValue) === dayNumericValue
}

/**
 * Toggle a day in the numeric value
 * @param numericValue Current sum of day values
 * @param dayValue Day value to toggle
 * @returns New sum of day values
 */
export const toggleDay = (numericValue: number, dayValue: string): number => {
  const dayNumericValue = getDayNumericValue(dayValue)

  if (isDaySelected(numericValue, dayValue)) {
    // Remove day
    return numericValue - dayNumericValue
  } else {
    // Add day
    return numericValue + dayNumericValue
  }
}

/**
 * Hours utility functions
 * Each hour has a value of 2^n where n is the hour (0h = 2^0, 1h = 2^1, 2h = 2^2, etc.)
 */

export interface HourSlot {
  value: string
  numericValue: number
}

export const TIME_SLOTS: HourSlot[] = [
  { value: '0h', numericValue: 1 }, // 2^0 = 1
  { value: '1h', numericValue: 2 }, // 2^1 = 2
  { value: '2h', numericValue: 4 }, // 2^2 = 4
  { value: '3h', numericValue: 8 }, // 2^3 = 8
  { value: '4h', numericValue: 16 }, // 2^4 = 16
  { value: '5h', numericValue: 32 }, // 2^5 = 32
  { value: '6h', numericValue: 64 }, // 2^6 = 64
  { value: '7h', numericValue: 128 }, // 2^7 = 128
  { value: '8h', numericValue: 256 }, // 2^8 = 256
  { value: '9h', numericValue: 512 }, // 2^9 = 512
  { value: '10h', numericValue: 1024 }, // 2^10 = 1024
  { value: '11h', numericValue: 2048 }, // 2^11 = 2048
  { value: '12h', numericValue: 4096 }, // 2^12 = 4096
  { value: '13h', numericValue: 8192 }, // 2^13 = 8192
  { value: '14h', numericValue: 16384 }, // 2^14 = 16384
  { value: '15h', numericValue: 32768 }, // 2^15 = 32768
  { value: '16h', numericValue: 65536 }, // 2^16 = 65536
  { value: '17h', numericValue: 131072 }, // 2^17 = 131072
  { value: '18h', numericValue: 262144 }, // 2^18 = 262144
  { value: '19h', numericValue: 524288 }, // 2^19 = 524288
  { value: '20h', numericValue: 1048576 }, // 2^20 = 1048576
  { value: '21h', numericValue: 2097152 }, // 2^21 = 2097152
  { value: '22h', numericValue: 4194304 }, // 2^22 = 4194304
  { value: '23h', numericValue: 8388608 } // 2^23 = 8388608
]

/**
 * Convert array of selected hour values to numeric sum
 * @param selectedHours Array of hour values (e.g., ['1h', '2h'])
 * @returns Sum of numeric values for selected hours
 */
export const convertHoursToNumeric = (selectedHours: string[]): number => {
  return selectedHours.reduce((sum, hourValue) => {
    const hour = TIME_SLOTS.find(h => h.value === hourValue)
    return sum + (hour?.numericValue || 0)
  }, 0)
}

/**
 * Convert numeric sum back to array of selected hour values
 * @param numericValue Sum of hour values
 * @returns Array of selected hour values
 */
export const convertNumericToHours = (numericValue: number): string[] => {
  const selectedHours: string[] = []

  // Check each hour from highest to lowest value
  const sortedHours = [...TIME_SLOTS].sort((a, b) => b.numericValue - a.numericValue)

  let remainingValue = numericValue

  for (const hour of sortedHours) {
    if (remainingValue >= hour.numericValue) {
      selectedHours.push(hour.value)
      remainingValue -= hour.numericValue
    }
  }

  // Return in original order (1h to 23h)
  return selectedHours.sort((a, b) => {
    const indexA = TIME_SLOTS.findIndex(h => h.value === a)
    const indexB = TIME_SLOTS.findIndex(h => h.value === b)
    return indexA - indexB
  })
}

/**
 * Example usage:
 *
 * // Days conversion
 * const selectedDays = ['monday', 'wednesday', 'friday']
 * const numericDays = convertDaysToNumeric(selectedDays) // Result: 4 + 16 + 64 = 84
 * const backToDays = convertNumericToDays(84) // Result: ['monday', 'wednesday', 'friday']
 *
 * // Hours conversion
 * const selectedHours = ['0h', '2h', '4h']
 * const numericHours = convertHoursToNumeric(selectedHours) // Result: 1 + 4 + 16 = 21
 * const backToHours = convertNumericToHours(21) // Result: ['0h', '2h', '4h']
 *
 * // Check if specific day/hour is selected
 * const isMonday = isDaySelected(84, 'monday') // Result: true
 * const isTuesday = isDaySelected(84, 'tuesday') // Result: false
 */
