import { useQuery } from '@tanstack/react-query'

import { getUsingMonthApi } from '@/lib/api/crm/using-month'

import { CRM_QUERY_KEYS } from '@/constants/crm/query-keys'

import type { UsingMonthData } from '@/features/crm/using-month/types'

interface UsingMonthParams {
  year: string
  month: string
  pos_parent: string
}

export function useUsingMonth(params: UsingMonthParams | null) {
  return useQuery<UsingMonthData, Error>({
    queryKey: [CRM_QUERY_KEYS.USING_MONTH, params],
    queryFn: async () => {
      if (!params) {
        throw new Error('Params are required')
      }
      const response = await getUsingMonthApi(params)
      return response.data
    },
    enabled: !!params
  })
}
