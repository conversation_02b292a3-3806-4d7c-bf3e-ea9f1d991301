import { apiClient } from './api/pos/pos-api'

// Payment Methods API Types
export interface ApiPaymentMethod {
  payment_method_id: string
  payment_method_name: string
  payment_fee_extra: number
  payment_fee_type: number
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  payment_type: number
  image_path: string
  description: string
  sort: number
  is_fb: number
  extra_data: {
    require_traceno?: number
  } | null
  active: number
  revision: number
  brand_uid: string
  company_uid: string
  is_fabi: number
  store_uid: string
  row: number
  stores: number
  list_payment_method_uid: string[]
}

export interface PaymentMethodsListParams {
  company_uid: string
  brand_uid: string
  page?: number
  limit?: number
  store_uid?: string
  skip_limit?: boolean
  is_fb?: number
}

export interface PaymentMethodsListResponse {
  data: ApiPaymentMethod[]
  track_id: string
}

export interface CreatePaymentMethodRequest {
  payment_method_name: string
  payment_method_id: string
  payment_fee_extra: number
  payment_fee_type: number
  payment_type: number
  stores: string[]
  config_keys: object
  company_uid: string
  brand_uid: string
  background?: string
  image_path?: string
  extra_data?: {
    require_traceno?: number
  }
}

export interface ImageUploadResponse {
  data: {
    image_url: string
  }
  track_id: string
}

export interface UpdatePaymentMethodRequest extends Partial<CreatePaymentMethodRequest> {
  id: string
}

export interface DeletePaymentMethodParams {
  id: string
  company_uid: string
  brand_uid: string
}

/**
 * Payment Methods API Service
 */
export const paymentMethodsApi = {
  /**
   * Get payment methods list
   */
  getPaymentMethods: async (params: PaymentMethodsListParams): Promise<PaymentMethodsListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid
    })

    // Add optional parameters only if provided
    if (params.page !== undefined) {
      queryParams.set('page', params.page.toString())
    }

    if (params.limit !== undefined) {
      queryParams.set('limit', params.limit.toString())
    }

    if (params.store_uid && params.store_uid !== 'all') {
      queryParams.set('store_uid', params.store_uid)
    }

    if (params.skip_limit !== undefined) {
      queryParams.set('skip_limit', params.skip_limit.toString())
    }

    if (params.is_fb !== undefined) {
      queryParams.set('is_fb', params.is_fb.toString())
    }

    const response = await apiClient.get(`/mdata/v1/payment-methods?${queryParams.toString()}`)

    return response.data
  },

  /**
   * Upload image for payment method
   */
  uploadImage: async (file: File): Promise<ImageUploadResponse> => {
    console.log('Uploading file:', file.name, file.size, file.type)

    const formData = new FormData()
    formData.append('file', file)

    console.log('FormData entries:', Array.from(formData.entries()))

    const response = await apiClient.post('/v3/pos-cms/image/upload', formData, {
      headers: {
        // Let axios set Content-Type automatically with boundary
        'Content-Type': undefined
      }
    })

    console.log('Upload response:', response.data)
    return response.data
  },

  /**
   * Create a new payment method
   */
  createPaymentMethod: async (data: CreatePaymentMethodRequest): Promise<ApiPaymentMethod> => {
    const response = await apiClient.post('/mdata/v1/payment-method', data)
    return response.data.data || response.data
  },

  /**
   * Update an existing payment method
   */
  updatePaymentMethod: async (data: any): Promise<ApiPaymentMethod> => {
    const response = await apiClient.put('/mdata/v1/payment-method', data)
    return response.data.data || response.data
  },

  /**
   * Delete a payment method
   */
  deletePaymentMethod: async (params: DeletePaymentMethodParams): Promise<void> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      id: params.id
    })

    await apiClient.delete(`/mdata/v1/payment-method?${queryParams.toString()}`)
  },

  /**
   * Get payment method by ID
   */
  getPaymentMethodById: async (id: string, companyUid: string, brandUid: string): Promise<ApiPaymentMethod> => {
    const queryParams = new URLSearchParams({
      company_uid: companyUid,
      brand_uid: brandUid
    })

    const response = await apiClient.get(`/mdata/v1/payment-method/${id}?${queryParams.toString()}`)

    return response.data.data || response.data
  },

  /**
   * Get payment method with stores details
   */
  getPaymentMethodWithStores: async (storeUids: string[]): Promise<any> => {
    const queryParams = new URLSearchParams({
      list_payment_method_uid: storeUids.join(',')
    })

    const response = await apiClient.get(`/mdata/v1/payment-method?${queryParams.toString()}`)

    return response.data.data || response.data
  },

  /**
   * Get payment method detail by payment_method_id
   */
  getPaymentMethodDetail: async (paymentMethodId: string, companyUid: string, brandUid: string): Promise<any> => {
    const queryParams = new URLSearchParams({
      company_uid: companyUid,
      brand_uid: brandUid,
      payment_method_id: paymentMethodId
    })

    const response = await apiClient.get(`/mdata/v1/payment-method?${queryParams.toString()}`)

    return response.data.data || response.data
  }
}

export default paymentMethodsApi
