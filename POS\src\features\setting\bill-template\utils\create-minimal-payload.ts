import type { UseBillTemplateRequest } from '@/lib/bill-template-api'

/**
 * Create minimal payload for "Use Bill Template" API call
 * Only includes required fields to avoid validation errors
 */
export const createMinimalUseBillTemplatePayload = (
  storeData: any,
  templateNumber: number = 1
): Partial<UseBillTemplateRequest> => {
  const storeName = storeData.store_name || storeData.name
  const storeId = storeData.store_id || storeData.code || `STORE-${storeData.id}`
  const brandUid = storeData.brand_uid || storeData.brandId
  const companyUid = storeData.company_uid || storeData.companyId

  if (!storeName) {
    throw new Error('Store name is required but not found in store data')
  }

  if (!storeData.id) {
    throw new Error('Store ID is required but not found in store data')
  }

  const payload = {
    id: storeData.id,
    store_name: storeName,
    store_id: storeId,
    fb_store_id: storeData.fb_store_id || 0,
    brand_uid: brandUid || '',
    company_uid: companyUid || '',
    extra_data: {
      ...(storeData.extra_data || storeData.extraData || {}),
      bill_template: templateNumber
    },
    active: storeData.active !== undefined ? storeData.active : 1,
    is_delivery_direct: storeData.is_delivery_direct || 0,
    latitude: storeData.latitude || 0,
    longitude: storeData.longitude || 0,
    email: storeData.email || '',
    phone: storeData.phone || '',
    logo: storeData.logo || '',
    background: storeData.background || '',
    facebook: storeData.facebook || '',
    website: storeData.website || '',
    address: storeData.address || '',
    description: storeData.description || '',
    workstation_id: storeData.workstation_id || 0,
    is_default: storeData.is_default || 0,
    is_test: storeData.is_test || 0,
    store_address: storeData.store_address || {},
    revision: storeData.revision || 0,
    expiry_date: storeData.expiry_date || Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60,
    sort: storeData.sort || 0,
    last_synced_transaction: storeData.last_synced_transaction || null,
    city_uid: storeData.city_uid || storeData.cityId || '',
    district: storeData.district || null,
    pos_server_group_uid: storeData.pos_server_group_uid || null,
    is_ahamove_active: storeData.is_ahamove_active || 0,
    delivery_services: storeData.delivery_services || '',
    email_delivery_service: storeData.email_delivery_service || '',
    phone_manager: storeData.phone_manager || '',
    is_fabi: storeData.is_fabi !== undefined ? storeData.is_fabi : 1,
    partner: storeData.partner || '',
    pos_type: storeData.pos_type || '',
    operation_form: storeData.operation_form || '',
    size: storeData.size || 0,
    currency: storeData.currency || 'VND',
    ward: storeData.ward || null,
    created_by: storeData.created_by || 'system',
    updated_by: storeData.updated_by || 'system',
    deleted_by: storeData.deleted_by || null,
    created_at: storeData.created_at || Math.floor(Date.now() / 1000),
    updated_at: Math.floor(Date.now() / 1000),
    deleted_at: storeData.deleted_at || null,
    deleted: storeData.deleted || false,
    brand: storeData.brand || {},
    company: storeData.company || {},
    city: storeData.city || {}
  }

  return payload
}
