import { useMemo, useEffect, useRef } from 'react'
import { useQuery } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { salesApi } from '@/lib/sales-api'
import { getStoredApiStores, fetchAndSyncStores } from '@/lib/stores-api'

// Query keys for cache management
export const accountingSalesAllKeys = {
  all: ['accounting-sales-all'] as const,
  lists: () => [...accountingSalesAllKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) =>
    [...accountingSalesAllKeys.lists(), filters] as const,
}

// AccountingSaleData type based on the API response structure
export interface AccountingSaleData {
  id: string
  created_at: string
  updated_at: string
  tran_id: string
  origin_tran_id: string
  tran_no: string
  tran_date: number
  currency: string
  discount_extra: number
  discount_extra_amount: number
  employee_id: string
  employee_name: string
  start_date: number
  start_hour: number
  start_minute: number
  end_hour: number
  end_minute: number
  sale_note: string
  sale_type: string
  sale_method: string | null
  service_charge: number
  service_charge_amount: number
  shift_id: string
  ship_fee_amount: number
  source_fb_id: string
  table_name: string
  total_amount: number
  voucher_amount: number
  voucher_code: string
  voucher_name: string
  foodbook_order_id: string
  source_voucher: string
  store_uid: string
  brand_uid: string
  company_uid: string
  table_id: string | null
  store_id: string | null
  brand_id: string
  company_id: string | null
  device_code: string
  order_type: string
  area_id: string | null
  area_name: string
  source_deli: string
  sync_status: number
  discount_extra_name: string
  service_charge_name: string
  voucher_extra: number
  vat_amount: number
  vat_extra: number
  amount_discount_detail: number
  amount_discount_price: number
  amount_origin: number
  raw_data: unknown | null
  voucher_amount_paid: number
  state_action_bill: number
  version_app: string
  city_uid: string
  sale_updated_at: number
  extra_data: {
    Point: number
    mkt_max: number
    comm_max: number
    peo_count: number
    customer_name: string
    customer_phone: string
    [key: string]: unknown
  }
  commission_amount: number
  partner_marketing_amount: number
  discount_vat_amount: number
  payment_method_id: string
  payment_method_name: string
}

interface UseAccountingSalesAllOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  autoFetch?: boolean
  maxPages?: number
}

interface UseAccountingSalesAllReturn {
  data: (AccountingSaleData & { storeName: string })[]
  totalAmount: number
  totalTransactions: number
  isLoading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Hook to fetch ALL accounting sales data at once
 * Fetches all pages automatically and displays skeleton while loading
 */
export function useAccountingSalesAll({
  dateRange,
  selectedStores = ['all-stores'],
  autoFetch = true,
  maxPages = 50,
}: UseAccountingSalesAllOptions): UseAccountingSalesAllReturn {
  const { selectedBrand, currentBrandApiStores, setApiStores } = usePosStores()
  const { company } = useCurrentCompany()

  // Use direct values instead of memoized to ensure query key changes
  const companyId = company?.id
  const brandId = selectedBrand?.id

  // Use refs to track current values for event listener
  const currentBrandIdRef = useRef(brandId)
  const currentCompanyIdRef = useRef(companyId)

  // Update refs when values change
  useEffect(() => {
    currentBrandIdRef.current = brandId
    currentCompanyIdRef.current = companyId
  }, [brandId, companyId])

  // Fetch stores when brand changes and no stores are available
  useEffect(() => {
    const fetchStores = async () => {
      if (brandId && companyId && currentBrandApiStores.length === 0) {
        try {
          const fetchedStores = await fetchAndSyncStores(companyId, brandId)
          setApiStores(fetchedStores)
        } catch (_error) {
          // Silent error handling
        }
      }
    }

    fetchStores()
  }, [brandId, companyId, currentBrandApiStores.length, setApiStores])

  // Convert dates to timestamps (in milliseconds)
  const startTime = dateRange?.from ? dateRange.from.getTime() : undefined
  const endTime = dateRange?.to ? dateRange.to.getTime() : undefined

  // Determine which stores to fetch
  const storesToFetch = useMemo(() => {
    if (selectedStores.includes('all-stores')) {
      return currentBrandApiStores?.map((store) => store.id) || []
    }
    return selectedStores
  }, [selectedStores, currentBrandApiStores])

  // Create store name mapping
  const storeNameMap = useMemo(() => {
    const map = new Map<string, string>()
    currentBrandApiStores?.forEach((store) => {
      map.set(store.id, store.store_name)
    })
    return map
  }, [currentBrandApiStores])

  const storeUidsString = storesToFetch.join(',')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: accountingSalesAllKeys.list({
      companyId,
      brandId,
      storeUidsString,
      startTime,
      endTime,
      maxPages,
    }),
    queryFn: async () => {
      if (
        !brandId ||
        !companyId ||
        !startTime ||
        !endTime ||
        !storeUidsString
      ) {
        // Return empty data instead of throwing error
        return []
      }

      const allSalesData: (AccountingSaleData & { storeName: string })[] = []
      let currentPage = 1
      const pageSize = 50

      // Fetch all pages
      while (currentPage <= maxPages) {
        try {
          const response = await salesApi.getSalesReport({
            companyUid: companyId,
            brandUid: brandId,
            listStoreUid: storeUidsString,
            startDate: startTime,
            endDate: endTime,
            page: currentPage,
          })

          const salesData = response.data || []

          // If no data returned, we've reached the end
          if (salesData.length === 0) {
            break
          }

          // Add store names and push to results
          const salesWithStoreName = salesData.map((sale) => {
            const storeNameFromMap = storeNameMap.get(sale.store_uid)
            const storeInfo = currentBrandApiStores?.find(
              (store) => store.id === sale.store_uid
            )
            const storedStores = getStoredApiStores()
            const storedStoreInfo = storedStores.find(
              (store) => store.id === sale.store_uid
            )

            const storeName =
              storeNameFromMap ||
              storeInfo?.store_name ||
              storedStoreInfo?.store_name ||
              sale.store_name ||
              `Store ${sale.store_uid}`

            // Cast SaleData to AccountingSaleData (same as other hooks do)
            return {
              ...(sale as unknown as AccountingSaleData),
              storeName,
            }
          })

          allSalesData.push(...salesWithStoreName)

          // If we got less than expected page size, we've reached the end
          if (salesData.length < pageSize) {
            break
          }

          currentPage++
        } catch (_error) {
          // Stop fetching on error
          break
        }
      }

      return allSalesData
    },
    enabled: Boolean(
      autoFetch &&
        brandId &&
        companyId &&
        startTime &&
        endTime &&
        storeUidsString
    ),
    staleTime: 0, // Always consider data stale to ensure refetch
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnMount: true, // Always refetch on mount to ensure fresh data
    refetchOnWindowFocus: false, // Don't refetch on window focus
  })

  // Force refetch when brand changes
  useEffect(() => {
    if (brandId && companyId && startTime && endTime && storeUidsString) {
      refetch()
    }
  }, [brandId, companyId, refetch, startTime, endTime, storeUidsString])

  // Process the data
  const processedData = useMemo(() => {
    const salesData = data || []
    let totalAmount = 0
    let totalTransactions = 0

    salesData.forEach((sale) => {
      totalAmount += sale.total_amount || 0
      totalTransactions += 1
    })

    return {
      data: salesData,
      totalAmount,
      totalTransactions,
    }
  }, [data])

  return {
    ...processedData,
    isLoading,
    error: error?.message || null,
    refetch,
  }
}
