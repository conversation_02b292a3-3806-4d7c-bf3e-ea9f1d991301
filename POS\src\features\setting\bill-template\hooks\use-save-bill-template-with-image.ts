import { useMutation, useQueryClient } from '@tanstack/react-query'

import { imagesApi } from '@/lib/api/pos/images-api'
import { billTemplateApi } from '@/lib/bill-template-api'

import type { SaveBillTemplateRequest, SaveBillTemplateResponse } from '../types'

interface SaveBillTemplateWithImageRequest {
  payload: SaveBillTemplateRequest
  logoFile?: File | null
  isLogoRemoved?: boolean
}

interface UseSaveBillTemplateWithImageOptions {
  onSuccess?: (data: SaveBillTemplateResponse) => void
  onError?: (error: Error) => void
}

export const useSaveBillTemplateWithImage = (options?: UseSaveBillTemplateWithImageOptions) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ payload, logoFile, isLogoRemoved }: SaveBillTemplateWithImageRequest) => {
      let finalPayload = { ...payload }

      if (logoFile) {
        // Upload new image
        try {
          const uploadResponse = await imagesApi.uploadImage(logoFile)

          finalPayload = {
            ...finalPayload,
            logo: uploadResponse.data.image_url,
            extra_data: {
              ...finalPayload.extra_data,
              logo: uploadResponse.data.image_url
            }
          }
        } catch (_) {
          throw new Error('Không thể upload ảnh. Vui lòng thử lại.')
        }
      } else if (isLogoRemoved) {
        // Logo was explicitly removed by user
        finalPayload = {
          ...finalPayload,
          logo: null,
          extra_data: {
            ...finalPayload.extra_data,
            logo: null
          } as any
        }
      }

      const result = await billTemplateApi.saveBillTemplate(finalPayload)
      return result
    },
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: ['bill-template']
      })

      options?.onSuccess?.(data)
    },
    onError: (error: Error) => {
      options?.onError?.(error)
    }
  })
}
