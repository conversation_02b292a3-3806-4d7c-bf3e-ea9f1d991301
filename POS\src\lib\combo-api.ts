import type { NormalCombosResponse, SpecialCombosResponse } from '@/types/api/combo'

import { crmApi } from '@/lib/api/crm/crm-api'

export const comboApi = {
  getNormalCombos: async (page: number = 1, posParent: string): Promise<NormalCombosResponse> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('page', page.toString())
      queryParams.append('pos_parent', posParent || '')

      const response = await crmApi.get(`/settings/get-normal-combos-by-pos-parent?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from normal combos API')
      }

      return response.data as NormalCombosResponse
    } catch (error) {
      await new Promise(resolve => setTimeout(resolve, 500))
      return { data: [], count: 0, totalPage: 0 }
    }
  },

  getSpecialCombos: async (page: number = 1, posParent: string): Promise<SpecialCombosResponse> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('page', page.toString())
      queryParams.append('pos_parent', posParent || '')

      const response = await crmApi.get(`/settings/get-special-combos-by-pos-parent?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from special combos API')
      }

      return response.data as SpecialCombosResponse
    } catch (error) {
      await new Promise(resolve => setTimeout(resolve, 500))
      return { data: [], count: 0, totalPage: 0 }
    }
  }
}
