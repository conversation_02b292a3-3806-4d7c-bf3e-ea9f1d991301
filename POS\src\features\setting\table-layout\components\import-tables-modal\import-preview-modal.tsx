import React, { useState } from 'react'

import { <PERSON><PERSON>, <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui'

import { useImportTables } from '../../hooks'
import { StoreSelector } from '../header'

interface TableData {
  tenBan: string
  khuVuc: string
  nguon: string
  monDatTruoc: string
  moTa: string
}

interface ImportPreviewModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tableData: TableData[]
  onSave: (selectedStore: string) => void
  onCancel: () => void
}

export const ImportPreviewModal: React.FC<ImportPreviewModalProps> = ({
  open,
  onOpenChange,
  tableData,
  onSave,
  onCancel
}) => {
  const [selectedStore, setSelectedStore] = useState<string>('')
  const { importTables, isImporting } = useImportTables()

  const handleSave = () => {
    if (selectedStore && tableData.length > 0) {
      importTables(
        {
          storeUid: selectedStore,
          tableData
        },
        {
          onSuccess: () => {
            onSave(selectedStore)
            onOpenChange(false)
          }
        }
      )
    }
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex max-h-[90vh] !max-w-4xl flex-col overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='text-xl font-semibold'>Thêm bàn</DialogTitle>
        </DialogHeader>

        <div className='flex flex-1 flex-col space-y-4 overflow-hidden'>
          {/* Store Selection */}
          <div className='flex items-center justify-between'>
            <div className='text-sm text-gray-600'>Các bàn sẽ áp dụng cho cửa hàng được chọn</div>
            <StoreSelector
              value={selectedStore}
              onValueChange={setSelectedStore}
              placeholder='Chọn điểm áp dụng'
              className='w-64'
            />
          </div>

          {/* Warning Message */}
          <div className='rounded-lg border border-orange-200 bg-orange-50 p-3'>
            <p className='text-sm font-medium text-orange-600'>
              Các khu vực chưa tồn tại sẽ được tạo trước khi thêm bàn
            </p>
          </div>

          {/* Table Preview */}
          <div className='flex-1 overflow-auto rounded-lg border'>
            <table className='w-full'>
              <thead className='sticky top-0 bg-gray-100'>
                <tr>
                  <th className='border-r px-4 py-3 text-left text-sm font-medium text-gray-700'>Tên bàn</th>
                  <th className='border-r px-4 py-3 text-left text-sm font-medium text-gray-700'>Khu vực</th>
                  <th className='border-r px-4 py-3 text-left text-sm font-medium text-gray-700'>Nguồn</th>
                  <th className='border-r px-4 py-3 text-left text-sm font-medium text-gray-700'>Món đặt trước</th>
                  <th className='px-4 py-3 text-left text-sm font-medium text-gray-700'>Mô tả</th>
                </tr>
              </thead>
              <tbody>
                {tableData.map((row, index) => (
                  <tr key={index} className='border-b hover:bg-gray-50'>
                    <td className='border-r px-4 py-3 text-sm text-gray-900'>{row.tenBan}</td>
                    <td className='border-r px-4 py-3 text-sm text-gray-900'>{row.khuVuc}</td>
                    <td className='border-r px-4 py-3 text-sm text-gray-900'>{row.nguon}</td>
                    <td className='border-r px-4 py-3 text-sm text-gray-900'>{row.monDatTruoc}</td>
                    <td className='px-4 py-3 text-sm text-gray-900'>{row.moTa}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Action Buttons */}
          <div className='flex justify-end space-x-3 border-t pt-4'>
            <Button variant='outline' onClick={handleCancel}>
              Đóng
            </Button>
            <Button
              onClick={handleSave}
              disabled={!selectedStore || isImporting}
              className='bg-green-600 hover:bg-green-700'
            >
              {isImporting ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
