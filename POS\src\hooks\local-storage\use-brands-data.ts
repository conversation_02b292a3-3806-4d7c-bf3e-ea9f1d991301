import { useMemo } from 'react'

import type { BrandsData } from '@/types/brands-data'

import { POS_BRANDS_DATA } from '@/constants/local-storage'

export const useBrandsData = (): BrandsData | null => {
  return useMemo(() => {
    try {
      const brandsData = localStorage.getItem(POS_BRANDS_DATA)
      if (brandsData) {
        return JSON.parse(brandsData) as BrandsData
      }
      return null
    } catch (error) {
      console.error('Error parsing pos_brands_data from localStorage:', error)
      return null
    }
  }, [])
}
