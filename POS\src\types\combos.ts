// Combo API data structure
export interface ComboApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  ta_combo: number
  ots_combo: number
  is_all: 0 | 1
  is_type: 0 | 1
  is_item: 0 | 1
  type_id: string
  item_id: string
  combo_type: 'PERCENT' | 'AMOUNT'
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: 0 | 1
  }
  active: 0 | 1
  revision: number | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  sort: number
  store_uid: string
  combo_clone_id: string | null
  source_uid: string
  promotion: {
    id: string
    sort: number
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number
    brand_uid: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    source_uid: string
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    promotion_id: string
    promotion_name: string
    partner_auto_gen: 0 | 1
  }
  store_name?: string
  source: {
    id: string
    sort: number
    is_fb: 0 | 1
    active: 0 | 1
    deleted: boolean
    is_fabi: 0 | 1
    revision: number | null
    brand_uid: string
    source_id: string
    store_uid: string
    created_at: number
    created_by: string | null
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, any>
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    source_name: string
    source_type: any[]
    partner_config: 0 | 1
  }
}

// Converted combo data structure
export interface Combo {
  id: string
  taCombo: number
  otsCombo: number
  isAll: number
  isType: number
  isItem: number
  typeId: string
  itemId: string
  comboType: string
  fromDate: number
  toDate: number
  active: number
  storeUid: string
  storeName: string
  sourceUid: string
  promotionUid: string
  promotionId: string
  promotionName: string
  partnerAutoGen: number
  // Combo information
  comboId: string
  isCombo: number
  source: {
    id: string
    sourceId: string
    sourceName: string
    sourceType: string[]
    active: number
  }
  createdAt: number
  createdBy: string
  updatedAt: number
  updatedBy: string | null
  deleted?: boolean
  deletedAt?: number | null
  deletedBy?: string | null
}

// API response structure
export interface CombosApiResponse {
  data: ComboApiData[]
  track_id: string
}

// Parameters for fetching combos
export interface GetCombosParams {
  companyUid?: string
  brandUid?: string
  page?: number
  listStoreUid?: string[]
  promotionPartnerAutoGen?: number
  status?: 'expired' | 'unexpired'
  active?: number
  searchTerm?: string
}

// Convert API data to internal format
export function convertApiComboToCombo(apiCombo: ComboApiData): Combo {
  return {
    id: apiCombo.id,
    taCombo: apiCombo.ta_combo,
    otsCombo: apiCombo.ots_combo,
    isAll: apiCombo.is_all,
    isType: apiCombo.is_type,
    isItem: apiCombo.is_item,
    typeId: apiCombo.type_id,
    itemId: apiCombo.item_id,
    comboType: apiCombo.combo_type,
    fromDate: apiCombo.from_date,
    toDate: apiCombo.to_date,
    active: apiCombo.active,
    storeUid: apiCombo.store_uid,
    storeName: apiCombo.store_name || '',
    sourceUid: apiCombo.source_uid,
    promotionUid: apiCombo.promotion_uid,
    promotionId: apiCombo.promotion.promotion_id,
    promotionName: apiCombo.promotion?.promotion_name || '',
    partnerAutoGen: apiCombo.promotion.partner_auto_gen,
    // Combo information
    comboId: apiCombo.extra_data.combo_id,
    isCombo: apiCombo.extra_data.is_combo,
    source: {
      id: apiCombo.source.id,
      sourceId: apiCombo.source.source_id,
      sourceName: apiCombo.source.source_name,
      sourceType: apiCombo.source.source_type,
      active: apiCombo.source.active
    },
    createdAt: apiCombo.created_at,
    createdBy: apiCombo.created_by,
    updatedAt: apiCombo.updated_at,
    updatedBy: apiCombo.updated_by,
    deleted: apiCombo.deleted,
    deletedAt: apiCombo.deleted_at,
    deletedBy: apiCombo.deleted_by
  }
}

// Membership Combo Types
export interface MembershipComboApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number | null
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  membership_type_id: string
  membership_type_name: string
  birth_ta_combo: number
  birth_ots_combo: number
  birth_time_range_apply: number
  ta_combo: number
  ots_combo: number
  is_all: number
  is_type: number
  is_item: number
  type_id: string
  item_id: string
  combo_type: string
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: number
  }
  active: number
  revision: number
  sort: number
  promotion_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  combo_clone_id: string | null
  promotion: {
    id: string
    company_uid: string
    brand_uid: string
    deleted: boolean
    promotion_id: string
    promotion_name: string
    active: number
    partner_auto_gen: number
    is_fabi: number
    source_uid: string
    description: string | null
  }
}

export interface MembershipComboApiResponse {
  data: MembershipComboApiData[]
  track_id: string
}

// Converted membership combo data structure
export interface MembershipCombo {
  id: string
  membershipTypeId: string
  membershipTypeName: string
  birthTaCombo: number
  birthOtsCombo: number
  birthTimeRangeApply: number
  taCombo: number
  otsCombo: number
  isAll: number
  isType: number
  isItem: number
  typeId: string
  itemId: string
  comboType: string
  fromDate: number
  toDate: number
  active: number
  storeUid: string
  storeName: string
  promotionUid: string
  promotionId: string
  promotionName: string
  createdAt: number
  createdBy: string
  updatedAt: number | null
  updatedBy: string | null
  deleted: boolean
  deletedAt: number | null
  deletedBy: string | null
  timeSaleHourDay: number
  timeSaleDateWeek: number
  description: string | null
  extraData: {
    combo_id: string
    is_combo: number
  }
  revision: number
  sort: number
  brandUid: string
  companyUid: string
  comboCloneId: string | null
  promotion: {
    id: string
    company_uid: string
    brand_uid: string
    deleted: boolean
    promotion_id: string
    promotion_name: string
    active: number
    partner_auto_gen: number
    is_fabi: number
    source_uid: string
    description: string | null
  }
}

export interface GetMembershipCombosParams {
  companyUid?: string
  brandUid?: string
  page?: number
  listStoreUid?: string[]
  status?: 'expired' | 'unexpired'
  active?: number
}

// Convert API data to frontend format for membership combos
export function convertMembershipComboApiData(
  apiCombo: MembershipComboApiData,
  storeName: string
): MembershipCombo {
  return {
    id: apiCombo.id,
    membershipTypeId: apiCombo.membership_type_id,
    membershipTypeName: apiCombo.membership_type_name,
    birthTaCombo: apiCombo.birth_ta_combo,
    birthOtsCombo: apiCombo.birth_ots_combo,
    birthTimeRangeApply: apiCombo.birth_time_range_apply,
    taCombo: apiCombo.ta_combo,
    otsCombo: apiCombo.ots_combo,
    isAll: apiCombo.is_all,
    isType: apiCombo.is_type,
    isItem: apiCombo.is_item,
    typeId: apiCombo.type_id,
    itemId: apiCombo.item_id,
    comboType: apiCombo.combo_type,
    fromDate: apiCombo.from_date,
    toDate: apiCombo.to_date,
    active: apiCombo.active,
    storeUid: apiCombo.store_uid,
    storeName: storeName,
    promotionUid: apiCombo.promotion_uid,
    promotionId: apiCombo.promotion.promotion_id,
    promotionName: apiCombo.promotion.promotion_name,
    createdAt: apiCombo.created_at,
    createdBy: apiCombo.created_by,
    updatedAt: apiCombo.updated_at,
    updatedBy: apiCombo.updated_by,
    deleted: apiCombo.deleted,
    deletedAt: apiCombo.deleted_at,
    deletedBy: apiCombo.deleted_by,
    timeSaleHourDay: apiCombo.time_sale_hour_day,
    timeSaleDateWeek: apiCombo.time_sale_date_week,
    description: apiCombo.description,
    extraData: apiCombo.extra_data,
    revision: apiCombo.revision,
    sort: apiCombo.sort,
    brandUid: apiCombo.brand_uid,
    companyUid: apiCombo.company_uid,
    comboCloneId: apiCombo.combo_clone_id,
    promotion: apiCombo.promotion
  }
}
