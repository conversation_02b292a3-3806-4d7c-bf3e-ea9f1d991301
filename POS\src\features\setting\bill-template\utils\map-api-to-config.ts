import type { SaveBillTemplateRequest, BillTemplateConfig } from '../types'

/**
 * Map API response data to BillTemplateConfig for form
 */
export const mapApiToBillTemplateConfig = (apiData: SaveBillTemplateRequest): BillTemplateConfig => {
  const { extra_data, extra_bill_template_3 } = apiData

  const currencyRates =
    extra_data?.currency_exchanges?.map((exchange, index) => ({
      id: `${index}`,
      currencyCode: exchange.currency.split('-')[1] || 'USD',
      rate: exchange.exchange
    })) || []

  const config: BillTemplateConfig = {
    showLogo: Boolean(apiData.logo || extra_bill_template_3?.logo),
    logoUrl: apiData.logo || extra_bill_template_3?.logo || undefined,
    customText1: extra_data?.custom_text_1 || '',
    customText2: extra_data?.custom_text_2 || '',
    hotlineNumber: extra_data?.hotline || '',
    showAmountReceived: Boolean(extra_data?.enable_cash_change),
    showToppings: Boolean(extra_data?.enable_topping),
    showVoucherGift: Boolean(extra_data?.show_voucher_gift),
    showBankCardCode: Boolean(extra_data?.show_payment_id),
    showMainItemQuantity: Boolean(extra_data?.show_count_item_bill),
    showBillFrame: Boolean(extra_data?.enable_border_bill),
    secureCustomerInfo: extra_data ? !extra_data.show_customer_phone : false,
    showItemDiscount: Boolean(extra_data?.enable_discount),
    showVatPercentage: Boolean(extra_data?.enable_vat_rate),
    showVatAmount: Boolean(extra_data?.enable_vat_amount),
    showUnit: Boolean(extra_data?.is_show_unit),
    mergeInvoiceAndOrderSource: Boolean(extra_data?.is_group_source_and_tranno),
    showAccumulatedPoints: Boolean(extra_data?.show_points),
    showVatInformation: Boolean(extra_data?.show_vat_info),
    showTotalIncludingVat: Boolean(extra_data?.show_amount_with_vat),
    showVatQrCode: Boolean(extra_data?.show_qr_vat_info),
    showItemType: Boolean(extra_data?.show_item_class),
    showTotalItemType: Boolean(extra_data?.show_total_item_class_amount),
    hideInvoiceCorrectionNotes: Boolean(extra_data?.hide_note_sale_change),
    showServiceItemTime: Boolean(extra_data?.show_start_end_item_service),
    showDebtInformation: Boolean(extra_data?.display_debt_amount),
    showAioMomoQr: Boolean(extra_data?.momo_qr_aio),
    showVietQr: false,
    fontSize: extra_data?.font_size_rate || 100,
    showFoodItemNotes: Boolean(extra_data?.show_item_note),
    showCardFee: Boolean(extra_data?.is_show_payment_fee),
    currencyRates,
    showScanQrCode: Boolean(extra_data?.enable_qr_code),
    scanQrTitle: extra_data?.qr_title || '',
    scanQrContent: extra_data?.qr_content || ''
  }

  return config
}

/**
 * Map BillTemplateConfig back to API format (for saving)
 */
export const mapConfigToApiFormat = (
  config: BillTemplateConfig,
  baseData: Partial<SaveBillTemplateRequest> = {}
): SaveBillTemplateRequest => {
  const currencyExchanges = config.currencyRates.map(rate => ({
    currency: `VND-${rate.currencyCode}`,
    exchange: rate.rate
  }))

  const extra_data = {
    logo: config.logoUrl || null,
    custom_text_1: config.customText1,
    custom_text_2: config.customText2,
    enable_cash_change: config.showAmountReceived,
    enable_qr_code: config.showScanQrCode,
    enable_topping: config.showToppings,
    show_voucher_gift: config.showVoucherGift,
    qr_title: config.scanQrTitle,
    qr_content: config.scanQrContent,
    apply_new_logo_template: false,
    font_size_rate: config.fontSize,
    show_payment_id: config.showBankCardCode,
    show_count_item_bill: config.showMainItemQuantity ? 1 : 0,
    show_customer_phone: config.secureCustomerInfo ? 0 : 1,
    enable_discount: config.showItemDiscount,
    is_show_unit: config.showUnit ? 1 : 0,
    is_group_source_and_tranno: config.mergeInvoiceAndOrderSource ? 1 : 0,
    show_points: config.showAccumulatedPoints ? 1 : 0,
    show_vat_info: config.showVatInformation ? 1 : 0,
    show_item_class: config.showItemType ? 1 : 0,
    hide_note_sale_change: config.hideInvoiceCorrectionNotes ? 1 : 0,
    show_vat_reverse: config.showVatQrCode ? 1 : 0,
    show_qr_vat_info: config.showVatQrCode ? 1 : 0,
    show_start_end_item_service: config.showServiceItemTime ? 1 : 0,
    momo_qr_aio: config.showAioMomoQr ? 1 : 0,
    hotline: config.hotlineNumber,
    currency_exchanges: currencyExchanges,
    show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0,
    display_debt_amount: config.showDebtInformation ? 1 : 0,
    show_total_item_class_amount: config.showTotalItemType ? 1 : 0,
    show_item_note: config.showFoodItemNotes ? 1 : 0,
    enable_border_bill: config.showBillFrame ? 1 : 0,
    enable_vat_rate: config.showVatPercentage,
    enable_vat_amount: config.showVatAmount,
    is_show_payment_fee: config.showCardFee ? 1 : 0,
    number_table_column: 254
  }

  const apiRequest: SaveBillTemplateRequest = {
    company_uid: baseData.company_uid || '',
    brand_uid: baseData.brand_uid || '',
    store_uid: baseData.store_uid || '',
    id: baseData.id || crypto.randomUUID(),
    logo: config.logoUrl || null,
    name: baseData.name || null,
    address: baseData.address || null,
    city: baseData.city || null,
    phone: baseData.phone || null,
    facebook: baseData.facebook || null,
    revision: baseData.revision || Date.now(),
    extra_data,
    extra_config: baseData.extra_config || null,
    extra_pos_mini: baseData.extra_pos_mini || null,
    extra_bill_template_3: {
      enable_vat_rate: config.showVatPercentage,
      enable_vat_amount: config.showVatAmount,
      logo: config.logoUrl || null,
      hotline: config.hotlineNumber || null,
      qr_title: config.scanQrTitle || null,
      qr_content: config.scanQrContent || null,
      momo_qr_aio: config.showAioMomoQr ? 1 : 0,
      show_points: config.showAccumulatedPoints ? 1 : 0,
      is_show_unit: config.showUnit ? 1 : 0,
      custom_text_1: config.customText1,
      custom_text_2: config.customText2,
      show_vat_info: config.showVatInformation ? 1 : 0,
      enable_qr_code: config.showScanQrCode,
      enable_topping: config.showToppings,
      font_size_rate: config.fontSize.toString(),
      show_item_note: config.showFoodItemNotes ? 1 : 0,
      enable_discount: config.showItemDiscount,
      show_item_class: config.showItemType ? 1 : 0,
      show_payment_id: config.showBankCardCode,
      show_qr_vat_info: config.showVatQrCode ? 1 : 0,
      show_vat_reverse: config.showVatQrCode ? 1 : 0,
      show_voucher_gift: config.showVoucherGift,
      currency_exchanges: currencyExchanges,
      enable_border_bill: config.showBillFrame ? 1 : 0,
      enable_cash_change: config.showAmountReceived,
      display_debt_amount: config.showDebtInformation ? 1 : 0,
      number_table_column: 62,
      show_customer_phone: config.secureCustomerInfo ? 0 : 1,
      show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0,
      show_count_item_bill: config.showMainItemQuantity ? 1 : 0,
      hide_note_sale_change: config.hideInvoiceCorrectionNotes ? 1 : 0,
      apply_new_logo_template: false,
      is_group_source_and_tranno: config.mergeInvoiceAndOrderSource ? 1 : 0,
      show_start_end_item_service: config.showServiceItemTime ? 1 : 0,
      show_total_item_class_amount: config.showTotalItemType ? 1 : 0
    },
    extra_bill_template_4: baseData.extra_bill_template_4 || null,
    extra_bill_template_custom: baseData.extra_bill_template_custom || {
      content: '',
      title_stt: 'TT',
      momo_qr_aio: config.showAioMomoQr ? 1 : 0,
      title_price: 'Đơn giá',
      is_show_unit: config.showUnit ? 1 : 0,
      title_amount: 'Thành tiền',
      enable_topping: config.showToppings,
      show_item_note: config.showFoodItemNotes ? 1 : 0,
      title_discount: 'GG',
      title_quantity: 'SL',
      title_vat_rate: 'VAT',
      enable_discount: config.showItemDiscount,
      show_item_class: config.showItemType ? 1 : 0,
      title_name_item: 'Tên món',
      title_vat_amount: 'Tiền VAT',
      enable_border_bill: config.showBillFrame ? 1 : 0,
      display_debt_amount: config.showDebtInformation ? 1 : 0,
      number_table_column: 62,
      show_amount_with_vat: config.showTotalIncludingVat ? 1 : 0
    },
    extra_order_note: baseData.extra_order_note || null,
    extra_label: baseData.extra_label || null,
    extra_provisional: baseData.extra_provisional || null,
    extra_deposit: baseData.extra_deposit || {
      note: null,
      address: null,
      hotline: config.hotlineNumber || null,
      title_text: null,
      custom_text: null,
      padding_top: 10,
      logo_deposit: null,
      font_size_rate: config.fontSize,
      operating_time: null,
      show_item_type: config.showItemType ? 1 : 0,
      show_raw_quanity: 0,
      item_font_size_rate: 15,
      font_size_title_text: 30,
      price_font_size_rate: 15,
      new_templace_order_bill: 0,
      quantity_font_size_rate: 15,
      disable_unit_in_order_note: config.showUnit ? 0 : 1,
      total_price_font_size_rate: 15,
      display_confirmation_signature: 1
    },
    store_id: baseData.store_id || null,
    brand_id: baseData.brand_id || null,
    company_id: baseData.company_id || null,
    is_fabi: baseData.is_fabi || 1,
    created_by: baseData.created_by || 'system',
    updated_by: baseData.updated_by || 'system',
    deleted_by: baseData.deleted_by || null,
    created_at: baseData.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted_at: baseData.deleted_at || null
  }

  return apiRequest
}
