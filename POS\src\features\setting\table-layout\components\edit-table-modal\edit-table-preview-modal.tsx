import React, { useState } from 'react'

import { Trash2 } from 'lucide-react'

import { ConfirmModal } from '@/components/pos'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

interface TablePreviewData {
  id: string
  tenBan: string
  nguon: string
  monDatTruoc: string
  moTa: string
}

interface EditTablePreviewModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: TablePreviewData[]
  onConfirm: () => void
  onRemoveRow: (index: number) => void
}

export const EditTablePreviewModal: React.FC<EditTablePreviewModalProps> = ({
  open,
  onOpenChange,
  data,
  onConfirm,
  onRemoveRow
}) => {
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [rowToDelete, setRowToDelete] = useState<number | null>(null)

  const handleDeleteClick = (index: number) => {
    setRowToDelete(index)
    setIsConfirmDeleteOpen(true)
  }

  const handleConfirmDelete = () => {
    if (rowToDelete !== null) {
      onRemoveRow(rowToDelete)
      setRowToDelete(null)
    }
    setIsConfirmDeleteOpen(false)
  }

  const handleCancelDelete = () => {
    setRowToDelete(null)
    setIsConfirmDeleteOpen(false)
  }
  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='flex max-h-[80vh] !max-w-6xl flex-col overflow-hidden'>
          <DialogHeader>
            <DialogTitle className='text-xl font-semibold'>Sửa thông tin bàn</DialogTitle>
          </DialogHeader>

          <div className='flex-1 overflow-auto'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-8'></TableHead>
                  <TableHead className='min-w-[300px]'>id</TableHead>
                  <TableHead className='min-w-[120px]'>Tên bàn</TableHead>
                  <TableHead className='min-w-[120px]'>Nguồn</TableHead>
                  <TableHead className='min-w-[150px]'>Món đặt trước</TableHead>
                  <TableHead className='min-w-[200px]'>Mô tả</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, index) => (
                  <TableRow key={index} className='hover:bg-gray-50'>
                    <TableCell>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleDeleteClick(index)}
                        className='h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </TableCell>
                    <TableCell className='font-mono text-xs break-all'>{row.id}</TableCell>
                    <TableCell className='font-medium'>{row.tenBan}</TableCell>
                    <TableCell>{row.nguon}</TableCell>
                    <TableCell className='font-mono text-sm'>{row.monDatTruoc}</TableCell>
                    <TableCell>{row.moTa}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className='flex items-center justify-end border-t pt-4'>
            <div className='flex gap-2'>
              <Button variant='outline' onClick={() => onOpenChange(false)}>
                Đóng
              </Button>
              <Button onClick={onConfirm} className='bg-green-600 hover:bg-green-700' disabled={data.length === 0}>
                Lưu
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <ConfirmModal
        open={isConfirmDeleteOpen}
        onOpenChange={setIsConfirmDeleteOpen}
        content='Bạn có muốn bỏ món'
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </>
  )
}
