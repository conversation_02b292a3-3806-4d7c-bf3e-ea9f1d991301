import { apiClient } from './api/pos/pos-api'

export interface PromotionRevenueData {
  promotion_id: string
  promotion_name: string
  total_bill: number
  revenue_net: number
  revenue_gross: number
  commission_amount: number
  discount_amount: number
  deduct_tax_amount: number
  list_data: Array<{
    date: string
    total_bill: number
    revenue_net: number
    revenue_gross: number
    commission_amount: number
    discount_amount: number
    deduct_tax_amount: number
  }>
}

export interface PromotionRevenueParams {
  startDate: number
  endDate: number
  selectedStoreIds: string[]
  companyUid: string
  brandUid: string
}

export const promotionRevenueApi = {
  getPromotionRevenue: async (params: PromotionRevenueParams): Promise<PromotionRevenueData[]> => {
    const { startDate, endDate, selectedStoreIds, companyUid, brandUid } = params

    const response = await apiClient.get('/api/v1/reports/sale-summary/promotions', {
      params: {
        brand_uid: brandUid,
        company_uid: companyUid,
        start_date: startDate,
        end_date: endDate,
        store_open_at: 0,
        by_days: 1,
        list_store_uid: selectedStoreIds.join(',')
      }
    })

    return response.data || []
  },

  exportPromotionReport: async (params: PromotionRevenueParams): Promise<PromotionRevenueData[]> => {
    const { startDate, endDate, selectedStoreIds, companyUid, brandUid } = params

    const response = await apiClient.get('/api/v1/reports/sale-summary/promotions', {
      params: {
        brand_uid: brandUid,
        company_uid: companyUid,
        start_date: startDate,
        end_date: endDate,
        store_open_at: 0,
        by_days: 1,
        list_store_uid: selectedStoreIds.join(',')
      }
    })

    return response.data || []
  }
}
