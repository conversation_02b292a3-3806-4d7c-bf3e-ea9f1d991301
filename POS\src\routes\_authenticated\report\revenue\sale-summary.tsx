import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import SaleSummary from '@/features/reports/revenue/sale-summary'

export const Route = createFileRoute('/_authenticated/report/revenue/sale-summary')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: SaleSummary
})
