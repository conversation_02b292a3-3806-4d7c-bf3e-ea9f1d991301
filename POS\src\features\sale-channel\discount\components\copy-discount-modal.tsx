import { useState } from 'react'

import { toast } from 'sonner'

import { usePosStores, useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useDiscountPrograms, useCloneDiscount } from '@/hooks/api'

import { PosModal } from '@/components/pos/modal'
import { Checkbox, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

interface CopyDiscountModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CopyDiscountModal({ isOpen, onClose }: CopyDiscountModalProps) {
  const [sourceStoreId, setSourceStoreId] = useState<string>('')
  const [selectedTargetStores, setSelectedTargetStores] = useState<Set<string>>(new Set())
  const [selectedPrograms, setSelectedPrograms] = useState<Set<string>>(new Set())

  const { currentBrandStores } = usePosStores()
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  // Clone discount mutation
  const cloneDiscountMutation = useCloneDiscount()

  const { data: discountProgramsData, isLoading: isLoadingPrograms } = useDiscountPrograms({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    storeUid: sourceStoreId,
    enabled: !!sourceStoreId
  })

  const discountPrograms = discountProgramsData?.data || []

  // Filter target stores (exclude source store)
  const targetStores = currentBrandStores.filter(store => store.id !== sourceStoreId)

  const handleProgramToggle = (programId: string) => {
    const newSelected = new Set(selectedPrograms)
    if (newSelected.has(programId)) {
      newSelected.delete(programId)
    } else {
      newSelected.add(programId)
    }
    setSelectedPrograms(newSelected)
  }

  const handleTargetStoreToggle = (storeId: string) => {
    const newSelected = new Set(selectedTargetStores)
    if (newSelected.has(storeId)) {
      newSelected.delete(storeId)
    } else {
      newSelected.add(storeId)
    }
    setSelectedTargetStores(newSelected)
  }

  const handleSelectAllTargetStores = () => {
    if (selectedTargetStores.size === targetStores.length) {
      // If all are selected, deselect all
      setSelectedTargetStores(new Set())
    } else {
      // Select all stores
      setSelectedTargetStores(new Set(targetStores.map(store => store.id)))
    }
  }

  const handleCopy = async () => {
    if (
      !company?.id ||
      !selectedBrand?.id ||
      !sourceStoreId ||
      selectedTargetStores.size === 0 ||
      selectedPrograms.size === 0
    ) {
      return
    }

    try {
      const selectedProgramIds = Array.from(selectedPrograms)
      const targetStoreIds = Array.from(selectedTargetStores)

      // Call API for each target store sequentially
      for (const targetStoreId of targetStoreIds) {
        await cloneDiscountMutation.mutateAsync({
          companyUid: company.id,
          brandUid: selectedBrand.id,
          listDiscountUid: selectedProgramIds,
          sourceStore: sourceStoreId,
          targetStore: targetStoreId
        })
      }

      toast.success('Sao chép thành công')
      handleClose()
    } catch (error) {
      console.error('Error copying discount programs:', error)
      // Error handling will be managed by the mutation's onError if needed
    }
  }

  const handleClose = () => {
    setSourceStoreId('')
    setSelectedTargetStores(new Set())
    setSelectedPrograms(new Set())
    onClose()
  }

  const selectedProgramsData = discountPrograms.filter(program => selectedPrograms.has(program.id))

  return (
    <PosModal
      title='Sao chép chương trình giảm giá'
      open={isOpen}
      onOpenChange={handleClose}
      onCancel={handleClose}
      onConfirm={handleCopy}
      confirmText={`Sao chép`}
      confirmDisabled={
        !sourceStoreId ||
        selectedTargetStores.size === 0 ||
        selectedPrograms.size === 0 ||
        cloneDiscountMutation.isPending
      }
      maxWidth='sm:max-w-6xl'
    >

      {/* Two Panel Layout */}
      <div className='grid grid-cols-2 gap-6'>
        {/* Left Panel - Source Store */}
        <div className='space-y-4'>
          <div>
            <label className='text-sm font-medium'>Cửa hàng nguồn</label>
            <Select value={sourceStoreId} onValueChange={setSourceStoreId}>
              <SelectTrigger className='mt-1'>
                <SelectValue placeholder='Chọn cửa hàng nguồn' />
              </SelectTrigger>
              <SelectContent>
                {currentBrandStores.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.store_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Discount Programs List */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Chương trình giảm giá</label>
            <div className='max-h-64 overflow-y-auto rounded-md border p-3'>
              {isLoadingPrograms ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>Đang tải...</div>
              ) : discountPrograms.length === 0 ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>
                  {sourceStoreId ? 'Không có chương trình giảm giá' : 'Chọn cửa hàng nguồn'}
                </div>
              ) : (
                <div className='space-y-3'>
                  {discountPrograms.map(program => (
                    <div key={program.id} className='flex items-start space-x-3'>
                      <Checkbox
                        id={program.id}
                        checked={selectedPrograms.has(program.id)}
                        onCheckedChange={() => handleProgramToggle(program.id)}
                      />
                      <div className='min-w-0 flex-1'>
                        <div className='text-sm font-medium'>{program.promotion?.promotion_name || 'N/A'}</div>
                        <div className='text-muted-foreground text-xs'>
                          Giảm{' '}
                          {program.discount_type === 'AMOUNT'
                            ? `${(program.ta_discount || program.ots_discount || 0).toLocaleString('vi-VN')} ₫`
                            : `${program.ta_discount || program.ots_discount || 0}%`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Panel - Target Stores */}
        <div className='space-y-4'>
          <div>
            <label className='text-sm font-medium'>Cửa hàng đích</label>
            <Select>
              <SelectTrigger className='mt-1'>
                <SelectValue
                  placeholder={
                    selectedTargetStores.size === 0
                      ? 'Chọn cửa hàng đích'
                      : `Đã chọn ${selectedTargetStores.size} cửa hàng`
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {targetStores.length === 0 ? (
                  <div className='text-muted-foreground py-4 text-center text-sm'>Chọn cửa hàng nguồn trước</div>
                ) : (
                  <>
                    {/* Select All Option */}
                    <div className='flex items-center space-x-3 border-b px-2 py-1.5'>
                      <Checkbox
                        id='select-all-targets'
                        checked={selectedTargetStores.size === targetStores.length && targetStores.length > 0}
                        onCheckedChange={handleSelectAllTargetStores}
                        onClick={e => e.stopPropagation()}
                      />
                      <label
                        htmlFor='select-all-targets'
                        className='flex-1 cursor-pointer text-sm font-medium'
                        onClick={e => {
                          e.preventDefault()
                          handleSelectAllTargetStores()
                        }}
                      >
                        Chọn tất cả
                      </label>
                    </div>

                    {/* Individual Store Options */}
                    {targetStores.map(store => (
                      <div key={store.id} className='flex items-center space-x-3 px-2 py-1.5'>
                        <Checkbox
                          id={`target-${store.id}`}
                          checked={selectedTargetStores.has(store.id)}
                          onCheckedChange={() => handleTargetStoreToggle(store.id)}
                          onClick={e => e.stopPropagation()}
                        />
                        <label
                          htmlFor={`target-${store.id}`}
                          className='flex-1 cursor-pointer text-sm font-medium'
                          onClick={e => {
                            e.preventDefault()
                            handleTargetStoreToggle(store.id)
                          }}
                        >
                          {store.store_name}
                        </label>
                      </div>
                    ))}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Selected Programs Display */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Chương trình được chọn</label>
            <div className='max-h-64 overflow-y-auto rounded-md border p-3'>
              {selectedProgramsData.length === 0 ? (
                <div className='text-muted-foreground py-4 text-center text-sm'>Chưa chọn chương trình nào</div>
              ) : (
                <div className='space-y-2'>
                  {selectedProgramsData.map(program => (
                    <div key={program.id} className='bg-muted rounded-md p-2'>
                      <div className='text-sm font-medium'>{program.promotion?.promotion_name || 'N/A'}</div>
                      <div className='text-muted-foreground text-xs'>
                        Giảm{' '}
                        {program.discount_type === 'AMOUNT'
                          ? `${(program.ta_discount || program.ots_discount || 0).toLocaleString('vi-VN')} ₫`
                          : `${((program.ta_discount || program.ots_discount || 0) * 100).toFixed(0)}%`}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </PosModal>
  )
}
