import {
  HeaderForm,
  ServiceChargeInformationSection,
  DateApplicationSection,
  MarketingTimeframeSection,
  SynchronizationSection
} from './components'
import { useServiceChargeForm } from './hooks'
import { ServiceChargeFormProvider } from './stores'

interface ServiceChargeFormProps {
  serviceChargeId?: string
  storeUid?: string
}

export function ServiceChargeForm({ serviceChargeId, storeUid: initialStoreUid }: ServiceChargeFormProps = {}) {
  const formState = useServiceChargeForm({
    serviceChargeId,
    initialStoreUid
  })

  return (
    <ServiceChargeFormProvider value={formState}>
      <div className='container mx-auto px-4 py-8'>
        <HeaderForm />

        <div className='mx-auto max-w-4xl'>
          <div className='p-6'>
            <div className='space-y-6'>
              <SynchronizationSection />
              <ServiceChargeInformationSection />
              <DateApplicationSection />
              <MarketingTimeframeSection />
            </div>
          </div>
        </div>
      </div>
    </ServiceChargeFormProvider>
  )
}
