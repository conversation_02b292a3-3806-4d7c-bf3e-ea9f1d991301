/**
 * Utility functions for handling appliedSources in different formats
 */

export type AppliedSourceItem = {
  source_id: string
  [key: string]: any
} | {
  id: string
  [key: string]: any
}

export type AppliedSources = AppliedSourceItem[] | string[]

/**
 * Extract source IDs from appliedSources regardless of format
 * Handles both array of objects and array of string IDs
 */
export function extractSourceIds(appliedSources: AppliedSources | null | undefined): string[] {
  if (!appliedSources || !Array.isArray(appliedSources)) {
    return []
  }

  // If empty array
  if (appliedSources.length === 0) {
    return []
  }

  // Check first item to determine format
  const firstItem = appliedSources[0]
  
  // If first item is string, assume all are strings
  if (typeof firstItem === 'string') {
    return appliedSources as string[]
  }

  // If first item is object, extract IDs based on property name
  if (typeof firstItem === 'object' && firstItem !== null) {
    // Check for source_id property first
    if ('source_id' in firstItem) {
      return (appliedSources as AppliedSourceItem[]).map(item => (item as any).source_id)
    }
    // Check for id property
    if ('id' in firstItem) {
      return (appliedSources as AppliedSourceItem[]).map(item => (item as any).id)
    }
  }

  // Fallback: return empty array if format is not recognized
  return []
}

/**
 * Check if appliedSources is in string array format
 */
export function isStringArrayFormat(appliedSources: AppliedSources | null | undefined): boolean {
  if (!appliedSources || !Array.isArray(appliedSources) || appliedSources.length === 0) {
    return false
  }
  
  return typeof appliedSources[0] === 'string'
}

/**
 * Check if appliedSources is in object array format
 */
export function isObjectArrayFormat(appliedSources: AppliedSources | null | undefined): boolean {
  if (!appliedSources || !Array.isArray(appliedSources) || appliedSources.length === 0) {
    return false
  }
  
  return typeof appliedSources[0] === 'object' && appliedSources[0] !== null
}
