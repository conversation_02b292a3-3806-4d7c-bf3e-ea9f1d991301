import { useMemo } from 'react'

import { POS_STORES_DATA } from '@/constants/local-storage'

export interface PosStore {
  id: string
  store_id: string
  store_name: string
  city_uid: string
  brand_uid: string
  company_uid: string
  active: number
  address: string
  phone: string
  background: string
  bill_template: number
  change_log_detail: number
  dateend: string
  enable_change_item_in_store: number
  enable_change_item_type_in_store: number
  enable_change_printer_position_in_store: number
  enable_turn_order_report: number
  expiry_date: number
  facebook: string
  fb_store_id: number
  is_franchise: number
  istrial: string
  latitude: number
  logo: string
  open_at: number
  sale_change_vat_enable: number
  sort: number
  tracking_sale: number
  website: string
}

/**
 * Hook to get POS stores data with utility functions
 */
export const usePosStoresData = () => {
  // Get stores data from localStorage
  const stores = useMemo(() => {
    try {
      const storedData = localStorage.getItem(POS_STORES_DATA)
      if (storedData) {
        const parsed = JSON.parse(storedData)
        return Array.isArray(parsed) ? parsed : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data from localStorage:', error)
      return []
    }
  }, [])

  const activeStores = useMemo(() => {
    return stores.filter((store: PosStore) => store.active === 1)
  }, [stores])

  const storesByCity = useMemo(() => {
    const grouped = new Map<string, PosStore[]>()

    activeStores.forEach((store: PosStore) => {
      const cityKey = store.city_uid
      if (!grouped.has(cityKey)) {
        grouped.set(cityKey, [])
      }
      grouped.get(cityKey)!.push(store)
    })

    return grouped
  }, [activeStores])

  const getStoresByCity = (cityId: string): PosStore[] => {
    return storesByCity.get(cityId) || []
  }

  const getStoreById = (storeId: string): PosStore | undefined => {
    return stores.find((store: PosStore) => store.id === storeId || store.store_id === storeId)
  }

  const getAllStoreIds = (): string[] => {
    return activeStores.map(store => store.id || store.store_id)
  }

  const getSelectedStoreIds = (selectedCities: string[], selectedStores: string[]): string[] => {
    const storeIds = new Set<string>()

    // Add stores from selected cities
    selectedCities.forEach(cityId => {
      const cityStores = getStoresByCity(cityId)
      cityStores.forEach(store => {
        storeIds.add(store.id || store.store_id)
      })
    })

    // Add individually selected stores
    selectedStores.forEach(storeId => {
      storeIds.add(storeId)
    })

    return Array.from(storeIds)
  }

  return {
    stores,
    activeStores,
    storesByCity,

    // Utility functions
    getStoresByCity,
    getStoreById,
    getAllStoreIds,
    getSelectedStoreIds,

    // Computed values
    hasStores: activeStores.length > 0,
    isEmpty: activeStores.length === 0,
    totalStores: activeStores.length
  }
}
