import { z } from 'zod'

// Removed Item schema based on API response
const removedItemSchema = z.object({
  id: z.string(),
  item_id: z.string(),
  item_name: z.string(),
  ots_price: z.number(),
  ta_price: z.number(),
  deleted_at: z.number(),
  deleted: z.boolean(),
  deleted_by: z.string(),
  city_uid: z.string(),
  store_uid: z.string().optional() // Add store_uid field for store-based filtering
})

// API response schema
const removedItemApiResponseSchema = z.object({
  data: z.array(removedItemSchema),
  track_id: z.string()
})

// Export types
export type RemovedItem = z.infer<typeof removedItemSchema>
export type RemovedItemApiResponse = z.infer<typeof removedItemApiResponseSchema>

// Export schemas
export { removedItemSchema, removedItemApiResponseSchema }

// API data interface (raw from API)
export interface RemovedItemApiData {
  id: string
  item_id: string
  item_name: string
  ots_price: number
  ta_price: number
  deleted_at: number
  deleted: boolean
  deleted_by: string
  city_uid: string
  store_uid?: string // Add store_uid field for store-based filtering
}

// Conversion function from API data to RemovedItem
export const convertApiRemovedItemToRemovedItem = (
  apiRemovedItem: RemovedItemApiData
): RemovedItem => {
  return {
    ...apiRemovedItem
  }
}

// City data from localStorage
export interface CityData {
  id: string
  city_name: string
  [key: string]: unknown
}
