import { useState } from 'react'

import { Search, ChevronDown } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import type { ItemsInStore } from '../../../data'
import { useItemsInStoreForTable } from '../../../hooks'

interface MenuItemSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  sourceStoreUid: string
  selectedItems: string[]
  onItemsChange: (items: string[]) => void
  onSave: () => void
}

export function MenuItemSelectionModal({
  open,
  onOpenChange,
  sourceStoreUid,
  selectedItems,
  onItemsChange,
  onSave
}: MenuItemSelectionModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [groupFilter, setGroupFilter] = useState('all')
  const [selectedItemsOpen, setSelectedItemsOpen] = useState(false)

  // Fetch menu items from source store
  const { data: menuItems = [], isLoading } = useItemsInStoreForTable({
    params: {
      store_uid: sourceStoreUid
    },
    enabled: open && !!sourceStoreUid
  })

  // Filter items based on search and group
  const filteredItems = menuItems.filter((item: ItemsInStore) => {
    const matchesSearch = item.item_name?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesGroup = groupFilter === 'all' || item.item_type_uid === groupFilter
    return matchesSearch && matchesGroup
  })

  // Get unique item types for filter
  const itemTypes = Array.from(new Set(menuItems.map((item: ItemsInStore) => item.item_type_uid))).map(typeUid => {
    const item = menuItems.find((item: ItemsInStore) => item.item_type_uid === typeUid)
    return {
      uid: typeUid,
      name: item?.item_type_uid || typeUid
    }
  })

  const handleSelectAll = () => {
    const activeItems = filteredItems.filter((item: ItemsInStore) => item.active === 1)

    if (selectedItems.length === activeItems.length) {
      onItemsChange([])
    } else {
      onItemsChange(activeItems.map((item: ItemsInStore) => item.id as string))
    }
  }

  const handleItemToggle = (itemCode: string) => {
    if (selectedItems.includes(itemCode)) {
      onItemsChange(selectedItems.filter(code => code !== itemCode))
    } else {
      onItemsChange([...selectedItems, itemCode])
    }
  }

  const handleSave = () => {
    onSave()
    onOpenChange(false)
  }

  // Get selected menu items details
  const selectedMenuItems = menuItems.filter((item: ItemsInStore) => selectedItems.includes(item.id as string))

  // Only count active items for select all functionality
  const activeItems = filteredItems.filter((item: ItemsInStore) => item.active === 1)
  const isAllSelected = activeItems.length > 0 && selectedItems.length === activeItems.length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex h-[90vh] max-w-4xl flex-col'>
        <DialogHeader className='flex-shrink-0'>
          <DialogTitle>Chọn món ăn</DialogTitle>
        </DialogHeader>

        <div className='flex min-h-0 flex-1 flex-col space-y-4'>
          {/* Search and Filter */}
          <div className='flex flex-shrink-0 gap-4'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
              <Input
                placeholder='Tìm kiếm'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <Select value={groupFilter} onValueChange={setGroupFilter}>
              <SelectTrigger className='w-48'>
                <SelectValue placeholder='Tất cả nhóm món' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả nhóm món</SelectItem>
                {itemTypes.map(type => (
                  <SelectItem key={type.uid} value={type.uid as string}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Select All */}
          <div className='flex-shrink-0'>
            <Collapsible open={selectedItemsOpen} onOpenChange={setSelectedItemsOpen}>
              <div className='bg-muted/50 flex items-center space-x-2 rounded p-2'>
                <Checkbox id='select-all' checked={isAllSelected} onCheckedChange={handleSelectAll} />
                <CollapsibleTrigger asChild>
                  <button className='hover:text-primary flex items-center space-x-2 text-sm font-medium'>
                    <span>Đã chọn {selectedItems.length}</span>
                    {selectedItems.length > 0 && (
                      <ChevronDown
                        className={`h-4 w-4 transition-transform ${selectedItemsOpen ? 'rotate-180' : ''}`}
                      />
                    )}
                  </button>
                </CollapsibleTrigger>
              </div>

              {selectedItems.length > 0 && (
                <CollapsibleContent className='mt-2'>
                  <div className='bg-muted/30 max-h-32 overflow-y-auto rounded-md p-2'>
                    <div className='space-y-1'>
                      {selectedMenuItems.map((item: ItemsInStore) => (
                        <div key={item.id} className='flex items-center space-x-2 text-sm'>
                          <Checkbox
                            checked={true}
                            onCheckedChange={() => handleItemToggle(item.id as string)}
                            className='h-3 w-3'
                          />
                          <span className='flex-1 truncate'>{item.item_name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CollapsibleContent>
              )}
            </Collapsible>
          </div>

          {/* Items List */}
          <div className='min-h-0 flex-1'>
            <ScrollArea className='h-full'>
              <div className='space-y-2 pr-4'>
                {isLoading ? (
                  <div className='py-8 text-center'>Đang tải...</div>
                ) : filteredItems.length === 0 ? (
                  <div className='text-muted-foreground py-8 text-center'>Không tìm thấy món ăn nào</div>
                ) : (
                  filteredItems.map((item: ItemsInStore) => {
                    const isActive = item.active === 1

                    return (
                      <div
                        key={item.id}
                        className={`flex items-center space-x-3 rounded border p-3 ${
                          isActive ? 'hover:bg-muted/50' : 'bg-muted/20'
                        }`}
                      >
                        <Checkbox
                          id={item.id}
                          checked={selectedItems.includes(item.id as string)}
                          onCheckedChange={() => handleItemToggle(item.id as string)}
                          disabled={!isActive}
                          className={!isActive ? 'opacity-50' : ''}
                        />
                        <div className='min-w-0 flex-1'>
                          <label
                            htmlFor={item.id}
                            className={`block truncate text-sm font-medium ${
                              isActive ? 'cursor-pointer' : 'text-muted-foreground cursor-not-allowed'
                            }`}
                          >
                            {item.item_name}
                          </label>
                          <p className='text-muted-foreground mt-1 truncate text-xs'>{item.item_type_uid}</p>
                        </div>
                        {!isActive && (
                          <div className='rounded-full bg-red-500 px-2 py-1 text-xs font-medium text-white'>
                            Deactive
                          </div>
                        )}
                      </div>
                    )
                  })
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

        <DialogFooter className='flex-shrink-0'>
          <Button variant='outline' onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleSave} className='bg-blue-600 hover:bg-blue-700'>
            Xong
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
