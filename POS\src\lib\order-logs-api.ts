import { api } from './api/pos/pos-api'

// Types for Order Logs API
export interface OrderLogItem {
  item_id: string
  item_name: string
  quantity: number
  amount: number
  price: number
  unit_id: string
  note?: string
  toppings?: OrderLogItem[]
}

export interface OrderLogEntry {
  created_at: number
  updated_at: number
  tran_id: string
  device_code: string
  start_date: number
  log_type: string
  table_name: string
  change_data: {
    tran_id: string
    table_name: string
    total_amount: number
    sale_detail: OrderLogItem[]
    extra_data: {
      peo_count?: number
      deposit_amount?: number
      deposit_payment_name?: string
      note_deposit?: string
      customer_name?: string
      customer_phone?: string
      money_received?: number
    }
    tran_no: string
    order_no: string
    start_date: number
    tran_date: number
    sale_note?: string
    employee_name?: string
  }
  sale_current: any
  fmt_created_at: string
  fmt_updated_at: string
}

export interface OrderLogsApiResponse {
  data: OrderLogEntry[]
  track_id?: string
}

export interface GetOrderLogsParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  device_code: string
  start_date: number
  end_date: number
  search?: string
}

// Cache for order logs requests
const orderLogsCache = new Map<string, { data: OrderLogsApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<OrderLogsApiResponse>>()
const CACHE_DURATION = 2 * 60 * 1000 // 2 minutes (order logs change frequently)

// Order Logs API Service
export const orderLogsApi = {
  /**
   * Get order logs data with request deduplication and caching
   */
  getOrderLogs: async (params: GetOrderLogsParams): Promise<OrderLogsApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.store_uid}-${params.device_code}-${params.start_date}-${params.end_date}-${params.search || ''}`

    // Check cache first
    const cached = orderLogsCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    // Create new request
    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.append('company_uid', params.company_uid)
        queryParams.append('brand_uid', params.brand_uid)
        queryParams.append('store_uid', params.store_uid)
        queryParams.append('device_code', params.device_code)
        queryParams.append('start_date', params.start_date.toString())
        queryParams.append('end_date', params.end_date.toString())

        if (params.search) {
          queryParams.append('search', params.search)
        }

        const response = await api.get(`/v3/pos-cms/sale-change-log?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000' // GMT+7 timezone offset in milliseconds
          },
          timeout: 30000 // 30 seconds timeout
        })

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from order logs API')
        }

        const result = response.data as OrderLogsApiResponse

        // Cache the result
        orderLogsCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    // Store the pending request
    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Clear cache for order logs
   */
  clearCache: () => {
    orderLogsCache.clear()
    pendingRequests.clear()
  },

  /**
   * Get cache stats
   */
  getCacheStats: () => ({
    cacheSize: orderLogsCache.size,
    pendingRequests: pendingRequests.size
  }),

  /**
   * Get order log by transaction ID from cache
   */
  getOrderLogByTranId: (tranId: string): OrderLogEntry | undefined => {
    for (const cached of orderLogsCache.values()) {
      const orderLog = cached.data.data.find(item => item.tran_id === tranId)
      if (orderLog) return orderLog
    }
    return undefined
  }
}
