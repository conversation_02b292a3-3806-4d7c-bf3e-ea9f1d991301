import { useQuery } from '@tanstack/react-query'

import type { BillingDetailResponse, BillingDetailParams } from '@/types/api/crm/billing-detail'

import { getBillingDetailApi } from '@/lib/api/crm/billing-detail'

import { CRM_QUERY_KEYS } from '@/constants/crm/query-keys'

export function useBillingDetail(params: BillingDetailParams | null) {
  return useQuery<BillingDetailResponse, Error>({
    queryKey: [CRM_QUERY_KEYS.BILLING_DETAIL, params],
    queryFn: async () => {
      if (!params) {
        throw new Error('Params are required')
      }
      const response = await getBillingDetailApi(params)
      return response.data
    },
    enabled: !!params && !!params.date_start && !!params.type
  })
}
