import { useState } from 'react'

import { Trash2 } from 'lucide-react'

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogT<PERSON>le,
  DialogFooter,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

import { VatDiscountFormModal } from './vat-discount-form-modal'
import type { VatDiscountConfig } from './vat-discount-types'

interface VatDiscountConfigModalProps {
  isOpen: boolean
  onClose: () => void
  configs: VatDiscountConfig[]
  onConfigsChange: (configs: VatDiscountConfig[]) => void
}

export function VatDiscountConfigModal({ isOpen, onClose, configs, onConfigsChange }: VatDiscountConfigModalProps) {
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingConfig, setEditingConfig] = useState<VatDiscountConfig | null>(null)

  const handleAddConfig = () => {
    setEditingConfig(null)
    setIsFormModalOpen(true)
  }

  const handleDeleteConfig = (configId: string) => {
    const updatedConfigs = configs.filter(config => config.id !== configId)
    onConfigsChange(updatedConfigs)
  }

  const handleSaveConfig = (configData: VatDiscountConfig) => {
    if (editingConfig) {
      // Update existing config
      const updatedConfigs = configs.map(config => (config.id === editingConfig.id ? configData : config))
      onConfigsChange(updatedConfigs)
    } else {
      // Add new config
      onConfigsChange([...configs, configData])
    }
    setIsFormModalOpen(false)
    setEditingConfig(null)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN')
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='max-h-[80vh] w-[60vw] !max-w-[95vw]'>
          <DialogHeader>
            <DialogTitle>Cấu hình Giảm giá VAT theo nghị định</DialogTitle>
          </DialogHeader>

          <div className='space-y-4'>
            <div className='flex justify-end'>
              <Button onClick={handleAddConfig} className='bg-green-600 hover:bg-green-700'>
                Thêm cấu hình
              </Button>
            </div>

            <div className='overflow-x-auto rounded-lg border'>
              <Table className='min-w-[800px]'>
                <TableHeader>
                  <TableRow>
                    <TableHead className='w-16'>STT</TableHead>
                    <TableHead className='w-32'>Mức giảm (%)</TableHead>
                    <TableHead className='w-32'>Thuế suất (%)</TableHead>
                    <TableHead className='min-w-[200px]'>Tên chương trình chiến dịch</TableHead>
                    <TableHead className='w-40'>Thời gian bắt đầu</TableHead>
                    <TableHead className='w-40'>Thời gian kết thúc</TableHead>
                    <TableHead className='w-16'></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {configs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className='py-8 text-center text-gray-500'>
                        Chưa có cấu hình nào
                      </TableCell>
                    </TableRow>
                  ) : (
                    configs.map((config, index) => (
                      <TableRow key={config.id}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{config.discountPercentage}</TableCell>
                        <TableCell>{config.vatPercentage}</TableCell>
                        <TableCell>{config.programName}</TableCell>
                        <TableCell>{formatDate(config.startDate)}</TableCell>
                        <TableCell>{formatDate(config.endDate)}</TableCell>
                        <TableCell>
                          <Button
                            variant='ghost'
                            size='sm'
                            className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
                            onClick={() => handleDeleteConfig(config.id)}
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <DialogFooter>
            <Button variant='outline' onClick={onClose}>
              Hủy
            </Button>
            <Button onClick={onClose}>Lưu</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <VatDiscountFormModal
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false)
          setEditingConfig(null)
        }}
        onSave={handleSaveConfig}
        editingConfig={editingConfig}
      />
    </>
  )
}
