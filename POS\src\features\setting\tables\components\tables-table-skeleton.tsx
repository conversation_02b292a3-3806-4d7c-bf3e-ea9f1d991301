import { Skeleton } from '@/components/ui/skeleton'
import { TableCell, TableRow } from '@/components/ui/table'

export function TablesTableSkeleton() {
  return (
    <>
      {Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Skeleton className='h-4 w-4' />
          </TableCell>
          <TableCell className='text-center'>
            <Skeleton className='mx-auto h-4 w-6' />
          </TableCell>
          <TableCell>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' />
              <Skeleton className='h-3 w-24' />
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className='h-4 w-20' />
          </TableCell>
          <TableCell className='text-center'>
            <Skeleton className='mx-auto h-4 w-16' />
          </TableCell>
          <TableCell className='text-center'>
            <div className='flex items-center justify-center gap-2'>
              <Skeleton className='h-4 w-12' />
              <Skeleton className='h-6 w-6 rounded' />
            </div>
          </TableCell>
          <TableCell className='text-center'>
            <div className='flex items-center justify-center gap-2'>
              <Skeleton className='h-6 w-16 rounded-full' />
              <Skeleton className='h-8 w-8 rounded' />
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  )
}

export function TablesTableEmpty() {
  return (
    <TableRow>
      <TableCell colSpan={7} className='h-24 text-center'>
        <div className='flex flex-col items-center justify-center space-y-2'>
          <p className='text-muted-foreground'>Không có bàn nào</p>
          <p className='text-muted-foreground text-sm'>Hãy tạo bàn mới để bắt đầu quản lý</p>
        </div>
      </TableCell>
    </TableRow>
  )
}
