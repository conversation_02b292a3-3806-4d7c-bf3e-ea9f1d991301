import type { AxiosResponse } from 'axios'

import type { BillingDetailResponse, BillingDetailParams } from '@/types/api/crm/billing-detail'

import { crmApi } from './crm-api'

export const getBillingDetailApi = async (params: BillingDetailParams): Promise<AxiosResponse<BillingDetailResponse>> => {
  const queryParams = new URLSearchParams({
    page: params.page.toString(),
    type: params.type,
    date_start: params.date_start,
    date_end: params.date_end,
    pos_parent: params.pos_parent
  })

  return crmApi.get(`/billing/get-detail-using-month?${queryParams.toString()}`)
}
