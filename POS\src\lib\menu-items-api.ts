import type { MenuItemsResponse, UpdateMenuItemRequest } from '@/types/api/menu-items'

import { crmApi } from '@/lib/api/crm/crm-api'

export const menuItemsApi = {
  getMenuItems: async (page: number = 0, posParent: string): Promise<MenuItemsResponse> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('page', page.toString())
      queryParams.append('pos_parent', posParent || '')

      const response = await crmApi.get(`/settings/get-items-by-pos-parent?${queryParams.toString()}`)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from menu items API')
      }

      return response.data as MenuItemsResponse
    } catch (error) {
      await new Promise(resolve => setTimeout(resolve, 500))
      return { count: 0, totalPage: 0, list_item: [] }
    }
  },

  updateMenuItem: async (updateData: UpdateMenuItemRequest): Promise<any> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('pos_parent', updateData.pos_parent || '')

      const response = await crmApi.post(`/settings/post-sync?${queryParams.toString()}`, updateData)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from update menu item API')
      }

      return response.data
    } catch (error) {
      throw error
    }
  },

  updateCombo: async (updateData: UpdateMenuItemRequest): Promise<any> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('pos_parent', updateData.pos_parent || '')

      const response = await crmApi.post(`/settings/post-sync?${queryParams.toString()}`, updateData)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from update combo API')
      }

      return response.data
    } catch (error) {
      throw error
    }
  }
}
