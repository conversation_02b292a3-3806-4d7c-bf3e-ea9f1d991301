import { createFileRoute, redirect } from '@tanstack/react-router'

import { isAuthenticated } from '@/lib/auth'

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: async ({ location }) => {
    if (!isAuthenticated()) {
      throw redirect({
        to: '/sign-in',
        search: {
          redirect: location.href
        }
      })
    }
  },
  component: AuthenticatedLayout
})
