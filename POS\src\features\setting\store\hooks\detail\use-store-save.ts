import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { updateStoreStatus } from '@/lib/stores-api'

import { useCreateStore } from '@/hooks/api/use-stores'

import { validateRequiredFields, type StoreFormValues, STORE_CONSTANTS } from '../../data'
import { mapFormDataToCreateStoreRequest, mapFormDataToUpdateStoreRequest } from '../../utils'
import { useCurrentStoreData } from './use-store-data'

interface UseStoreSaveProps {
  formData: StoreFormValues
  isFormValid: boolean
  storeId?: string
  onSuccess?: () => void
}

export const useStoreSave = ({ formData, storeId, onSuccess }: UseStoreSaveProps) => {
  const navigate = useNavigate()
  const { company, brands } = useAuthStore(state => state.auth)
  const { createStore, isCreating } = useCreateStore()
  const isEditMode = !!storeId

  const { data: currentStoreData } = useCurrentStoreData({
    storeId,
    enabled: isEditMode
  })

  const handleBack = () => {
    navigate({ to: STORE_CONSTANTS.ROUTE_STORE_LIST }).then(() => {
      window.location.reload()
    })
  }

  const handleSave = () => {
    if (!validateRequiredFields(formData)) {
      toast.error('Vui lòng điền đầy đủ: Tên điểm, Địa chỉ, Điện thoại, Thành phố')
      return
    }

    if (!company?.id || !brands?.[0]?.id) {
      toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
      return
    }

    if (isEditMode) {
      if (!currentStoreData) {
        toast.error('Không thể lấy dữ liệu cửa hàng hiện tại')
        return
      }

      const updateStoreData = mapFormDataToUpdateStoreRequest(formData, currentStoreData)

      updateStoreStatus(updateStoreData)
        .then(() => {
          toast.success('Cập nhật cửa hàng thành công')
          onSuccess?.()
          handleBack()
        })
        .catch(error => {
          toast.error(error.message || 'Có lỗi xảy ra khi cập nhật cửa hàng')
        })
    } else {
      const createStoreData = mapFormDataToCreateStoreRequest(
        formData,
        company.id,
        brands[0].id,
        formData.sources_print
      )

      createStore(createStoreData, {
        onSuccess: () => {
          onSuccess?.()
          handleBack()
        }
      })
    }
  }

  return {
    handleBack,
    handleSave,
    isEditMode,
    isCreating
  }
}
