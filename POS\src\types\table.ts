export interface Area {
  id: string
  area_id: string
  area_name: string
  active: number
  sort: number
  extra_data: Record<string, unknown>
}

export interface Table {
  id: string
  table_id: string
  table_name: string
  description: string
  extra_data: Record<string, unknown>
  active: number
  revision: string | null
  sort: number
  is_fabi: number
  source_id: string
  area_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  area_id: string
  store_id: string | null
  brand_id: string | null
  company_id: string | null
  created_by: string
  updated_by: string
  created_at: string
  updated_at: string
  area: Area
}

export interface GetTablesParams {
  skip_limit?: boolean
  brand_uid?: string
  company_uid?: string
  list_store_uid?: string
  store_uid?: string
}

export interface TablesApiResponse {
  data: Table[]
  total?: number
}

export interface CreateTableRequest {
  company_uid: string
  brand_uid: string
  table_name: string
  table_id: string
  extra_data?: {
    order_list?: any[]
  }
  description?: string
  store_uid: string
  source_id: string
  area_uid: string
  area_id: string
  sort: number
}

export type BulkCreateTablesRequest = CreateTableRequest[]
