import React, { useCallback } from 'react'

import QRCode from 'react-qr-code'

import { useSaveBillTemplate } from '../../hooks'
import { BillData, SaveBillTemplateRequest } from '../../types'
import { EditableField } from '../editable-field'

interface BillTemplate3Props {
  billData: BillData
  billTemplateData?: SaveBillTemplateRequest
}

export function BillTemplate3({ billData, billTemplateData }: BillTemplate3Props) {
  const saveBillTemplate = useSaveBillTemplate()

  // Get config from extra_bill_template_3
  const data = (billTemplateData?.extra_bill_template_3 || {}) as any

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const displaySubtotal = data.show_discount ? billData.totalWithVat - billData.itemDiscountAmount : billData.subtotal
  const displayTotalWithVat = data.show_discount
    ? billData.totalWithVat - billData.itemDiscountAmount
    : billData.totalWithVat
  const displayGrandTotal = data.show_discount ? billData.grandTotal - billData.itemDiscountAmount : billData.grandTotal

  const handleFieldChange = useCallback(
    (fieldName: string, value: string) => {
      if (billTemplateData) {
        const updatedPayload: SaveBillTemplateRequest = {
          ...billTemplateData,
          revision: billTemplateData.revision + 1,
          updated_at: new Date().toISOString(),
          extra_bill_template_3: {
            ...(billTemplateData.extra_bill_template_3 || {}),
            [fieldName]: value
          } as any
        }

        saveBillTemplate.mutate(updatedPayload)
      }
    },
    [billTemplateData, saveBillTemplate]
  )

  // Group items by type
  const groupedItems = billData.items.reduce(
    (acc, item) => {
      const type = item.itemType || 'Không có loại'
      if (!acc[type]) {
        acc[type] = []
      }
      acc[type].push(item)
      return acc
    },
    {} as Record<string, typeof billData.items>
  )

  return (
    <div
      className={`relative w-full border border-gray-300 bg-white font-sans text-sm shadow-md md:w-[460px] md:text-base ${saveBillTemplate.isPending ? 'opacity-75' : ''}`}
      style={{
        fontSize: `${(14 * (data.font_size_rate || 100)) / 100}px`
      }}
    >
      {saveBillTemplate.isPending && (
        <div className='bg-opacity-50 absolute inset-0 z-10 flex items-center justify-center bg-white'>
          <div className='text-sm text-black'>Đang lưu...</div>
        </div>
      )}

      {/* Header with store info */}
      <div className='flex items-center justify-center px-4 pt-5'>
        <div className='text-center'>
          <h5 className='text-center text-2xl font-bold text-black'>{billData.restaurantName}</h5>
          <p className='mb-0 text-center text-sm text-black'>Địa chỉ: {billData.restaurantAddress}</p>
          <p className='mb-0 text-center text-sm text-black'>
            Số Hotline: {billTemplateData?.extra_bill_template_3?.hotline || data.hotline || '123'}
          </p>
        </div>
      </div>

      <div className='px-4'>
        {/* Bill title */}
        <h3 className='mb-0 pt-3 text-center font-bold'>
          <EditableField
            value={billTemplateData?.extra_bill_template_3?.title_custom || 'HOÁ ĐƠN THANH TOÁN'}
            onSave={value => handleFieldChange('title_custom', value)}
            className='text-2xl text-black'
          />
        </h3>

        {/* Bill number */}
        <div className='mb-2 text-center font-bold text-black' style={{ fontSize: '1.3rem' }}>
          {data.show_merge_invoice_source
            ? `${billData.orderSource} ${billData.billNumber}`
            : `Số HĐ: ${billData.billNumber}`}
        </div>

        {/* Transaction details */}
        <div className='flex justify-between'>
          <div>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_code || 'Mã HĐ'}
                onSave={value => handleFieldChange('title_code', value)}
                className='font-bold'
              />
              : {billData.billId}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_table || 'Bàn'}
                onSave={value => handleFieldChange('title_table', value)}
                className='font-bold'
              />
              : {billData.table}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_time_in || 'Giờ vào'}
                onSave={value => handleFieldChange('title_time_in', value)}
                className='font-bold'
              />
              : {billData.checkInTime}
            </p>
          </div>
          <div>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_cashier || 'TN'}
                onSave={value => handleFieldChange('title_cashier', value)}
                className='font-bold'
              />
              : {billData.cashier}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_date || 'Ngày'}
                onSave={value => handleFieldChange('title_date', value)}
                className='font-bold'
              />
              : {billData.date}
            </p>
            <p className='mb-0'>
              <EditableField
                value={billTemplateData?.extra_bill_template_3?.title_time_out || 'Giờ ra'}
                onSave={value => handleFieldChange('title_time_out', value)}
                className='font-bold'
              />
              : {billData.checkOutTime}
            </p>
          </div>
        </div>

        {/* Items table */}
        <div className='border-b-2 border-dashed border-gray-300 pb-3'>
          <table className='w-full border-collapse border border-gray-700'>
            <thead>
              <th className='border-collapse border border-gray-700 px-1'>
                <EditableField
                  value={billTemplateData?.extra_bill_template_3?.title_stt || 'TT'}
                  onSave={value => handleFieldChange('title_stt', value)}
                  className='font-bold text-black'
                />
              </th>
              <th className='border-collapse border border-gray-700 px-1'>
                <EditableField
                  value={billTemplateData?.extra_bill_template_3?.title_name_item || 'Tên món'}
                  onSave={value => handleFieldChange('title_name_item', value)}
                  className='font-bold text-black'
                />
              </th>
              <th className='border-collapse border border-gray-700 px-1 text-center'>
                <EditableField
                  value={billTemplateData?.extra_bill_template_3?.title_quantity || 'SL'}
                  onSave={value => handleFieldChange('title_quantity', value)}
                  className='font-bold text-black'
                />
              </th>
              <th className='border-collapse border border-gray-700 px-1'>
                <EditableField
                  value={billTemplateData?.extra_bill_template_3?.title_price || 'Đơn giá'}
                  onSave={value => handleFieldChange('title_price', value)}
                  className='font-bold text-black'
                />
              </th>
              {data.enable_vat_rate && (
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_3?.title_vat_rate || 'VAT'}
                    onSave={value => handleFieldChange('title_vat_rate', value)}
                    className='font-bold text-black'
                  />
                </th>
              )}
              {data.enable_vat_amount && (
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_3?.title_vat_amount || 'Tiền VAT'}
                    onSave={value => handleFieldChange('title_vat_amount', value)}
                    className='font-bold text-black'
                  />
                </th>
              )}
              {data.enable_discount && (
                <th className='border-collapse border border-gray-700 px-1'>
                  <EditableField
                    value={billTemplateData?.extra_bill_template_3?.title_discount || 'GG'}
                    onSave={value => handleFieldChange('title_discount', value)}
                    className='font-bold text-black'
                  />
                </th>
              )}
              <th className='border-collapse border border-gray-700 px-1 text-right'>
                <EditableField
                  value={billTemplateData?.extra_bill_template_3?.title_amount || 'Thành tiền'}
                  onSave={value => handleFieldChange('title_amount', value)}
                  className='font-bold text-black'
                />
              </th>
            </thead>
            <tbody>
              {Object.entries(groupedItems).map(([itemType, items]) => (
                <React.Fragment key={itemType}>
                  {/* Item type header */}
                  {data.show_item_class && (
                    <tr>
                      <td
                        valign='top'
                        colSpan={2}
                        className='border-collapse border border-gray-700 px-1 font-semibold text-black'
                      >
                        {itemType}
                      </td>
                      {data.show_total_item_class_amount && (
                        <td
                          colSpan={
                            3 +
                            (data.enable_vat_rate ? 1 : 0) +
                            (data.enable_vat_amount ? 1 : 0) +
                            (data.enable_discount ? 1 : 0)
                          }
                          className='border-collapse border border-gray-700 text-center text-sm font-bold text-black'
                        >
                          {formatCurrency(items.reduce((sum, item) => sum + item.subtotal, 0))}
                        </td>
                      )}
                    </tr>
                  )}

                  {/* Items in this type */}
                  {items.map(item => (
                    <tr key={item.id}>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        {items.indexOf(item) + 1}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        <div>{item.name}</div>
                        {data.enable_topping && item.toppings && item.toppings.length > 0 && (
                          <small className='mb-0 ml-1'>+ {item.toppings.map(t => t.name).join(', ')}</small>
                        )}
                        {data.show_item_note && item.notes ? (
                          <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                            <span style={{ textDecoration: 'underline' }}>Ghi chú</span>: {item.notes}
                          </div>
                        ) : null}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-center text-black'>
                        <div>
                          {item.quantity}
                          {data.is_show_unit && <span>{item.unit}</span>}
                        </div>
                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>
                              {topping.quantity}
                              {data.is_show_unit && <small>{topping.unit}</small>}
                            </div>
                          ))}
                      </td>
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                        <div>{formatCurrency(item.unitPrice)}</div>
                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>{formatCurrency(topping.unitPrice)}</div>
                          ))}
                      </td>
                      {data.enable_vat_rate && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{item.vatPercentage || 10}%</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{topping.vatPercentage || 10}%</div>
                            ))}
                        </td>
                      )}
                      {data.enable_vat_amount && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{formatCurrency(item.vatAmount || item.subtotal * 0.1)}</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{formatCurrency(topping.vatAmount || topping.subtotal * 0.1)}</div>
                            ))}
                        </td>
                      )}
                      {data.enable_discount && (
                        <td valign='top' className='border-collapse border border-gray-700 px-1 text-black'>
                          <div>{item.discountPercentage || 0}%</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings.length > 0 &&
                            item.toppings.map((topping, tIndex) => (
                              <div key={tIndex}>{topping.discountPercentage || 0}%</div>
                            ))}
                        </td>
                      )}
                      <td valign='top' className='border-collapse border border-gray-700 px-1 text-right text-black'>
                        <div>{formatCurrency(item.subtotal)}</div>
                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings.length > 0 &&
                          item.toppings.map((topping, tIndex) => (
                            <div key={tIndex}>{formatCurrency(topping.subtotal)}</div>
                          ))}
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary section */}
        <div className='border-b-2 border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Thành tiền:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displaySubtotal)} ₫</p>
          </div>

          {data.enable_discount && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Tiền chiết khấu:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.itemDiscountAmount)} ₫</p>
            </div>
          )}

          {data.enable_vat_amount && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Tiền thuế(VAT):</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.vatAmount)} ₫</p>
            </div>
          )}

          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Thành tiền VAT:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displayTotalWithVat)} ₫</p>
          </div>

          {billData.shippingFee > 0 && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Phí vận chuyển:</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.shippingFee)} ₫</p>
            </div>
          )}

          {billData.voucherDiscount > 0 && (
            <div className='flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Phiếu giảm giá:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.voucherDiscount)} ₫</p>
            </div>
          )}
        </div>

        {/* Total section */}
        <div className='border-b-2 border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Tổng cộng:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(displayGrandTotal)} ₫</p>
          </div>

          {data.enable_cash_change && (
            <>
              <div className='mb-2 flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Tiền nhận:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(billData.amountReceived)} ₫</p>
              </div>
              <div className='flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Tiền thừa:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(billData.changeAmount)} ₫</p>
              </div>
            </>
          )}
        </div>

        {/* Additional info */}
        <div className='py-3'>
          {data.show_vat_reverse && (
            <div className='flex justify-between'>
              <p className='mb-0 text-center text-sm text-black'>+ Tổng tiền trên đã bao gồm</p>
              <p className='mb-0 font-bold text-black'>{formatCurrency(billData.vatAmount)} VAT</p>
            </div>
          )}
          {data.show_card_fee && (
            <div className='mb-2 flex justify-between'>
              <p className='mb-0 text-sm text-black'>+ Phí cà thẻ</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.cardFeeAmount)}</p>
            </div>
          )}
          <div className='flex justify-between'>
            <p className='mb-0 text-center text-sm text-black'>+ Thanh toán {billData.paymentMethod}</p>
            <p className='mb-0 text-sm text-black'>{formatCurrency(billData.paymentAmount)} ₫</p>
          </div>
        </div>

        {/* Customer info */}
        <div className='pb-3'>
          <div className='mx-auto mb-1' style={{ width: '25%', border: '1px solid rgb(0, 0, 0)' }}></div>

          <div className='mx-4 mt-2'>
            <p className='mb-0 text-sm text-black'>
              Khách hàng: {billData.customerName} -{' '}
              <span>
                {data.show_secure_customer_info
                  ? billData.customerPhone.substring(0, 2) + '*'.repeat(billData.customerPhone.length - 2)
                  : billData.customerPhone}
              </span>
            </p>
          </div>

          {data.show_points && (
            <div className='mx-4 mb-2'>
              <p className='mb-0 text-sm text-black'>Số điểm đã tích: {billData.accumulatedPoints?.toLocaleString()}</p>
            </div>
          )}

          <p className='my-2 text-center text-sm font-bold text-black'>{billData.voucherName}</p>

          <p className='mt-2 mb-0 text-center text-sm text-black' style={{ fontSize: '0.875rem' }}>
            <div>{billTemplateData?.extra_bill_template_3?.custom_text_1 || data.custom_text_1}</div>
          </p>

          <div className='mx-4 mt-3 text-center'>
            {data.qr_title && <p className='text-md mb-2 text-black'>{data.qr_title}</p>}
            {data.qr_content && data.qr_content.trim() && (
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={data.qr_content.trim()}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            )}
          </div>

          {data.show_vat_info && (
            <div className='mx-4 mt-3 text-black'>
              <p className='mb-1'>Thông tin khách hàng</p>
              <p className='mb-1'>Tên khách hàng: {billData.customerName}</p>
              <p className='mb-1'>Mã số thuế: {billData.customerTaxId}</p>
              <p className='mb-0'>Địa chỉ: {billData.customerAddress}</p>
            </div>
          )}

          {data.show_vat_qr_code && (
            <div className='mx-5 mt-2 flex flex-col justify-between'>
              <p className='mx-auto mb-1 text-center text-sm text-black'>
                Quét mã QR dưới đây để cung cấp thông tin hoá đơn điện tử
              </p>
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={'https://fabi.ipos.vn/'}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='border-t py-3'>
          <p className='mb-0 text-center text-sm font-bold text-black'>
            {billTemplateData?.extra_bill_template_3?.custom_text_2 || data.custom_text_2}
          </p>
          <p className='mb-0 text-center text-sm text-black'>Powered by iPOS.vn</p>
        </div>
      </div>
    </div>
  )
}
