import { useState } from 'react'

import { fromUnixTime, isValid, format } from 'date-fns'

import type { UseFormReturn } from 'react-hook-form'

import { toast } from 'sonner'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormControl, FormMessage, Input, Button } from '@/components/ui'

import { PAYMENT_LOCK_TIME_OPTIONS, type StoreFormValues } from '../../../../data'
import { useLicenseCheck } from '../../../../hooks'
import { LicenseDetailsDialog, type LicenseDetailsData } from './license-details-dialog'

const formatUnixToString = (value: unknown, fmt = 'dd/MM/yyyy HH:mm'): string => {
  const n = typeof value === 'string' ? parseInt(value, 10) : typeof value === 'number' ? value : NaN
  if (!Number.isFinite(n) || n <= 0) return ''
  const seconds = n > 1_000_000_000_000 ? Math.floor(n / 1000) : n
  const d = fromUnixTime(seconds)
  return isValid(d) ? format(d, fmt) : ''
}

interface ContactInfoSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  mode: 'add' | 'edit'
}

export function ContactInfoSection({ form, isLoading = false, mode }: ContactInfoSectionProps) {
  const { checkLicense, isCheckingLicense } = useLicenseCheck({ form })
  const [isLicenseDialogOpen, setIsLicenseDialogOpen] = useState(false)

  const licenseData: LicenseDetailsData = {
    storeName: form.getValues('store_name') || '',
    status: form.getValues('active') === 1 ? 'active' : 'inactive',
    startDate: formatUnixToString(form.getValues('created_at')),
    endDate: formatUnixToString(form.getValues('expiry_date')),
    type: 'Store',
    productCode: form.getValues('is_fabi') === 1 ? 'FABI' : '',
    serviceCode: 'FABI_SUB'
  }

  const handleCheckLicense = async () => {
    try {
      await checkLicense()
      toast.success('Kiểm tra bản quyền thành công')
    } catch (error) {
      toast.error('Kiểm tra bản quyền thất bại')
    }
  }

  const handleViewLicenseDetails = () => {
    setIsLicenseDialogOpen(true)
  }

  return (
    <div className='space-y-4'>
      {/* Email and Phone */}
      <div className='grid grid-cols-12 items-start gap-4'>
        <div className='col-span-3 pt-2'>
          <label className='text-sm font-medium text-gray-700'>Email</label>
        </div>
        <div className='col-span-3'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input type='email' placeholder='Nhập địa chỉ email' disabled={isLoading} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='col-span-2 pt-2'>
          <label className='text-sm font-medium text-gray-700'>
            Điện thoại <span className='text-red-500'>*</span>
          </label>
        </div>
        <div className='col-span-4'>
          <FormField
            control={form.control}
            name='phone'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder='Nhập số điện thoại' disabled={isLoading} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Foodbook Store ID and Brand Code */}
      {mode !== 'add' && (
        <>
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Foodbook Store ID</label>
            </div>
            <div className='col-span-3'>
              <FormField
                control={form.control}
                name='fb_store_id'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input placeholder='123939' disabled={true} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className='col-span-2 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Mã thương hiệu</label>
            </div>
            <div className='col-span-4'>
              <FormField
                control={form.control}
                name='partner_id'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input placeholder='BRAND-953H' disabled={true} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* License Expiry and Package */}
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Thời hạn bản quyền</label>
            </div>
            <div className='col-span-9'>
              <div className='flex w-full gap-2'>
                <FormField
                  control={form.control}
                  name='license_expiry'
                  render={({ field }) => (
                    <FormItem className='flex-1'>
                      <FormControl>
                        <Input placeholder='18/08/2025 23:59' disabled={true} {...field} className='w-full' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={handleCheckLicense}
                  disabled={isLoading || isCheckingLicense}
                >
                  {isCheckingLicense ? 'Đang kiểm tra...' : 'Kiểm tra'}
                </Button>
              </div>
            </div>
          </div>
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Gói bản quyền</label>
            </div>
            <div className='col-span-4'>
              <Button
                type='button'
                variant='link'
                className='h-auto p-0 text-blue-600 hover:text-blue-800'
                onClick={handleViewLicenseDetails}
                disabled={isLoading}
              >
                Xem chi tiết
              </Button>
            </div>
          </div>
          {/* Payment Lock Time */}
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Thời gian khóa PTTT chuyển khoản</label>
            </div>
            <div className='col-span-9'>
              <FormField
                control={form.control}
                name='payment_lock_time'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Combobox
                        options={PAYMENT_LOCK_TIME_OPTIONS}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder='Chọn thời gian khóa'
                        searchPlaceholder='Tìm kiếm...'
                        emptyText='Không tìm thấy tùy chọn nào.'
                        disabled={true}
                        className='w-full'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </>
      )}

      {/* License Details Dialog - Only show in edit mode */}
      {mode === 'edit' && (
        <LicenseDetailsDialog
          open={isLicenseDialogOpen}
          onOpenChange={setIsLicenseDialogOpen}
          licenseData={licenseData}
        />
      )}
    </div>
  )
}
