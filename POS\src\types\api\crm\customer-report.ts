// Customer Report API Types

export interface CustomerReportParams {
  start_date: string
  end_date: string
  pos_parent?: string
}

// 1. Customer Registration Report
export interface CustomerReportByPos {
  pos_parent: string
  pos_id: number
  number_eat_first_time: number
  number_eat_2nd_time: number
  number_eat_3_or_more_time: number
}

export interface CustomerReportByDate {
  pos_parent: string
  date_hash: string
  number_member_register: number
  number_eat_first_time: number
  number_eat_2nd_time: number
  number_eat_3_or_more_time: number
}

export interface CustomerRegistrationReportResponse {
  member_register_count: number
  total_eat_count: number
  first_time_count: number
  second_times_count: number
  three_and_above_times_count: number
  report_by_pos: CustomerReportByPos[]
  report_by_date: CustomerReportByDate[]
}

// 2. Membership Type Change Statistics
export interface MembershipTypeChangeItem {
  pos_parent: string
  date_hash: string
  membership_type_from: string
  membership_type_to: string
  count: number
}

export interface MembershipTypeChangeStatsResponse {
  data: MembershipTypeChangeItem[]
  total_count: number
}

// 3. Order by Membership Type
export interface OrderByMembershipTypeItem {
  pos_parent: string
  membership_type: string
  order_count: number
  total_amount: number
  avg_amount: number
  date_hash?: string
}

export interface OrderByMembershipTypeResponse {
  data: OrderByMembershipTypeItem[]
  total_orders: number
  total_amount: number
}

// Combined hook response
export interface CustomerReportData {
  registrationReport: CustomerRegistrationReportResponse | null
  membershipTypeStats: MembershipTypeChangeStatsResponse | null
  orderByMembershipType: OrderByMembershipTypeResponse | null
}
