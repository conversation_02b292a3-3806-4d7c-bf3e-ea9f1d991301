import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import { Combobox } from '@/components/pos'
import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  Input,
  Textarea,
  RadioGroup,
  RadioGroupItem,
  Label,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Button
} from '@/components/ui'

import {
  VAT_TYPE_OPTIONS,
  INVOICE_CONFIG_OPTIONS,
  INVOICE_OUTPUT_OPTIONS,
  type StoreFormValues
} from '../../../../data'
import { ContactInfoSection } from './contact-info-section'
import { LocationSection } from './location-section'
import { VatDiscountConfigModal, type VatDiscountConfig } from './vat-discount-config'

interface BasicInfoSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  mode: 'add' | 'edit'
}

export function BasicInfoSection({ form, isLoading = false, mode }: BasicInfoSectionProps) {
  const [isMapModalOpen, setIsMapModalOpen] = useState(false)
  const [isVatDiscountModalOpen, setIsVatDiscountModalOpen] = useState(false)

  const vatType = form.watch('sale_change_vat_enable')
  const vatDiscountConfigs = form.watch('vat_discount_configs') || []

  const shouldShowVatPercentage = [1, 2, 3, 4, 5].includes(vatType)
  const shouldShowVatDiscountConfig = vatType === 5

  const handleVatDiscountConfigsChange = (configs: VatDiscountConfig[]) => {
    form.setValue('vat_discount_configs', configs)
    const configText = configs.length > 0 ? `${configs.length} cấu hình` : '0 cấu hình'
    form.setValue('vat_discount_config', configText)
  }

  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Thông tin chi tiết</h2>

      <div className='space-y-4'>
        {/* Tên điểm */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>
              Tên điểm <span className='text-red-500'>*</span>
            </label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='store_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder='Nhập tên điểm' disabled={isLoading} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Mô tả */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Mô tả</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder='Mô tả' disabled={isLoading} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Loại VAT */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Loại VAT</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='sale_change_vat_enable'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Combobox
                      options={VAT_TYPE_OPTIONS}
                      value={field.value}
                      onValueChange={value => field.onChange(Number(value))}
                      placeholder='Tìm kiếm'
                      searchPlaceholder='Tìm kiếm loại VAT...'
                      emptyText='Không tìm thấy loại VAT nào.'
                      disabled={isLoading}
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Cấu hình in tách hóa đơn khi chọn vat theo món */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>Cấu hình in tách hóa đơn khi chọn vat theo món</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='print_bill_split'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Combobox
                      options={INVOICE_CONFIG_OPTIONS}
                      value={field.value}
                      onValueChange={value => field.onChange(Number(value))}
                      placeholder='Chọn cấu hình in hóa đơn'
                      searchPlaceholder='Tìm kiếm cấu hình...'
                      emptyText='Không tìm thấy cấu hình nào.'
                      disabled={isLoading}
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Đối tác xuất HĐĐT */}
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <div className='flex items-center gap-2'>
              <label className='text-sm font-medium text-gray-700'>Đối tác xuất HĐĐT</label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-xs'>
                    <p className='text-sm'>
                      Mỗi cửa hàng chỉ chọn một bên kết nối. Mỗi hóa đơn chỉ xuất cho một bên đối tác.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='partner_id'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Combobox
                      options={INVOICE_OUTPUT_OPTIONS}
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder='Chọn đối tác xuất HĐDT'
                      searchPlaceholder='Tìm kiếm đối tác...'
                      emptyText='Không tìm thấy đối tác nào.'
                      disabled={isLoading}
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* VAT Percentage - Show for specific VAT types */}
      {shouldShowVatPercentage && (
        <div className='grid grid-cols-12 items-start gap-4'>
          <div className='col-span-3 pt-2'>
            <label className='text-sm font-medium text-gray-700'>VAT (%)</label>
          </div>
          <div className='col-span-9'>
            <FormField
              control={form.control}
              name='value_vat'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='0'
                      disabled={isLoading}
                      {...field}
                      onChange={e => field.onChange(e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      )}

      {/* VAT Discount Config - Show for individual business */}
      {shouldShowVatDiscountConfig && (
        <>
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>Cấu hình Giảm giá VAT theo nghị định</label>
            </div>
            <div className='col-span-9'>
              <FormField
                control={form.control}
                name='vat_discount_config'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Button
                        type='button'
                        variant='outline'
                        className='w-full justify-start text-left font-normal'
                        onClick={() => setIsVatDiscountModalOpen(true)}
                        disabled={isLoading}
                      >
                        {field.value || `${vatDiscountConfigs.length} cấu hình`}
                      </Button>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className='grid grid-cols-12 items-start gap-4'>
            <div className='col-span-3 pt-2'>
              <label className='text-sm font-medium text-gray-700'>VAT (%)</label>
            </div>
            <div className='col-span-9'>
              <FormField
                control={form.control}
                name='value_vat'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='0'
                        disabled={isLoading}
                        {...field}
                        onChange={e => field.onChange(e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </>
      )}

      {/* Cấu hình kết nối thiết bị nội bộ qua mạng */}
      <div className='grid grid-cols-12 items-start gap-4'>
        <div className='col-span-3 pt-2'>
          <label className='text-sm font-medium text-gray-700'>Cấu hình kết nối thiết bị nội bộ qua mạng</label>
        </div>
        <div className='col-span-9'>
          <FormField
            control={form.control}
            name='net_work'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className='flex h-9 items-center gap-6'>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className='flex gap-6'
                      disabled={isLoading}
                    >
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='0' id='lan' />
                        <Label htmlFor='lan'>LAN</Label>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='1' id='lan-internet' />
                        <Label htmlFor='lan-internet'>LAN + Internet</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <LocationSection
        form={form}
        isLoading={isLoading}
        isMapModalOpen={isMapModalOpen}
        setIsMapModalOpen={setIsMapModalOpen}
      />

      <ContactInfoSection form={form} isLoading={isLoading} mode={mode} />

      {/* VAT Discount Configuration Modal */}
      <VatDiscountConfigModal
        isOpen={isVatDiscountModalOpen}
        onClose={() => setIsVatDiscountModalOpen(false)}
        configs={vatDiscountConfigs}
        onConfigsChange={handleVatDiscountConfigsChange}
      />
    </div>
  )
}
