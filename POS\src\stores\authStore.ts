import Cookies from 'js-cookie'

import type { LoginResponse, UserRole, Company, Brand, Store, City } from '@/types/auth'
import { create } from 'zustand'

import {
  POS_USER_DATA,
  POS_JWT_TOKEN,
  POS_BRANDS_DATA,
  POS_STORES_DATA,
  POS_CITIES_DATA,
  POS_COMPANY_DATA,
  POS_USER_ROLE_DATA
} from '@/constants/local-storage'

const ACCESS_TOKEN = 'thisisjustarandomstring'
const STATIC_ACCESS_TOKEN = import.meta.env.VITE_POS_ACCESS_TOKEN || '5c885b2ef8c34fb7b1d1fad11eef7bec'

interface AuthUser {
  id: string
  email: string
  full_name: string
  phone: string
  role_uid: string
  company_uid: string
  country_id: string
  active: number
  is_verified: number
  last_login_at: number
}

interface AuthState {
  auth: {
    user: AuthUser | null
    userRole: UserRole | null
    company: Company | null
    brands: Brand[]
    cities: City[]
    stores: Store[]

    // Tokens
    accessToken: string
    jwtToken: string

    // Actions
    setUser: (user: AuthUser | null) => void
    setLoginData: (loginResponse: LoginResponse) => void
    setAccessToken: (accessToken: string) => void
    setJwtToken: (jwtToken: string) => void
    resetAccessToken: () => void
    reset: () => void
  }
}

const getStoredUserData = (): AuthUser | null => {
  try {
    const stored = localStorage.getItem(POS_USER_DATA)
    return stored ? JSON.parse(stored) : null
  } catch (_error) {
    return null
  }
}

const getStoredJwtToken = (): string => {
  try {
    const stored = localStorage.getItem(POS_JWT_TOKEN)
    return stored || ''
  } catch (_error) {
    return ''
  }
}

const getStoredBrands = (): Brand[] => {
  try {
    const stored = localStorage.getItem(POS_BRANDS_DATA)
    return stored ? JSON.parse(stored) : []
  } catch (_error) {
    return []
  }
}

const getStoredStores = (): Store[] => {
  try {
    const stored = localStorage.getItem(POS_STORES_DATA)
    return stored ? JSON.parse(stored) : []
  } catch (_error) {
    return []
  }
}

const getStoredCities = (): City[] => {
  try {
    const stored = localStorage.getItem(POS_CITIES_DATA)
    return stored ? JSON.parse(stored) : []
  } catch (_error) {
    return []
  }
}

const getStoredCompany = (): Company | null => {
  try {
    const stored = localStorage.getItem(POS_COMPANY_DATA)
    return stored ? JSON.parse(stored) : null
  } catch (_error) {
    return null
  }
}

const getStoredUserRole = (): UserRole | null => {
  try {
    const stored = localStorage.getItem(POS_USER_ROLE_DATA)
    return stored ? JSON.parse(stored) : null
  } catch (_error) {
    return null
  }
}

export const useAuthStore = create<AuthState>()(set => {
  const cookieState = Cookies.get(ACCESS_TOKEN)
  let initToken = STATIC_ACCESS_TOKEN

  try {
    if (cookieState && cookieState !== 'undefined') {
      initToken = JSON.parse(cookieState)
    }
  } catch (_error) {
    initToken = STATIC_ACCESS_TOKEN
  }

  return {
    auth: {
      user: getStoredUserData(),
      userRole: getStoredUserRole(),
      company: getStoredCompany(),
      brands: getStoredBrands(),
      cities: getStoredCities(),
      stores: getStoredStores(),
      accessToken: initToken,
      jwtToken: getStoredJwtToken(),

      setUser: user =>
        set(state => {
          if (user) {
            localStorage.setItem(POS_USER_DATA, JSON.stringify(user))
          } else {
            localStorage.removeItem(POS_USER_DATA)
          }
          return { ...state, auth: { ...state.auth, user } }
        }),

      setLoginData: loginResponse =>
        set(state => {
          localStorage.setItem(POS_JWT_TOKEN, loginResponse.token)

          const userData: AuthUser = {
            id: loginResponse.user.id,
            email: loginResponse.user.email,
            full_name: loginResponse.user.full_name,
            phone: loginResponse.user.phone,
            role_uid: loginResponse.user.role_uid,
            company_uid: loginResponse.user.company_uid,
            country_id: loginResponse.user.country_id,
            active: loginResponse.user.active,
            is_verified: loginResponse.user.is_verified,
            last_login_at: loginResponse.user.last_login_at
          }
          localStorage.setItem(POS_USER_DATA, JSON.stringify(userData))

          localStorage.setItem(POS_USER_ROLE_DATA, JSON.stringify(loginResponse.user_role))
          localStorage.setItem(POS_COMPANY_DATA, JSON.stringify(loginResponse.company))
          localStorage.setItem(POS_BRANDS_DATA, JSON.stringify(loginResponse.brands))
          localStorage.setItem(POS_CITIES_DATA, JSON.stringify(loginResponse.cities))
          localStorage.setItem(POS_STORES_DATA, JSON.stringify(loginResponse.stores))

          return {
            ...state,
            auth: {
              ...state.auth,
              user: userData,
              userRole: loginResponse.user_role,
              company: loginResponse.company,
              brands: loginResponse.brands,
              cities: loginResponse.cities,
              stores: loginResponse.stores,
              jwtToken: loginResponse.token
            }
          }
        }),

      setAccessToken: accessToken =>
        set(state => {
          if (accessToken) {
            Cookies.set(ACCESS_TOKEN, JSON.stringify(accessToken))
          }
          return { ...state, auth: { ...state.auth, accessToken } }
        }),

      setJwtToken: jwtToken =>
        set(state => {
          if (jwtToken) {
            localStorage.setItem(POS_JWT_TOKEN, jwtToken)
          } else {
            localStorage.removeItem(POS_JWT_TOKEN)
          }
          return { ...state, auth: { ...state.auth, jwtToken } }
        }),

      resetAccessToken: () =>
        set(state => {
          Cookies.remove(ACCESS_TOKEN)
          return {
            ...state,
            auth: { ...state.auth, accessToken: STATIC_ACCESS_TOKEN }
          }
        }),

      reset: () =>
        set(state => {
          Cookies.remove(ACCESS_TOKEN)
          localStorage.removeItem(POS_USER_DATA)
          localStorage.removeItem(POS_JWT_TOKEN)
          localStorage.removeItem(POS_USER_ROLE_DATA)
          localStorage.removeItem(POS_COMPANY_DATA)
          localStorage.removeItem(POS_BRANDS_DATA)
          localStorage.removeItem(POS_CITIES_DATA)
          localStorage.removeItem(POS_STORES_DATA)

          localStorage.removeItem('pos_selected_brand')
          localStorage.removeItem('crm_token')
          return {
            ...state,
            auth: {
              ...state.auth,
              user: null,
              userRole: null,
              company: null,
              brands: [],
              cities: [],
              stores: [],
              accessToken: STATIC_ACCESS_TOKEN,
              jwtToken: ''
            }
          }
        })
    }
  }
})
