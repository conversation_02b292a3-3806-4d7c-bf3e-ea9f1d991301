import type { GetReportsWeekdaysParams, ReportsWeekdaysResponse } from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Get reports weekdays data
 */
export const getReportsWeekdays = async (
  params: GetReportsWeekdaysParams
): Promise<ReportsWeekdaysResponse> => {
  const response = await apiClient.get<ReportsWeekdaysResponse>('/v1/reports/sale-summary/weekdays', {
    params: {
      brand_uid: params.brand_uid,
      company_uid: params.company_uid,
      list_store_uid: params.list_store_uid,
      start_date: params.start_date,
      end_date: params.end_date,
      store_open_at: params.store_open_at ?? 0,
      limit: params.limit ?? 5
    }
  })

  return response.data
}
