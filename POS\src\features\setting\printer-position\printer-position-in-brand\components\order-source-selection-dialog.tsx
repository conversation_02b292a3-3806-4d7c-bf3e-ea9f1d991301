import { useEffect, useState } from 'react'

import type { Source } from '@/types/sources'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'

interface OrderSourceSelectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  orderSources: Source[]
  selectedOrderSources: string[]
  onConfirm: (selectedIds: string[]) => void
  onCancel: () => void
}

export function OrderSourceSelectionDialog({
  open,
  onOpenChange,
  orderSources,
  selectedOrderSources,
  onConfirm,
  onCancel
}: OrderSourceSelectionDialogProps) {
  const [tempSelectedOrderSources, setTempSelectedOrderSources] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const uniqueOrderSources = orderSources.filter(
    (source, index, self) => index === self.findIndex(s => s.sourceId === source.sourceId)
  )

  useEffect(() => {
    if (open) {
      setTempSelectedOrderSources([...selectedOrderSources])
    }
  }, [open, selectedOrderSources])

  const selectedSources = uniqueOrderSources.filter(
    source =>
      tempSelectedOrderSources.includes(source.sourceId) &&
      source.sourceName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const unselectedSources = uniqueOrderSources.filter(
    source =>
      !tempSelectedOrderSources.includes(source.sourceId) &&
      source.sourceName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleConfirm = () => {
    onConfirm(tempSelectedOrderSources)
    setSearchTerm('')
  }

  const handleCancel = () => {
    setTempSelectedOrderSources([])
    setSearchTerm('')
    onCancel()
  }

  const handleToggleOrderSource = (sourceId: string, checked: boolean) => {
    if (checked) {
      setTempSelectedOrderSources([...tempSelectedOrderSources, sourceId])
    } else {
      setTempSelectedOrderSources(tempSelectedOrderSources.filter(id => id !== sourceId))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl'>
        <div className='space-y-6 p-2'>
          {/* Search Input */}
          <OrderSourceSearchInput value={searchTerm} onChange={setSearchTerm} />

          {/* Order Sources List with Sections */}
          <OrderSourceSectionList
            selectedSources={selectedSources}
            unselectedSources={unselectedSources}
            selectedIds={tempSelectedOrderSources}
            onToggle={handleToggleOrderSource}
          />
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleConfirm}>Xong</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Search Input Component
interface OrderSourceSearchInputProps {
  value: string
  onChange: (value: string) => void
}

function OrderSourceSearchInput({ value, onChange }: OrderSourceSearchInputProps) {
  return <Input placeholder='Tìm kiếm' value={value} onChange={e => onChange(e.target.value)} />
}

// Order Sources Section List Component
interface OrderSourceSectionListProps {
  selectedSources: Source[]
  unselectedSources: Source[]
  selectedIds: string[]
  onToggle: (sourceId: string, checked: boolean) => void
}

function OrderSourceSectionList({
  selectedSources,
  unselectedSources,

  onToggle
}: OrderSourceSectionListProps) {
  return (
    <ScrollArea className='h-[60vh] w-full'>
      <div className='space-y-4'>
        {/* Selected Sources Section */}
        {selectedSources.length > 0 && (
          <div>
            <div className='mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600'>
              <span>✓ Đã chọn {selectedSources.length}</span>
            </div>
            <div className='space-y-3'>
              {selectedSources.map(source => (
                <OrderSourceItem key={source.sourceId} source={source} isSelected={true} onToggle={onToggle} />
              ))}
            </div>
          </div>
        )}

        {/* Unselected Sources Section */}
        {unselectedSources.length > 0 && (
          <div>
            <div className='mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600'>
              <span>Còn lại {unselectedSources.length}</span>
            </div>
            <div className='space-y-3'>
              {unselectedSources.map(source => (
                <OrderSourceItem key={source.sourceId} source={source} isSelected={false} onToggle={onToggle} />
              ))}
            </div>
          </div>
        )}
      </div>
    </ScrollArea>
  )
}

// Order Source Item Component
interface OrderSourceItemProps {
  source: Source
  isSelected: boolean
  onToggle: (sourceId: string, checked: boolean) => void
}

function OrderSourceItem({ source, isSelected, onToggle }: OrderSourceItemProps) {
  return (
    <div className='flex items-center space-x-3 rounded p-2 hover:bg-gray-50'>
      <Checkbox
        id={source.sourceId}
        checked={isSelected}
        onCheckedChange={checked => onToggle(source.sourceId, !!checked)}
        className='h-5 w-5'
      />
      <Label htmlFor={source.sourceId} className='flex-1 cursor-pointer text-base'>
        {source.sourceName}
      </Label>
    </div>
  )
}
