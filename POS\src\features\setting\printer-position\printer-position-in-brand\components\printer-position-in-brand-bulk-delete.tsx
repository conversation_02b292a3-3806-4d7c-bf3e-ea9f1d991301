'use client'

import { ConfirmDialog } from '@/components/confirm-dialog'

import { usePrinterPositionInBrand } from '../context'
import { useDeletePrinterPosition } from '../hooks'

export function PrinterPositionInBrandBulkDelete() {
  const { open, setOpen, selectedRows, setSelectedRows } = usePrinterPositionInBrand()
  const deletePrinterPositionMutation = useDeletePrinterPosition()

  const isOpen = open === 'bulk-delete'

  const handleClose = () => {
    setOpen(null)
    setSelectedRows([])
  }

  const handleConfirm = async () => {
    if (selectedRows.length === 0) return

    try {
      const ids = selectedRows.map(row => row.id)
      await deletePrinterPositionMutation.mutateAsync(ids)
      handleClose()
    } catch (_error) {
      // Error handling is done in the mutation hook
    }
  }

  const description = (
    <>
      Bạn có chắc chắn muốn xóa <strong>{selectedRows.length}</strong> vị trí máy in đã chọn không?
      <br />
      <br />
      Danh sách sẽ bị xóa:
      <ul className='mt-2 list-inside list-disc space-y-1'>
        {selectedRows.slice(0, 5).map(row => (
          <li key={row.id} className='text-sm'>
            {row.printerPositionName}
          </li>
        ))}
        {selectedRows.length > 5 && (
          <li className='text-muted-foreground text-sm'>... và {selectedRows.length - 5} vị trí khác</li>
        )}
      </ul>
      <br />
      Hành động này không thể hoàn tác.
    </>
  )

  return (
    <ConfirmDialog
      destructive
      open={isOpen}
      onOpenChange={handleClose}
      handleConfirm={handleConfirm}
      title='Xác nhận xóa'
      desc={description}
      confirmText={`Xóa ${selectedRows.length} vị trí`}
      className='max-w-md'
    />
  )
}
