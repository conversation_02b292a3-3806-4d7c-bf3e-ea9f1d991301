import type { DiscountPaymentApiResponse, GetDiscountPaymentParams, DiscountPayment } from '@/types/discount-payment'

import { api } from './api/pos/pos-api'

// Cache for discount payment requests
const discountPaymentCache = new Map<string, { data: DiscountPaymentApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<DiscountPaymentApiResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export const discountPaymentApi = {
  /**
   * Get discount payments with request deduplication and caching
   */
  getDiscountPayments: async (params: GetDiscountPaymentParams): Promise<DiscountPaymentApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.page || 1}-${params.list_store_uid || 'all'}-${params.active ?? 'all'}`

    // Check cache first
    const cached = discountPaymentCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request for this key
    const pendingRequest = pendingRequests.get(requestKey)
    if (pendingRequest) {
      return pendingRequest
    }

    // Create new request
    const requestPromise = (async (): Promise<DiscountPaymentApiResponse> => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.set('company_uid', params.company_uid)
        queryParams.set('brand_uid', params.brand_uid)

        if (params.page) {
          queryParams.set('page', params.page.toString())
        }

        if (params.list_store_uid) {
          queryParams.set('list_store_uid', params.list_store_uid)
        }

        if (params.active !== undefined) {
          queryParams.set('active', params.active.toString())
        }

        if (params.limit) {
          queryParams.set('limit', params.limit.toString())
        }

        const response = await api.get(`/mdata/v1/discount-payment?${queryParams.toString()}`)

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from discount payment API')
        }

        const result = response.data as DiscountPaymentApiResponse

        // Cache the result
        discountPaymentCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } catch (error: any) {
        console.error('Error fetching discount payments:', error)

        if (error.response?.status === 429) {
          throw new Error('Too many requests - please wait a moment before trying again.')
        }

        if (error.response?.status === 401) {
          throw new Error('Unauthorized - please check your authentication.')
        }

        if (error.response?.status === 403) {
          throw new Error('Forbidden - you do not have permission to access this resource.')
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Update discount payment
   */
  updateDiscountPayment: async (discountPaymentData: any): Promise<void> => {
    try {
      await api.put('/mdata/v1/discount-payment', discountPaymentData)

      // Clear cache after successful update
      discountPaymentCache.clear()
    } catch (error: any) {
      console.error('Error updating discount payment:', error)
      throw error
    }
  },

  /**
   * Delete discount payment
   */
  deleteDiscountPayment: async (params: { id: string; company_uid: string; brand_uid: string }): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('id', params.id)

      await api.delete(`/mdata/v1/discount-payment?${queryParams.toString()}`)

      // Clear cache after successful deletion
      discountPaymentCache.clear()
    } catch (error: any) {
      console.error('Error deleting discount payment:', error)
      throw error
    }
  },

  /**
   * Get discount payment programs for a specific store (for copying)
   */
  getDiscountPaymentPrograms: async (params: {
    company_uid: string
    brand_uid: string
    store_uid: string
  }): Promise<DiscountPayment[]> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('skip_limit', 'true')
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('store_uid', params.store_uid)

      const response = await api.get(`/mdata/v1/discount-payment?${queryParams.toString()}`)

      return (response.data as DiscountPaymentApiResponse)?.data || []
    } catch (error: any) {
      console.error('Error fetching discount payment programs:', error)
      throw error
    }
  },

  /**
   * Clone discount payment programs to target store
   */
  cloneDiscountPayments: async (params: {
    company_uid: string
    brand_uid: string
    list_discount_payment_uid: string[]
    store_uid_root: string
    store_uid_target: string
  }): Promise<{ success: boolean; message?: string; track_id: string }> => {
    try {
      const payload = {
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        list_discount_payment_uid: params.list_discount_payment_uid,
        store_uid_root: params.store_uid_root,
        store_uid_target: params.store_uid_target
      }

      const response = await api.post('/mdata/v1/discount-payment/clone', payload)

      return response.data as unknown as { success: boolean; message?: string; track_id: string }
    } catch (error: any) {
      console.error('Error cloning discount payments:', error)
      throw error
    }
  },

  /**
   * Get payment discount promotions for a specific store
   * Matches the exact API call: /mdata/v1/promotions?skip_limit=true&company_uid=...&brand_uid=...&store_uid=...&partner_auto_gen=0&active=1
   */
  getPaymentDiscountPromotions: async (params: {
    companyUid: string
    brandUid: string
    storeUid: string
  }): Promise<{ promotion_uid: string; promotion_name: string }[]> => {
    try {
      const queryParams = new URLSearchParams({
        skip_limit: 'true',
        company_uid: params.companyUid,
        brand_uid: params.brandUid,
        store_uid: params.storeUid,
        partner_auto_gen: '0',
        active: '1'
      })

      const url = `/mdata/v1/promotions?${queryParams.toString()}`
      console.log('🔥 Payment Discount Promotions API Call:', url)
      console.log('🔥 Params:', params)

      const response = await api.get(url)

      console.log('🔥 Payment Discount Promotions Response:', response.data)

      // Parse the nested structure: data[].list_data[]
      const promotionGroups = Array.isArray(response.data?.data) ? response.data.data : []
      const flatPromotions = promotionGroups.flatMap((group: any) =>
        Array.isArray(group.list_data)
          ? group.list_data.map((item: any) => ({
              promotion_uid: item.id, // The 'id' field is the promotion_uid we need
              promotion_name: item.promotion_name
            }))
          : []
      )

      console.log('🔥 Parsed Payment Discount Promotions:', flatPromotions)
      return flatPromotions
    } catch (error: any) {
      console.error('Error fetching payment discount promotions:', error)
      return []
    }
  },

  /**
   * Get discount payment by ID
   */
  getDiscountPaymentById: async (params: { id: string; company_uid: string; brand_uid: string }): Promise<any> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.set('company_uid', params.company_uid)
      queryParams.set('brand_uid', params.brand_uid)
      queryParams.set('id', params.id)

      const response = await api.get(`/mdata/v1/discount-payment?${queryParams.toString()}`)

      return response.data?.data || null
    } catch (error: any) {
      console.error('Error fetching discount payment by ID:', error)
      throw error
    }
  },

  /**
   * Create payment discount
   */
  createPaymentDiscount: async (discountPaymentData: any): Promise<void> => {
    try {
      await api.post('/mdata/v1/discount-payment', discountPaymentData)

      // Clear cache after successful creation
      discountPaymentCache.clear()
    } catch (error: any) {
      console.error('Error creating payment discount:', error)
      throw error
    }
  },

  /**
   * Clear cache manually
   */
  clearCache: () => {
    discountPaymentCache.clear()
    pendingRequests.clear()
  }
}
