import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import General from '@/features/reports/revenue/revenue/general'

export const Route = createFileRoute('/_authenticated/report/revenue/revenue/general')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: General
})
