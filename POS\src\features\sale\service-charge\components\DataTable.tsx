import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { ServiceCharge } from '@/types/service-charge'

import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { ExpandedConditionsModal } from './ExpandedConditionsModal'

interface Store {
  id: string
  store_name: string
}

interface DataTableProps {
  serviceCharges: ServiceCharge[]
  isLoading: boolean
  currentBrandStores: Store[]
  onToggleActive: (serviceCharge: ServiceCharge) => void
  currentPage: number
  pageSize?: number
}

export function DataTable({
  serviceCharges,
  isLoading,
  currentBrandStores,
  onToggleActive,
  currentPage,
  pageSize = 20
}: DataTableProps) {
  const navigate = useNavigate()
  const [selectedServiceCharge, setSelectedServiceCharge] = useState<ServiceCharge | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const getStoreName = (storeUid: string) => {
    const store = currentBrandStores.find(s => s.id === storeUid)
    return store?.store_name || 'Không xác định'
  }

  const handleRowClick = (serviceCharge: ServiceCharge, event: React.MouseEvent) => {
    const target = event.target as HTMLElement
    // Prevent navigation when clicking on badges or buttons
    if (
      target.closest('button') ||
      target.closest('[role="button"]') ||
      target.closest('.badge') ||
      target.tagName === 'BUTTON'
    ) {
      return
    }

    // Navigate to service charge detail with service charge ID only
    navigate({
      to: '/sale/service-charge/detail/$id',
      params: { id: serviceCharge.id }
    })
  }

  const formatDateRange = (fromDate: number, toDate: number) => {
    const from = new Date(fromDate).toLocaleDateString('vi-VN')
    const to = new Date(toDate).toLocaleDateString('vi-VN')
    return `${from} - ${to}`
  }

  const formatAmount = (serviceCharge: ServiceCharge) => {
    if (serviceCharge.charge_type === 'PERCENT') {
      return `${(serviceCharge.service_charge * 100).toFixed(0)}%`
    }
    return `${serviceCharge.service_charge.toLocaleString('vi-VN')} ₫`
  }

  const formatCondition = (serviceCharge: ServiceCharge) => {
    const conditions = []

    // Amount condition
    if (serviceCharge.from_amount > 0 || serviceCharge.to_amount > 0) {
      if (serviceCharge.from_amount === serviceCharge.to_amount && serviceCharge.from_amount === 0) {
        conditions.push('Tất cả đơn hàng')
      } else {
        conditions.push(
          `Từ ${serviceCharge.from_amount.toLocaleString('vi-VN')}₫ đến ${serviceCharge.to_amount.toLocaleString('vi-VN')}₫`
        )
      }
    }

    // Source condition
    if (serviceCharge.extra_data?.is_source === 1 && serviceCharge.extra_data?.source_ids?.length > 0) {
      conditions.push(`Nguồn: ${serviceCharge.extra_data.source_ids.length} nguồn`)
    }

    // Table condition
    if (serviceCharge.extra_data?.is_table === 1 && serviceCharge.extra_data?.table_ids?.length > 0) {
      conditions.push(`Bàn: ${serviceCharge.extra_data.table_ids.length} bàn`)
    }

    return conditions.length > 0 ? conditions.join(', ') : 'Không có điều kiện'
  }

  const handleExpandConditions = (serviceCharge: ServiceCharge) => {
    setSelectedServiceCharge(serviceCharge)
    setIsModalOpen(true)
  }

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-[50px]'>#</TableHead>
            <TableHead>Tên</TableHead>
            <TableHead>Cửa hàng</TableHead>
            <TableHead>Điều kiện áp dụng</TableHead>
            <TableHead>Thời gian áp dụng</TableHead>
            <TableHead>Số tiền</TableHead>
            <TableHead>Thao tác</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={7} className='h-24 text-center'>
                Đang tải dữ liệu...
              </TableCell>
            </TableRow>
          ) : serviceCharges.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className='text-muted-foreground h-24 text-center'>
                Không có dữ liệu
              </TableCell>
            </TableRow>
          ) : (
            serviceCharges.map((serviceCharge, index) => {
              const rowNumber = (currentPage - 1) * pageSize + index + 1
              return (
                <TableRow
                  key={serviceCharge.id}
                  className='hover:bg-muted/50 cursor-pointer'
                  onClick={e => handleRowClick(serviceCharge, e)}
                >
                  <TableCell className='font-medium'>{rowNumber}</TableCell>
                  <TableCell>
                    <div className='font-medium'>Phí dịch vụ {formatAmount(serviceCharge)}</div>
                  </TableCell>
                  <TableCell>{getStoreName(serviceCharge.store_uid)}</TableCell>
                  <TableCell>
                    <div className='space-y-1'>
                      <div className='text-sm'>{formatCondition(serviceCharge)}</div>
                      <button
                        onClick={e => {
                          e.stopPropagation()
                          handleExpandConditions(serviceCharge)
                        }}
                        className='cursor-pointer text-xs text-blue-600 hover:text-blue-800 hover:underline'
                      >
                        Điều kiện mở rộng
                      </button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className='space-y-1'>
                      {/* Check if expired */}
                      {serviceCharge.to_date < Date.now() ? (
                        <div className='font-medium text-red-600'>Hết hạn</div>
                      ) : (
                        <div className='text-sm'>{formatDateRange(serviceCharge.from_date, serviceCharge.to_date)}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className='font-medium'>{formatAmount(serviceCharge)}</TableCell>
                  <TableCell>
                    {serviceCharge.active === 1 ? (
                      <Badge
                        variant='default'
                        className='cursor-pointer bg-green-100 text-green-800 hover:bg-green-200'
                        onClick={e => {
                          e.stopPropagation()
                          onToggleActive(serviceCharge)
                        }}
                      >
                        Active
                      </Badge>
                    ) : (
                      <Badge
                        variant='destructive'
                        className='cursor-pointer bg-red-100 text-red-800 hover:bg-red-200'
                        onClick={e => {
                          e.stopPropagation()
                          onToggleActive(serviceCharge)
                        }}
                      >
                        Deactive
                      </Badge>
                    )}
                  </TableCell>
                </TableRow>
              )
            })
          )}
        </TableBody>
      </Table>

      {/* Expanded Conditions Modal */}
      {selectedServiceCharge && (
        <ExpandedConditionsModal
          serviceCharge={selectedServiceCharge}
          open={isModalOpen}
          onOpenChange={setIsModalOpen}
        />
      )}
    </div>
  )
}
