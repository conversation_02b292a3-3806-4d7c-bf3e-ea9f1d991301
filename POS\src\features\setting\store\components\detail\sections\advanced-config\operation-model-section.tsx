import type { UseFormReturn } from 'react-hook-form'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  Label
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface OperationModelSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function OperationModelSection({ form, isLoading = false }: OperationModelSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>M<PERSON> hình hoạt động</h2>

      <div className='space-y-6'>
        <FormField
          control={form.control}
          name='operate_model'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-start gap-4'>
                <FormLabel className='w-[200px] pt-2'><PERSON><PERSON> hình hoạt động</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value?.toString() || '0'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    className='flex-1 space-y-4'
                  >
                    {/* Cho phép thu ngân bỏ món */}
                    <div className='flex items-start space-x-3'>
                      <RadioGroupItem value='0' id='allow_cashier_remove' />
                      <div className='space-y-2'>
                        <Label htmlFor='allow_cashier_remove' className='text-base font-medium'>
                          Cho phép thu ngân bỏ món
                        </Label>
                        <p className='text-sm text-gray-600'>
                          Mô hình cho phép thu ngân bỏ món ngay cả sau khi in tạm tính
                        </p>
                      </div>
                    </div>

                    {/* Giới hạn quyền bỏ món của thu ngân */}
                    <div className='flex items-start space-x-3'>
                      <RadioGroupItem value='1' id='limit_cashier_remove' />
                      <div className='space-y-2'>
                        <Label htmlFor='limit_cashier_remove' className='text-base leading-relaxed font-medium'>
                          Giới hạn quyền bỏ món của thu ngân
                        </Label>
                        <p className='text-sm leading-relaxed text-gray-600'>
                          Mô hình cho phép thu ngân bỏ món nhưng không được phép bỏ những món đã in tạm tính. Thu ngân
                          cũng không được phép thêm voucher sau khi đã in tạm tính
                        </p>
                      </div>
                    </div>

                    {/* Không cho phép thu ngân bỏ món */}
                    <div className='flex items-start space-x-3'>
                      <RadioGroupItem value='2' id='no_cashier_remove' />
                      <div className='space-y-2'>
                        <Label htmlFor='no_cashier_remove' className='text-base leading-relaxed font-medium'>
                          Không cho phép thu ngân bỏ món
                        </Label>
                        <p className='text-sm leading-relaxed text-gray-600'>
                          Mô hình không cho phép thu ngân bỏ món, các yêu cầu bỏ món cần được sự đồng ý của Quản lý
                        </p>
                      </div>
                    </div>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
