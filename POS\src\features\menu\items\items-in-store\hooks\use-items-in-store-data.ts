import { useQuery } from '@tanstack/react-query'

import { useCurrentBrand } from '@/stores'

import { useAuthStore } from '@/stores/authStore'

import { QUERY_KEYS } from '@/constants/query-keys'

import { useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import type { ItemsInStore } from '../data'
import { itemsInStoreApiService } from './items-in-store-api'
import { type GetItemsInStoreParams, type GetItemByListIdParams, type ItemInStore } from './items-in-store-types'

export interface UseItemsInStoreDataOptions {
  params?: Partial<GetItemsInStoreParams>
  enabled?: boolean
}

export const useItemsInStoreData = (options: UseItemsInStoreDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const dynamicParams: GetItemsInStoreParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    reverse: 1,
    ...params,
    apply_with_store: params.apply_with_store
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  const itemsQuery = useQuery({
    queryKey: [
      QUERY_KEYS.ITEMS_IN_STORE_LIST,
      JSON.stringify(dynamicParams),
      typeof params.apply_with_store === 'undefined' ? 'no-apply-with-store' : String(params.apply_with_store)
    ],
    queryFn: async (): Promise<ItemInStore[]> => {
      const response = await itemsInStoreApiService.getItemsInStore(dynamicParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })

  const nextPageParams: GetItemsInStoreParams = {
    ...dynamicParams,
    page: (dynamicParams.page || 1) + 1
  }

  const nextPageQuery = useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST, 'next', JSON.stringify(nextPageParams)],
    queryFn: async (): Promise<ItemInStore[]> => {
      const response = await itemsInStoreApiService.getItemsInStore(nextPageParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth && (itemsQuery.data ? itemsQuery.data.length > 0 : false),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000
  })

  const pageSize = dynamicParams.limit || 50
  const hasNextPage =
    (nextPageQuery.data ? nextPageQuery.data.length > 0 : false) ||
    (itemsQuery.data ? itemsQuery.data.length === pageSize : false)

  return {
    data: itemsQuery.data,
    isLoading: itemsQuery.isLoading,
    error: itemsQuery.error,
    refetch: itemsQuery.refetch,
    isFetching: itemsQuery.isFetching,
    nextPageData: nextPageQuery.data || [],
    hasNextPage
  }
}

export const useItemInStoreDetail = (id?: string, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL, id],
    queryFn: () => itemsInStoreApiService.getItemById({ id: id! }),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemByListId = (params: GetItemByListIdParams, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST, 'by_list_id', params],
    queryFn: () => itemsInStoreApiService.getItemByListId(params),
    enabled: enabled && !!params.list_item_id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemsInStoreForTable = (options: UseItemsInStoreDataOptions = {}) => {
  const itemsQuery = useItemsInStoreData(options)

  const { data: itemTypesData = [] } = useItemTypesData({ skip_limit: true })
  const { data: itemClassesData = [] } = useItemClassesData({ skip_limit: true })
  const { data: unitsData = [] } = useUnitsData()
  const { data: citiesData = [] } = useCitiesData()

  const transformedData =
    (itemsQuery.data?.map(item => {
      const itemData = item as unknown as Record<string, unknown>
      const cityName = citiesData.find(city => city.id === itemData.city_uid)?.city_name || ''

      const itemType = itemData.item_type_uid ? itemTypesData.find(type => type.id === itemData.item_type_uid) : null
      const itemTypeName = itemType?.item_type_name || 'LOẠI KHÁC'

      const itemClass = itemData.item_class_uid ? itemClassesData.find(cls => cls.id === itemData.item_class_uid) : null
      const itemClassName = itemClass?.item_class_name || ''

      const unit = itemData.unit_uid ? unitsData.find(u => u.id === itemData.unit_uid) : null
      const unitName = unit?.unit_name || ''

      const hasEatWith = itemData.is_eat_with === 1 || (itemData.item_id_eat_with && itemData.item_id_eat_with !== '')
      const sideItemsValue = hasEatWith ? (itemData.item_id_eat_with as string) || 'Món ăn kèm' : ''

      return {
        ...item,
        code: (itemData.item_id as string) || '',
        name: item.item_name,
        price: item.ots_price,
        vatPercent: item.ots_tax,
        cookingTime: item.time_cooking,
        categoryGroup: itemTypeName,
        itemType: itemTypeName,
        itemClass: itemClassName,
        unit: unitName,
        sideItems: sideItemsValue || undefined,
        city: cityName,
        buffetConfig:
          (itemData.extra_data as Record<string, unknown>)?.is_buffet_item === 1 ? 'Đã cấu hình' : 'Chưa cấu hình',
        customization: (itemData.customization_uid as string) || undefined,
        applyWithStore: itemData.apply_with_store,
        isActive: Boolean(item.active),
        createdAt:
          typeof item.created_at === 'number'
            ? new Date(item.created_at * 1000)
            : new Date(new Date(item.created_at as unknown as string).getTime())
      }
    }) as unknown as ItemsInStore[]) || []

  return {
    data: transformedData,
    isLoading: itemsQuery.isLoading,
    error: itemsQuery.error,
    refetch: itemsQuery.refetch,
    isFetching: itemsQuery.isFetching,
    nextPageData: itemsQuery.nextPageData,
    hasNextPage: itemsQuery.hasNextPage
  }
}
