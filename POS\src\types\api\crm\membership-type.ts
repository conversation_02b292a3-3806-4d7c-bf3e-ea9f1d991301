export interface MembershipTypeItem {
  id?: string
  type_name?: string
  type_id?: string
  company_id?: string
  active?: number
  point_rate?: number
  upgrade_amount?: number | string
  upgrade_minus_point_amount?: number | string
  upgrade_reset_point_amount?: number | string
  downgrade_amount?: number
  downgrade_to_level?: string
  downgrade_minus_point_amount?: number
  downgrade_reset_point_amount?: number
  downgrade_minus_point?: number
  downgrade_reset_point?: number
  unchange_minus_point_amount?: number
  unchange_reset_point_amount?: number
  is_no_change?: number
  created_at?: string
  updated_at?: string
  image?: string
}

export interface MembershipTypeResponse {
  count: number
  totalPage: number
  list_membership_type: MembershipTypeItem[]
}

export interface MembershipTypeParams {
  company_id: string
  pos_parent: string
}

export interface CreateMembershipTypeParams {
  company_id: string
  type_id: string
  type_name: string
  point_rate: number
  active: number
  upgrade_amount?: number | string
  upgrade_minus_point_amount?: number | string
  upgrade_reset_point_amount?: number | string
  downgrade_amount?: number
  downgrade_to_level?: string
  downgrade_minus_point_amount?: number
  downgrade_reset_point_amount?: number
  downgrade_minus_point?: number
  downgrade_reset_point?: number
  unchange_minus_point_amount?: number
  unchange_reset_point_amount?: number
  is_no_change: number
}

export interface CreateMembershipTypeResponse {
  data: string
  trackid: string
  ip: string
}

// Update API returns the same shape as create
export type UpdateMembershipTypeResponse = CreateMembershipTypeResponse

export interface UpdateMembershipTypeParams {
  pos_parent: string
}
