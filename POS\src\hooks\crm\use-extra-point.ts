import { useQuery } from '@tanstack/react-query'

import { extraPointApi } from '@/lib/api/crm'

import { CRM_QUERY_KEYS } from '@/constants/crm'

import { usePosCompanyData, useBrandsData } from '@/hooks/local-storage'

export const useExtraPoint = () => {
  const posCompanyData = usePosCompanyData()
  const brandsData = useBrandsData()

  const params =
    posCompanyData && brandsData
      ? {
          company_id: posCompanyData.company_id,
          pos_parent: brandsData[0]?.brand_id || ''
        }
      : {
          company_id: '',
          pos_parent: ''
        }

  return useQuery({
    queryKey: ['crm', CRM_QUERY_KEYS.EXTRA_POINT, params],
    queryFn: () => extraPointApi.getList(params),
    enabled: !!params.company_id && !!params.pos_parent
  })
}
