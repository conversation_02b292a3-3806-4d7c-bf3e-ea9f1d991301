import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { Discount } from '@/types/discounts'
import { Trash2 } from 'lucide-react'

import { ConfirmModal } from '@/components/pos/modal/ConfirmModal'
import { Badge, Button } from '@/components/ui'

interface DiscountDataTableProps {
  discounts: Discount[]
  isLoading: boolean
  onToggleActive: (discount: Discount) => void
  onDeleteDiscount: (discountId: string) => void
  isDeleting: boolean
}

export function DiscountDataTable({
  discounts,
  isLoading,
  onToggleActive,
  onDeleteDiscount,
  isDeleting
}: DiscountDataTableProps) {
  const navigate = useNavigate()
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [selectedDiscountId, setSelectedDiscountId] = useState<string | null>(null)

  const handleDeleteClick = (discountId: string) => {
    setSelectedDiscountId(discountId)
    setDeleteModalOpen(true)
  }

  const handleConfirmDelete = () => {
    if (selectedDiscountId) {
      onDeleteDiscount(selectedDiscountId)
      setDeleteModalOpen(false)
      setSelectedDiscountId(null)
    }
  }

  const handleCancelDelete = () => {
    setDeleteModalOpen(false)
    setSelectedDiscountId(null)
  }

  const handleRowClick = (discount: Discount, event: React.MouseEvent) => {
    const target = event.target as HTMLElement
    if (
      target.closest('button') ||
      target.closest('[role="button"]') ||
      target.closest('.badge') ||
      target.tagName === 'BUTTON'
    ) {
      return
    }

    console.log('Navigating to discount detail:', discount.id)
    navigate({
      to: '/sale-channel/discount/detail/$id',
      params: { id: discount.id },
      search: { store_uid: discount.storeUid }
    })
  }
  return (
    <div className='rounded-md border'>
      <table className='w-full'>
        <thead>
          <tr className='bg-muted/50 border-b'>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>#</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>Kênh</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>Cửa hàng</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>Tuỳ chỉnh</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>Giảm giá</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>Thao tác</th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'></th>
          </tr>
        </thead>
        <tbody>
          {isLoading ? (
            <tr>
              <td colSpan={7} className='h-24 text-center'>
                Đang tải dữ liệu...
              </td>
            </tr>
          ) : discounts.length === 0 ? (
            <tr>
              <td colSpan={7} className='text-muted-foreground h-24 text-center'>
                Không có dữ liệu
              </td>
            </tr>
          ) : (
            discounts.map((discount, index) => (
              <tr
                key={discount.id}
                className='hover:bg-muted/50 cursor-pointer border-b'
                onClick={e => handleRowClick(discount, e)}
              >
                <td className='px-4 py-3'>{index + 1}</td>
                <td className='px-4 py-3'>{discount.source.sourceName}</td>
                <td className='px-4 py-3'>{discount.storeName}</td>
                <td className='px-4 py-3'>
                  <div className='space-y-1'>
                    {/* Check if expired */}
                    {discount.toDate < Date.now() ? (
                      <div className='font-medium text-red-600'>Hết hạn</div>
                    ) : (
                      <div className='text-sm'>
                        Từ {new Date(discount.fromDate).toLocaleDateString('vi-VN')} đến{' '}
                        {new Date(discount.toDate).toLocaleDateString('vi-VN')}
                      </div>
                    )}
                    {/* Application scope */}
                    <div className='text-muted-foreground text-xs'>
                      {discount.isAll === 1 && '(Áp dụng cho tất cả)'}
                      {discount.isItem === 1 &&
                        discount.itemId &&
                        `(Áp dụng cho ${discount.itemId.split(',').length} món)`}
                      {discount.isType === 1 &&
                        discount.typeId &&
                        `(Áp dụng cho ${discount.typeId.split(',').length} loại món)`}
                      {discount.isCombo === 1 &&
                        discount.comboId &&
                        `(Áp dụng cho ${discount.comboId.split(',').length} combo)`}
                    </div>
                  </div>
                </td>
                <td className='px-4 py-3'>
                  {discount.discountType === 'AMOUNT'
                    ? `${discount.taDiscount.toLocaleString('vi-VN')}đ`
                    : `${(discount.taDiscount * 100).toFixed(0)}%`}
                </td>
                <td className='px-4 py-3'>
                  {discount.active === 1 ? (
                    <Badge
                      variant='default'
                      className='cursor-pointer bg-green-100 text-green-800 hover:bg-green-200'
                      onClick={e => {
                        e.stopPropagation()
                        onToggleActive(discount)
                      }}
                    >
                      Active
                    </Badge>
                  ) : (
                    <Badge
                      variant='destructive'
                      className='cursor-pointer bg-red-100 text-red-800 hover:bg-red-200'
                      onClick={e => {
                        e.stopPropagation()
                        onToggleActive(discount)
                      }}
                    >
                      Deactive
                    </Badge>
                  )}
                </td>
                <td className='px-4 py-3'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleDeleteClick(discount.id)}
                    disabled={isDeleting}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>

      <ConfirmModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        title='Xác nhận xóa'
        content='Bạn có chắc chắn muốn xóa chương trình giảm giá này? Hành động này không thể hoàn tác.'
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        confirmText='Xóa'
        cancelText='Hủy'
        isLoading={isDeleting}
      />
    </div>
  )
}
