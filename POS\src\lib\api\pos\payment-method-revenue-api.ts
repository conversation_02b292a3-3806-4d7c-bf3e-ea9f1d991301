import type {
  PaymentMethodRevenueParams,
  PaymentMethodRevenueResponse,
  PaymentMethodDetailsParams,
  PaymentMethodDetailsResponse
} from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Get payment method revenue data
 */
export const getPaymentMethodRevenue = async (
  params: PaymentMethodRevenueParams
): Promise<PaymentMethodRevenueResponse> => {
  const response = await apiClient.get<PaymentMethodRevenueResponse>('/v1/reports/sale-summary/payment-methods', {
    params
  })
  return response.data
}

/**
 * Get payment method details data
 */
export const getPaymentMethodDetails = async (
  params: PaymentMethodDetailsParams
): Promise<PaymentMethodDetailsResponse> => {
  const response = await apiClient.get<PaymentMethodDetailsResponse>(
    '/v2/reports/sale-summary/payment-method-details',
    {
      params
    }
  )
  return response.data
}
