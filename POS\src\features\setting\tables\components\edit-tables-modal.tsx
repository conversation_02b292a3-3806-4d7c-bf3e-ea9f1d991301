import { useState, useMemo, useRef } from 'react'

import { Download, Upload } from 'lucide-react'
import { toast } from 'sonner'

import { useCurrentCompany, useCurrentBrand } from '@/stores/posStore'

import { useSalesChannels } from '@/hooks/api/use-sales-channels'
import { useBulkUpdateTables } from '@/hooks/api/use-tables'
import { useTablesData } from '@/hooks/api/use-tables'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { useTablesEditExcelParser } from '../hooks/use-tables-edit-excel-parser'

interface ParsedTableEditData {
  id: string
  table_name: string
  source_name?: string
  pre_order_items?: string
  description?: string
}

interface Store {
  id: string
  store_name: string
  active: number
}

interface EditTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onSuccess: () => void
}

export function EditTablesModal({ open, onOpenChange, onCancel, onSuccess }: EditTablesModalProps) {
  // Import state
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedTableEditData[]>([])
  const [currentStep, setCurrentStep] = useState<1 | 2>(1)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  // Store selection state
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')

  // Hooks
  const { parseExcelFile, downloadTablesData } = useTablesEditExcelParser()
  const bulkUpdateTablesMutation = useBulkUpdateTables()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  // Get stores from localStorage
  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData) ? storesData.filter((store: Store) => store.active === 1) : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  // Get tables data for the selected store
  const { data: tables = [] } = useTablesData({
    storeUid: selectedStoreId
  })

  // Get sales channels data
  const { data: salesChannels = [] } = useSalesChannels({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    storeUid: selectedStoreId,
    skipLimit: true
  })

  // Set default store when stores are loaded
  useMemo(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores, selectedStoreId])

  // Reset import state when modal closes
  useMemo(() => {
    if (!open) {
      setCurrentStep(1)
      setImportParsedData([])
      setImportSelectedFile(null)
      setSelectedStoreId(stores[0]?.id || '')
    }
  }, [open, stores])

  const handleDownloadTablesData = async () => {
    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    try {
      await downloadTablesData(selectedStoreId, tables, salesChannels)
    } catch (error) {
      toast.error('Lỗi khi tải dữ liệu bàn')
    }
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedTables = await parseExcelFile(file)
      setImportParsedData(parsedTables)
      setCurrentStep(2)
      toast.success(`Đã phân tích ${parsedTables.length} bàn từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleConfirm = async () => {
    if (currentStep === 2 && selectedStoreId && importParsedData.length > 0) {
      try {
        // Convert parsed data to API format
        const tablesData = importParsedData.map(table => {
          // Find original table data
          const originalTable = tables.find(t => t.id === table.id)
          // Find source by name
          const source = salesChannels.find(s => s.sourceName === table.source_name)

          if (!originalTable) {
            throw new Error(`Không tìm thấy bàn với ID "${table.id}" trong hệ thống.`)
          }

          return {
            ...originalTable,
            table_name: table.table_name,
            source_id: source?.sourceId || originalTable.source_id || '',
            description: table.description || '',
            extra_data: {
              ...originalTable.extra_data,
              order_list: originalTable.extra_data?.order_list || []
            }
          }
        })

        await bulkUpdateTablesMutation.mutateAsync({
          storeUid: selectedStoreId,
          tables: tablesData
        })

        toast.success(`Đã cập nhật thành công ${importParsedData.length} bàn!`)
        onSuccess()
      } catch (error) {
        if (error instanceof Error) {
          toast.error(error.message)
        } else {
          toast.error('Lỗi khi cập nhật bàn. Vui lòng thử lại.')
        }
      }
    }
  }

  return (
    <PosModal
      title='Sửa thông tin bàn'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={currentStep === 2 ? handleConfirm : () => {}}
      confirmText={currentStep === 2 ? 'Cập nhật' : undefined}
      cancelText='Đóng'
      centerTitle={true}
      maxWidth='sm:max-w-4xl'
      isLoading={bulkUpdateTablesMutation.isPending}
      hideButtons={currentStep === 1}
      confirmDisabled={currentStep === 2 && (!selectedStoreId || importParsedData.length === 0)}
    >
      <div className='max-h-[70vh] space-y-6 overflow-y-auto'>
        {/* Step 1: Chọn cửa hàng, khu vực */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Bước 1. Chọn cửa hàng, khu vực</h3>
          <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Chọn cửa hàng' />
            </SelectTrigger>
            <SelectContent>
              {stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.store_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Step 2: Tải file dữ liệu bàn đã có */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>Bước 2. Tải file dữ liệu bàn đã có.</h3>
            <Button
              variant='outline'
              size='sm'
              onClick={handleDownloadTablesData}
              disabled={!selectedStoreId}
              className='flex items-center gap-2'
            >
              <Download className='h-4 w-4' />
              Tải xuống
            </Button>
          </div>
        </div>

        {/* Step 3: Thêm cấu hình vào file */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Bước 3. Thêm cấu hình vào file</h3>
          <p className='text-sm text-gray-600'>
            Không sửa các cột <span className='font-medium'>id</span>.
          </p>
        </div>

        {/* Step 4: Tải file lên */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>Bước 4. Tải file lên</h3>
            <Button variant='outline' size='sm' onClick={handleImportFileUpload} className='flex items-center gap-2'>
              <Upload className='h-4 w-4' />
              Tải file lên
            </Button>
          </div>
          <p className='text-sm text-gray-600'>Sau khi đã điền đầy đủ ban bạn có thể tải file lên</p>

          {importSelectedFile && (
            <div className='rounded-lg border bg-gray-50 p-3'>
              <p className='text-sm text-gray-700'>
                File đã chọn: <span className='font-medium'>{importSelectedFile.name}</span>
              </p>
            </div>
          )}
        </div>

        {/* Step 2 Content: Show parsed data */}
        {currentStep === 2 && importParsedData.length > 0 && (
          <div className='space-y-4 border-t pt-4'>
            <h4 className='text-md font-medium'>Xem trước dữ liệu cập nhật</h4>

            {/* Data Preview Table with fixed height */}
            <div className='h-64 overflow-y-auto rounded-lg border'>
              <table className='w-full text-sm'>
                <thead className='sticky top-0 z-10 bg-gray-50'>
                  <tr>
                    <th className='px-3 py-2 text-left text-xs font-medium'>ID</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Tên bàn</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Nguồn</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Món đặt trước</th>
                    <th className='px-3 py-2 text-left text-xs font-medium'>Mô tả</th>
                  </tr>
                </thead>
                <tbody>
                  {importParsedData.map((table, index) => (
                    <tr key={index} className='border-t hover:bg-gray-50'>
                      <td className='px-3 py-2 text-sm'>{table.id}</td>
                      <td className='px-3 py-2 text-sm'>{table.table_name}</td>
                      <td className='px-3 py-2 text-sm'>{table.source_name || '-'}</td>
                      <td className='px-3 py-2 text-sm'>{table.pre_order_items || '-'}</td>
                      <td className='px-3 py-2 text-sm'>{table.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Summary */}
            <div className='text-center text-sm text-gray-600'>
              Tổng cộng: <span className='font-medium'>{importParsedData.length}</span> bàn sẽ được cập nhật
            </div>
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={importFileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={handleImportFileChange}
        className='hidden'
      />
    </PosModal>
  )
}
