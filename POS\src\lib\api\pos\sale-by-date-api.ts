import { SaleByDateResponse, GetSaleByDateParams } from '@/types/api'

import { apiClient } from './pos-api'

/**
 * Sale by Date API functions
 */
export const saleByDateApi = {
  /**
   * Get sale by date report data
   */
  getSaleByDate: async (params: GetSaleByDateParams): Promise<SaleByDateResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      list_store_uid: params.list_store_uid,
      start_date: params.start_date.toString(),
      end_date: params.end_date.toString()
    })

    if (params.csv !== undefined) {
      queryParams.append('csv', params.csv.toString())
    }

    if (params.store_open_at !== undefined) {
      queryParams.append('store_open_at', params.store_open_at.toString())
    }

    if (params.results_per_page !== undefined) {
      queryParams.append('results_per_page', params.results_per_page.toString())
    }

    if (params.page !== undefined) {
      queryParams.append('page', params.page.toString())
    }

    const response = await apiClient.get(`/reports_v1/v3/pos-cms/report/sale-by-date?${queryParams.toString()}`)

    return response.data as SaleByDateResponse
  }
}

export default saleByDateApi
