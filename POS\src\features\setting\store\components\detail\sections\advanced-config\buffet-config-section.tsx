import type { UseFormReturn } from 'react-hook-form'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Checkbox } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface BuffetConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function BuffetConfigSection({ form, isLoading = false }: BuffetConfigSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Cấu hình nhà hàng buffet</h2>

      <div className='space-y-6'>
        {/* Nhà hàng buffet */}
        <FormField
          control={form.control}
          name='is_run_buffet'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Nhà hàng buffet</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Bắt buộc phải chọn vé buffet */}
        <FormField
          control={form.control}
          name='require_buffet_item'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Bắt buộc phải chọn vé buffet</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
