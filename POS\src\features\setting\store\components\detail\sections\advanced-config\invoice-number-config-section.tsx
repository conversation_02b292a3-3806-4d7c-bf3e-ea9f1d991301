import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import { Combobox } from '@/components/pos'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox,
  Button,
  Input,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'
import { OrderSourceSelectionModal } from '../order-source/order-source-selection-modal'

interface InvoiceNumberConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  storeUid?: string
}

export function InvoiceNumberConfigSection({ form, isLoading = false }: InvoiceNumberConfigSectionProps) {
  const [isSourceModalOpen, setIsSourceModalOpen] = useState(false)

  const formSources = form.watch('sources_not_print')
  const useInvoicePrefix = form.watch('enable_tran_no_prefix')

  const handleOpenSourceModal = () => {
    setIsSourceModalOpen(true)
  }

  const handleSourcesChange = (sources: string[]) => {
    form.setValue('sources_not_print', sources)
  }

  const invoiceNumberOptions = [
    {
      value: '0',
      label: 'Sinh số hoá đơn ngẫu nhiên'
    },
    {
      value: '1',
      label: 'Sinh số hoá đơn theo thứ tự khi in order'
    },
    {
      value: '2',
      label: 'Sinh số hoá đơn theo thứ tự khi in bill'
    }
  ]

  const resetScheduleOptions = [
    {
      value: 'DAILY',
      label: 'Đặt lại số hoá đơn theo ngày'
    },
    {
      value: 'MONTHLY',
      label: 'Đặt lại số hoá đơn theo tháng'
    },
    {
      value: 'YEARLY',
      label: 'Đặt lại số hoá đơn theo năm'
    },
    {
      value: 'NO_RESET',
      label: 'Không đặt lại'
    }
  ]

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Cấu hình số hóa đơn</h2>
      </div>

      <div className='space-y-4'>
        {/* Sinh số hóa đơn theo thứ tự */}
        <FormField
          control={form.control}
          name='tran_no_syn_order'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sinh số hóa đơn theo thứ tự</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value?.toString() || '0'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn cách sinh số hóa đơn'
                    className='flex-1'
                    options={invoiceNumberOptions}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sử dụng tiền tố cho số hóa đơn */}
        <FormField
          control={form.control}
          name='enable_tran_no_prefix'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sử dụng tiền tố cho số hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tiền tố của số hóa đơn - Only show when useInvoicePrefix is checked */}
        {useInvoicePrefix && (
          <FormField
            control={form.control}
            name='tran_no_prefix'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <div className='flex w-[240px] items-center gap-2'>
                    <FormLabel className='font-medium'>Tiền tố của số hóa đơn</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                        </TooltipTrigger>
                        <TooltipContent className='max-w-md p-4'>
                          <div className='space-y-2 text-sm'>
                            <p>Ký tự này sẽ được chèn vào trước mỗi mã số hóa đơn.</p>
                            <p className='font-medium'>Một số định dạng có thể hữu ích với bạn:</p>
                            <ul className='space-y-1 text-xs'>
                              <li>
                                <strong>TIME</strong> hoặc <strong>TIME_XXX</strong>: Lấy ngày hiện tại làm tiền tố.
                              </li>
                              <li>
                                <strong>TFYYMMdd</strong> hoặc <strong>TFYYMMdd_XXX</strong>: Lấy thời gian theo format
                                YYMMdd làm tiền tố. Khi sử dụng định dạng này, mã số hóa đơn của bạn sẽ là: 250101001.
                              </li>
                              <li>
                                <strong>NOPRE</strong> hoặc <strong>NOPRE_XXX</strong>: Không sử dụng tiền tố ở số hóa
                                đơn.
                              </li>
                            </ul>
                            <p className='text-xs text-gray-600'>
                              <strong>*Chú thích:</strong>
                              <br />
                              _XXX: dành để giới hạn số ký tự trong dãy số hóa đơn. Định dạng hợp lệ: _X; _XX; _XXX;
                              _XXXX. Nếu không sử dụng tiền tố này thì mặc định số hóa đơn có 4 ký tự.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormControl>
                    <Input
                      {...field}
                      value={field.value || ''}
                      placeholder='Nhập tiền tố cho số hóa đơn'
                      disabled={isLoading}
                      className='flex-1'
                      maxLength={30}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Thời gian đặt lại số hóa đơn */}
        <FormField
          control={form.control}
          name='reset_tran_no_period'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Thời gian đặt lại số hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    value={field.value || 'never'}
                    onValueChange={field.onChange}
                    disabled={isLoading}
                    placeholder='Chọn thời gian đặt lại'
                    className='flex-1'
                    options={resetScheduleOptions}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình nguồn không in hóa đơn */}
        <FormField
          control={form.control}
          name='sources_not_print'
          render={({ field: _field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cấu hình nguồn không in hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Button
                    type='button'
                    variant='outline'
                    disabled={isLoading}
                    onClick={handleOpenSourceModal}
                    className='border-blue-600 text-blue-600 hover:bg-blue-50'
                  >
                    {formSources?.length} nguồn được áp dụng
                  </Button>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Order Source Selection Modal */}
      <OrderSourceSelectionModal
        open={isSourceModalOpen}
        onOpenChange={setIsSourceModalOpen}
        onSourcesChange={handleSourcesChange}
        appliedSources={formSources}
      />
    </div>
  )
}
