/**
 * Date utility functions for consistent date handling across the application
 */

/**
 * Get default date range for monthly filter (3 months: from start of 3rd month ago to today)
 * Logic:
 * - Current month: from start of month to today (if month not finished)
 * - Previous months: full months (from start to end of month)
 */
export function getDefaultMonthlyDateRange(): { from: Date; to: Date } {
  return getLast3MonthsRange()
}

/**
 * Get default date range for daily filter (7 days ago to today)
 */
export function getDefaultDailyDateRange(): { from: Date; to: Date } {
  const today = new Date()
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(today.getDate() - 7)

  return {
    from: sevenDaysAgo,
    to: today,
  }
}

/**
 * Get date range for a specific number of months ago
 * Logic: from start of Nth month ago to today
 * Example: getMonthsAgoDateRange(3) = from start of 3rd month ago to today
 */
export function getMonthsAgoDateRange(months: number): {
  from: Date
  to: Date
} {
  const today = new Date()

  // Calculate start date: beginning of the month N months ago
  // months - 1 because we want to include the current month
  const startDate = new Date(
    today.getFullYear(),
    today.getMonth() - (months - 1),
    1
  )

  return {
    from: startDate,
    to: today,
  }
}

/**
 * Get date range for today only
 */
export function getTodayDateRange(): { from: Date; to: Date } {
  // Start of today (00:00:00)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // End of today (23:59:59.999)
  const endOfToday = new Date(today)
  endOfToday.setHours(23, 59, 59, 999)

  return {
    from: today,
    to: endOfToday,
  }
}

/**
 * Get date range for a specific number of days ago
 * Note: days = 1 means from yesterday to today, days = 7 means from 7 days ago to today
 */
export function getDaysAgoDateRange(days: number): { from: Date; to: Date } {
  // Normalize to start of today (00:00:00)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // Calculate days ago, also normalized to start of day
  const daysAgo = new Date(today)
  daysAgo.setDate(today.getDate() - days)

  // End of today (23:59:59.999)
  const endOfToday = new Date(today)
  endOfToday.setHours(23, 59, 59, 999)

  return {
    from: daysAgo,
    to: endOfToday,
  }
}
/**
 * Get the last 3 months range with proper monthly logic
 *
 * Logic:
 * - Current month: from start of month to today (if month not finished)
 * - Previous 2 months: full months (from start to end of month)
 *
 * Examples:
 * - If today is 26/06/2025 (June):
 *   * June: 01/06/2025 → 26/06/2025 (current date)
 *   * May: 01/05/2025 → 31/05/2025 (full month)
 *   * April: 01/04/2025 → 30/04/2025 (full month)
 *   * Result: 01/04/2025 → 26/06/2025
 */
export function getLast3MonthsRange(): { from: Date; to: Date } {
  const today = new Date()

  // Start from the beginning of the 3rd month ago
  // If today is June 26, we want: Apr 1 → Jun 26
  // So we go back 2 months from current month to get to the 3rd month ago
  const startDate = new Date(today.getFullYear(), today.getMonth() - 2, 1)

  return {
    from: startDate,
    to: today,
  }
}

/**
 * Get default date range based on filter type
 */
export function getDefaultDateRange(filterType: 'monthly' | 'daily'): {
  from: Date
  to: Date
} {
  return filterType === 'monthly'
    ? getLast3MonthsRange()
    : getDefaultDailyDateRange()
}
