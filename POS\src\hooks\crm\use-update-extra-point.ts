import { useMutation, useQueryClient } from '@tanstack/react-query'

import { extraPointApi } from '@/lib/api/crm'
import { CRM_QUERY_KEYS } from '@/constants/crm'
import { useBrandsData } from '@/hooks/local-storage'
import type { UpdateExtraPointRequest } from '@/types/api/crm'

export const useUpdateExtraPoint = () => {
  const queryClient = useQueryClient()
  const brandsData = useBrandsData()

  return useMutation({
    mutationFn: async (data: UpdateExtraPointRequest) => {
      const params = {
        pos_parent: brandsData?.[0]?.brand_id || ''
      }
      return extraPointApi.update(data, params)
    },
    onSuccess: () => {
      // Invalidate and refetch extra point data
      queryClient.invalidateQueries({
        queryKey: ['crm', CRM_QUERY_KEYS.EXTRA_POINT]
      })
    },
    onError: (error) => {
      console.error('Error updating extra point:', error)
    }
  })
}
