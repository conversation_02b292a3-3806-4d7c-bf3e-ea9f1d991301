import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  combosApi,
  type CombosListParams,
  type ComboPromotionsListParams,
  type DeleteCombosParams,
  type GetComboDetailsParams,
  type CreateCombosParams,
  type CreateComboRequest,
  type ComboDetail
} from '@/lib/combos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseCombosDataOptions {
  storeUid?: string | string[]
  page?: number
  status?: 'unexpired' | 'expired' | 'all'
  search?: string
  promotionUid?: string
  enabled?: boolean
  skipLimit?: boolean
}

/**
 * Hook to fetch combos data
 */
export const useCombosData = (options: UseCombosDataOptions = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { storeUid, page = 1, status = 'all', search, promotionUid, enabled = true, skipLimit = false } = options

  return useQuery({
    queryKey: [
      QUERY_KEYS.COMBOS_LIST,
      {
        company_uid: company?.id,
        brand_uid: selectedBrand?.id,
        storeUid,
        page,
        status,
        search,
        promotionUid,
        skipLimit
      }
    ],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand not found')
      }

      const params: CombosListParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        page,
        status,
        skip_limit: skipLimit
      }

      if (storeUid) {
        params.list_store_uid = storeUid
      }

      if (search) {
        params.search = search
      }

      if (promotionUid) {
        params.promotion_id = promotionUid
      }

      const response = await combosApi.getCombosList(params)
      return response.data
    },
    enabled: enabled && !!company?.id && !!selectedBrand?.id
  })
}

/**
 * Hook to fetch combo promotions data
 */
export const useComboPromotionsData = (
  options: { enabled?: boolean; storeUid?: string; skipLimit?: boolean; aggregate?: boolean } = {}
) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { enabled = true, storeUid, skipLimit = true, aggregate = true } = options

  return useQuery({
    queryKey: [
      QUERY_KEYS.COMBO_PROMOTIONS_LIST,
      {
        company_uid: company?.id,
        brand_uid: selectedBrand?.id,
        storeUid,
        skipLimit,
        aggregate
      }
    ],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand not found')
      }

      const params: ComboPromotionsListParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        skip_limit: skipLimit,
        aggregate: aggregate
      }

      if (storeUid) {
        params.store_uid = storeUid
      }

      const response = await combosApi.getComboPromotionsList(params)
      return response.data
    },
    enabled: enabled && !!company?.id && !!selectedBrand?.id
  })
}

/**
 * Hook to delete combos
 */
export const useDeleteCombos = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: DeleteCombosParams) => combosApi.deleteCombos(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.COMBOS_LIST]
      })
      toast.success('Xóa combo thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa combo'
      toast.error(errorMessage)
    }
  })
}

/**
 * Hook to get combo details for copying
 */
export const useGetComboDetails = () => {
  return useMutation({
    mutationFn: (params: GetComboDetailsParams) => combosApi.getComboDetails(params),
    onError: _error => {
      toast.error('Có lỗi xảy ra khi lấy thông tin combo')
    }
  })
}

/**
 * Hook to copy combos
 */
export const useCopyCombos = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CreateCombosParams) => combosApi.createCombos(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.COMBOS_LIST]
      })
      toast.success('Sao chép combo thành công')
    },
    onError: _error => {
      toast.error('Có lỗi xảy ra khi sao chép combo')
    }
  })
}

/**
 * Hook for creating combo promotion
 */
export function useCreateComboPromotion() {
  return {
    createPromotion: async (_params: any) => {
      return Promise.resolve()
    }
  }
}

/**
 * Hook for updating combos (bulk update)
 */
export function useUpdateCombos() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (combos: any[]) => {
      return await combosApi.updateCombos(combos)
    },
    onSuccess: () => {
      // Invalidate all combo-related queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBOS_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBO_PROMOTIONS_LIST] })
    },
    onError: error => {
      console.error('Error updating combos:', error)
    }
  })
}

/**
 * Hook for creating combo
 */
export function useCreateCombo() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: CreateComboRequest) => {
      return await combosApi.createCombo(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBOS_LIST] })
      toast.success('Tạo combo thành công!')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi tạo combo'
      toast.error(errorMessage)
    }
  })
}

/**
 * Hook for getting combo detail by package UID
 */
export function useComboDetailByPackageUid(packageUids: string | string[], enabled = true) {
  const uids = Array.isArray(packageUids) ? packageUids : [packageUids]

  return useQuery({
    queryKey: [QUERY_KEYS.COMBO_DETAIL, uids],
    queryFn: async () => {
      if (uids.length === 0) {
        return []
      }
      const response = await combosApi.getComboDetail(uids.join(','))
      return response.data
    },
    enabled: enabled && uids.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

/**
 * Hook for updating combo
 */
export function useUpdateCombo() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: ComboDetail | ComboDetail[]) => {
      if (Array.isArray(data)) {
        return await combosApi.updateCombos(data)
      }
      return await combosApi.updateCombo(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBOS_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBO_DETAIL] })
      toast.success('Cập nhật combo thành công!')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi cập nhật combo'
      toast.error(errorMessage)
    }
  })
}

/**
 * Hook for combo detail (for combo form - legacy)
 */
export function useComboDetail(_comboId: string) {
  return {
    data: null,
    isLoading: false,
    error: null
  }
}

/**
 * Hook for editing combo
 */
export function useEditCombo(_comboId: string, options: any = {}) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (_data: any) => {
      return Promise.resolve()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMBOS_LIST] })
      options.onSuccess?.()
    },
    onError: error => {
      options.onError?.(error)
    }
  })
}

/**
 * Hook for combo form validation
 */
export function useComboFormValidation() {
  return {
    validateFormData: (_data: any) => {
      return []
    }
  }
}
