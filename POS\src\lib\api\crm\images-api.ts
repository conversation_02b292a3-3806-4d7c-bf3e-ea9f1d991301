import { crmApi } from './crm-api'

/**
 * Convert File to Base64 string
 */
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      const base64String = result.split(',')[1]
      resolve(base64String)
    }
    reader.onerror = error => reject(error)
  })
}

export interface CrmImageUploadResponse {
  success?: boolean
  message?: string
  data: string // Direct URL string like "https://image.foodbook.vn/upload/20250823/1755965340741.png"
}

export interface CrmImageUploadRequest {
  image_string: string // Base64 encoded image string
  pos_parent: string
}

/**
 * CRM Images API Service
 */
export const crmImagesApi = {
  /**
   * Upload image to CRM settings
   * Endpoint: https://crm.ipos.vn/settings/upload?pos_parent=BRAND-953H
   */
  uploadImage: async (file: File, pos_parent: string): Promise<CrmImageUploadResponse> => {
    try {
      const base64String = await fileToBase64(file)

      const requestData: CrmImageUploadRequest = {
        image_string: base64String,
        pos_parent: pos_parent
      }

      const queryParams = new URLSearchParams({ pos_parent })
      const response = await crmApi.post<CrmImageUploadResponse>(
        `/settings/upload?${queryParams.toString()}`,
        requestData
      )

      return response.data
    } catch (error) {
      console.error('Error uploading image to CRM:', error)
      throw error
    }
  }
}
