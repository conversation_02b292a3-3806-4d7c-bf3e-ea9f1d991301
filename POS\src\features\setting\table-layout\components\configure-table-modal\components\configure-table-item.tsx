import React from 'react'

interface Table {
  id: string
  table_name: string
  extra_data?: {
    color?: string
    font_size?: string
  }
}

interface ConfigureTableItemProps {
  table: Table
  isSelected: boolean
  displayColor: string
  displayFontSize: string
  onSelect: (tableId: string) => void
}

export const ConfigureTableItem: React.FC<ConfigureTableItemProps> = ({
  table,
  isSelected,
  displayColor,
  displayFontSize,
  onSelect
}) => {
  return (
    <div
      className={`relative cursor-pointer transition-all ${
        isSelected ? 'opacity-100' : 'opacity-70 hover:opacity-100'
      }`}
      onClick={() => onSelect(table.id)}
    >
      <div className='relative flex flex-col items-center'>
        <div className='relative'>
          <div
            className={`relative mx-auto h-16 w-20 cursor-pointer rounded-[12px] shadow-md transition-all duration-200 hover:shadow-lg ${
              isSelected ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          >
            <div
              className={`pointer-events-none absolute -top-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-t-[8px] ${
                isSelected ? 'bg-blue-600' : 'bg-gray-400'
              }`}
            ></div>
            <div
              className={`pointer-events-none absolute top-1/2 -right-[8px] h-6 w-[8px] -translate-y-1/2 rounded-r-[8px] ${
                isSelected ? 'bg-blue-600' : 'bg-gray-400'
              }`}
            ></div>
            <div
              className={`pointer-events-none absolute -bottom-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-b-[8px] ${
                isSelected ? 'bg-blue-600' : 'bg-gray-400'
              }`}
            ></div>
            <div
              className={`pointer-events-none absolute top-1/2 -left-[8px] h-6 w-[8px] -translate-y-1/2 rounded-l-[8px] ${
                isSelected ? 'bg-blue-600' : 'bg-gray-400'
              }`}
            ></div>

            <div className='pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2'>
              <span
                className='truncate text-center text-xs font-medium'
                title={table.table_name}
                style={{
                  color: isSelected ? 'white' : displayColor,
                  fontSize: `${Math.min(parseInt(displayFontSize), 12)}px`
                }}
              >
                {table.table_name}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
