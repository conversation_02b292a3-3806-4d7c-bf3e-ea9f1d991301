export interface ComboItem {
  name: string
  ta_price?: number
  ots_price?: number
  store_item_id: string
}

export interface ComboDetail {
  item_id_list: string
  sub_id: string
  min_permitted: number
  max_permitted: number
  items: ComboItem[]
}

export interface NormalCombo {
  combo_id: string
  combo_name: string
  active: number
  type: string
  combo_details: ComboDetail[]
  allow_take_away: number
  sort: number
  ta_value: number
  ots_value: number
  update_at: string
}

export interface SpecialCombo {
  combo_id: string
  combo_name: string
  active: number
  hour_time_day?: number
  date_time_week?: number
  type: string
  combo_details: ComboDetail[]
  image?: string
  allow_take_away: number
  sort: number
  ta_value: number
  ots_value: number
  update_at: string
}

export interface NormalCombosResponse {
  data: NormalCombo[]
  count: number
  totalPage: number
}

export interface SpecialCombosResponse {
  data: SpecialCombo[]
  count: number
  totalPage: number
}
