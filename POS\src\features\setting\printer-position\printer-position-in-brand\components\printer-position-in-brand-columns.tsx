'use client'

import type { ColumnDef } from '@tanstack/react-table'

import { Checkbox } from '@/components/ui/checkbox'

import { DataTableColumnHeader } from '@/components/data-table'

import { PrinterPositionInBrand } from '../data'
import { PrinterPositionInBrandRowActions } from './printer-position-in-brand-row-actions'

export const columns: ColumnDef<PrinterPositionInBrand>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'printerPositionName',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tên vị trí máy in' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('printerPositionName')}</div>,
    enableSorting: false,
    enableHiding: false
  },
  {
    id: 'actions',
    header: '',
    cell: ({ row }) => <PrinterPositionInBrandRowActions row={row} />,
    enableSorting: false,
    enableHiding: false,
    size: 50
  }
]
