import { useState } from 'react'

import { But<PERSON>, Label } from '@/components/ui'

import { useServiceChargeFormData } from '../stores'
import { AddModal } from './add-modal'

export function CustomizationSection() {
  const { formData, updateFormData } = useServiceChargeFormData()
  const [modalOpen, setModalOpen] = useState(false)

  const handleAddItems = () => {
    setModalOpen(true)
  }

  const createBaseFilterState = (applyToAll: boolean) => ({
    ...formData.filterState,
    is_all: (applyToAll ? 1 : 0) as 0 | 1,
    is_type: 0 as 0 | 1,
    is_item: 0 as 0 | 1,
    is_combo: 0 as 0 | 1,
    type_id: '',
    item_id: '',
    combo_id: ''
  })

  const applyFilterByType = (filterState: any, activeFilter: string, selectedItems: string[]) => {
    const itemsString = selectedItems.join(',')

    switch (activeFilter) {
      case 'groups':
        filterState.is_type = 1 as 0 | 1
        filterState.type_id = itemsString
        break
      case 'items':
        filterState.is_item = 1 as 0 | 1
        filterState.item_id = itemsString
        break
      case 'packages':
        filterState.is_combo = 1 as 0 | 1
        filterState.combo_id = itemsString
        break
    }
  }

  const handleSaveItems = (selectedItems: string[], applyToAll: boolean, activeFilter: string | null) => {
    const newFilterState = createBaseFilterState(applyToAll)

    const hasItemsToApply = !applyToAll && selectedItems.length > 0 && activeFilter
    if (hasItemsToApply) {
      applyFilterByType(newFilterState, activeFilter!, selectedItems)
    }

    updateFormData({ filterState: newFilterState })
  }

  const isApplyToAll = formData.filterState?.is_all === 1

  const FILTER_TYPE_MAP = {
    groups: { flag: 'is_type', field: 'type_id', label: 'nhóm' },
    items: { flag: 'is_item', field: 'item_id', label: 'món' },
    packages: { flag: 'is_combo', field: 'combo_id', label: 'combo' }
  } as const

  const getActiveFilterType = () => {
    const filterState = formData.filterState
    if (!filterState) return null

    for (const [type, config] of Object.entries(FILTER_TYPE_MAP)) {
      if (filterState[config.flag as keyof typeof filterState] === 1) {
        return type as keyof typeof FILTER_TYPE_MAP
      }
    }
    return null
  }

  const getSelectedItemIds = () => {
    const filterState = formData.filterState
    const activeFilter = getActiveFilterType()

    if (!filterState || !activeFilter) return []

    const config = FILTER_TYPE_MAP[activeFilter]
    const fieldValue = filterState[config.field as keyof typeof filterState] as string

    return fieldValue ? fieldValue.split(',').filter(id => id.trim()) : []
  }

  const getDisplayText = () => {
    if (isApplyToAll) return 'Áp dụng cho tất cả'

    const selectedItems = getSelectedItemIds()
    if (selectedItems.length > 0) {
      const activeFilter = getActiveFilterType()
      const filterLabel = activeFilter ? FILTER_TYPE_MAP[activeFilter].label : ''
      return `${selectedItems.length} ${filterLabel}`
    }

    return 'Thêm'
  }

  return (
    <div className='space-y-4'>
      <h2 className='text-lg font-medium text-gray-900'>Tuỳ chỉnh</h2>
      <div className='mb-4 text-sm text-gray-600'>Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể</div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Áp dụng cho <span className='text-red-500'>*</span>
        </Label>
        <Button
          type='button'
          variant='outline'
          onClick={handleAddItems}
          disabled={!formData.storeUid}
          className='flex-1 justify-start'
        >
          {getDisplayText()}
        </Button>
      </div>

      <AddModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        storeUid={formData.storeUid}
        onSave={handleSaveItems}
        initialApplyToAll={isApplyToAll}
        initialActiveFilter={getActiveFilterType()}
        initialSelectedItems={getSelectedItemIds()}
      />
    </div>
  )
}
