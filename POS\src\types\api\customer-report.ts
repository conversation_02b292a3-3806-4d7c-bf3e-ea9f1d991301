// Customer Report API Types

export interface CustomerReportData {
  member_register_count: number
  total_eat_count: number
  first_time_count: number
  second_times_count: number
  three_and_above_times_count: number
  report_by_pos: ReportByPos[]
  report_by_date: ReportByDate[]
}

export interface ReportByPos {
  pos_parent: string
  pos_id: number
  number_eat_first_time: number
  number_eat_2nd_time: number
  number_eat_3_or_more_time: number
}

export interface ReportByDate {
  pos_parent: string
  date_hash: string
  number_member_register: number
  number_eat_first_time: number
  number_eat_2nd_time: number
  number_eat_3_or_more_time: number
}

export interface CustomerReportResponse {
  data?: CustomerReportData
  success?: boolean
  message?: string
}

// Order by Membership Type
export interface MembershipOrderData {
  membership_type: string
  order_count: number
  total_revenue: number
  average_order_value: number
  customer_count: number
}

export interface MembershipOrderResponse {
  data?: MembershipOrderData[]
  success?: boolean
  message?: string
}

// Membership Type Change Statistics
export interface MembershipChangeData {
  membership_type: string
  previous_count: number
  current_count: number
  change_count: number
  change_percentage: number
}

export interface MembershipChangeResponse {
  data?: MembershipChangeData[]
  success?: boolean
  message?: string
}

// Chart data interfaces for UI
export interface CustomerChartData {
  name: string
  value: number
}

export interface CustomerFrequencyChartData {
  name: string
  'Lượt khách hàng chi tiêu lần đầu': number
  'Lượt khách hàng chi tiêu lần 2': number
  'Lượt khách hàng chi tiêu từ 3 lần': number
}

export interface StoreReportData {
  store_name: string
  first_time_count: number
  second_times_count: number
  three_and_above_times_count: number
}

// Request parameters
export interface CustomerReportParams {
  start_date: string
  end_date: string
  pos_parent?: string
}
