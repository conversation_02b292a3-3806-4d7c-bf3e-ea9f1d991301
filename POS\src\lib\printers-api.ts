import { apiClient } from './api/pos/pos-api'

// Types based on the API response structure from danh_sach_may_in.md
export interface PrinterData {
  id: string
  company_uid: string
  brand_uid: string
  printer_device_id: string
  printer_name: string
  device_code: string
  revision: number
  sort: number
  created_by: string
  updated_by: string
  deleted_by: string | null
  store_uid: string
  ip: string
  port: number
  print_order: boolean
  extra_data: {
    x_label?: string
    y_label?: string
    he_label?: string
    wi_label?: string
    beep_beep?: number
    gab_label?: string
    hash_order?: boolean
    size_label?: string
    device_name?: string | null
    endpoint_id?: string
    size_qrcode?: number
    h_size_label?: string
    rotate_paper?: number
    screen_width?: number
    sheet_number?: number
    w_size_label?: string
    hight_quality?: number
    padding_label?: string
    qr_code_label?: number
    driver_printer?: number
    list_categories?: any[]
    identify_printer?: string
    printer_position?: string | null
    time_delay_command?: number
    visible_info_store?: number
    total_feed_in_delay?: number
    allow_edit_item_label?: number
    no_print_switch_table?: number
    split_toping_customize?: number
  }
  product_id: number
  vendor_id: number
  type_printer: string
  type: string
  created_at: string
  updated_at: string
  deleted_at: string | null
  deleted: boolean
  device_name: string
}

export interface PrintersApiParams {
  pos_device_code: string
  results_per_page?: number
}

export interface PrintersApiResponse {
  data: PrinterData[]
}

export interface CreatePrinterParams {
  type_printer: string // "Usb", "Lan", "Sunmi", "Kds"
  port: number
  extra_data: {
    hash_order: boolean
    size_label: string // "LABEL4x3"
    w_size_label: string // "38"
    h_size_label: string // "30"
    gab_label: string // "3"
    he_label: string // "220"
    sheet_number: number
    list_categories: any[]
    printer_position: string | null
    screen_width: number // 580
    rotate_paper: number // 0
    no_print_switch_table: number // 0
    device_name: string | null
  }
  product_id: string | number // "2"
  vendor_id: string | number // "2"
  device_code: string
  printer_name: string
  printer_device_id: string
  type: string // "PRINT_HS"
  print_order: boolean
}

/**
 * Printers API Service
 */
export const printersApi = {
  /**
   * Get printers list for a specific device
   */
  getPrinters: async (params: PrintersApiParams): Promise<PrintersApiResponse> => {
    const queryParams = new URLSearchParams({
      pos_device_code: params.pos_device_code,
      results_per_page: (params.results_per_page || 15000).toString()
    })

    const response = await apiClient.get<PrintersApiResponse>(`/v3/pos-cms/printer?${queryParams.toString()}`)

    return response.data
  },

  /**
   * Create a new printer
   */
  createPrinter: async (params: CreatePrinterParams): Promise<PrinterData> => {
    const response = await apiClient.post<{ data: PrinterData }>('/v3/pos-cms/printer', params, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      }
    })

    return response.data.data
  },

  /**
   * Delete a printer
   */
  deletePrinter: async (printerId: string): Promise<void> => {
    await apiClient.delete(`/v3/pos-cms/printer?id=${printerId}`, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      }
    })
  }
}
