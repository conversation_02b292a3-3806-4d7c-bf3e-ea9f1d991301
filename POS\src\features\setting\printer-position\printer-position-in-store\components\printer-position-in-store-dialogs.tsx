import { usePrinterPositionInStore } from '../context'
import { PrinterPositionInStoreMutate } from './printer-position-in-store-mutate'

export function PrinterPositionInStoreDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = usePrinterPositionInStore()

  return (
    <>
      <PrinterPositionInStoreMutate
        key='printer-position-create'
        open={open === 'create'}
        onOpenChange={() => setOpen(null)}
      />

      {currentRow && (
        <>
          <PrinterPositionInStoreMutate
            key={`printer-position-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={() => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />
        </>
      )}
    </>
  )
}
