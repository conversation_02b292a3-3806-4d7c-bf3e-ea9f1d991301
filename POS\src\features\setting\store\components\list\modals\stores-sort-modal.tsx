import { useEffect, useMemo, useState } from 'react'

import type { ApiStore } from '@/lib/stores-api'

import { useStoresForSort, useUpdateStoresSort } from '@/hooks/api/use-stores'

import { Skeleton } from '@/components/ui/skeleton'

import { PosModal } from '@/components/pos'

const SKELETON_ITEMS_COUNT = 8
const GRID_COLUMNS = 6
const DRAG_DATA_TYPE = 'text/plain'

const MODAL_CONFIG = {
  title: 'Sắp xếp cửa hàng',
  confirmText: 'Lưu',
  maxWidth: 'sm:max-w-4xl',
  description: 'Thứ tự hiển thị các cửa hàng sẽ được áp dụng tại hệ thống'
}

const MESSAGES = {
  loadError: 'C<PERSON> lỗi xảy ra khi tải danh sách cửa hàng',
  noStores: '<PERSON>hông có cửa hàng nào để sắp xếp'
}

const STYLES = {
  description: 'text-muted-foreground text-sm',
  errorText: 'text-sm text-red-600',
  emptyState: 'py-8 text-center',
  grid: `grid grid-cols-${GRID_COLUMNS} gap-2`,
  draggableItem:
    'flex aspect-square cursor-move items-center justify-center bg-slate-300 p-1 transition-colors select-none hover:bg-slate-400 rounded-sm text-xs',
  itemText: 'text-center text-xs font-medium leading-tight',
  skeletonContainer: 'aspect-square bg-slate-300 p-1 rounded-sm'
}

interface StoresSortModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

function LoadingSkeleton() {
  return (
    <div className={STYLES.grid}>
      {Array.from({ length: SKELETON_ITEMS_COUNT }).map((_, index) => (
        <div key={index} className={STYLES.skeletonContainer}>
          <Skeleton className='h-4 w-full' />
        </div>
      ))}
    </div>
  )
}

function ErrorState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.errorText}>{MESSAGES.loadError}</p>
    </div>
  )
}

function EmptyState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.description}>{MESSAGES.noStores}</p>
    </div>
  )
}

interface DraggableStoreItemProps {
  store: ApiStore
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
}

function DraggableStoreItem({ store, index, onDragStart, onDragOver, onDrop }: DraggableStoreItemProps) {
  return (
    <div
      key={store.id}
      draggable
      onDragStart={e => onDragStart(e, index)}
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      className={STYLES.draggableItem}
    >
      <div className={STYLES.itemText}>{store.store_name}</div>
    </div>
  )
}

function useFilteredStores(storesResponse: any) {
  return useMemo(() => {
    if (!storesResponse?.data) return []

    const isValidStore = (store: ApiStore) => store.active === 1

    return storesResponse.data.filter(isValidStore).sort((a: ApiStore, b: ApiStore) => a.sort - b.sort)
  }, [storesResponse?.data])
}

function useDragAndDrop(initialStores: ApiStore[]) {
  const [sortedStores, setSortedStores] = useState<ApiStore[]>([])

  useEffect(() => {
    setSortedStores(initialStores)
  }, [initialStores])

  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData(DRAG_DATA_TYPE, index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    const dragIndex = parseInt(e.dataTransfer.getData(DRAG_DATA_TYPE))

    if (dragIndex === dropIndex) return

    const newStores = [...sortedStores]
    const draggedItem = newStores[dragIndex]

    newStores.splice(dragIndex, 1)
    newStores.splice(dropIndex, 0, draggedItem)

    setSortedStores(newStores)
  }

  return {
    sortedStores,
    handleDragStart,
    handleDragOver,
    handleDrop
  }
}

export function StoresSortModal({ open, onOpenChange }: StoresSortModalProps) {
  const { data: storesResponse, isLoading, error } = useStoresForSort()
  const { updateSort, isUpdating } = useUpdateStoresSort()

  const filteredStores = useFilteredStores(storesResponse)
  const { sortedStores, handleDragStart, handleDragOver, handleDrop } = useDragAndDrop(filteredStores)

  const isProcessing = isLoading || isUpdating
  const hasNoStores = sortedStores.length === 0

  const handleSave = () => {
    const sortData = sortedStores.map((store, index) => ({
      ...store,
      sort: index
    }))

    updateSort(sortData, {
      onSuccess: () => onOpenChange(false)
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const renderContent = () => {
    if (isLoading) return <LoadingSkeleton />
    if (error) return <ErrorState />
    if (hasNoStores) return <EmptyState />

    return (
      <div className={STYLES.grid}>
        {sortedStores.map((store, index) => (
          <DraggableStoreItem
            key={store.id}
            store={store}
            index={index}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          />
        ))}
      </div>
    )
  }

  return (
    <PosModal
      open={open}
      onOpenChange={onOpenChange}
      title={MODAL_CONFIG.title}
      onCancel={handleCancel}
      onConfirm={handleSave}
      confirmText={MODAL_CONFIG.confirmText}
      hideButtons={false}
      confirmDisabled={isProcessing}
      isLoading={isUpdating}
      disableCancelButton={true}
      maxWidth={MODAL_CONFIG.maxWidth}
    >
      <div className='mb-4'>
        <p className={STYLES.description}>{MODAL_CONFIG.description}</p>
      </div>

      <div>{renderContent()}</div>
    </PosModal>
  )
}
