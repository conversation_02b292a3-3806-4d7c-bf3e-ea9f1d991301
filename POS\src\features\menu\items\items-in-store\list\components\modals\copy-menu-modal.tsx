import { useState, useEffect } from 'react'

import { useAuthStore, useCurrentBrand } from '@/stores'
import type { Store } from '@/types/store'
import { toast } from 'sonner'

import { useStoresData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { MultiSelect } from '@/components/multi-select'

import { useCloneMenu } from '../../../hooks'
import { CopyMenuConfirmDialog } from './copy-menu-confirm-dialog'
import { MenuItemSelectionModal } from './menu-item-selection-modal'

interface CopyMenuModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CopyMenuModal({ open, onOpenChange }: CopyMenuModalProps) {
  const [sourceStoreUid, setSourceStoreUid] = useState<string>('')
  const [targetStoreUids, setTargetStoreUids] = useState<string[]>([])
  const [selectedMenuItems, setSelectedMenuItems] = useState<string[]>([])
  const [itemSelectionOpen, setItemSelectionOpen] = useState(false)
  const [confirmOpen, setConfirmOpen] = useState(false)

  const { data: stores = [], isLoading: isLoadingStores } = useStoresData({
    enabled: open
  })

  const cloneMenuMutation = useCloneMenu()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  useEffect(() => {
    if (!open) {
      setSourceStoreUid('')
      setTargetStoreUids([])
      setSelectedMenuItems([])
      setItemSelectionOpen(false)
      setConfirmOpen(false)
    }
  }, [open])

  const storeOptions = stores.map((store: Store) => ({
    label: store.name,
    value: store.id
  }))

  const targetStoreOptions = storeOptions.filter(option => option.value !== sourceStoreUid)

  const handleSelectMenuItems = () => {
    if (!sourceStoreUid) {
      toast.error('Vui lòng chọn cửa hàng nguồn trước')
      return
    }
    setItemSelectionOpen(true)
  }

  const handleSync = () => {
    if (!sourceStoreUid) {
      toast.error('Vui lòng chọn cửa hàng nguồn')
      return
    }
    if (targetStoreUids.length === 0) {
      toast.error('Vui lòng chọn ít nhất một cửa hàng đích')
      return
    }
    if (selectedMenuItems.length === 0) {
      toast.error('Vui lòng chọn ít nhất một món ăn')
      return
    }
    setConfirmOpen(true)
  }

  const handleConfirmSync = async () => {
    try {
      if (!selectedBrand?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      // For each target store, clone the menu
      for (const targetStoreUid of targetStoreUids) {
        await cloneMenuMutation.mutateAsync({
          company_uid: company?.id as string,
          brand_uid: selectedBrand.id,
          store_uid_root: sourceStoreUid,
          store_uid_target: targetStoreUid,
          list_item_id: selectedMenuItems,
          menu_type: 'store'
        })
      }

      toast.success('Đồng bộ thực đơn thành công!')
      setConfirmOpen(false)
      onOpenChange(false)
    } catch (error) {
      console.error('Error syncing menu:', error)
    }
  }

  const getSourceStoreName = () => {
    const store = stores.find((s: Store) => s.id === sourceStoreUid)
    return store ? store.name : ''
  }

  const getTargetStoreNames = () => {
    return targetStoreUids
      .map(uid => {
        const store = stores.find((s: Store) => s.id === uid)
        return store ? store.name : ''
      })
      .filter(Boolean)
      .join(', ')
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Đồng bộ thực đơn</DialogTitle>
          </DialogHeader>

          {/* Warning message */}
          <div className='mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
            <p className='text-sm text-yellow-800'>
              Nếu không chọn món, thực đơn tại cửa hàng đích sẽ bị xoá để đồng bộ dữ liệu!
            </p>
            <p className='mt-1 text-sm text-yellow-800'>
              Trước khi đồng bộ món, để đảm bảo dữ liệu tại cửa hàng{' '}
              <span className='font-semibold text-red-600'>A Hưng</span>, hãy đồng bộ món tại thành phố{' '}
              <span className='font-semibold text-red-600'>Hà Nội</span>!
            </p>
          </div>

          <div className='space-y-6'>
            {/* Source Store */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Cửa hàng nguồn</label>
              <Select value={sourceStoreUid} onValueChange={setSourceStoreUid}>
                <SelectTrigger>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingStores ? (
                    <SelectItem disabled value='loading'>
                      Đang tải...
                    </SelectItem>
                  ) : (
                    storeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Target Stores */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Cửa hàng đích</label>
              <MultiSelect
                options={targetStoreOptions}
                value={targetStoreUids}
                onValueChange={setTargetStoreUids}
                placeholder='Tìm kiếm'
                variant='default'
                animation={2}
                maxCount={3}
              />
            </div>

            {/* Menu Items Selection */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Chọn món</label>
              <div className='flex gap-2'>
                <Button
                  variant='outline'
                  onClick={handleSelectMenuItems}
                  className='flex-1 justify-start text-left'
                  disabled={!sourceStoreUid}
                >
                  {selectedMenuItems.length > 0 ? `Đã chọn ${selectedMenuItems.length} món` : 'Tất cả các món'}
                </Button>
                <Button
                  onClick={handleSync}
                  className='bg-green-600 hover:bg-green-700'
                  disabled={cloneMenuMutation.isPending}
                >
                  {cloneMenuMutation.isPending ? 'Đang đồng bộ...' : 'Đồng bộ'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Menu Item Selection Modal */}
      <MenuItemSelectionModal
        open={itemSelectionOpen}
        onOpenChange={setItemSelectionOpen}
        sourceStoreUid={sourceStoreUid}
        selectedItems={selectedMenuItems}
        onItemsChange={setSelectedMenuItems}
        onSave={() => setItemSelectionOpen(false)}
      />

      {/* Confirmation Dialog */}
      <CopyMenuConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        onConfirm={handleConfirmSync}
        title={`Bạn có muốn đồng bộ ${selectedMenuItems.length > 0 ? `${selectedMenuItems.length} món` : 'bộ thực đơn'} của cửa hàng ${getSourceStoreName()} tới ${getTargetStoreNames()}?`}
        isLoading={cloneMenuMutation.isPending}
      />
    </>
  )
}
