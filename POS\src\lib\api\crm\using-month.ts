import type { AxiosResponse } from 'axios'

import type { UsingMonthData, UsingMonthParams } from '@/features/crm/using-month/types'

import { crmApi } from './crm-api'

export const getUsingMonthApi = async (params: UsingMonthParams): Promise<AxiosResponse<UsingMonthData>> => {
  const queryParams = new URLSearchParams({
    year: params.year,
    month: params.month,
    pos_parent: params.pos_parent
  })

  return crmApi.get(`/billing/get-using-month?${queryParams.toString()}`)
}
