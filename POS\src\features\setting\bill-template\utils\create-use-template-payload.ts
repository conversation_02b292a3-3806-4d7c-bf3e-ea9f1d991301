import type { UseBillTemplateRequest } from '@/lib/bill-template-api'

/**
 * Create payload for "Use Bill Template" API call
 * This updates the store's bill_template setting in extra_data
 */
export const createUseBillTemplatePayload = (storeData: any, templateNumber: number = 1): UseBillTemplateRequest => {
  if (!storeData.store_name && !storeData.name) {
    throw new Error('Store name is required but not found in store data')
  }

  if (!storeData.id) {
    throw new Error('Store ID is required but not found in store data')
  }

  if (!storeData.brand_uid && !storeData.brandId) {
    throw new Error('Brand UID is required but not found in store data')
  }

  if (!storeData.company_uid && !storeData.companyId) {
    throw new Error('Company UID is required but not found in store data')
  }

  const payload: UseBillTemplateRequest = {
    id: storeData.id,
    store_id: storeData.store_id || storeData.code || `STORE-${storeData.id}`,
    fb_store_id: storeData.fb_store_id || 0,
    store_name: storeData.store_name || storeData.name || 'Unknown Store',
    is_delivery_direct: storeData.is_delivery_direct || 0,
    latitude: storeData.latitude || 0,
    longitude: storeData.longitude || 0,
    email: storeData.email || '',
    phone: storeData.phone || '',
    logo: storeData.logo || '',
    background: storeData.background || '',
    facebook: storeData.facebook || '',
    website: storeData.website || '',
    address: storeData.address || '',
    description: storeData.description || '',
    workstation_id: storeData.workstation_id || 0,
    active: storeData.active || (storeData.isActive ? 1 : 0) || 1,
    is_default: storeData.is_default || 0,
    is_test: storeData.is_test || 0,
    store_address: storeData.store_address || {},
    extra_data: {
      ...(storeData.extra_data || storeData.extraData || {}),
      bill_template: templateNumber
    },
    revision: storeData.revision || 0,
    expiry_date: storeData.expiry_date || Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60,
    sort: storeData.sort || 0,
    last_synced_transaction: storeData.last_synced_transaction || null,
    city_uid: storeData.city_uid || storeData.cityId || '',
    district: storeData.district || null,
    pos_server_group_uid: storeData.pos_server_group_uid || null,
    brand_uid: storeData.brand_uid || storeData.brandId || '',
    company_uid: storeData.company_uid || storeData.companyId || '',
    is_ahamove_active: storeData.is_ahamove_active || 0,
    delivery_services: storeData.delivery_services || '',
    email_delivery_service: storeData.email_delivery_service || '',
    phone_manager: storeData.phone_manager || '',
    is_fabi: storeData.is_fabi || 1,
    partner: storeData.partner || '',
    pos_type: storeData.pos_type || '',
    operation_form: storeData.operation_form || '',
    size: storeData.size || 0,
    currency: storeData.currency || 'VND',
    ward: storeData.ward || null,
    created_by: storeData.created_by,
    updated_by: storeData.updated_by,
    deleted_by: storeData.deleted_by || null,
    created_at: storeData.created_at,
    updated_at: Math.floor(Date.now() / 1000),
    deleted_at: storeData.deleted_at || null,
    deleted: storeData.deleted || false,
    brand: storeData.brand || {},
    company: storeData.company || {},
    city: storeData.city || {}
  }

  return payload
}

/**
 * Get template number from active tab
 */
export const getTemplateNumberFromTab = (activeTab: string): number => {
  switch (activeTab) {
    case 'template-1':
      return 1
    case 'template-2':
      return 2
    case 'template-3':
      return 3
    case 'template-4':
      return 4
    case 'custom-template':
      return 5
    default:
      return 1
  }
}
