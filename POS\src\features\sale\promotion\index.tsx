import { useState } from 'react'

import { usePromotionsData, useStoresData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { columns, PromotionsButtons, PromotionTableSkeleton, PromotionsDialogs, PromotionDataTable } from './components'
import PromotionsProvider, { usePromotions } from './context'

function PromotionPageContent() {
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('all')
  const { data: storesData } = useStoresData()
  const { promotions, isLoading, error, refetch } = usePromotionsData({
    storeUid: selectedStoreUid === 'all' ? undefined : selectedStoreUid
  })
  const { open } = usePromotions()

  const showDataTable = !open || (open !== 'create' && open !== 'update')

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu khuyến mãi</p>
          <button onClick={() => refetch()} className='text-primary text-sm hover:underline'>
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {showDataTable && (
        <>
          <Header>
            <div className='ml-auto flex items-center space-x-4'>
              <Search />
              <ThemeSwitch />
              <ProfileDropdown />
            </div>
          </Header>

          <Main>
            <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
              <div>
                <h2 className='text-2xl font-bold tracking-tight'>Chương trình khuyến mãi</h2>
              </div>
              <PromotionsButtons />
            </div>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
              {isLoading && <PromotionTableSkeleton />}
              {!isLoading && (
                <PromotionDataTable
                  columns={columns}
                  data={promotions}
                  storesData={storesData}
                  selectedStoreUid={selectedStoreUid}
                  onStoreChange={setSelectedStoreUid}
                />
              )}
            </div>
          </Main>
        </>
      )}

      <PromotionsDialogs />
    </>
  )
}

export default function PromotionPage() {
  return (
    <PromotionsProvider>
      <PromotionPageContent />
    </PromotionsProvider>
  )
}
