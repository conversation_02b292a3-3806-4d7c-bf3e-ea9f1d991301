export interface CrmService {
  code: string
  productCode: string
  name: string
  desc: string
  type: string
  isPrePaid: number
  unitPrice: number
  unitScope: string
  unitName: string
  minChoice: number
  maxChoice: number
  autoSerial: number
  sort: number
  isOnline: number
  isHasPos: number
  marketPrice: number
  tutorialLink: string
  _id: string
  status: 'ACTIVE' | 'INACTIVE'
  createdBy: string
  createdTime: string
}

export interface CrmServicesResponse {
  count: number
  numPerPage: number
  totalPage: number
  services: CrmService[]
}

export interface CrmServicesParams {
  pos_parent?: string
  page?: number
  limit?: number
  search?: string
}

export interface CrmApiError {
  message: string
  code?: string
  status?: number
}

export interface CrmServiceTableRow extends CrmService {
  formattedPrice: string
  formattedMarketPrice: string
  statusBadge: 'success' | 'secondary'
}

export interface CrmPaginationInfo {
  currentPage: number
  totalPages: number
  totalCount: number
  onPageChange: (page: number) => void
}
