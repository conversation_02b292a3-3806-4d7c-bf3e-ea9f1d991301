import { useState, useMemo, useEffect } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import { extractSourceIds } from '@/utils/applied-sources-utils'

import useSourceData from '@/hooks/use-source-data'

import { PosModal } from '@/components/pos/modal'
import { Input, Checkbox, Button } from '@/components/ui'

interface OrderSourceSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSourcesChange: (sources: string[]) => void
  appliedSources: any[]
}

export function OrderSourceSelectionModal({
  open,
  onOpenChange,
  onSourcesChange,
  appliedSources
}: OrderSourceSelectionModalProps) {
  const { sources: allSources } = useSourceData({ skipLimit: true })
  const [searchTerm, setSearchTerm] = useState('')
  const [tempSelectedSources, setTempSelectedSources] = useState<string[]>([])
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)

  useEffect(() => {
    const appliedSourceIds = extractSourceIds(appliedSources)
    setTempSelectedSources(appliedSourceIds)
  }, [appliedSources, open])

  const filteredAllSources = useMemo(() => {
    if (!allSources) return []
    return allSources.filter(source => source.source_name.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [allSources, searchTerm])

  const selectedSources = useMemo(() => {
    if (!filteredAllSources.length) return []
    return filteredAllSources.filter(source => tempSelectedSources.includes(source.source_id))
  }, [filteredAllSources, tempSelectedSources])

  const unselectedSources = useMemo(() => {
    if (!filteredAllSources.length) return []
    return filteredAllSources.filter(source => !tempSelectedSources.includes(source.source_id))
  }, [filteredAllSources, tempSelectedSources])

  const handleSourceToggle = (sourceId: string) => {
    setTempSelectedSources(prev => (prev.includes(sourceId) ? prev.filter(id => id !== sourceId) : [...prev, sourceId]))
  }

  const selectedCount = selectedSources.length
  const totalFilteredCount = filteredAllSources.length
  const isAllSelected = totalFilteredCount > 0 && selectedCount === totalFilteredCount
  const isIndeterminate = selectedCount > 0 && selectedCount < totalFilteredCount

  const handleSelectAll = () => {
    if (isAllSelected) {
      const allFilteredIds = filteredAllSources.map(s => s.source_id)
      setTempSelectedSources(prev => prev.filter(id => !allFilteredIds.includes(id)))
    } else {
      const allFilteredIds = filteredAllSources.map(s => s.source_id)
      setTempSelectedSources(prev => {
        const newSelection = [...prev]
        allFilteredIds.forEach(id => {
          if (!newSelection.includes(id)) {
            newSelection.push(id)
          }
        })
        return newSelection
      })
    }
  }

  const handleConfirm = () => {
    onSourcesChange(tempSelectedSources)
    onOpenChange(false)
  }

  const handleCancel = () => {
    const appliedSourceIds = extractSourceIds(appliedSources)
    setTempSelectedSources(appliedSourceIds)
    onOpenChange(false)
  }

  return (
    <PosModal
      title='Chọn nguồn đơn'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      cancelText='Hủy'
      confirmText='Xong'
      maxWidth='sm:max-w-md'
    >
      <div className='space-y-4'>
        {/* Search Input */}
        <Input
          placeholder='Tìm kiếm'
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className='w-full'
        />

        {/* Selected Sources Section */}
        {selectedSources.length > 0 && (
          <div className='rounded-lg bg-green-50 p-3'>
            <div className='flex items-center space-x-3'>
              <Checkbox
                id='select-all'
                checked={isAllSelected}
                {...(isIndeterminate && { 'data-indeterminate': 'true' })}
                onCheckedChange={handleSelectAll}
                className='data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600'
              />
              <label htmlFor='select-all' className='cursor-pointer text-sm font-medium text-green-700'>
                Đã chọn {selectedCount}
              </label>
              <Button
                variant='ghost'
                size='sm'
                className='ml-auto h-6 px-2 text-xs'
                onClick={() => setSelectedCollapsed(!selectedCollapsed)}
              >
                {selectedCollapsed ? <ChevronRight className='h-3 w-3' /> : <ChevronDown className='h-3 w-3' />}
              </Button>
            </div>

            {/* Selected Sources List */}
            {!selectedCollapsed && (
              <div className='mt-3 space-y-2'>
                {selectedSources.map((source: any) => (
                  <div key={source.source_id} className='flex items-center space-x-3'>
                    <Checkbox
                      id={`selected-${source.source_id}`}
                      checked={tempSelectedSources.includes(source.source_id)}
                      onCheckedChange={() => handleSourceToggle(source.source_id)}
                    />
                    <label htmlFor={`selected-${source.source_id}`} className='flex-1 cursor-pointer text-sm'>
                      {source.source_name}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Unselected Sources Section */}
        {unselectedSources.length > 0 && (
          <div className='rounded-lg bg-gray-50 p-3'>
            <div className='flex items-center space-x-3'>
              <div className='text-sm font-medium text-gray-700'>Còn lại {unselectedSources.length}</div>
              <Button
                variant='ghost'
                size='sm'
                className='ml-auto h-6 px-2 text-xs'
                onClick={() => setRemainingCollapsed(!remainingCollapsed)}
              >
                {remainingCollapsed ? <ChevronRight className='h-3 w-3' /> : <ChevronDown className='h-3 w-3' />}
              </Button>
            </div>

            {/* Unselected Sources List */}
            {!remainingCollapsed && (
              <div className='mt-3 max-h-80 space-y-2 overflow-y-auto'>
                {unselectedSources.map((source: any) => (
                  <div key={source.source_id} className='flex items-center space-x-3'>
                    <Checkbox
                      id={source.source_id}
                      checked={tempSelectedSources.includes(source.source_id)}
                      onCheckedChange={() => handleSourceToggle(source.source_id)}
                    />
                    <label htmlFor={source.source_id} className='flex-1 cursor-pointer text-sm'>
                      {source.source_name}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </PosModal>
  )
}
