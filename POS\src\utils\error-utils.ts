import { AxiosError } from 'axios'

/**
 * Extracts error message from various error types
 * @param error - The error object to extract message from
 * @param defaultMessage - Default message if no specific error message found
 * @returns Extracted error message string
 */
export function getErrorMessage(
  error: unknown,
  defaultMessage: string = 'Đã xảy ra lỗi không xác đ<PERSON>nh'
): string {
  // Handle AxiosError specifically
  if (error instanceof AxiosError) {
    // Check for nested error message structure
    if (error.response?.data?.error?.message) {
      return error.response.data.error.message
    }
    
    // Check for direct message in response data
    if (error.response?.data?.message) {
      return error.response.data.message
    }
    
    // Check for title in response data (common pattern)
    if (error.response?.data?.title) {
      return error.response.data.title
    }
    
    // Fallback to axios error message
    if (error.message) {
      return error.message
    }
  }
  
  // Handle generic error objects with message property
  if (error && typeof error === 'object' && 'message' in error) {
    const errorMessage = (error as { message: string }).message
    if (typeof errorMessage === 'string') {
      return errorMessage
    }
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error
  }
  
  return defaultMessage
}

/**
 * Handles server errors by extracting message and showing toast notification
 * @param error - The error object to handle
 * @param defaultMessage - Default message if no specific error message found
 */
export function handleServerErrorWithToast(
  error: unknown,
  defaultMessage: string = 'Đã xảy ra lỗi không xác định'
): void {
  const errorMessage = getErrorMessage(error, defaultMessage)
  
  // Import toast dynamically to avoid circular dependencies
  import('sonner').then(({ toast }) => {
    toast.error(errorMessage)
  })
}