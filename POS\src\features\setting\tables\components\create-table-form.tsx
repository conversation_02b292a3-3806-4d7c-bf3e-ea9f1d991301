import { useState, useMemo, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X, Plus, Search, Minus, ChevronDown, ChevronRight } from 'lucide-react'

import { useCurrentCompany, useCurrentBrand } from '@/stores/posStore'

import { useAreasData } from '@/hooks/api/use-areas'
import { useSalesChannels } from '@/hooks/api/use-sales-channels'
import { useCreateTable, useUpdateTable, useTableDetail, useToggleTableStatus } from '@/hooks/api/use-tables'

import {
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogTrigger,
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui'

import { useItemsInStoreForTable } from '@/features/menu/items/items-in-store/hooks'

import { CopyItemsToTablesModal } from './copy-items-to-tables-modal'

interface Store {
  id: string
  store_name: string
  active: number
}

interface CreateTableFormProps {
  areaId?: string
  storeUid?: string
  fromTableLayout?: boolean
}

const FONT_SIZE_OPTIONS = [
  { label: '12px', value: '12' },
  { label: '14px', value: '14' },
  { label: '15px (Mặc định)', value: '15' },
  { label: '16px', value: '16' },
  { label: '18px', value: '18' },
  { label: '20px', value: '20' },
  { label: '24px', value: '24' }
] as const

export function CreateTableForm({ areaId, storeUid: initialStoreUid }: CreateTableFormProps = {}) {
  const navigate = useNavigate()
  const { createTable, isCreating } = useCreateTable()
  const { updateTable, isUpdating } = useUpdateTable()
  const { toggleTableStatus, isToggling } = useToggleTableStatus()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false)
  const [itemSearchTerm, setItemSearchTerm] = useState('')

  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  const isEditMode = !!areaId && !!initialStoreUid

  const { data: areaData, isLoading: isLoadingTable } = useTableDetail(areaId || '', initialStoreUid || '')

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData) ? storesData.filter((store: Store) => store.active === 1) : []
      }
      return []
    } catch (error) {
      return []
    }
  }, [])

  const [formData, setFormData] = useState({
    areaName: '',
    storeUid: initialStoreUid || stores[0]?.id || '',
    areaUid: '',
    description: '',
    sort: '',
    sourceId: 'none',
    color: '',
    fontSize: '15',
    active: 1,
    selectedItems: [] as Array<{
      item_id: string
      item_name: string
      quantity: number
    }>
  })

  const { data: areas = [] } = useAreasData({
    storeUid: formData.storeUid,
    page: 1,
    results_per_page: 15000
  })

  const { data: salesChannelsData = [] } = useSalesChannels({
    skipLimit: true,
    page: 1,
    results_per_page: 15000,
    companyUid: company?.id || '',
    brandUid: selectedBrand?.id || '',
    storeUid: formData.storeUid || ''
  })
  const orderSources = salesChannelsData || []

  const { data: items = [] } = useItemsInStoreForTable({
    params: {
      store_uid: formData.storeUid,
      skip_limit: true
    },
    enabled: !!formData.storeUid
  })

  useEffect(() => {
    if (areaData && isEditMode && stores.length > 0) {
      const matchingStore = stores.find(store => store.id === areaData.store_uid)

      const newFormData = {
        areaName: areaData.table_name || '',
        storeUid: matchingStore ? matchingStore.id : stores[0]?.id || '',
        areaUid: areaData.area_uid || '',
        description: areaData.description || '',
        sort: areaData.sort?.toString() || '',
        sourceId: areaData.source_id || 'none',
        color: (areaData.extra_data as any)?.color || '',
        fontSize: (areaData.extra_data as any)?.font_size || '15',
        active: areaData.active ?? 1,
        selectedItems: (areaData.extra_data?.order_list || []).map((item: any) => ({
          item_id: item.item_id,
          item_name: item.item_name || item.item_id,
          quantity: item.quantity || 0
        }))
      }
      setFormData(newFormData)
    }
  }, [areaData, isEditMode, stores])

  const [hasInitializedItems, setHasInitializedItems] = useState(false)

  useEffect(() => {
    if (isEditMode && areaData && items.length > 0 && !hasInitializedItems) {
      const orderList = areaData.extra_data?.order_list || []
      if (orderList.length > 0) {
        const updatedSelectedItems = orderList.map((item: any) => {
          const foundItem = items.find(menuItem => (menuItem as any).code === item.item_id)
          return {
            item_id: item.item_id,
            item_name: (foundItem as any)?.name || item.item_id,
            quantity: item.quantity || 0
          }
        })

        setFormData(prev => ({
          ...prev,
          selectedItems: updatedSelectedItems
        }))
        setHasInitializedItems(true)
      }
    }
  }, [items, isEditMode, areaData?.extra_data?.order_list, hasInitializedItems])

  useEffect(() => {
    setHasInitializedItems(false)
  }, [areaData?.id])

  const handleBack = () => {
    navigate({ to: '/setting/table' })
  }

  const filteredItems = useMemo(() => {
    if (!itemSearchTerm) return items
    return items.filter(
      item =>
        (item as any).name?.toLowerCase().includes(itemSearchTerm.toLowerCase()) ||
        (item as any).code?.toLowerCase().includes(itemSearchTerm.toLowerCase())
    )
  }, [items, itemSearchTerm])

  const selectedItems = useMemo(() => {
    return filteredItems.filter(item =>
      formData.selectedItems.some(selected => selected.item_id === (item as any).code)
    )
  }, [filteredItems, formData.selectedItems])

  const remainingItems = useMemo(() => {
    return filteredItems.filter(
      item => !formData.selectedItems.some(selected => selected.item_id === (item as any).code)
    )
  }, [filteredItems, formData.selectedItems])

  const handleItemToggle = (item: any) => {
    const itemId = (item as any).code
    const itemName = (item as any).name

    if (!itemId) {
      return
    }

    const isSelected = formData.selectedItems.some(selected => selected.item_id === itemId)

    if (isSelected) {
      setFormData(prev => ({
        ...prev,
        selectedItems: prev.selectedItems.filter(selected => selected.item_id !== itemId)
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        selectedItems: [
          ...prev.selectedItems,
          {
            item_id: itemId,
            item_name: itemName,
            quantity: 1
          }
        ]
      }))
    }
  }

  const handleQuantityChange = (itemId: string, quantity: number) => {
    setFormData(prev => ({
      ...prev,
      selectedItems: prev.selectedItems.map(item => (item.item_id === itemId ? { ...item, quantity } : item))
    }))
  }

  const handleRemoveItem = (itemId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedItems: prev.selectedItems.filter(item => item.item_id !== itemId)
    }))
  }

  const handleSave = async () => {
    if (!isFormValid) return

    if (isEditMode && areaData) {
      const updatedTableData = {
        ...areaData,
        table_name: formData.areaName,
        description: formData.description || undefined,
        sort: formData.sort ? parseInt(formData.sort) : areaData.sort,
        store_uid: formData.storeUid,
        area_uid: formData.areaUid,
        source_id: formData.sourceId === 'none' ? '' : formData.sourceId,
        active: formData.active,
        extra_data: {
          ...areaData.extra_data,
          color: formData.color,
          font_size: formData.fontSize,
          order_list: formData.selectedItems.map(item => ({
            item_id: item.item_id,
            quantity: item.quantity
          }))
        }
      }

      updateTable(updatedTableData, {
        onSuccess: () => {
          navigate({ to: '/setting/table' })
        }
      })
    } else {
      const newTableData = {
        table_name: formData.areaName,
        description: formData.description || undefined,
        store_uid: formData.storeUid,
        area_uid: formData.areaUid || areas[0]?.id || '',
        sort: formData.sort ? parseInt(formData.sort) : 1,
        sourceId: formData.sourceId === 'none' ? '' : formData.sourceId,
        color: formData.color,
        fontSize: formData.fontSize,
        selectedItems: formData.selectedItems
      }

      createTable(newTableData, {
        onSuccess: () => {
          navigate({ to: '/setting/table' })
        }
      })
    }
  }

  const handleToggleActive = async () => {
    if (!isEditMode || !areaData) return

    // Use the standardized toggle hook
    toggleTableStatus(areaData)

    // Update local form state to reflect the change
    const newActiveStatus = formData.active === 1 ? 0 : 1
    setFormData(prev => ({ ...prev, active: newActiveStatus }))
  }

  const isFormValid =
    formData.areaName.trim() !== '' && formData.storeUid !== '' && (isEditMode || formData.areaUid !== '')
  const isLoading = isCreating || isUpdating || isLoadingTable || isToggling

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <div className='text-center'>
            <h1 className='mb-2 text-3xl font-medium'>{isEditMode ? 'Chi tiết bàn' : 'Tạo bàn'}</h1>
          </div>
          <div className='flex gap-2'>
            {isEditMode && (
              <Button
                type='button'
                variant={formData.active === 1 ? 'destructive' : 'default'}
                disabled={isToggling}
                className='min-w-[100px]'
                onClick={handleToggleActive}
              >
                {isToggling ? 'Đang cập nhật...' : formData.active === 1 ? 'Deactive' : 'Active'}
              </Button>
            )}
            <Button type='button' disabled={isLoading || !isFormValid} className='min-w-[100px]' onClick={handleSave}>
              {isLoading ? (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') : isEditMode ? 'Lưu' : 'Lưu'}
            </Button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        {isEditMode && isLoadingTable ? (
          <div className='p-6 text-center'>
            <p className='text-muted-foreground'>Đang tải dữ liệu bàn...</p>
          </div>
        ) : (
          <div className='p-6'>
            <div className='space-y-6'>
              {/* Tên bàn */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='area-name' className='min-w-[200px] text-sm font-medium'>
                  Tên bàn *
                </Label>
                <Input
                  id='area-name'
                  value={formData.areaName}
                  onChange={e => setFormData({ ...formData, areaName: e.target.value })}
                  placeholder='Nhập tên bàn'
                  className='flex-1'
                />
              </div>

              {/* Cửa hàng */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Cửa hàng *</Label>
                <Select
                  value={formData.storeUid}
                  onValueChange={value => setFormData({ ...formData, storeUid: value })}
                  disabled={isEditMode}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        <span className='text-blue-500'>{store.store_name}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Khu vực */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Khu vực *</Label>
                <Select value={formData.areaUid} onValueChange={value => setFormData({ ...formData, areaUid: value })}>
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn khu vực' />
                  </SelectTrigger>
                  <SelectContent>
                    {areas.map(area => (
                      <SelectItem key={area.id} value={area.id}>
                        <span className='text-blue-500'>{area.area_name}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Nguồn đơn */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Nguồn đơn</Label>
                <Select
                  value={formData.sourceId}
                  onValueChange={value => setFormData({ ...formData, sourceId: value })}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn nguồn đơn' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='none'>
                      <span className='text-blue-500'>None</span>
                    </SelectItem>
                    {orderSources.map(source => (
                      <SelectItem key={source.id} value={source.sourceId}>
                        <span className='text-blue-500'>{source.sourceName}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Thêm món */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Món đặt trước</Label>
                <div className='flex flex-1 gap-2'>
                  <Dialog open={isItemDialogOpen} onOpenChange={setIsItemDialogOpen}>
                    <DialogTrigger asChild>
                      <Button type='button' variant='outline' size='sm'>
                        <Plus className='mr-2 h-4 w-4' />
                        Thêm món
                      </Button>
                    </DialogTrigger>
                    <DialogContent className='flex max-h-[80vh] max-w-2xl flex-col overflow-hidden'>
                      {/* Search */}
                      <div className='relative'>
                        <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                        <Input
                          placeholder='Tìm kiếm món ăn...'
                          value={itemSearchTerm}
                          onChange={e => setItemSearchTerm(e.target.value)}
                          className='pl-10'
                        />
                      </div>

                      {/* Selected items count */}
                      {formData.selectedItems.length > 0 && (
                        <div className='mb-4 rounded-lg border border-green-200 bg-green-50 p-3'>
                          <div className='text-sm font-medium text-green-700'>
                            ✓ Đã chọn {formData.selectedItems.length} món
                          </div>
                        </div>
                      )}

                      {/* Items list with Collapsible */}
                      <div className='max-h-96 flex-1 space-y-4 overflow-y-auto'>
                        {/* Selected items section */}
                        <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                            <span className='font-medium'>Đã chọn ({selectedItems.length})</span>
                            {selectedSectionOpen ? (
                              <ChevronDown className='h-4 w-4' />
                            ) : (
                              <ChevronRight className='h-4 w-4' />
                            )}
                          </CollapsibleTrigger>
                          <CollapsibleContent className='mt-2'>
                            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                              {selectedItems.length === 0 ? (
                                <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                              ) : (
                                selectedItems.map(item => {
                                  const itemId = (item as any).code
                                  const isSelected = formData.selectedItems.some(
                                    selected => selected.item_id === itemId
                                  )
                                  return (
                                    <div
                                      key={item.id}
                                      className='flex items-center space-x-3 rounded-lg border p-3 hover:bg-gray-50'
                                    >
                                      <Checkbox checked={isSelected} onCheckedChange={() => handleItemToggle(item)} />
                                      <div className='flex-1'>
                                        <div className='font-medium'>{(item as any).name}</div>
                                        <div className='text-sm text-gray-500'>{itemId}</div>
                                      </div>
                                    </div>
                                  )
                                })
                              )}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>

                        {/* Remaining items section */}
                        <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                            <span className='font-medium'>Còn lại ({remainingItems.length})</span>
                            {remainingSectionOpen ? (
                              <ChevronDown className='h-4 w-4' />
                            ) : (
                              <ChevronRight className='h-4 w-4' />
                            )}
                          </CollapsibleTrigger>
                          <CollapsibleContent className='mt-2'>
                            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                              {remainingItems.length === 0 ? (
                                <p className='text-sm text-gray-500'>Không có món nào</p>
                              ) : (
                                remainingItems.map(item => {
                                  const itemId = (item as any).code
                                  const isSelected = formData.selectedItems.some(
                                    selected => selected.item_id === itemId
                                  )
                                  return (
                                    <div
                                      key={item.id}
                                      className='flex items-center space-x-3 rounded-lg border p-3 hover:bg-gray-50'
                                    >
                                      <Checkbox checked={isSelected} onCheckedChange={() => handleItemToggle(item)} />
                                      <div className='flex-1'>
                                        <div className='font-medium'>{(item as any).name}</div>
                                        <div className='text-sm text-gray-500'>{itemId}</div>
                                      </div>
                                    </div>
                                  )
                                })
                              )}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      </div>

                      {/* Dialog actions */}
                      <div className='flex justify-end gap-2 border-t pt-4'>
                        <Button type='button' variant='outline' onClick={() => setIsItemDialogOpen(false)}>
                          Hủy
                        </Button>
                        <Button type='button' onClick={() => setIsItemDialogOpen(false)}>
                          Xong
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Copy to tables button */}
                  <CopyItemsToTablesModal selectedItems={formData.selectedItems} storeUid={formData.storeUid} />
                </div>
              </div>

              {/* Selected items display */}
              {formData.selectedItems.length > 0 && (
                <div className='flex items-start gap-4'>
                  <Label className='min-w-[200px] pt-2 text-sm font-medium'></Label>
                  <div className='flex-1'>
                    {/* Summary header */}
                    <div className='mb-3 rounded-lg border border-blue-200 bg-blue-50 p-3'>
                      <div className='text-sm font-medium text-blue-700'>
                        Tổng số món: {formData.selectedItems.length}
                      </div>
                    </div>

                    {/* Items list */}
                    <div className='max-h-40 space-y-2 overflow-y-auto rounded-lg border p-3'>
                      {formData.selectedItems.map(item => (
                        <div
                          key={item.item_id}
                          className='flex items-center justify-between gap-2 rounded bg-gray-50 p-2'
                        >
                          <div className='flex-1'>
                            <div className='text-sm font-medium'>{item.item_name}</div>
                            <div className='text-xs text-gray-500'>{item.item_id}</div>
                          </div>
                          <div className='flex items-center gap-2'>
                            <Button
                              type='button'
                              variant='outline'
                              size='sm'
                              className='h-8 w-8 p-0'
                              onClick={() => {
                                if (item.quantity > 1) {
                                  handleQuantityChange(item.item_id, item.quantity - 1)
                                } else {
                                  handleRemoveItem(item.item_id)
                                }
                              }}
                            >
                              <Minus className='h-4 w-4' />
                            </Button>
                            <div className='w-12 text-center text-sm font-medium'>{item.quantity}</div>
                            <Button
                              type='button'
                              variant='outline'
                              size='sm'
                              className='h-8 w-8 p-0'
                              onClick={() => handleQuantityChange(item.item_id, item.quantity + 1)}
                            >
                              <Plus className='h-4 w-4' />
                            </Button>
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              onClick={() => handleRemoveItem(item.item_id)}
                              className='ml-2 h-8 w-8 p-0 text-red-500 hover:text-red-700'
                            >
                              <X className='h-4 w-4' />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Mô tả */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='area-description' className='min-w-[200px] text-sm font-medium'>
                  Mô tả
                </Label>
                <Input
                  id='area-description'
                  value={formData.description}
                  onChange={e => setFormData({ ...formData, description: e.target.value })}
                  placeholder='Mô tả'
                  className='flex-1'
                />
              </div>

              {/* Thứ tự hiển thị trong thiết bị bán hàng */}
              <div className='space-y-4'>
                <h2 className='text-lg font-medium text-gray-900'>Thứ tự hiển thị trong thiết bị bán hàng</h2>

                <div className='space-y-2'>
                  <div className='text-sm text-gray-600'>
                    Khu vực có số nhỏ hơn sẽ được sắp xếp lên trên trong thiết bị bán hàng
                  </div>
                  <div className='flex items-center gap-4'>
                    <Label htmlFor='area-sort' className='min-w-[200px] text-sm font-medium'>
                      Thứ tự hiển thị
                    </Label>
                    <Input
                      id='area-sort'
                      type='number'
                      value={formData.sort}
                      onChange={e => setFormData({ ...formData, sort: e.target.value })}
                      placeholder='Nhập số thứ tự hiển thị'
                      className='flex-1'
                    />
                  </div>
                </div>
              </div>

              {/* Màu chữ */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Màu chữ</Label>
                <div className='flex-1'>
                  <input
                    type='color'
                    value={formData.color || '#545454'}
                    onChange={e => setFormData({ ...formData, color: e.target.value })}
                    className='h-8 w-12 cursor-pointer rounded border border-gray-300'
                  />
                </div>
              </div>

              {/* Kích thước chữ */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Kích thước chữ (pixels)</Label>
                <Select
                  value={formData.fontSize}
                  onValueChange={value => setFormData({ ...formData, fontSize: value })}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn kích thước chữ' />
                  </SelectTrigger>
                  <SelectContent>
                    {FONT_SIZE_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
