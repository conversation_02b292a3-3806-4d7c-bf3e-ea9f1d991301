import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { useVietQRBanks, useGenerateVietQR } from '@/hooks/api'

import { Combobox } from '@/components/pos'
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Input, Button } from '@/components/ui'

import { QR_SERVICE_PRINT_OPTIONS, type StoreFormValues } from '../../../../data'

interface VietQrSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function VietQrSection({ form, isLoading = false }: VietQrSectionProps) {
  const [qrImageUrl, setQrImageUrl] = useState<string | null>(null)

  const { data: banks = [], isLoading: banksLoading } = useVietQRBanks()

  const generateQRMutation = useGenerateVietQR()

  const bankId = form.watch('bank_id')
  const bankAcc = form.watch('bank_acc')
  const bankAccName = form.watch('bank_acc_name')

  const bankOptions = banks.map(bank => ({
    value: bank.id.toString(),
    label: `${bank.name} (${bank.shortName})`
  }))

  const currentBank = bankId && banks.length > 0 ? banks.find(bank => bank.id.toString() === bankId) || null : null

  const qrServiceOptions = QR_SERVICE_PRINT_OPTIONS.map(option => ({
    value: option.value,
    label: option.label
  }))

  const canGenerateQR = !!(currentBank && bankAcc?.trim() && bankAccName?.trim())

  const handleGenerateQr = async () => {
    if (!canGenerateQR || !currentBank || !bankAcc || !bankAccName) return

    try {
      const response = await generateQRMutation.mutateAsync({
        bank_id: currentBank.id.toString(),
        bank_acc: bankAcc.trim(),
        bank_acc_name: bankAccName.trim()
      })

      if (response.data?.imageURL) {
        setQrImageUrl(response.data.imageURL)
      }
    } catch (_error) {
      // Error is already handled by the mutation's onError callback
    }
  }

  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Cấu hình VietQr</h2>

      <div className='flex gap-6'>
        {/* 9/10 - Form inputs section (Left column) */}
        <div className='flex-[9] space-y-4'>
          {/* Danh sách ngân hàng */}
          <FormField
            control={form.control}
            name='bank_id'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <div className='flex w-[240px] items-center gap-2'>
                    <FormLabel className='font-medium'>Danh sách ngân hàng</FormLabel>
                  </div>
                  <FormControl>
                    <Combobox
                      options={bankOptions}
                      value={field.value?.toString() || ''}
                      onValueChange={field.onChange}
                      placeholder='Chọn ngân hàng'
                      searchPlaceholder='Tìm kiếm ngân hàng...'
                      emptyText='Không tìm thấy ngân hàng'
                      disabled={isLoading || banksLoading}
                      className='flex-1'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Số tài khoản */}
          <FormField
            control={form.control}
            name='bank_acc'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <div className='flex w-[240px] items-center gap-2'>
                    <FormLabel className='font-medium'>Số tài khoản</FormLabel>
                  </div>
                  <FormControl>
                    <Input placeholder='Nhập số tài khoản' disabled={isLoading} className='flex-1' {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Tên tài khoản */}
          <FormField
            control={form.control}
            name='bank_acc_name'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <div className='flex w-[240px] items-center gap-2'>
                    <FormLabel className='font-medium'>Tên tài khoản</FormLabel>
                  </div>
                  <FormControl>
                    <Input placeholder='Nhập tên tài khoản' disabled={isLoading} className='flex-1' {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* In mã Qrcode phục vụ thanh toán */}
          <FormField
            control={form.control}
            name='print_qrcode_pay'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <div className='flex w-[240px] items-center gap-2'>
                    <FormLabel className='font-medium'>In mã Qrcode phục vụ thanh toán</FormLabel>
                  </div>
                  <FormControl>
                    <Combobox
                      options={qrServiceOptions}
                      value={field.value?.toString() || '0'}
                      onValueChange={field.onChange}
                      placeholder='Chọn cấu hình in QR'
                      searchPlaceholder='Tìm kiếm cấu hình...'
                      emptyText='Không tìm thấy cấu hình'
                      disabled={isLoading}
                      className='flex-1'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* 1/10 - Button/QR section (Right column) */}
        <div className='flex items-start justify-center'>
          {!qrImageUrl ? (
            <Button
              type='button'
              variant='outline'
              onClick={handleGenerateQr}
              disabled={isLoading || !canGenerateQR || generateQRMutation.isPending}
              className='border-blue-600 text-blue-600 hover:bg-blue-50'
            >
              {generateQRMutation.isPending ? 'Đang tạo...' : 'Lấy mã Qr'}
            </Button>
          ) : (
            <div className='rounded-lg border p-2 shadow-sm'>
              <img
                src={qrImageUrl}
                alt='VietQR Code'
                className='mx-auto h-36 object-contain'
                onError={() => setQrImageUrl(null)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
