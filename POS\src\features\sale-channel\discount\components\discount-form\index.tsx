import {
  HeaderForm,
  DiscountInformationSection,
  CustomizationSection,
  DateApplicationSection,
  MarketingTimeframeSection
} from './components'
import { useDiscountForm } from './hooks'
import { DiscountFormProvider } from './stores'

interface DiscountFormProps {
  discountId?: string
  storeUid?: string
}

export function DiscountForm({ discountId, storeUid: initialStoreUid }: DiscountFormProps = {}) {
  const formState = useDiscountForm({
    discountId,
    initialStoreUid
  })

  return (
    <DiscountFormProvider value={formState}>
      <div className='container mx-auto px-4 py-8'>
        <HeaderForm />

        <div className='mx-auto max-w-4xl'>
          <div className='p-6'>
            <div className='space-y-6'>
              <DiscountInformationSection />
              <CustomizationSection />
              <DateApplicationSection />
              <MarketingTimeframeSection />
            </div>
          </div>
        </div>
      </div>
    </DiscountFormProvider>
  )
}
