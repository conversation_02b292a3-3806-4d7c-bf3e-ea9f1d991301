import { useState } from 'react'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  PrinterPositionInStoreDialogs,
  PrinterPositionInStorePagination,
  PrinterPositionInStoreTableSkeleton,
  PrinterPositionInStoreTableWrapper
} from './components'
import PrinterPositionInStoreProvider, { usePrinterPositionInStore } from './context'
import { usePrinterPositionsInStoreForTable } from './hooks/use-printer-positions-in-store-for-table'

function PrinterPositionInStoreContent() {
  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()
  const { open } = usePrinterPositionInStore()
  const [filterStoreId, setFilterStoreId] = useState<string | null>(null)
  const [scopeType, setScopeType] = useState<string>('0')
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [pageSize] = useState<number>(10)

  const storeUidForQuery = filterStoreId || ''

  const apiParams = {
    company_uid: companyUid || '',
    brand_uid: selectedBrand?.id || '',
    ...(storeUidForQuery && { store_uid: storeUidForQuery }),
    ...(scopeType === '1' && { apply_with_store: 1 }),
    page: currentPage,
    limit: pageSize
  }

  const shouldFetchData = !!filterStoreId || scopeType === '1'

  const {
    data: printerPositionsData,
    hasNextPage,
    isLoading,
    error,
    refetch
  } = usePrinterPositionsInStoreForTable({
    params: apiParams,
    enabled: shouldFetchData
  })

  const finalData = shouldFetchData ? printerPositionsData || [] : []

  const handleScopeTypeChange = (newScopeType: string) => {
    setScopeType(newScopeType)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleStoreFilterChangeWithReset = (storeId: string | null) => {
    setFilterStoreId(storeId)
    setCurrentPage(1)
  }

  const showDataTable = !open || (open !== 'create' && open !== 'update')

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu vị trí máy in</p>
          <button onClick={() => refetch()} className='text-primary text-sm hover:underline'>
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {showDataTable && (
        <>
          <Header>
            <div className='ml-auto flex items-center space-x-4'>
              <Search />
              <ThemeSwitch />
              <ProfileDropdown />
            </div>
          </Header>

          <Main>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
              {isLoading ? (
                <PrinterPositionInStoreTableSkeleton />
              ) : (
                <>
                  <PrinterPositionInStoreTableWrapper
                    columns={columns}
                    data={finalData}
                    onStoreFilterChange={handleStoreFilterChangeWithReset}
                    onScopeTypeChange={handleScopeTypeChange}
                    hasStoreSelected={!!filterStoreId}
                    currentScopeType={scopeType}
                    currentStoreId={filterStoreId}
                  />
                  {finalData.length > 0 && (
                    <PrinterPositionInStorePagination
                      currentPage={currentPage}
                      onPageChange={handlePageChange}
                      hasNextPage={hasNextPage}
                    />
                  )}
                </>
              )}
            </div>
          </Main>
        </>
      )}

      <PrinterPositionInStoreDialogs />
    </>
  )
}

export default function PrinterPositionInStorePage() {
  return (
    <PrinterPositionInStoreProvider>
      <PrinterPositionInStoreContent />
    </PrinterPositionInStoreProvider>
  )
}
