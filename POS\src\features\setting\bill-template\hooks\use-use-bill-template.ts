import { useMutation, useQueryClient } from '@tanstack/react-query'

import { billTemplateApi } from '@/lib/bill-template-api'
import type { UseBillTemplateRequest, UseBillTemplateResponse } from '@/lib/bill-template-api'

interface UseUseBillTemplateOptions {
  onSuccess?: (data: UseBillTemplateResponse) => void
  onError?: (error: Error) => void
}

export const useUseBillTemplate = (options?: UseUseBillTemplateOptions) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: UseBillTemplateRequest) => {
      const result = await billTemplateApi.useBillTemplate(payload)
      return result
    },
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: ['stores']
      })

      queryClient.invalidateQueries({
        queryKey: ['store']
      })

      options?.onSuccess?.(data)
    },
    onError: (error: Error) => {
      options?.onError?.(error)
    }
  })
}
