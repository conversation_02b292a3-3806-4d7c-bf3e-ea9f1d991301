import { format } from 'date-fns'

import type { SystemLogApiResponse } from '@/features/crm/system-log/data'

import { crmApi } from './crm-api'

export interface SystemLogApiParams {
  start_time: string // Format: YYYY-MM-DD HH:mm:ss
  end_time: string // Format: YYYY-MM-DD HH:mm:ss
  pos_parent: string
  page?: number
  limit?: number
}

/**
 * System Log API Service
 */
export const systemLogApi = {
  /**
   * Get system logs with date range and filters
   */
  getSystemLogs: async (params: SystemLogApiParams): Promise<SystemLogApiResponse> => {
    try {
      const queryParams = new URLSearchParams({
        start_time: params.start_time,
        end_time: params.end_time,
        pos_parent: params.pos_parent
      })

      if (params.page) {
        queryParams.set('page', params.page.toString())
      }

      if (params.limit) {
        queryParams.set('limit', params.limit.toString())
      }

      const response = await crmApi.get(`/settings/system-log?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from system log API')
      }

      return response.data as SystemLogApiResponse
    } catch (error) {
      console.error('Error fetching system logs:', error)
      throw error
    }
  }
}

/**
 * Helper function to format start date for API
 */
export const formatStartDateForApi = (date: Date): string => {
  return format(date, 'yyyy-MM-dd 00:00:00')
}

/**
 * Helper function to format end date for API (include full day)
 */
export const formatEndDateForApi = (date: Date): string => {
  return format(date, 'yyyy-MM-dd 23:59:59')
}

/**
 * Helper function to format date for API (backward compatibility)
 */
export const formatDateForApi = (date: Date): string => {
  return format(date, 'yyyy-MM-dd HH:mm:ss')
}
