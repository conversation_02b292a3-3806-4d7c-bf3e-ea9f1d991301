import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { useSourceData } from '@/hooks/use-source-data'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Button } from '@/components/ui'

import { type StoreFormValues } from '../../../../data'
import { OrderSourceSelectionModal } from './order-source-selection-modal'

interface OrderSourceSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  storeUid?: string
}

export function OrderSourceSection({ form, isLoading = false, storeUid }: OrderSourceSectionProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { sources: appliedSources } = useSourceData({ storeUid, skipLimit: true })

  const appliedSourcesCount = appliedSources?.length || 0

  const handleSourcesChange = (sources: string[]) => {
    form.setValue('source_ids_selected', sources)
  }

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-6 text-xl font-semibold'>Áp dụng nguồn đơn tại cửa hàng</h2>
        <p className='mb-4 text-sm text-gray-600'>Mặc định áp dụng nguồn đơn Tại chỗ và Mang về</p>
      </div>

      <FormField
        control={form.control}
        name='sources_print'
        render={({ field: _field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-[200px]'>Áp dụng nguồn đơn</FormLabel>
              <FormControl>
                <Button
                  type='button'
                  variant='outline'
                  disabled={isLoading}
                  onClick={handleOpenModal}
                  className='border-blue-600 text-blue-600 hover:bg-blue-50'
                >
                  {appliedSourcesCount} nguồn được áp dụng
                </Button>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Order Source Selection Modal */}
      <OrderSourceSelectionModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSourcesChange={handleSourcesChange}
        appliedSources={appliedSources}
      />
    </div>
  )
}
