import { useState } from 'react'

import { CityData } from '@/types'
import { ChevronDown, ChevronUp } from 'lucide-react'

import type { Item } from '@/lib/item-api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface ItemSelectionSectionProps {
  items: Item[]
  isLoading?: boolean
  cities?: CityData[]
  stores?: Array<{ id: string; store_name?: string; name?: string }>
  mode: 'city' | 'store'
  onChangeMode: (mode: 'city' | 'store') => void
  selectedCityUid?: string
  onChangeCity?: (cityUid: string) => void
  selectedStoreUid?: string
  onChangeStore?: (storeUid: string) => void
  selectedItems?: string[]
  onItemsChange?: (items: string[]) => void
}

export function ItemSelectionSection({
  items,
  isLoading = false,
  cities = [],
  stores = [],
  mode,
  onChangeMode,
  selectedCityUid,
  onChangeCity,
  selectedStoreUid,
  onChangeStore,
  selectedItems = [],
  onItemsChange
}: ItemSelectionSectionProps) {
  const [isSelectedCollapsed, setIsSelectedCollapsed] = useState(false)

  const selectedItemsData = items.filter(item => selectedItems.includes(item.id))
  const unselectedItemsData = items.filter(item => !selectedItems.includes(item.id))

  const handleToggleItem = (itemId: string, checked: boolean) => {
    if (!onItemsChange) return

    if (checked) {
      onItemsChange([...selectedItems, itemId])
    } else {
      onItemsChange(selectedItems.filter(id => id !== itemId))
    }
  }

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <div className='text-center'>
          <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900'></div>
          <p className='mt-2 text-gray-600'>Đang tải dữ liệu món ăn...</p>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <div className='flex items-center gap-4'>
        <Button
          variant={mode === 'city' ? 'default' : 'outline'}
          size='sm'
          onClick={() => onChangeMode('city')}
          className='h-10'
        >
          Món tại thành phố
        </Button>
        <Button
          variant={mode === 'store' ? 'default' : 'outline'}
          size='sm'
          onClick={() => onChangeMode('store')}
          className='h-10'
        >
          Món tại cửa hàng
        </Button>

        <div className='ml-auto'>
          {mode === 'store' ? (
            <Select value={selectedStoreUid} onValueChange={v => onChangeStore?.(v)}>
              <SelectTrigger className='h-10 w-56'>
                <SelectValue placeholder='Chọn cửa hàng' />
              </SelectTrigger>
              <SelectContent>
                {stores.map(s => (
                  <SelectItem key={s.id} value={s.id}>
                    {s.store_name || s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Select value={selectedCityUid} onValueChange={v => onChangeCity?.(v)}>
              <SelectTrigger className='h-10 w-40'>
                <SelectValue placeholder='Chọn thành phố' />
              </SelectTrigger>
              <SelectContent>
                {cities.map(c => (
                  <SelectItem key={c.id} value={c.id}>
                    {c.city_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <div className='rounded-md border'>
        <div
          className='flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100'
          onClick={() => setIsSelectedCollapsed(!isSelectedCollapsed)}
        >
          <span>✓ Đã chọn {selectedItems.length}</span>
          {selectedItemsData.length > 0 &&
            (isSelectedCollapsed ? <ChevronDown className='h-4 w-4' /> : <ChevronUp className='h-4 w-4' />)}
        </div>

        {selectedItemsData.length > 0 && !isSelectedCollapsed && (
          <div className='border-b'>
            <div className='p-4'>
              <div className='max-h-48 space-y-2 overflow-y-auto'>
                {selectedItemsData.map(item => (
                  <ItemCheckbox key={item.id} item={item} isSelected={true} onToggle={handleToggleItem} />
                ))}
              </div>
            </div>
          </div>
        )}

        {unselectedItemsData.length > 0 && (
          <div>
            {selectedItemsData.length > 0 && (
              <div className='border-b bg-gray-50 p-3 text-sm text-gray-600'>
                <span>Còn lại {unselectedItemsData.length}</span>
              </div>
            )}
            <div className='p-4'>
              <div className='max-h-48 space-y-2 overflow-y-auto'>
                {unselectedItemsData.map(item => (
                  <ItemCheckbox key={item.id} item={item} isSelected={false} onToggle={handleToggleItem} />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

interface ItemCheckboxProps {
  item: Item
  isSelected: boolean
  onToggle: (itemId: string, checked: boolean) => void
}

function ItemCheckbox({ item, isSelected, onToggle }: ItemCheckboxProps) {
  return (
    <div className='flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50'>
      <Checkbox
        id={item.id}
        checked={isSelected}
        onCheckedChange={checked => onToggle(item.id, !!checked)}
        className='h-4 w-4'
      />
      <Label htmlFor={item.id} className='flex-1 cursor-pointer text-sm leading-relaxed'>
        {item.item_name} - {item.item_id}
      </Label>
    </div>
  )
}
