import { useState } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import { Button, Checkbox, Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui'

interface Package {
  id: string
  package_id?: string
  name?: string
  package_name?: string
  active?: number
}

interface PackagesSectionProps {
  packages: Package[]
  selectedItems: string[]
  searchTerm: string
  isLoading: boolean
  onItemToggle: (itemId: string) => void
}

export function PackagesSection({
  packages,
  selectedItems,
  searchTerm,
  isLoading,
  onItemToggle
}: PackagesSectionProps) {
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)

  const getFilteredData = () => {
    return (Array.isArray(packages) ? packages : [])
      .filter((pkg: Package) => {
        const name = pkg?.package_name || pkg?.name || ''
        const nameMatch = name.toLowerCase().includes(searchTerm.toLowerCase())
        return pkg.active !== 0 && nameMatch
      })
      .map((pkg: Package) => ({
        ...pkg,
        name: pkg.package_name || pkg.name || 'Không có tên'
      }))
  }

  const filteredData = getFilteredData()
  const selectedData = filteredData.filter(item => selectedItems.includes(item.package_id || item.id))
  const remainingData = filteredData.filter(item => !selectedItems.includes(item.package_id || item.id))

  if (isLoading) {
    return <div className='py-4 text-center text-sm text-gray-500'>Đang tải...</div>
  }

  return (
    <div className='space-y-4'>
      <Collapsible open={!selectedCollapsed} onOpenChange={open => setSelectedCollapsed(!open)}>
        <CollapsibleTrigger asChild>
          <Button variant='ghost' className='flex w-full items-center justify-between p-2 text-left'>
            <span>Đã chọn ({selectedData.length})</span>
            {selectedCollapsed && <ChevronRight className='h-4 w-4' />}
            {!selectedCollapsed && <ChevronDown className='h-4 w-4' />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className='max-h-40 space-y-2 overflow-y-auto'>
          {selectedData.map(item => (
            <div key={item.id} className='flex items-center space-x-2 p-2'>
              <Checkbox checked={true} onCheckedChange={() => onItemToggle(item.package_id || item.id)} />
              <span className='text-sm'>
                <span className='font-medium'>{item.name || 'Không có tên'}</span>
              </span>
            </div>
          ))}
          {selectedData.length === 0 && <div className='p-2 text-sm text-gray-500'>Chưa chọn combo nào</div>}
        </CollapsibleContent>
      </Collapsible>

      <Collapsible open={!remainingCollapsed} onOpenChange={open => setRemainingCollapsed(!open)}>
        <CollapsibleTrigger asChild>
          <Button variant='ghost' className='flex w-full items-center justify-between p-2 text-left'>
            <span>Còn lại ({remainingData.length})</span>
            {remainingCollapsed && <ChevronRight className='h-4 w-4' />}
            {!remainingCollapsed && <ChevronDown className='h-4 w-4' />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className='max-h-40 space-y-2 overflow-y-auto'>
          {remainingData.map(item => (
            <div key={item.id} className='flex items-center space-x-2 p-2'>
              <Checkbox checked={false} onCheckedChange={() => onItemToggle(item.package_id || item.id)} />
              <span className='text-sm'>
                <span className='font-medium'>{item.name || 'Không có tên'}</span>
              </span>
            </div>
          ))}
          {remainingData.length === 0 && <div className='p-2 text-sm text-gray-500'>Không có combo nào</div>}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
