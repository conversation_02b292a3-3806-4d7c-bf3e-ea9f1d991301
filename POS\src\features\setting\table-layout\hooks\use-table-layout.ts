import React from 'react'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  tableLayoutApi,
  type GetTablesParams,
  type UpdateTablePositionRequest,
  type Table
} from '@/lib/table-layout-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseTableLayoutOptions {
  areaUid: string
  storeUid?: string
  enabled?: boolean
}

/**
 * Hook to fetch table layout data
 */
export const useTableLayout = (options: UseTableLayoutOptions) => {
  const { areaUid, storeUid, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const defaultStoreUid = React.useMemo(() => {
    if (storeUid) return storeUid

    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        const activeStores = Array.isArray(storesData) ? storesData.filter((store: any) => store.active === 1) : []
        return activeStores[0]?.id || ''
      }
    } catch (error) {
      console.error('Error getting store from localStorage:', error)
    }

    return ''
  }, [storeUid])

  const params: GetTablesParams = {
    skip_limit: true,
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    store_uid: defaultStoreUid,
    area_uid: areaUid,
    order: 'sort-asc, table_name'
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id && areaUid && defaultStoreUid)

  return useQuery({
    queryKey: [QUERY_KEYS.TABLE_LAYOUT, params],
    queryFn: async () => {
      const response = await tableLayoutApi.getTables(params)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

/**
 * Hook to update table position
 */
export const useUpdateTablePosition = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdateTablePositionRequest) => tableLayoutApi.updateTablePosition(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLE_LAYOUT]
      })
      toast.success('Vị trí bàn đã được cập nhật')
    },
    onError: (error: any) => {
      console.error('Error updating table position:', error)
      toast.error('Có lỗi xảy ra khi cập nhật vị trí bàn')
    }
  })
}

/**
 * Hook to get table by ID
 */
export const useTableById = (tableId: string, tables: Table[] = []) => {
  return tables.find(table => table.id === tableId) || null
}
