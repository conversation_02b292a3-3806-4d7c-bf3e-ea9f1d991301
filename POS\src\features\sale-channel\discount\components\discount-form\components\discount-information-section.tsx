import { usePosStores } from '@/stores/posStore'

import { useCreateDiscountPromotion } from '@/hooks/api/use-discount'

import {
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsList,
  TabsTrigger
} from '@/components/ui'

import { useDiscountSalesChannels } from '../hooks'
import { useDiscountFormData, useDiscountFormStatus } from '../stores'

export function DiscountInformationSection() {
  const { formData, updateFormData } = useDiscountFormData()
  const { isEditMode } = useDiscountFormStatus()
  const { currentBrandStores } = usePosStores()
  const { salesChannels, isLoadingChannels } = useDiscountSalesChannels({
    storeUid: formData.storeUid
  })
  const { createPromotion } = useCreateDiscountPromotion()

  const handleChannelChange = async (channelUid: string) => {
    updateFormData({ channelUid })

    const selectedChannel = salesChannels.find(channel => channel.id === channelUid)

    if (selectedChannel && formData.storeUid) {
      await createPromotion({
        storeUid: formData.storeUid,
        channelUid: channelUid,
        channelName: selectedChannel.sourceName
      })
    }
  }

  return (
    <div className='space-y-4'>
      <h2 className='text-lg font-medium text-gray-900'>Thông tin giảm giá</h2>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Cửa hàng <span className='text-red-500'>*</span>
        </Label>
        <Select
          value={formData.storeUid}
          onValueChange={value => {
            updateFormData({ storeUid: value, channelUid: '' })
          }}
          disabled={isEditMode}
        >
          <SelectTrigger className='flex-1'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {currentBrandStores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.store_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Kênh <span className='text-red-500'>*</span>
        </Label>
        <Select
          value={formData.channelUid}
          onValueChange={handleChannelChange}
          disabled={!formData.storeUid || isLoadingChannels || isEditMode}
        >
          <SelectTrigger className='flex-1'>
            <SelectValue
              placeholder={!formData.storeUid ? 'Chọn cửa hàng trước' : isLoadingChannels ? 'Đang tải...' : 'Chọn kênh'}
            />
          </SelectTrigger>
          <SelectContent>
            {salesChannels.map(channel => (
              <SelectItem key={channel.id} value={channel.id}>
                {channel.sourceName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          Chương trình khuyến mãi <span className='text-red-500'>*</span>
        </Label>
        <Input
          value={
            isEditMode && formData.promotionName
              ? formData.promotionName
              : formData.channelUid && salesChannels.length > 0
                ? `CTKM tự động theo kênh ${salesChannels.find(channel => channel.id === formData.channelUid)?.sourceName || ''}`
                : 'CTKM được tự động tạo theo kênh'
          }
          disabled
          className='flex-1'
        />
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>
          {formData.discountType === 'PERCENT' ? 'Phần trăm giảm giá' : 'Số tiền giảm giá'}{' '}
          <span className='text-red-500'>*</span>
        </Label>
        <div className='flex flex-1 gap-2'>
          <Input
            type='number'
            min='0'
            max={formData.discountType === 'PERCENT' ? '100' : undefined}
            value={
              formData.discountType === 'PERCENT' ? formData.discountPercentage || '' : formData.discountAmount || ''
            }
            onChange={e => {
              const value = Number(e.target.value)
              if (formData.discountType === 'PERCENT') {
                const cappedValue = value > 100 ? 100 : value
                updateFormData({ discountPercentage: cappedValue })
              } else {
                updateFormData({ discountAmount: value })
              }
            }}
            placeholder='0'
            className='flex-1'
          />
          <Tabs
            value={formData.discountType}
            onValueChange={value => updateFormData({ discountType: value as 'PERCENT' | 'AMOUNT' })}
            className='w-auto'
          >
            <TabsList className='grid w-fit grid-cols-2'>
              <TabsTrigger value='PERCENT'>%</TabsTrigger>
              <TabsTrigger value='AMOUNT'>đ</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
