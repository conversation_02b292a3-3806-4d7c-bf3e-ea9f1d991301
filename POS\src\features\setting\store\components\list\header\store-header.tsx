import { IconPlus } from '@tabler/icons-react'

import { Button, Input } from '@/components/ui'

import { CitySelect } from './city-select'
import { StoreActionsDropdown } from './store-actions-dropdown'

interface City {
  id: string
  name: string
}

interface StoreHeaderProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  selectedCity: string
  onCityChange: (value: string) => void
  cities?: City[]
  citiesLoading?: boolean
  onCreateStore: () => void
  onSyncSecondaryScreen: () => void
}

export function StoreHeader({
  searchTerm,
  onSearchChange,
  selectedCity,
  onCityChange,
  cities,
  citiesLoading,
  onCreateStore,
  onSyncSecondaryScreen
}: StoreHeaderProps) {
  return (
    <div className='mb-4 space-y-4'>
      <div className='flex items-center justify-between gap-4'>
        <div className='flex min-w-0 flex-1 items-center gap-3'>
          <h2 className='text-xl font-semibold whitespace-nowrap'>Danh sách nhà hàng</h2>

          <Input
            placeholder='Tìm kiếm nhà hàng'
            value={searchTerm}
            onChange={e => onSearchChange(e.target.value)}
            className='w-64'
          />

          <CitySelect value={selectedCity} onValueChange={onCityChange} cities={cities} isLoading={citiesLoading} />
        </div>

        <div className='flex items-center gap-2'>
          <StoreActionsDropdown onSyncSecondaryScreen={onSyncSecondaryScreen} />
          <Button onClick={onCreateStore}>
            <IconPlus className='mr-2 h-4 w-4' />
            Tạo nhà hàng mới
          </Button>
        </div>
      </div>
    </div>
  )
}
