import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface UserState {
  currentBrandId: string | null
  setCurrentBrandId: (brandId: string) => void
  clearCurrentBrand: () => void
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      currentBrandId: null,
      
      setCurrentBrandId: (brandId: string) => {
        set({ currentBrandId: brandId })
      },
      
      clearCurrentBrand: () => {
        set({ currentBrandId: null })
      }
    }),
    {
      name: 'user-store', // localStorage key
      partialize: (state) => ({ currentBrandId: state.currentBrandId })
    }
  )
)
