import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Store } from '@/types/store'

import { useCitiesData } from '@/hooks/api/use-removed-items'
import { useStoresData } from '@/hooks/api/use-stores'

import { STORE_CONSTANTS } from '../../data'
import { useCitiesTransform } from './use-cities-transform'
import { useStoreFiltering } from './use-store-filtering'
import { useStoreListState } from './use-store-list-state'

export interface UseStoreListReturn {
  stores: Store[]
  filteredStores: Store[]
  cities: Array<{ id: string; name: string }>

  isLoading: boolean
  citiesLoading: boolean

  error: Error | null

  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedCity: string
  setSelectedCity: (city: string) => void

  handleCreateStore: () => void
  handleSyncSecondaryScreen: () => void

  syncModalOpen: boolean
  setSyncModalOpen: (open: boolean) => void
}

/**
 * Main hook that combines all store list functionality
 */
export function useStoreList(): UseStoreListReturn {
  const navigate = useNavigate()

  const [syncModalOpen, setSyncModalOpen] = useState(false)

  const { searchTerm, setSearchTerm, selectedCity, setSelectedCity } = useStoreListState()

  const { data: stores = [], isLoading, error } = useStoresData()
  const { data: citiesData = [], isLoading: citiesLoading } = useCitiesData()

  const { cities } = useCitiesTransform(citiesData)

  const { filteredStores } = useStoreFiltering({
    stores,
    searchTerm,
    selectedCity
  })

  const handleCreateStore = () => {
    navigate({ to: STORE_CONSTANTS.ROUTE_STORE_DETAIL })
  }

  const handleSyncSecondaryScreen = () => {
    setSyncModalOpen(true)
  }

  return {
    stores,
    filteredStores,
    cities,

    isLoading,
    citiesLoading,

    error,

    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity,

    handleCreateStore,
    handleSyncSecondaryScreen,

    syncModalOpen,
    setSyncModalOpen
  }
}
