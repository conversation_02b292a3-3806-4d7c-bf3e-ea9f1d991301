import { useMemo } from 'react'

import type { PosCompanyData } from '@/types/pos-company'

import { POS_COMPANY_DATA } from '@/constants/local-storage'

export const usePosCompanyData = (): PosCompanyData | null => {
  return useMemo(() => {
    try {
      const posCompanyData = localStorage.getItem(POS_COMPANY_DATA)
      if (posCompanyData) {
        return JSON.parse(posCompanyData) as PosCompanyData
      }
      return null
    } catch (error) {
      console.error('Error parsing pos_company_data from localStorage:', error)
      return null
    }
  }, [])
}
