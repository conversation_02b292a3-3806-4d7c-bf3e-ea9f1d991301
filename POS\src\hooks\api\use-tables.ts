import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  tablesApi,
  type Table,
  type TablesListParams,
  type CreateTableRequest,
  type DeleteTablesRequest,
  type DeleteTableRequest,
  type UpdateTableStatusRequest,
  type UpdateTablePositionsRequest,
  type BulkCreateTablesRequest
} from '@/lib/tables-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseTablesDataOptions {
  storeUid?: string
  page?: number
  limit?: number
  skip_limit?: boolean
  company_uid?: string
  brand_uid?: string
  store_uid?: string
  list_store_uid?: string
  enabled?: boolean
}

/**
 * Hook to fetch tables data
 */
export const useTablesData = (options: UseTablesDataOptions = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const tablesQuery = useQuery({
    queryKey: [QUERY_KEYS.TABLES_LIST, options.storeUid, options.page, options.limit, options.skip_limit],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''
      const storeUid = options.storeUid || ''

      if (!companyUid || !brandUid || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      const params: TablesListParams = {
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: storeUid,
        page: options.page || 1,
        limit: options.limit,
        skip_limit: options.skip_limit
      }

      return await tablesApi.getTablesList(params)
    },
    enabled: !!(company?.id && selectedBrand?.id && options.storeUid),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })

  return {
    ...tablesQuery,
    data: tablesQuery.data?.data || []
  }
}

/**
 * Hook to update a table
 */
export const useUpdateTable = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: Table) => {
      return await tablesApi.updateTable(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_DETAIL]
      })
      toast.success('Cập nhật bàn thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật bàn'
      toast.error(errorMessage)
    }
  })

  return { updateTable: mutate, isUpdating: isPending }
}

/**
 * Hook to toggle table active status
 */
export const useToggleTableStatus = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (table: Table) => {
      // Toggle active status (0 -> 1, 1 -> 0)
      const newActiveStatus = table.active === 1 ? 0 : 1

      // Create complete table data with toggled active status (keeping all original fields)
      const updateData: UpdateTableStatusRequest = {
        id: table.id,
        table_id: table.table_id,
        table_name: table.table_name,
        description: table.description || null,
        extra_data: {
          color: (table.extra_data as any)?.color || '',
          font_size: (table.extra_data as any)?.font_size || '15',
          order_list: table.extra_data?.order_list || []
        },
        active: newActiveStatus,
        revision: table.revision ? String(table.revision) : null,
        sort: table.sort,
        is_fabi: table.is_fabi,
        source_id: table.source_id || null,
        area_uid: table.area_uid,
        store_uid: table.store_uid,
        brand_uid: table.brand_uid,
        company_uid: table.company_uid,
        area_id: table.area_id,
        store_id: table.store_id || null,
        brand_id: table.brand_id || null,
        company_id: table.company_id || null,
        created_by: table.created_by,
        updated_by: table.updated_by || table.created_by,
        created_at: table.created_at,
        updated_at: table.updated_at,
        area: {
          ...table.area,
          extra_data: {
            background: table.area?.extra_data?.background || ''
          }
        }
      }

      return await tablesApi.updateTableStatus(updateData)
    },
    onSuccess: updatedTable => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      const statusText = updatedTable.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`${statusText} bàn thành công`)
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật trạng thái bàn'
      toast.error(errorMessage)
    }
  })

  return { toggleTableStatus: mutate, isToggling: isPending }
}

/**
 * Hook to delete multiple tables
 */
export const useDeleteTables = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)

  const { mutate, isPending } = useMutation({
    mutationFn: async (params: { areaIds: string[]; storeUid: string }) => {
      const companyUid = company?.id || ''
      const brandUid = brands?.[0]?.id || ''

      if (!companyUid || !brandUid || !params.storeUid) {
        throw new Error('Missing required authentication information')
      }

      const deleteRequest: DeleteTablesRequest = {
        company_uid: companyUid,
        brand_uid: brandUid,
        list_id: params.areaIds,
        store_uid: params.storeUid,
        area_uid: ''
      }

      console.log('Deleting tables:', deleteRequest)
      return await tablesApi.deleteTables(deleteRequest)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      const count = variables.areaIds.length
      const message = count === 1 ? 'Xóa bàn thành công' : `Xóa ${count} bàn thành công`
      toast.success(message)
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa bàn'
      toast.error(errorMessage)
    }
  })

  return { deleteTables: mutate, isDeleting: isPending }
}

/**
 * Hook to fetch area detail
 */
export const useTableDetail = (areaId: string, storeUid: string) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.TABLES_DETAIL, areaId, storeUid],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !areaId || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      return await tablesApi.getTableById(areaId, companyUid, brandUid, storeUid)
    },
    enabled: !!(company?.id && selectedBrand?.id && areaId && storeUid),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Generate a random area ID in the format AREA-XXXX
 */
const generateTableId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = 'AREA-'
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Hook to create a new area
 */
export const useCreateTable = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: {
      table_name: string
      description?: string
      store_uid: string
      area_uid: string
      sort?: number
      sourceId?: string
      color?: string
      fontSize?: string
      selectedItems?: Array<{
        item_id: string
        item_name: string
        quantity: number
      }>
    }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      // Generate table_id and area_id
      const tableId = generateTableId()
      const areaId = generateTableId()

      // Create table payload matching the curl request
      const tableData: CreateTableRequest = {
        store_uid: data.store_uid,
        area_uid: data.area_uid,
        extra_data: {
          order_list:
            data.selectedItems?.map(item => ({
              item_id: item.item_id,
              quantity: item.quantity
            })) || [],
          color: data.color || '',
          font_size: data.fontSize || '15'
        },
        table_name: data.table_name,
        source_id: data.sourceId || '',
        description: data.description || '',
        sort: data.sort || 1,
        company_uid: companyUid,
        brand_uid: brandUid,
        table_id: tableId,
        area_id: areaId
      }

      return await tablesApi.createTable(tableData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      toast.success('Tạo bàn thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo bàn'
      toast.error(errorMessage)
    }
  })

  return {
    createTable: mutate,
    isCreating: isPending
  }
}

interface BulkImportInput {
  storeUid: string
  tables: Array<{
    table_name: string
    area_uid: string
    description?: string
  }>
}

/**
 * Hook to bulk import tables
 */
export const useBulkImportTables = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (data: BulkImportInput) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      // Build the array of table objects for bulk create
      const requestData: BulkCreateTablesRequest = {
        store_uid: data.storeUid,
        company_uid: companyUid,
        brand_uid: brandUid,
        tables: data.tables.map(table => ({
          table_name: table.table_name,
          area_uid: table.area_uid,
          description: table.description || '',
          extra_data: {
            order_list: [],
            color: '',
            font_size: '15'
          }
        }))
      }

      return await tablesApi.bulkCreateTables(requestData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi import bàn'
      toast.error(errorMessage)
    }
  })

  return { mutateAsync, isPending }
}

/**
 * Hook to delete a table
 */
export const useDeleteTable = (storeUid: string) => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (tableId: string) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      const params: DeleteTableRequest = {
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: storeUid,
        list_id: [tableId]
      }

      return await tablesApi.deleteTable(params)
    },
    onSuccess: () => {
      // Invalidate tables list query to refresh data
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TABLES_LIST] })
      toast.success('Xóa bàn thành công!')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa bàn'
      toast.error(errorMessage)
    }
  })

  return { deleteTable: mutateAsync, isDeleting: isPending }
}

/**
 * Hook to bulk update tables
 */
export const useBulkUpdateTables = () => {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (data: { storeUid: string; tables: Table[] }) => {
      return await tablesApi.bulkUpdateTables(data.tables)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật bàn'
      toast.error(errorMessage)
    }
  })

  return { mutateAsync, isPending }
}

/**
 * Hook to update table positions after drag and drop
 */
export const useUpdateTablePositions = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: { tables: Array<{ id: string; sort: number }>; storeUid: string }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !data.storeUid) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      const positions: UpdateTablePositionsRequest = data.tables.map(table => ({
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: data.storeUid,
        id: table.id,
        sort: table.sort
      }))

      return await tablesApi.updateTablePositions(positions)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TABLES_LIST]
      })
      toast.success('Lưu vị trí bàn thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi lưu vị trí bàn'
      toast.error(errorMessage)
    }
  })

  return { updateTablePositions: mutate, isUpdating: isPending }
}
