import { api } from './api/pos/pos-api'

// Types for Deleted Orders API
export interface DeletedOrderData {
  tran_id: string
  origin_tran_id: string
  tran_no: string
  shift_id: string
  store_uid: string
  store_name: string
  total_amount: number
  total_amount_origin: number
  employee_name: string
  employee_uid: string
  tran_date: number
  updated_at: number
  status: 'DELETE' | 'EDIT'
  table_name: string
  sale_note: string
  source_voucher: string | null
  source_deli: string
}

export interface ShiftData {
  id: string
  shift_id: string
  shift_name: string
  device_code: string
  start_date: number
  end_date: number
  balance: number
  number_order: number
  amount: number
  status: string
  user_name: string
  user_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
}

export interface DeletedOrdersResponse {
  data: DeletedOrderData[]
  data_shift: ShiftData[]
  track_id: string
}

export interface DeletedOrdersSummary {
  storeStats: Array<{
    storeUid: string
    storeName: string
    totalDeleted: number
    totalDeletedAmount: number
    totalOriginalAmount: number
    deletedPercentage: number
    topEmployees: Array<{
      employeeUid: string
      employeeName: string
      deletedCount: number
      deletedAmount: number
    }>
    topReasons: Array<{
      reason: string
      count: number
      amount: number
    }>
  }>
  employeeStats: Array<{
    employeeUid: string
    employeeName: string
    totalDeleted: number
    totalDeletedAmount: number
    storeCount: number
    stores: string[]
  }>
  reasonStats: Array<{
    reason: string
    count: number
    amount: number
    storeCount: number
  }>
  totalDeleted: number
  totalDeletedAmount: number
  totalOriginalAmount: number
  overallDeletedPercentage: number
}

// Cache for deleted orders requests
const deletedOrdersCache = new Map<string, { data: DeletedOrdersResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<DeletedOrdersResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Deleted Orders API Service
export const deletedOrdersApi = {
  /**
   * Get deleted orders data with request deduplication
   */
  getDeletedOrders: async (params: {
    companyUid: string
    brandUid: string
    storeUid?: string // Optional - if not provided, gets all stores
    startDate: number
    endDate: number
    page?: number
  }): Promise<DeletedOrdersResponse> => {
    const requestKey = `${params.companyUid}-${params.brandUid}-${params.storeUid || 'all'}-${params.startDate}-${params.endDate}-${params.page || 1}`

    const cached = deletedOrdersCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          page: (params.page || 1).toString(),
          store_open_at: '0'
        })

        // Add store_uid if provided, otherwise get all stores
        if (params.storeUid) {
          queryParams.set('store_uid', params.storeUid)
        }

        const response = await api.get(`/v3/pos-cms/report/sale-edit-delete?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000' // GMT+7 timezone offset in milliseconds
          },
          timeout: 30000 // 30 seconds timeout
        })

        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from deleted orders API')
        }

        deletedOrdersCache.set(requestKey, {
          data: response.data as DeletedOrdersResponse,
          timestamp: Date.now()
        })

        return response.data as DeletedOrdersResponse
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
          throw new Error('Request timeout - server is taking too long to respond. Try reducing the date range.')
        }

        if (error.response?.status === 504) {
          throw new Error('Gateway timeout (504) - server is overloaded. Please try again later.')
        }

        if (error.response?.status === 503) {
          throw new Error('Service unavailable (503) - server is temporarily down. Please try again later.')
        }

        if (error.response?.status >= 500) {
          throw new Error(`Server error (${error.response.status}) - please try again later.`)
        }

        if (error.response?.status === 429) {
          throw new Error('Too many requests - please wait a moment before trying again.')
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Process deleted orders data to create comprehensive statistics
   */
  processDeletedOrdersSummary: (response: DeletedOrdersResponse): DeletedOrdersSummary => {
    if (!response.data || response.data.length === 0) {
      return {
        storeStats: [],
        employeeStats: [],
        reasonStats: [],
        totalDeleted: 0,
        totalDeletedAmount: 0,
        totalOriginalAmount: 0,
        overallDeletedPercentage: 0
      }
    }

    const deletedOrders = response.data.filter(order => order.status === 'DELETE')

    // Group by store
    const storeGroups = new Map<string, DeletedOrderData[]>()
    const employeeGroups = new Map<string, DeletedOrderData[]>()
    const reasonGroups = new Map<string, DeletedOrderData[]>()

    let totalDeleted = 0
    let totalDeletedAmount = 0
    let totalOriginalAmount = 0

    deletedOrders.forEach(order => {
      // Store grouping
      if (!storeGroups.has(order.store_uid)) {
        storeGroups.set(order.store_uid, [])
      }
      storeGroups.get(order.store_uid)!.push(order)

      // Employee grouping
      if (!employeeGroups.has(order.employee_uid)) {
        employeeGroups.set(order.employee_uid, [])
      }
      employeeGroups.get(order.employee_uid)!.push(order)

      // Reason grouping
      const reason = order.sale_note || 'Không có lý do'
      if (!reasonGroups.has(reason)) {
        reasonGroups.set(reason, [])
      }
      reasonGroups.get(reason)!.push(order)

      // Totals
      totalDeleted++
      totalDeletedAmount += order.total_amount_origin
      totalOriginalAmount += order.total_amount_origin
    })

    // Process store statistics
    const storeStats = Array.from(storeGroups.entries())
      .map(([storeUid, orders]) => {
        const storeName = orders[0].store_name
        const storeDeletedAmount = orders.reduce((sum, order) => sum + order.total_amount_origin, 0)

        // Top employees in this store
        const storeEmployeeGroups = new Map<string, DeletedOrderData[]>()
        orders.forEach(order => {
          if (!storeEmployeeGroups.has(order.employee_uid)) {
            storeEmployeeGroups.set(order.employee_uid, [])
          }
          storeEmployeeGroups.get(order.employee_uid)!.push(order)
        })

        const topEmployees = Array.from(storeEmployeeGroups.entries())
          .map(([employeeUid, empOrders]) => ({
            employeeUid,
            employeeName: empOrders[0].employee_name,
            deletedCount: empOrders.length,
            deletedAmount: empOrders.reduce((sum, order) => sum + order.total_amount_origin, 0)
          }))
          .sort((a, b) => b.deletedCount - a.deletedCount)
          .slice(0, 5)

        // Top reasons in this store
        const storeReasonGroups = new Map<string, DeletedOrderData[]>()
        orders.forEach(order => {
          const reason = order.sale_note || 'Không có lý do'
          if (!storeReasonGroups.has(reason)) {
            storeReasonGroups.set(reason, [])
          }
          storeReasonGroups.get(reason)!.push(order)
        })

        const topReasons = Array.from(storeReasonGroups.entries())
          .map(([reason, reasonOrders]) => ({
            reason,
            count: reasonOrders.length,
            amount: reasonOrders.reduce((sum, order) => sum + order.total_amount_origin, 0)
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5)

        return {
          storeUid,
          storeName,
          totalDeleted: orders.length,
          totalDeletedAmount: storeDeletedAmount,
          totalOriginalAmount: storeDeletedAmount, // Same as deleted amount for deleted orders
          deletedPercentage: 100, // All are deleted orders
          topEmployees,
          topReasons
        }
      })
      .sort((a, b) => b.totalDeleted - a.totalDeleted)

    // Process employee statistics
    const employeeStats = Array.from(employeeGroups.entries())
      .map(([employeeUid, orders]) => {
        const employeeName = orders[0].employee_name
        const employeeDeletedAmount = orders.reduce((sum, order) => sum + order.total_amount_origin, 0)
        const uniqueStores = [...new Set(orders.map(order => order.store_name))]

        return {
          employeeUid,
          employeeName,
          totalDeleted: orders.length,
          totalDeletedAmount: employeeDeletedAmount,
          storeCount: uniqueStores.length,
          stores: uniqueStores
        }
      })
      .sort((a, b) => b.totalDeleted - a.totalDeleted)

    // Process reason statistics
    const reasonStats = Array.from(reasonGroups.entries())
      .map(([reason, orders]) => {
        const reasonAmount = orders.reduce((sum, order) => sum + order.total_amount_origin, 0)
        const uniqueStores = [...new Set(orders.map(order => order.store_uid))]

        return {
          reason,
          count: orders.length,
          amount: reasonAmount,
          storeCount: uniqueStores.length
        }
      })
      .sort((a, b) => b.count - a.count)

    return {
      storeStats,
      employeeStats,
      reasonStats,
      totalDeleted,
      totalDeletedAmount,
      totalOriginalAmount,
      overallDeletedPercentage: totalOriginalAmount > 0 ? (totalDeletedAmount / totalOriginalAmount) * 100 : 0
    }
  },

  /**
   * Format currency for display
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }
}

export default deletedOrdersApi
