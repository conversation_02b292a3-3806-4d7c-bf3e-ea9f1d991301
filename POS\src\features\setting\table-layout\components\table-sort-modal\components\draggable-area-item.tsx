import React from 'react'

import { GripVertical } from 'lucide-react'

import type { Area } from '@/lib/api'

interface DraggableAreaItemProps {
  area: Area
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
  isDragging: boolean
  isSelected: boolean
}

export const DraggableAreaItem: React.FC<DraggableAreaItemProps> = ({
  area,
  index,
  onDragStart,
  onDragOver,
  onDrop,
  isDragging,
  isSelected
}) => {
  return (
    <div
      draggable
      onDragStart={e => onDragStart(e, index)}
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      className={`flex min-w-0 items-center gap-3 rounded-lg border p-3 transition-all duration-200 ${
        isDragging
          ? 'opacity-50 shadow-lg'
          : isSelected
            ? 'cursor-move border-blue-500 bg-blue-50 shadow-md hover:shadow-lg'
            : 'cursor-move border-gray-200 bg-white hover:bg-gray-50 hover:shadow-md'
      }`}
    >
      <GripVertical className='h-5 w-5 flex-shrink-0 text-gray-400' />
      <div className='min-w-0 flex-1'>
        <div className='truncate font-medium text-gray-900' title={area.area_name}>
          {area.area_name}
        </div>
      </div>
    </div>
  )
}
