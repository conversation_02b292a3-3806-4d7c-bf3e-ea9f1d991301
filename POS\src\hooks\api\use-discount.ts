import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import type { DiscountFormData, UpdateDiscountRequest } from '@/types/api/discount-types'
import type { GetDiscountsParams } from '@/types/discounts'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'
import {
  createDiscountEnhanced,
  updateDiscountEnhanced,
  updateDiscountWithFullObject,
  transformFormDataToRequest,
  discountUtils,
  getDiscountById,
  getDiscounts,
  deleteDiscount,
  updateDiscount
} from '@/lib/discounts-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UseCreateDiscountOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useCreateDiscount(storeUid: string, options?: UseCreateDiscountOptions) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (formData: DiscountFormData) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Thiếu thông tin company hoặc brand')
      }

      const request = transformFormDataToRequest(formData, storeUid, company.id, selectedBrand.id)

      const response = await createDiscountEnhanced(request)

      if (!response.success) {
        throw new Error(response.message || 'Có lỗi xảy ra')
      }

      return response.data
    },
    onSuccess: () => {
      toast.success('Tạo discount thành công')

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNTS]
      })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi tạo discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

interface UseUpdateDiscountOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useUpdateDiscount(storeUid: string, options?: UseUpdateDiscountOptions) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (formData: DiscountFormData) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Thiếu thông tin company hoặc brand')
      }

      const request = transformFormDataToRequest(formData, storeUid, company.id, selectedBrand.id)

      const response = await updateDiscountEnhanced(request)

      if (!response.success) {
        throw new Error(response.message || 'Có lỗi xảy ra')
      }

      return response.data
    },
    onSuccess: () => {
      toast.success('Cập nhật discount thành công')

      // Invalidate all discount queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DISCOUNTS]
      })

      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật discount'

      toast.error(errorMessage)

      options?.onError?.(errorMessage)
    }
  })
}

/**
 * Hook for editing discount with full object (edit mode)
 */
export function useEditDiscount(options?: { onSuccess?: () => void; onError?: (error: any) => void }) {
  const queryClient = useQueryClient()
  const { onSuccess, onError } = options || {}

  return useMutation({
    mutationFn: (data: UpdateDiscountRequest) => updateDiscountWithFullObject(data),
    onSuccess: () => {
      // Invalidate all discount queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })

      onSuccess?.()
    },
    onError
  })
}

/**
 * Hook để validate form data trước khi submit
 */
export function useDiscountFormValidation() {
  return {
    validateFormData: discountUtils.validateFormData
  }
}

/**
 * Hook to fetch discount detail by ID
 */
export function useDiscountDetail(discountId: string) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()

  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, discountId],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !discountId) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      return await getDiscountById({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId
      })
    },
    enabled: !!(company?.id && selectedBrand?.id && discountId),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook for creating promotion
 */
interface CreatePromotionParams {
  storeUid: string
  channelUid: string
  channelName: string
}

interface CreatePromotionPayload {
  company_uid: string
  brand_uid: string
  promotion_name: string
  promotion_id: string
  source_uid: string
  store_uid: string
  partner_auto_gen: number
}

export function useCreateDiscountPromotion() {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const createPromotion = async ({ storeUid, channelUid, channelName }: CreatePromotionParams) => {
    if (!company?.id || !selectedBrand?.id) {
      return null
    }

    try {
      const payload: CreatePromotionPayload = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        promotion_name: `CTKM tự động theo kênh ${channelName}`,
        promotion_id: `PROMOTION_${Date.now()}`,
        source_uid: channelUid,
        store_uid: storeUid,
        partner_auto_gen: 1
      }

      const response = await api.post('/mdata/v1/promotion', payload)
      return response.data
    } catch (error) {
      return null
    }
  }

  return {
    createPromotion
  }
}

/**
 * Hook for fetching discounts list
 */
export function useDiscounts(params: GetDiscountsParams = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'channel', params],
    queryFn: () => getDiscounts(params),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000
  })
}

/**
 * Hook for deleting discount (simple version)
 */
export function useDeleteDiscountSimple() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteDiscount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

/**
 * Hook for updating discount (simple version)
 */
export function useUpdateDiscountSimple() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateDiscount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    }
  })
}

/**
 * Hook utilities cho discount
 */
export function useDiscountUtils() {
  return {
    formatDiscountValue: discountUtils.formatDiscountValue,
    validateFormData: discountUtils.validateFormData
  }
}
