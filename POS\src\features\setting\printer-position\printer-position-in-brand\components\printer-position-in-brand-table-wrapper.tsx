'use client'

import * as React from 'react'

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { DataTablePagination } from '@/components/data-table/data-table-pagination'

import { usePrinterPositionInBrand } from '../context'
import { PrinterPositionInBrand } from '../data'
import { PrinterPositionInBrandButtons } from './printer-position-in-brand-buttons'
import { PrinterPositionInBrandTableToolbar } from './printer-position-in-brand-table-toolbar'

interface PrinterPositionInBrandTableWrapperProps {
  columns: ColumnDef<PrinterPositionInBrand, unknown>[]
  data: PrinterPositionInBrand[]
}

export function PrinterPositionInBrandTableWrapper({ columns, data }: PrinterPositionInBrandTableWrapperProps) {
  const { setOpen, setCurrentRow, open } = usePrinterPositionInBrand()
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])

  // Reset row selection when bulk delete dialog closes
  React.useEffect(() => {
    if (open !== 'bulk-delete') {
      setRowSelection({})
    }
  }, [open])

  const handleRowClick = (row: PrinterPositionInBrand) => {
    setCurrentRow(row)
    setOpen('update')
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  })

  return (
    <div className='space-y-4'>
      {/* Header with buttons */}
      <div className='flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
        <h2 className='text-2xl font-bold tracking-tight'>Vị trí máy in trong thương hiệu</h2>
        <PrinterPositionInBrandButtons />
      </div>

      {/* Table toolbar */}
      <PrinterPositionInBrandTableToolbar table={table} />

      {/* Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='cursor-pointer'
                  onClick={e => {
                    // Don't trigger row click if clicking on actions column or checkbox
                    const target = e.target as HTMLElement
                    if (target.closest('[data-actions]') || target.closest('[role="checkbox"]')) {
                      return
                    }
                    handleRowClick(row.original)
                  }}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} {...(cell.column.id === 'actions' ? { 'data-actions': true } : {})}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu vị trí máy in.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  )
}
