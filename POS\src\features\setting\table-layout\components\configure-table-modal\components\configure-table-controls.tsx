import React from 'react'

import { Combobox } from '@/components/pos'

import { ColorPicker } from './color-picker'

interface Store {
  id: string
  store_name: string
  active: number
}

interface TableConfig {
  color: string
  fontSize: string
}

interface ConfigureTableControlsProps {
  stores: Store[]
  selectedStoreId: string
  config: TableConfig
  showColorPicker: boolean
  onStoreChange: (storeId: string) => void
  onConfigChange: (config: Partial<TableConfig>) => void
  onColorPickerToggle: (show: boolean) => void
  onColorSelect: (color: string) => void
}

export const ConfigureTableControls: React.FC<ConfigureTableControlsProps> = ({
  stores,
  selectedStoreId,
  config,
  showColorPicker,
  onStoreChange,
  onConfigChange,
  onColorPickerToggle,
  onColorSelect
}) => {
  const handleColorSelect = (color: string) => {
    onColorSelect(color)
    onColorPickerToggle(false)
  }

  const storeOptions = [
    { value: '', label: 'A Hùng' },
    ...stores.map(store => ({
      value: store.id,
      label: store.store_name
    }))
  ]

  return (
    <div className='w-full border-b bg-gray-50 p-4'>
      <div className='flex items-center justify-between gap-4'>
        <div className='flex items-center gap-4'>
          <Combobox
            options={storeOptions}
            value={selectedStoreId}
            onValueChange={onStoreChange}
            className='min-w-[250px]'
          />

          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium'>Màu chữ</label>
            <div className='relative'>
              <div
                className='h-6 w-8 cursor-pointer rounded border'
                style={{ backgroundColor: config.color }}
                onClick={() => onColorPickerToggle(!showColorPicker)}
              />
              {showColorPicker && <ColorPicker selectedColor={config.color} onColorSelect={handleColorSelect} />}
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium'>Kích cỡ chữ</label>
            <input
              type='range'
              min='10'
              max='30'
              value={config.fontSize}
              onChange={e => onConfigChange({ fontSize: e.target.value })}
              className='w-20'
            />
            <span className='min-w-[40px] text-sm'>{config.fontSize}px</span>
          </div>
        </div>
      </div>
    </div>
  )
}
