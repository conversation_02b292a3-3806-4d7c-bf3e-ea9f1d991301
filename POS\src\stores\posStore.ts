import React from 'react'

import type { User, Company, Brand, Store, City, UserRole, UserPermissions } from '@/types/auth'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import type { ApiStore } from '@/lib/stores-api'

export interface BrandDisplay {
  id: string
  name: string
  logo: React.ElementType | null
  plan: string
  brandId: string
  currency: string
  active: boolean
}

interface PosState {
  isAuthenticated: boolean
  user: User | null
  userRole: UserRole | null
  userPermissions: UserPermissions | null
  jwtToken: string | null

  company: Company | null
  brands: Brand[]
  selectedBrand: BrandDisplay | null

  stores: Store[]
  cities: City[]
  apiStores: ApiStore[]

  isLoading: boolean
  error: string | null

  setAuth: (data: {
    user: User
    userRole: UserRole | null
    userPermissions: UserPermissions | null
    company: Company
    brands: Brand[]
    stores: Store[]
    cities: City[]
    jwtToken: string
  }) => void

  setSelectedBrand: (brand: BrandDisplay) => void
  setApiStores: (stores: ApiStore[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearAuth: () => void

  getActiveBrands: () => Brand[]
  getActiveStores: () => Store[]
  getBrandById: (id: string) => Brand | undefined
  getStoreById: (id: string) => Store | undefined
  getStoresByBrand: (brandId: string) => Store[]
  getApiStoresByBrand: (brandId: string) => ApiStore[]
}

export const usePosStore = create<PosState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      userRole: null,
      userPermissions: null,
      jwtToken: null,
      company: null,
      brands: [],
      selectedBrand: null,
      stores: [],
      cities: [],
      apiStores: [],
      isLoading: false,
      error: null,

      setAuth: data => {
        let selectedBrand = null
        if (data.brands && data.brands.length > 0) {
          const firstBrand = data.brands[0]
          selectedBrand = {
            id: firstBrand.id,
            name: firstBrand.brand_name,
            logo: null, // Will be set by useBrandsData
            plan: `${firstBrand.currency} • ${firstBrand.brand_id}`,
            brandId: firstBrand.brand_id,
            currency: firstBrand.currency,
            active: firstBrand.active === 1
          }
        }

        set({
          isAuthenticated: true,
          user: data.user,
          userRole: data.userRole,
          userPermissions: data.userPermissions,
          company: data.company,
          brands: data.brands,
          stores: data.stores,
          cities: data.cities,
          jwtToken: data.jwtToken,
          selectedBrand: selectedBrand,
          error: null
        })

        window.dispatchEvent(new CustomEvent('posAuthChanged', { detail: data }))
      },

      setSelectedBrand: brand => {
        set({ selectedBrand: brand })

        window.dispatchEvent(new CustomEvent('brandChanged', { detail: brand }))
      },

      setApiStores: stores => {
        set({ apiStores: stores })
      },

      setLoading: loading => {
        set({ isLoading: loading })
      },

      setError: error => {
        set({ error })
      },

      clearAuth: () => {
        set({
          isAuthenticated: false,
          user: null,
          userRole: null,
          userPermissions: null,
          jwtToken: null,
          company: null,
          brands: [],
          selectedBrand: null,
          stores: [],
          cities: [],
          apiStores: [],
          error: null
        })

        // Clear local storage
        localStorage.removeItem('pos_user_data')
        localStorage.removeItem('pos_jwt_token')
        localStorage.removeItem('pos_selected_brand')
        localStorage.removeItem('pos_stores_data')

        window.dispatchEvent(new CustomEvent('posAuthCleared'))
      },

      getActiveBrands: () => {
        const { brands } = get()
        return brands.filter(brand => brand.active === 1)
      },

      getActiveStores: () => {
        const { stores } = get()
        return stores.filter(store => store.active === 1)
      },

      getBrandById: id => {
        const { brands } = get()
        return brands.find(brand => brand.id === id || brand.brand_id === id)
      },

      getStoreById: id => {
        const { stores } = get()
        return stores.find(store => store.id === id || store.store_id === id)
      },

      getStoresByBrand: brandId => {
        const { stores } = get()
        return stores.filter(store => store.brand_uid === brandId)
      },

      getApiStoresByBrand: brandId => {
        const { apiStores } = get()
        return apiStores.filter(store => store.brand_uid === brandId)
      }
    }),
    {
      name: 'pos-store',
      partialize: state => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        userRole: state.userRole,
        userPermissions: state.userPermissions,
        jwtToken: state.jwtToken,
        company: state.company,
        brands: state.brands,
        selectedBrand: state.selectedBrand,
        stores: state.stores,
        cities: state.cities,
        apiStores: state.apiStores
      })
    }
  )
)

export const useCurrentUser = () => {
  const { user, isAuthenticated, userRole, userPermissions, company } = usePosStore()
  return { user, isAuthenticated, userRole, userPermissions, company }
}

export const useCurrentBrand = () => {
  const { selectedBrand, setSelectedBrand, brands, getActiveBrands } = usePosStore()

  React.useEffect(() => {
    if (!selectedBrand && brands.length > 0) {
      const firstBrand = brands[0]
      const brandForSwitcher = {
        id: firstBrand.id,
        name: firstBrand.brand_name,
        logo: null,
        plan: `${firstBrand.currency} • ${firstBrand.brand_id}`,
        brandId: firstBrand.brand_id,
        currency: firstBrand.currency,
        active: firstBrand.active === 1
      }
      setSelectedBrand(brandForSwitcher)
    }
  }, [selectedBrand, brands, setSelectedBrand])

  return {
    selectedBrand,
    setSelectedBrand,
    brands,
    activeBrands: getActiveBrands(),
    isLoading: false
  }
}

export const useCurrentCompany = () => {
  const { company, user } = usePosStore()
  return {
    company,
    companyUid: company?.id || '',
    companyName: company?.company_name || '',
    userId: user?.id || ''
  }
}

export const usePosStores = () => {
  const { stores, apiStores, selectedBrand, getStoresByBrand, getApiStoresByBrand, setApiStores } = usePosStore()

  return {
    stores,
    apiStores,
    selectedBrand,
    getStoresByBrand,
    getApiStoresByBrand,
    setApiStores,
    currentBrandStores: selectedBrand ? getStoresByBrand(selectedBrand.id) : [],
    currentBrandApiStores: selectedBrand ? getApiStoresByBrand(selectedBrand.id) : []
  }
}

export const useCurrentStore = () => {
  const { stores, selectedBrand } = usePosStore()
  const [selectedStore, setSelectedStore] = React.useState<Store | null>(null)

  React.useEffect(() => {
    if (selectedBrand && stores.length > 0) {
      const brandStores = stores.filter(store => store.brand_uid === selectedBrand.id && store.active === 1)
      if (brandStores.length > 0 && !selectedStore) {
        setSelectedStore(brandStores[0])
      }
    }
  }, [selectedBrand, stores, selectedStore])

  return {
    selectedStore,
    setSelectedStore,
    stores: selectedBrand ? stores.filter(store => store.brand_uid === selectedBrand.id) : [],
    isLoading: false
  }
}

export const initializePosStore = (loginResponse: {
  user: User
  user_role: UserRole
  user_permissions: UserPermissions
  company: Company
  brands: Brand[]
  stores: Store[]
  cities: City[]
  token: string
}) => {
  const { setAuth } = usePosStore.getState()

  setAuth({
    user: loginResponse.user,
    userRole: loginResponse.user_role,
    userPermissions: loginResponse.user_permissions,
    company: loginResponse.company,
    brands: loginResponse.brands,
    stores: loginResponse.stores,
    cities: loginResponse.cities,
    jwtToken: loginResponse.token
  })
}

export const initializePosStoreFromLocalStorage = () => {
  try {
    const userData = localStorage.getItem('pos_user_data')
    const jwtToken = localStorage.getItem('pos_jwt_token')
    const userRoleData = localStorage.getItem('pos_user_role_data')
    const companyData = localStorage.getItem('pos_company_data')
    const brandsData = localStorage.getItem('pos_brands_data')
    const storesData = localStorage.getItem('pos_stores_data')
    const citiesData = localStorage.getItem('pos_cities_data')

    if (userData && jwtToken && companyData && brandsData) {
      const { setAuth } = usePosStore.getState()

      setAuth({
        user: JSON.parse(userData),
        userRole: userRoleData ? JSON.parse(userRoleData) : null,
        userPermissions: null,
        company: JSON.parse(companyData),
        brands: JSON.parse(brandsData),
        stores: storesData ? JSON.parse(storesData) : [],
        cities: citiesData ? JSON.parse(citiesData) : [],
        jwtToken: jwtToken
      })

      return true
    }
  } catch (_error) {}
  return false
}
