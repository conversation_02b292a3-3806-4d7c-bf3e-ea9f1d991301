// Customization option group interface
export interface CustomizationOptionGroup {
  id: string
  Name: string
  LstItem_Id: string[]
  Max_Permitted: number
  Min_Permitted: number
}

// Customization data structure
export interface CustomizationData {
  LstItem_Options: CustomizationOptionGroup[]
}

// API response interface for customization
export interface CustomizationApiData {
  id: string
  name: string
  data: CustomizationData
  extra_data: Record<string, unknown>
  active: number
  revision: number
  sort: number
  brand_uid: string
  company_uid: string
  is_customization_in_store: number
  updated_at: number
  city_uid: string
  city_name: string
  store_uid?: string
  customization_cloned_id: string | null
  list_item: string[]
}

// Converted customization interface for frontend use
export interface Customization {
  id: string
  name: string
  data: CustomizationData
  extraData: Record<string, unknown>
  active: number
  revision: number
  sort: number
  brandUid: string
  companyUid: string
  isCustomizationInStore: number
  updatedAt: number
  cityUid: string
  cityName: string
  storeUid?: string
  storeName?: string
  customizationClonedId: string | null
  listItem: string[]
}

// API response wrapper
export interface CustomizationsApiResponse {
  data: CustomizationApiData[]
  track_id: string
}

// Parameters for fetching customizations
export interface GetCustomizationsParams {
  searchTerm?: string
  list_city_uid?: string[]
  list_store_uid?: string[]
  storeName?: string
  skip_limit?: boolean
  store_uid?: string
  page?: number
  limit?: number
}

// Parameters for copying customization
export interface CopyCustomizationParams {
  customizationId: string
  newName: string
  targetCityUid?: string
  targetStoreUid?: string
}

// Parameters for creating new customization
export interface CreateCustomizationParams {
  name: string
  cityUid?: string
  storeUid?: string
  data: CustomizationData
  listItem: string[]
  sort?: number
  isUpdateSameCustomization?: boolean
}

// Parsed customization data from Excel import
export interface ParsedCustomizationData {
  id: string
  name: string
  cityName: string
  storeName?: string
  appliedItemCodes: string
  groupName: string
  minRequired: number
  maxAllowed: number
  groupItemCodes: string
}

// Convert API data to frontend format
export const convertApiCustomizationToCustomization = (apiCustomization: CustomizationApiData): Customization => {
  return {
    id: apiCustomization.id,
    name: apiCustomization.name,
    data: apiCustomization.data,
    extraData: apiCustomization.extra_data,
    active: apiCustomization.active,
    revision: apiCustomization.revision,
    sort: apiCustomization.sort,
    brandUid: apiCustomization.brand_uid,
    companyUid: apiCustomization.company_uid,
    isCustomizationInStore: apiCustomization.is_customization_in_store,
    updatedAt: apiCustomization.updated_at,
    cityUid: apiCustomization.city_uid,
    cityName: apiCustomization.city_name,
    storeUid: apiCustomization.store_uid,
    customizationClonedId: apiCustomization.customization_cloned_id,
    listItem: apiCustomization.list_item
  }
}

export interface ExistingCustomization {
  extra_data?: Record<string, unknown>
  extraData?: Record<string, unknown>
  active?: number
  revision?: number
  is_customization_in_store?: number
  isCustomizationInStore?: number
  customization_cloned_id?: string | null
  customizationClonedId?: string | null
  is_fabi?: number
  created_at?: string
  updated_at?: number
  updatedAt?: number
  city_uid?: string
  cityUid?: string
  store_uid?: string
  storeUid?: string
  created_by?: string
  updated_by?: string
  deleted_by?: string | null
  deleted_at?: string | null
  deleted?: boolean
  [key: string]: unknown
}
