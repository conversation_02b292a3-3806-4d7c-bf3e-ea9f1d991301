import { StatusBadge } from '@/components/pos'

import { STORE_CONSTANTS, STORE_STATUS } from '../../../../data'

interface StatusCellProps {
  store: any
}

export function StatusCell({ store }: StatusCellProps) {
  const isActive = store.active === STORE_STATUS.ACTIVE || store.status === STORE_STATUS.ACTIVE_TEXT

  return (
    <StatusBadge
      isActive={isActive}
      activeText={STORE_CONSTANTS.BUTTON_ACTIVE}
      inactiveText={STORE_CONSTANTS.BUTTON_DEACTIVE}
    />
  )
}
