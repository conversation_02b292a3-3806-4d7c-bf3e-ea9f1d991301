import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

import type { DiscountApiData } from '@/types/discounts'

import { api } from '@/lib/api/pos/pos-api'
import { getErrorMessage } from '@/utils/error-utils'

import { QUERY_KEYS } from '@/constants/query-keys'

interface DiscountProgramsParams {
  companyUid?: string
  brandUid?: string
  storeUid?: string
  enabled?: boolean
  includePromotionPartnerAutoGen?: boolean
}

interface DiscountProgramsResponse {
  data: DiscountApiData[]
  track_id: string
}

interface CloneDiscountParams {
  companyUid: string
  brandUid: string
  listDiscountUid: string[]
  sourceStore: string
  targetStore: string
}

interface CloneDiscountResponse {
  success: boolean
  message?: string
  track_id: string
}

export function useDiscountPrograms({
  companyUid,
  brandUid,
  storeUid,
  enabled = true,
  includePromotionPartnerAutoGen = true
}: DiscountProgramsParams) {
  return useQuery({
    queryKey: [QUERY_KEYS.DISCOUNTS, 'programs', companyUid, brandUid, storeUid, includePromotionPartnerAutoGen],
    queryFn: async (): Promise<DiscountProgramsResponse> => {
      if (!companyUid || !brandUid || !storeUid) {
        throw new Error('Missing required parameters')
      }

      const params = new URLSearchParams({
        skip_limit: 'true',
        company_uid: companyUid,
        brand_uid: brandUid,
        store_uid: storeUid
      })

      if (includePromotionPartnerAutoGen) {
        params.append('promotion_partner_auto_gen', '1')
      }

      const response = await api.get(`/mdata/v1/discounts?${params.toString()}`)
      return response.data as DiscountProgramsResponse
    },
    enabled: enabled && !!companyUid && !!brandUid && !!storeUid
  })
}

export function useCloneDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: CloneDiscountParams): Promise<CloneDiscountResponse> => {
      const payload = {
        company_uid: params.companyUid,
        brand_uid: params.brandUid,
        list_discount_uid: params.listDiscountUid,
        source_store: params.sourceStore,
        target_store: params.targetStore
      }

      const response = await api.post('/mdata/v1/clone_discount', payload)
      return response.data as unknown as CloneDiscountResponse
    },
    onSuccess: () => {
      // Invalidate all discount queries to refresh data
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DISCOUNTS] })
    },
    onError: (error: unknown) => {
      const errorMessage = getErrorMessage(error, 'Lỗi khi sao chép chương trình giảm giá')
      toast.error(errorMessage)
    }
  })
}
