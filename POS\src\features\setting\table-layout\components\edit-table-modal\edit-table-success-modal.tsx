import React from 'react'

import { Check } from 'lucide-react'

import { <PERSON>ton, <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui'

interface EditTableSuccessModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export const EditTableSuccessModal: React.FC<EditTableSuccessModalProps> = ({ open, onOpenChange }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-center text-xl font-semibold'>Sửa thông tin bàn</DialogTitle>
        </DialogHeader>

        <div className='flex flex-col items-center space-y-6 py-8'>
          {/* Success Icon */}
          <div className='flex h-20 w-20 items-center justify-center rounded-full bg-green-100'>
            <div className='flex h-16 w-16 items-center justify-center rounded-full bg-green-500'>
              <Check className='h-8 w-8 text-white' strokeWidth={3} />
            </div>
          </div>

          {/* Success Message */}
          <div className='text-center'>
            <h3 className='text-lg font-medium text-green-600'>Tải lên thành công</h3>
          </div>

          {/* Close Button */}
          <Button onClick={() => onOpenChange(false)} variant='outline' className='px-8'>
            Đóng
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
