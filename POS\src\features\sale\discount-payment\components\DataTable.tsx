import { useNavigate } from '@tanstack/react-router'

import type { DiscountPayment } from '@/types/discount-payment'
import { Trash2 } from 'lucide-react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

interface Store {
  id: string
  store_name: string
}

interface DataTableProps {
  discountPayments: DiscountPayment[]
  isLoading: boolean
  currentBrandStores: Store[]
  onToggleActive: (payment: DiscountPayment) => void
  onDelete: (payment: DiscountPayment) => void
  isDeleting: boolean
}

export function DataTable({
  discountPayments,
  isLoading,
  currentBrandStores,
  onToggleActive,
  onDelete,
  isDeleting
}: DataTableProps) {
  const navigate = useNavigate()

  const getStoreName = (storeUid: string) => {
    const store = currentBrandStores.find(s => s.id === storeUid)
    return store?.store_name || 'Không xác định'
  }

  const handleRowClick = (paymentId: string) => {
    navigate({
      to: '/sale/discount-payment/detail/$id',
      params: { id: paymentId }
    })
  }

  const formatDateRange = (fromDate: number, toDate: number) => {
    const from = new Date(fromDate).toLocaleDateString('vi-VN')
    const to = new Date(toDate).toLocaleDateString('vi-VN')
    return `${from} - ${to}`
  }

  const formatAmount = (payment: DiscountPayment) => {
    if (payment.type === 'PERCENT') {
      // Convert 0.25 to 25%
      return `${payment.value * 100}%`
    }
    return `${payment.value.toLocaleString('vi-VN')} ₫`
  }

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-[50px]'>#</TableHead>
            <TableHead>Tên</TableHead>
            <TableHead>Cửa hàng</TableHead>
            <TableHead>Thời gian áp dụng</TableHead>
            <TableHead>Số tiền</TableHead>
            <TableHead>Thao tác</TableHead>
            <TableHead className='w-[50px]'></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={7} className='h-24 text-center'>
                Đang tải dữ liệu...
              </TableCell>
            </TableRow>
          ) : discountPayments.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className='text-muted-foreground h-24 text-center'>
                Không có dữ liệu
              </TableCell>
            </TableRow>
          ) : (
            discountPayments.map((payment, index) => (
              <TableRow
                key={payment.id}
                className='hover:bg-muted/50 cursor-pointer'
                onClick={() => handleRowClick(payment.id)}
              >
                <TableCell className='font-medium'>{index + 1}</TableCell>
                <TableCell>
                  <div className='font-medium'>{payment.promotion?.promotion_name || 'N/A'}</div>
                </TableCell>
                <TableCell>{getStoreName(payment.store_uid)}</TableCell>
                <TableCell>
                  <div className='space-y-1'>
                    {/* Check if expired */}
                    {payment.to_date < Date.now() ? (
                      <div className='font-medium text-red-600'>Hết hạn</div>
                    ) : (
                      <div className='text-sm'>{formatDateRange(payment.from_date, payment.to_date)}</div>
                    )}
                  </div>
                </TableCell>
                <TableCell className='font-medium'>{formatAmount(payment)}</TableCell>
                <TableCell>
                  {payment.active === 1 ? (
                    <Badge
                      variant='default'
                      className='cursor-pointer bg-green-100 text-green-800 hover:bg-green-200'
                      onClick={e => {
                        e.stopPropagation()
                        onToggleActive(payment)
                      }}
                    >
                      Active
                    </Badge>
                  ) : (
                    <Badge
                      variant='destructive'
                      className='cursor-pointer bg-red-100 text-red-800 hover:bg-red-200'
                      onClick={e => {
                        e.stopPropagation()
                        onToggleActive(payment)
                      }}
                    >
                      Deactive
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={e => {
                      e.stopPropagation()
                      onDelete(payment)
                    }}
                    className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
                    disabled={isDeleting}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
