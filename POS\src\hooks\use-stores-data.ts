import React from 'react'
import {
  fetchAndSyncStores,
  getStoredApiStores,
  getStoredApiStoresByBrand,
  type ApiStore,
} from '@/lib/stores-api'

interface UseStoresDataOptions {
  companyUid?: string
  brandUid?: string
  autoFetch?: boolean
}

export function useStoresData(options: UseStoresDataOptions = {}) {
  const { companyUid, brandUid, autoFetch = false } = options
  const [stores, setStores] = React.useState<ApiStore[]>([])
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [lastFetch, setLastFetch] = React.useState<Date | null>(null)

  // Load stores from localStorage on mount
  React.useEffect(() => {
    const storedStores = getStoredApiStores()
    setStores(storedStores)
  }, [])

  const fetchStores = React.useCallback(async () => {
    if (!companyUid || !brandUid) {
      const errorMsg = 'Company UID and Brand UID are required'
      setError(errorMsg)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const fetchedStores = await fetchAndSyncStores(companyUid, brandUid)
      setStores(fetchedStores)
      setLastFetch(new Date())
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch stores'
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [companyUid, brandUid])

  // Auto-fetch stores when brand/company changes
  React.useEffect(() => {
    if (autoFetch && companyUid && brandUid) {
      fetchStores()
    }
  }, [companyUid, brandUid, autoFetch, fetchStores])

  const refreshStores = React.useCallback(() => {
    const storedStores = getStoredApiStores()
    setStores(storedStores)
  }, [])

  const getStoresByBrand = React.useCallback((brandId: string) => {
    return getStoredApiStoresByBrand(brandId)
  }, [])

  const getStoreById = React.useCallback(
    (storeId: string) => {
      return stores.find(
        (store) => store.id === storeId || store.store_id === storeId
      )
    },
    [stores]
  )

  const getActiveStores = React.useCallback(() => {
    return stores.filter((store) => store.active === 1)
  }, [stores])

  const getStoresByCity = React.useCallback(
    (cityName: string) => {
      return stores.filter((store) => store.city_name === cityName)
    },
    [stores]
  )

  const searchStores = React.useCallback(
    (query: string) => {
      const searchTerm = query.toLowerCase()
      return stores.filter(
        (store) =>
          store.store_name.toLowerCase().includes(searchTerm) ||
          store.store_id.toLowerCase().includes(searchTerm) ||
          store.address.toLowerCase().includes(searchTerm) ||
          store.phone.includes(searchTerm)
      )
    },
    [stores]
  )

  const getStoreStats = React.useCallback(() => {
    const activeStores = stores.filter((store) => store.active === 1)
    const cities = [...new Set(stores.map((store) => store.city_name))]

    return {
      total: stores.length,
      active: activeStores.length,
      inactive: stores.length - activeStores.length,
      cities: cities.length,
      cityList: cities.sort(),
    }
  }, [stores])

  return {
    // Data
    stores,
    isLoading,
    error,
    lastFetch,

    // Actions
    fetchStores,
    refreshStores,

    // Getters
    getStoresByBrand,
    getStoreById,
    getActiveStores,
    getStoresByCity,
    searchStores,
    getStoreStats,

    // Computed
    hasStores: stores.length > 0,
    isEmpty: stores.length === 0,
    canFetch: Boolean(companyUid && brandUid),
  }
}

/**
 * Hook to get stores for the current brand
 */
export function useCurrentBrandStores() {
  const [companyUid, setCompanyUid] = React.useState<string>('')
  const [brandUid, setBrandUid] = React.useState<string>('')

  // Function to get current brand and company UIDs
  const getCurrentUIDs = React.useCallback(() => {
    try {
      // Get user data for company UID
      const userData = localStorage.getItem('pos_user_data')
      let companyId = ''
      if (userData) {
        const parsed = JSON.parse(userData)
        companyId = parsed.company?.id || parsed.company_uid || ''
      }

      // Get selected brand for brand UID
      const selectedBrand = localStorage.getItem('pos_selected_brand')
      let brandId = ''
      if (selectedBrand) {
        const parsed = JSON.parse(selectedBrand)
        brandId = parsed.id || parsed.brandId || ''
      }

      // If we still don't have company UID, try other localStorage keys
      if (!companyId) {
        // Try to get from login response or other stored data
        const loginData = localStorage.getItem('pos_login_response')
        if (loginData) {
          const parsed = JSON.parse(loginData)
          companyId = parsed.company?.id || ''
        }
      }

      // Use the values from your curl request as fallback
      const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
      const fallbackBrandUid = '8b8f15f9-6986-4c5a-86bc-f7dba8966659'

      // Set final values
      const finalCompanyUid = companyId || fallbackCompanyUid
      const finalBrandUid = brandId || fallbackBrandUid

      return { companyUid: finalCompanyUid, brandUid: finalBrandUid }
    } catch {
      // Use fallback values from your curl request
      const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
      const fallbackBrandUid = '8b8f15f9-6986-4c5a-86bc-f7dba8966659'

      return { companyUid: fallbackCompanyUid, brandUid: fallbackBrandUid }
    }
  }, [])

  // Update UIDs when localStorage changes (e.g., when brand is switched)
  React.useEffect(() => {
    const updateUIDs = () => {
      const { companyUid: newCompanyUid, brandUid: newBrandUid } =
        getCurrentUIDs()
      setCompanyUid(newCompanyUid)
      setBrandUid(newBrandUid)
    }

    // Initial load
    updateUIDs()

    // Listen for localStorage changes (when brand is switched)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'pos_selected_brand' || e.key === 'pos_user_data') {
        updateUIDs()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Also listen for custom events when brand is changed in the same tab
    const handleBrandChange = () => {
      updateUIDs()
    }

    window.addEventListener('brandChanged', handleBrandChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('brandChanged', handleBrandChange)
    }
  }, [getCurrentUIDs])

  const storesData = useStoresData({
    companyUid,
    brandUid,
    autoFetch: true,
  })

  return storesData
}
