import { useEffect } from 'react'

import type { TableLayoutItem, AreaOption } from '../data/table-layout-types'

interface UseTableLayoutEffectsProps {
  allStores: any[] | undefined
  selectedStoreId: string
  setSelectedStoreId: (storeId: string) => void
  areas: AreaOption[]
  selectedAreaId: string
  setSelectedAreaId: (areaId: string) => void
  tables: any[]
  setLocalTables: React.Dispatch<React.SetStateAction<TableLayoutItem[]>>
  lastTablesRef: React.MutableRefObject<string>
}

export const useTableLayoutEffects = ({
  allStores,
  selectedStoreId,
  setSelectedStoreId,
  areas,
  selectedAreaId,
  setSelectedAreaId,
  tables,
  setLocalTables,
  lastTablesRef
}: UseTableLayoutEffectsProps) => {
  // Auto-select first store when stores are loaded
  useEffect(() => {
    if (allStores && allStores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(allStores[0].id)
    }
  }, [allStores, selectedStoreId, setSelectedStoreId])

  // Auto-select first area when areas are loaded
  useEffect(() => {
    if (areas.length > 0 && !selectedAreaId) {
      setSelectedAreaId(areas[0].id)
    }
  }, [areas, selectedAreaId, setSelectedAreaId])

  // Transform tables data when tables change
  useEffect(() => {
    const currentTablesKey = JSON.stringify(
      tables.map(t => ({ id: t.id, position_x: t.position_x, position_y: t.position_y }))
    )

    if (lastTablesRef.current === currentTablesKey) {
      return
    }

    lastTablesRef.current = currentTablesKey

    if (tables.length === 0) {
      setLocalTables([])
      return
    }

    const tableSize = 180
    const spacing = 60
    const startX = 50
    const startY = 50
    const tablesPerRow = 5

    const transformedTables = tables.map((table, index) => {
      const hasPosition = table.position_x !== undefined && table.position_y !== undefined
      const hasSavedPosition =
        'extra_data' in table &&
        table.extra_data?.position?.x !== undefined &&
        table.extra_data?.position?.y !== undefined

      let x, y
      if (hasPosition) {
        x = table.position_x!
        y = table.position_y!
      } else if (hasSavedPosition) {
        x = table.extra_data!.position!.x
        y = table.extra_data!.position!.y
      } else {
        const row = Math.floor(index / tablesPerRow)
        const col = index % tablesPerRow
        x = startX + col * (tableSize + spacing)
        y = startY + row * (tableSize + spacing)
      }

      return {
        ...table,
        position: { x, y },
        size: {
          width: table.width || ('extra_data' in table ? table.extra_data?.size?.width : undefined) || tableSize,
          height: table.height || ('extra_data' in table ? table.extra_data?.size?.height : undefined) || tableSize
        }
      }
    })

    setLocalTables(transformedTables)
  }, [tables, selectedAreaId, selectedStoreId, setLocalTables, lastTablesRef])
}
