import React from 'react'

import type { Area } from '@/lib/api'

import { DraggableAreaItem } from './draggable-area-item'

interface AreasListProps {
  areas: Area[]
  isLoading: boolean
  selectedAreaId: string
  draggedIndex: number | null
  onAreaClick: (areaId: string) => void
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
}

export const AreasList: React.FC<AreasListProps> = ({
  areas,
  isLoading,
  selectedAreaId,
  draggedIndex,
  onAreaClick,
  onDragStart,
  onDragOver,
  onDrop
}) => {
  return (
    <div className='flex h-full w-1/3 min-w-0 flex-col'>
      <h3 className='mb-2 flex-shrink-0 truncate font-medium text-gray-900'>Danh sách khu vực</h3>
      <div className='min-h-0 flex-1 space-y-2 overflow-x-hidden overflow-y-auto pr-2'>
        {isLoading ? (
          <div className='flex h-full items-center justify-center text-gray-500'>Đang tải khu vực...</div>
        ) : areas.length === 0 ? (
          <div className='flex h-full items-center justify-center text-gray-500'>Không có khu vực nào</div>
        ) : (
          areas.map((area, index) => (
            <div key={area.id} onClick={() => onAreaClick(area.id)}>
              <DraggableAreaItem
                area={area}
                index={index}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDrop={onDrop}
                isDragging={draggedIndex === index}
                isSelected={selectedAreaId === area.id}
              />
            </div>
          ))
        )}
      </div>
    </div>
  )
}
