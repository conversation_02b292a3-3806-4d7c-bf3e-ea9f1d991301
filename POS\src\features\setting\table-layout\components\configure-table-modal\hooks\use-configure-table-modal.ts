import { useConfigureTableActions } from './use-configure-table-actions'
import { useConfigureTableState } from './use-configure-table-state'

export const useConfigureTableModal = () => {
  const state = useConfigureTableState()

  const actions = useConfigureTableActions({
    selectedStoreId: state.selectedStoreId,
    selectedAreaId: state.selectedAreaId,
    searchTerm: state.searchTerm,
    selectedTables: state.selectedTables,
    config: state.config,
    setSelectedStoreId: state.setSelectedStoreId,
    setSelectedAreaId: state.setSelectedAreaId,
    setSelectedTables: state.setSelectedTables,
    setSearchTerm: state.setSearchTerm,
    setSelectAllInArea: state.setSelectAllInArea,
    setApplyToAllTables: state.setApplyToAllTables
  })

  const handleClose = async () => {
    state.resetState()
    return true
  }

  const handleSave = async () => {
    const success = await actions.handleSave()
    if (success) {
      state.resetState()
    }
    return success
  }

  return {
    // State
    ...state,

    // Actions
    ...actions,

    // Modal actions
    handleClose,
    handleSave: handleSave
  }
}
