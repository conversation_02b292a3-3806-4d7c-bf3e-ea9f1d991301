import { useState } from 'react'

import { IconTrash } from '@tabler/icons-react'

import { Channel } from '@/types/channels'
import { toast } from 'sonner'

import { channelsApi } from '@/lib/channels-api'

import { Button } from '@/components/ui/button'

import { ConfirmDialog } from '@/components/confirm-dialog'

import { getApiErrorMessage, getErrorTrackId } from '../../detail/utils'

interface DeleteChannelButtonProps {
  channel: Channel
}

export function DeleteChannelButton({ channel }: DeleteChannelButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click when clicking delete button
    e.preventDefault() // Prevent any default behavior
    e.nativeEvent.stopImmediatePropagation() // Stop all event propagation
    setIsDialogOpen(true)
  }

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open)
  }

  const handleConfirmDelete = async () => {
    setIsDeleting(true)

    try {
      // Call delete API
      await channelsApi.deleteChannel(channel.id)

      // Show success message
      toast.success('Xóa kênh bán hàng thành công!')

      // Close dialog
      setIsDialogOpen(false)

      // Reload the page to refresh the data
      window.location.reload()
    } catch (error) {
      // Use the new error handler to extract message
      const errorMessage = getApiErrorMessage(error)
      const trackId = getErrorTrackId(error)

      console.error('Error deleting channel:', error)
      if (trackId) {
        console.error('Track ID:', trackId)
      }

      toast.error(errorMessage)

      // Close dialog even when error occurs
      setIsDialogOpen(false)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div onClick={e => e.stopPropagation()}>
      <Button
        variant='ghost'
        size='sm'
        onClick={handleDeleteClick}
        className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
      >
        <IconTrash className='h-4 w-4' />
      </Button>

      <ConfirmDialog
        open={isDialogOpen}
        onOpenChange={handleDialogOpenChange}
        title='Xác nhận xóa kênh bán hàng'
        desc={`Bạn có chắc chắn muốn xóa kênh "${channel.source_name}"? Hành động này không thể hoàn tác.`}
        confirmText='Xóa'
        cancelBtnText='Hủy'
        destructive={true}
        isLoading={isDeleting}
        handleConfirm={handleConfirmDelete}
      />
    </div>
  )
}
