import type {
  User,
  GetUsersParams,
  UsersApiResponse,
  CreateUserPermissions,
  CreateUserParams,
  CreateUserResponse,
  UpdateUserParams,
  UpdateUserProfileParams,
  UpdateUserResponse,
  DeactivateUserResponse,
  ApiUser,
  ApiUserRole,
  ApiUserPermissions,
  UserApiResponse
} from '@/types'
// Import types from auth-api for proper typing
import type { Brand, City, Store } from '@/types/auth'

import { api } from './api/pos/pos-api'

export type {
  GetUsersParams,
  User,
  CreateUserPermissions,
  CreateUserParams,
  CreateUserResponse,
  UpdateUserParams,
  UpdateUserProfileParams,
  UpdateUserResponse,
  DeactivateUserResponse
}

/**
 * Users API Service
 */
export const usersApi = {
  /**
   * Create a new user
   */
  createUser: async (params: CreateUserParams): Promise<CreateUserResponse> => {
    const response = await api.post<CreateUserResponse>('/accounts/v1/users', params)
    return response.data.data || response.data
  },

  /**
   * Get a single user by ID
   */
  getUser: async (userId: string): Promise<User> => {
    const response = await api.get<UserApiResponse>(`/accounts/v1/user?user_uid=${userId}`)

    // Extract user data from the nested structure
    // response.data is ApiResponse<UserApiResponse>, so the actual data is in response.data.data
    const apiResponseData = response.data.data || response.data
    const apiUserData = (apiResponseData?.user ||
      apiResponseData?.data?.user ||
      apiResponseData?.data ||
      apiResponseData) as ApiUser
    const userRole = (apiResponseData?.user_role || apiResponseData?.data?.user_role) as ApiUserRole | undefined
    const userPermissions = (apiResponseData?.user_permissions || apiResponseData?.data?.user_permissions) as
      | ApiUserPermissions
      | undefined

    // Extract additional data from API response
    const brands = (apiResponseData?.brands || apiResponseData?.data?.brands) as Brand[] | undefined
    const cities = (apiResponseData?.cities || apiResponseData?.data?.cities) as City[] | undefined
    const stores = (apiResponseData?.stores || apiResponseData?.data?.stores) as Store[] | undefined

    // Extract brand access from user permissions
    const brandAccess: string[] = []
    if (userPermissions?.stores && Object.keys(userPermissions.stores).length > 0) {
      // The stores object structure is: { brandId: { cityId: [storeIds] } }
      // We need to extract actual store IDs from the nested structure
      Object.keys(userPermissions.stores).forEach(brandId => {
        const brandStores = userPermissions.stores[brandId]
        if (brandStores && typeof brandStores === 'object') {
          Object.keys(brandStores).forEach(cityId => {
            const cityStores = brandStores[cityId]
            if (Array.isArray(cityStores)) {
              cityStores.forEach(storeId => {
                if (typeof storeId === 'string') {
                  brandAccess.push(storeId.startsWith('store:') ? storeId : `store:${storeId}`)
                }
              })
            }
          })
        }
      })

      // If no specific stores found, use brand-level access
      if (brandAccess.length === 0) {
        Object.keys(userPermissions.stores).forEach(brandId => {
          brandAccess.push(brandId.startsWith('store:') ? brandId : `store:${brandId}`)
        })
      }
    } else {
      // If no specific store permissions, check if user has access to all stores based on role
      // For now, we'll leave this empty and let the form handle it
      // This could be enhanced based on business logic
    }

    // Map API response to our User interface
    const userData: User = {
      id: apiUserData.id,
      email: apiUserData.email,
      full_name: apiUserData.full_name,
      phone: apiUserData.phone || '',
      role_uid: apiUserData.role_uid,
      active: apiUserData.active,
      phone_verified_at: apiUserData.phone_verified_at,
      role_name: userRole?.role_name || '',
      role_id: userRole?.role_id || '',
      role_description: userRole?.description || '',
      stores: userPermissions?.stores || {},
      brand_access: brandAccess,
      user_permissions: userPermissions
        ? {
            id: userPermissions.id,
            user_uid: userPermissions.user_uid,
            company_uid: userPermissions.company_uid,
            stores: userPermissions.stores,
            tables: userPermissions.tables
          }
        : undefined,
      // Include additional data for display purposes
      brands: brands,
      cities: cities,
      storeDetails: stores
    }

    return userData
  },

  /**
   * Update a user (legacy endpoint)
   */
  updateUser: async (userId: string, params: UpdateUserParams): Promise<UpdateUserResponse> => {
    const response = await api.put<UpdateUserResponse>(`/accounts/v1/users/${userId}`, params)
    return response.data.data || response.data
  },

  /**
   * Update user profile (new endpoint matching actual API)
   */
  updateUserProfile: async (params: UpdateUserProfileParams): Promise<UpdateUserResponse> => {
    const response = await api.put<UpdateUserResponse>('/accounts/v1/user', params)
    return response.data.data || response.data
  },

  /**
   * Deactivate a user (legacy endpoint)
   */
  deactivateUser: async (userId: string): Promise<DeactivateUserResponse> => {
    const response = await api.patch<DeactivateUserResponse>(`/accounts/v1/users/${userId}/deactivate`)
    return response.data.data || response.data
  },

  /**
   * Activate a user (new endpoint)
   */
  activateUser: async (userId: string): Promise<DeactivateUserResponse> => {
    const response = await api.patch<DeactivateUserResponse>(`/accounts/v1/user?user_uid=${userId}&active=1`)
    return response.data.data || response.data
  },

  /**
   * Deactivate a user (new endpoint)
   */
  deactivateUserNew: async (userId: string): Promise<DeactivateUserResponse> => {
    const response = await api.patch<DeactivateUserResponse>(`/accounts/v1/user?user_uid=${userId}&active=0`)
    return response.data.data || response.data
  },

  /**
   * Change user password
   */
  changePassword: async (params: {
    user_uid: string
    new_password: string
    confirm_password: string
  }): Promise<{ message: string }> => {
    const response = await api.post<{ message: string }>('/accounts/v1/user/change-password', params)
    return response.data.data || response.data
  },

  /**
   * Get list of users
   */
  getUsers: async (params: GetUsersParams = {}): Promise<UsersApiResponse> => {
    const searchParams = new URLSearchParams()

    if (params.company_uid) {
      searchParams.append('company_uid', params.company_uid)
    }
    if (params.active !== undefined) {
      searchParams.append('active', params.active.toString())
    }
    if (params.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params.search) {
      searchParams.append('search', params.search)
    }
    if (params.brand_uid && params.brand_uid !== 'all') {
      searchParams.append('brand_uid', params.brand_uid)
    }
    if (params.city_uid && params.city_uid !== 'all') {
      searchParams.append('city_uid', params.city_uid)
    }
    if (params.store_uid && params.store_uid !== 'all') {
      searchParams.append('store_uid', params.store_uid)
    }

    const url = `/accounts/v1/users${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

    const response = await api.get<User[]>(url)

    // Handle different response formats
    if (Array.isArray(response.data)) {
      return {
        data: response.data,
        total: response.data.length
      }
    }

    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data as UsersApiResponse
    }

    return {
      data: [],
      total: 0
    }
  }
}
