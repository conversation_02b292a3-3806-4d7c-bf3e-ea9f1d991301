export interface Role {
  id: string
  role_id: string
  role_name: string
  description: string
  scope: string
  scope_value: string | null
  allow_access: string[]
  reject_permissions: string[]
  created_at: string
  updated_at: string
  deleted_at: string | null
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  deleted: boolean
}

export interface RolesResponse {
  data: Role[]
  track_id: string
}

export interface CreateRoleRequest {
  allow_access: string[]
  role_name: string
  role_id: string
  description: string
  reject_permissions: string[]
  scope: string
  scope_value?: string | null
}

export interface UpdateRoleRequest extends CreateRoleRequest {
  id: string
}

export interface RolePermission {
  id: string
  permission_name: string
  permission_key: string
  description: string
  category: string
  is_system: boolean
}

export interface RolePermissionsResponse {
  data: RolePermission[]
  total?: number
}

// Role scope types
export type RoleScope = 'global' | 'company' | 'brand' | 'city' | 'store' | 'custom'

export interface RoleScopeOption {
  value: RoleScope
  label: string
  description: string
}

// Common role names (can be extended based on business needs)
export type CommonRoleName =
  | 'super_admin'
  | 'company_admin'
  | 'brand_manager'
  | 'store_manager'
  | 'cashier'
  | 'kitchen_staff'
  | 'waiter'
  | 'accountant'
  | 'viewer'

// Role assignment types
export interface UserRoleAssignment {
  user_id: string
  role_id: string
  assigned_at: string
  assigned_by: string
  scope_value?: string
}

export interface AssignRoleParams {
  user_id: string
  role_id: string
  scope_value?: string
}

export interface AssignRoleResponse {
  assignment: UserRoleAssignment
  message?: string
}

// Role hierarchy types
export interface RoleHierarchy {
  role_id: string
  parent_role_id: string | null
  level: number
  path: string[]
}

// Role validation types
export interface RoleValidationError {
  field: string
  message: string
  code: string
}

export interface RoleValidationResult {
  isValid: boolean
  errors: RoleValidationError[]
}