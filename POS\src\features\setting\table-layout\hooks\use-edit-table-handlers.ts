import ExcelJS from 'exceljs'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { sourcesApi } from '@/lib/sources-api'
import { tableLayoutApi } from '@/lib/table-layout-api'

import type { TableLayoutItem } from '../data/table-layout-types'

interface UseEditTableHandlersProps {
  localTables: TableLayoutItem[]
  setIsEditTableModalOpen: (open: boolean) => void
}

export const useEditTableHandlers = ({ localTables, setIsEditTableModalOpen }: UseEditTableHandlersProps) => {
  const { auth } = useAuthStore()

  const handleEditTableInfo = () => {
    setIsEditTableModalOpen(true)
  }

  const handleDownloadExistingTableData = async (storeId: string) => {
    if (!storeId) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    try {
      toast.info('Đang tải dữ liệu bàn...')

      let tables: any[] = []
      let sources: any[] = []

      // Try to get data from API if auth is available
      if (auth.company?.id && auth.brands?.[0]?.id) {
        try {
          // Get tables data
          const tablesResponse = await tableLayoutApi.getTables({
            skip_limit: true,
            company_uid: auth.company.id,
            brand_uid: auth.brands[0].id,
            store_uid: storeId,
            area_uid: '' // Empty to get all areas
          })
          tables = tablesResponse.data || []

          // Get sources data
          const sourcesResponse = await sourcesApi.getSources({
            skip_limit: true,
            company_uid: auth.company.id,
            brand_uid: auth.brands[0].id,
            store_uid: storeId
          })
          sources = sourcesResponse || []
        } catch (apiError) {
          console.warn('API call failed, using local data:', apiError)
          tables = localTables
          sources = []
        }
      } else {
        // Use sample data for demo
        tables = localTables.length > 0 ? localTables : getSampleTables(storeId)
        sources = getSampleSources()
      }

      await createExcelFile(tables, sources)
      toast.success(`Đã tải xuống dữ liệu ${tables.length} bàn`)
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu bàn:', error)
      toast.error('Không thể tải dữ liệu bàn. Vui lòng thử lại.')
    }
  }

  const handleUploadEditedTableFile = (_storeId: string) => {
    // This function is now handled by useEditTablePreview hook
    // Keep for backward compatibility but functionality moved to preview hook
    console.warn('handleUploadEditedTableFile is deprecated. Use useEditTablePreview hook instead.')
  }

  return {
    handleEditTableInfo,
    handleDownloadExistingTableData,
    handleUploadEditedTableFile
  }
}

// Helper functions
const getSampleTables = (storeId: string) => [
  {
    id: 'table-001',
    table_name: 'Bàn 01',
    table_id: 'B01',
    description: 'Bàn gần cửa sổ',
    source_id: '10000045',
    area_uid: 'area-001',
    store_uid: storeId,
    company_uid: '',
    brand_uid: '',
    sort: 1,
    active: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    extra_data: {
      order_list: [
        { item_id: 'ITEM001', quantity: 2 },
        { item_id: 'ITEM002', quantity: 1 }
      ]
    },
    area: {
      id: 'area-001',
      area_id: 'AREA-A',
      area_name: 'Khu vực A',
      active: 1,
      sort: 1
    }
  },
  {
    id: 'table-002',
    table_name: 'Bàn 02',
    table_id: 'B02',
    description: 'Bàn ở giữa',
    source_id: '10000046',
    area_uid: 'area-001',
    store_uid: storeId,
    company_uid: '',
    brand_uid: '',
    sort: 2,
    active: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    area: {
      id: 'area-001',
      area_id: 'AREA-A',
      area_name: 'Khu vực A',
      active: 1,
      sort: 1
    }
  },
  {
    id: 'table-003',
    table_name: 'Bàn 03',
    table_id: 'B03',
    description: 'Bàn VIP',
    source_id: '10000047',
    area_uid: 'area-002',
    store_uid: storeId,
    company_uid: '',
    brand_uid: '',
    sort: 3,
    active: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    extra_data: {
      order_list: [
        { item_id: 'ITEM003', quantity: 3 },
        { item_id: 'ITEM004', quantity: 1 },
        { item_id: 'ITEM005', quantity: 2 }
      ]
    },
    area: {
      id: 'area-002',
      area_id: 'AREA-B',
      area_name: 'Khu vực B',
      active: 1,
      sort: 2
    }
  }
]

const getSampleSources = () => [
  { id: '1', sourceId: '10000045', sourceName: 'Tại chỗ' },
  { id: '2', sourceId: '10000046', sourceName: 'Mang về' },
  { id: '3', sourceId: '10000047', sourceName: 'Giao hàng' }
]

const createExcelFile = async (tables: any[], sources: any[]) => {
  // Create sources lookup map
  const sourcesMap = new Map()
  sources.forEach(source => {
    sourcesMap.set(source.sourceId, source.sourceName)
  })

  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Sheet')

  // Header row: id | Tên bàn | Nguồn | Món đặt trước | Mô tả
  const headerRow = worksheet.addRow(['id', 'Tên bàn', 'Nguồn', 'Món đặt trước', 'Mô tả'])

  headerRow.eachCell(cell => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF79abe3' }
    }
    cell.font = {
      bold: true,
      color: { argb: 'FFFFFFFF' }
    }
    cell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    }
  })

  // Add table data
  tables.forEach(table => {
    const sourceName = table.source_id ? sourcesMap.get(table.source_id) || '' : ''
    const orderList = table.extra_data?.order_list || []

    // Format pre-order items as "item_id x quantity" for each item
    const preOrderItems =
      orderList.length > 0 ? orderList.map((item: any) => `${item.item_id}x${item.quantity}`).join(', ') : ''

    worksheet.addRow([table.id, table.table_name || '', sourceName, preOrderItems, table.description || ''])
  })

  worksheet.columns = [
    { width: 60 }, // id - rộng hơn để hiển thị UUID
    { width: 15 }, // Tên bàn
    { width: 15 }, // Nguồn
    { width: 30 }, // Món đặt trước - tăng độ rộng để hiển thị item_id x quantity
    { width: 30 } // Mô tả
  ]

  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', 'update-table.xlsx')
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
