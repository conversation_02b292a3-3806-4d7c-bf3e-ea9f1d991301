import React, { useState } from 'react'

import useDialogState from '@/hooks/ui/use-dialog-state'

import { PrinterPositionInStore } from '../data'

type PrinterPositionInStoreDialogType = 'create' | 'update' | 'delete' | 'bulk-delete'

interface PrinterPositionInStoreContextType {
  open: PrinterPositionInStoreDialogType | null
  setOpen: (str: PrinterPositionInStoreDialogType | null) => void
  currentRow: PrinterPositionInStore | null
  setCurrentRow: React.Dispatch<React.SetStateAction<PrinterPositionInStore | null>>
  selectedRows: PrinterPositionInStore[]
  setSelectedRows: React.Dispatch<React.SetStateAction<PrinterPositionInStore[]>>
}

const PrinterPositionInStoreContext = React.createContext<PrinterPositionInStoreContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function PrinterPositionInStoreProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<PrinterPositionInStoreDialogType>(null)
  const [currentRow, setCurrentRow] = useState<PrinterPositionInStore | null>(null)
  const [selectedRows, setSelectedRows] = useState<PrinterPositionInStore[]>([])

  return (
    <PrinterPositionInStoreContext
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow,
        selectedRows,
        setSelectedRows
      }}
    >
      {children}
    </PrinterPositionInStoreContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const usePrinterPositionInStore = () => {
  const printerPositionInStoreContext = React.useContext(PrinterPositionInStoreContext)

  if (!printerPositionInStoreContext) {
    throw new Error('usePrinterPositionInStore has to be used within <PrinterPositionInStoreContext>')
  }

  return printerPositionInStoreContext
}
