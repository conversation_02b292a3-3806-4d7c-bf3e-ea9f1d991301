import React, { useState, useEffect } from 'react'

import { Download, Upload } from 'lucide-react'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { useEditTablePreview } from '../../hooks/use-edit-table-preview'
import { EditTablePreviewModal } from './edit-table-preview-modal'
import { EditTableSuccessModal } from './edit-table-success-modal'

interface EditTableInfoModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onDownloadExistingData: (storeId: string) => void
  onUploadFile?: (storeId: string) => void // Made optional since we use preview hook
  stores: Array<{ id: string; name: string }>
}

export const EditTableInfoModal: React.FC<EditTableInfoModalProps> = ({
  open,
  onOpenChange,
  onDownloadExistingData,
  onUploadFile: _onUploadFile, // Unused, kept for backward compatibility
  stores
}) => {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')

  // Use preview hook for upload functionality
  const {
    isPreviewModalOpen,
    isSuccessModalOpen,
    previewData,
    handleUploadFile,
    handleRemoveRow,
    handleConfirmImport,
    handleClosePreview,
    handleCloseSuccess
  } = useEditTablePreview({
    onSuccess: () => onOpenChange(false)
  })

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
  }

  const handleDownloadClick = () => {
    if (selectedStoreId) {
      onDownloadExistingData(selectedStoreId)
    }
  }

  const handleUploadClick = () => {
    if (selectedStoreId) {
      // Use new preview upload instead of old onUploadFile
      handleUploadFile(selectedStoreId)
    }
  }

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedStoreId('')
    }
  }, [open])

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle className='text-xl font-semibold'>Sửa thông tin bàn</DialogTitle>
          </DialogHeader>

          <div className='space-y-6 py-4'>
            {/* Bước 1: Chọn cửa hàng */}
            <div className='rounded-lg bg-gray-50 p-4'>
              <h3 className='mb-3 font-medium text-gray-900'>Bước 1. Chọn cửa hàng</h3>
              <div>
                <label className='mb-2 block text-sm font-medium text-gray-700'>Chọn cửa hàng</label>
                <Select value={selectedStoreId} onValueChange={handleStoreChange}>
                  <SelectTrigger>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Bước 2: Tải file dữ liệu bàn đã có */}
            <div className='rounded-lg bg-gray-50 p-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h3 className='font-medium text-gray-900'>Bước 2. Tải file dữ liệu bàn đã có.</h3>
                  <p className='mt-1 text-sm text-gray-600'>Tải xuống</p>
                </div>
                <div className='flex gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleDownloadClick}
                    className='flex items-center gap-2'
                    disabled={!selectedStoreId}
                  >
                    <Download className='h-4 w-4' />
                    Tải xuống
                  </Button>
                </div>
              </div>
            </div>

            {/* Bước 3: Thêm cấu hình vào file */}
            <div className='rounded-lg bg-gray-50 p-4'>
              <h3 className='mb-3 font-medium text-gray-900'>Bước 3. Thêm cấu hình vào file</h3>
              <div className='space-y-2 text-sm text-gray-600'>
                <div>
                  Không sửa các cột <span className='font-medium'>id</span>.
                </div>
              </div>
            </div>

            {/* Bước 4: Tải file lên */}
            <div className='rounded-lg bg-gray-50 p-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h3 className='mb-2 font-medium text-gray-900'>Bước 4. Tải file lên</h3>
                  <p className='text-sm text-gray-600'>Sau khi đã điền đầy đủ bàn có thể tải file lên</p>
                </div>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleUploadClick}
                  className='flex items-center gap-2'
                  disabled={!selectedStoreId}
                >
                  <Upload className='h-4 w-4' />
                  Tải file lên
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      <EditTablePreviewModal
        open={isPreviewModalOpen}
        onOpenChange={handleClosePreview}
        data={previewData}
        onConfirm={handleConfirmImport}
        onRemoveRow={handleRemoveRow}
      />

      {/* Success Modal */}
      <EditTableSuccessModal open={isSuccessModalOpen} onOpenChange={handleCloseSuccess} />
    </>
  )
}
