import type { GetSourcesParams, SourcesApiResponse, Source } from '@/types/sources'

import { apiClient } from './api/pos/pos-api'

/**
 * Fetch sales channels (sources) from API
 */
export const getSalesChannels = async (params: GetSourcesParams = {}): Promise<Source[]> => {
  const queryParams = new URLSearchParams()

  // Add skip limit first (as shown in the correct API)
  if (params.skipLimit) {
    queryParams.append('skip_limit', 'true')
  }

  // Add pagination parameters
  if (params.page !== undefined) {
    queryParams.append('page', params.page.toString())
  }
  if (params.results_per_page !== undefined) {
    queryParams.append('results_per_page', params.results_per_page.toString())
  }

  // Add company and brand UIDs
  if (params.companyUid) {
    queryParams.append('company_uid', params.companyUid)
  }
  if (params.brandUid) {
    queryParams.append('brand_uid', params.brandUid)
  }

  // Add store UID (single store, not list)
  if (params.storeUid) {
    queryParams.append('store_uid', params.storeUid)
  }

  const response = await apiClient.get<SourcesApiResponse>(`/mdata/v1/sources?${queryParams.toString()}`)

  if (response.data?.data) {
    // Import the conversion function dynamically to avoid circular dependency
    const { convertApiSourceToSource } = await import('@/types/sources')
    return response.data.data.map(convertApiSourceToSource)
  }

  return []
}
