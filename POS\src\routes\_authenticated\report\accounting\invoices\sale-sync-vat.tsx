import { createFileRoute, redirect } from '@tanstack/react-router'

import { useAuthStore } from '@/stores/authStore'

import SaleSyncVat from '@/features/reports/accounting/invoices/sale-sync-vat'

export const Route = createFileRoute('/_authenticated/report/accounting/invoices/sale-sync-vat')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    if (!user || !jwtToken) {
      throw redirect({ to: '/sign-in' })
    }
  },
  component: SaleSyncVat
})
