import { useQuery } from '@tanstack/react-query'

import { comboApi } from '@/lib/combo-api'

export const useNormalCombos = (page: number = 1, posParent: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['normal-combos', page, posParent],
    queryFn: () => comboApi.getNormalCombos(page, posParent),
    enabled: options?.enabled !== false && !!posParent,
    staleTime: 0,
    gcTime: 0,
    retry: false,
    refetchOnWindowFocus: false
  })
}

export const useSpecialCombos = (page: number = 1, posParent: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['special-combos', page, posParent],
    queryFn: () => comboApi.getSpecialCombos(page, posParent),
    enabled: options?.enabled !== false && !!posParent,
    staleTime: 0,
    gcTime: 0,
    retry: false,
    refetchOnWindowFocus: false
  })
}
