import { useState, useEffect } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox,
  Button,
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'
import { OrderSourceSelectionModal } from '../order-source/order-source-selection-modal'

interface LabelOrderManagementSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  storeUid?: string
}

export function LabelOrderManagementSection({ form, isLoading = false }: LabelOrderManagementSectionProps) {
  const [isLabelSourceModalOpen, setIsLabelSourceModalOpen] = useState(false)
  const [isOrderSourceModalOpen, setIsOrderSourceModalOpen] = useState(false)

  const [selectedLabelSources, setSelectedLabelSources] = useState<string[]>([])
  const [selectedOrderSources, setSelectedOrderSources] = useState<string[]>([])

  const formLabelSources = form.watch('sources_label_print')
  const formOrderSources = form.watch('sources_print')

  useEffect(() => {
    if (Array.isArray(formLabelSources)) {
      setSelectedLabelSources(formLabelSources)
    } else {
      setSelectedLabelSources([])
    }
  }, [formLabelSources])

  useEffect(() => {
    if (Array.isArray(formOrderSources)) {
      setSelectedOrderSources(formOrderSources)
    } else {
      setSelectedOrderSources([])
    }
  }, [formOrderSources])

  const handleOpenLabelSourceModal = () => {
    setIsLabelSourceModalOpen(true)
  }

  const handleOpenOrderSourceModal = () => {
    setIsOrderSourceModalOpen(true)
  }

  const handleLabelSourcesChange = (sources: string[]) => {
    setSelectedLabelSources(sources)
    form.setValue('sources_label_print', sources)
  }

  const handleOrderSourcesChange = (sources: string[]) => {
    setSelectedOrderSources(sources)
    form.setValue('sources_print', sources)
  }

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Quản lý in tem nhãn và yêu cầu (order)</h2>
      </div>

      <div className='space-y-4'>
        {/* In phiếu yêu cầu khi thanh toán */}
        <FormField
          control={form.control}
          name='print_order_at_checkout'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>In phiếu yêu cầu khi thanh toán</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* In tem nhãn khi thanh toán */}
        <FormField
          control={form.control}
          name='print_label_at_checkout'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>In tem nhãn khi thanh toán</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình in tem nhãn theo nguồn */}
        <FormField
          control={form.control}
          name='sources_label_print'
          render={({ field: _field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cấu hình in tem nhãn theo nguồn</FormLabel>
                </div>
                <FormControl>
                  <Button
                    type='button'
                    variant='outline'
                    disabled={isLoading}
                    onClick={handleOpenLabelSourceModal}
                    className='flex-1 justify-start border-blue-600 text-blue-600 hover:bg-blue-50'
                  >
                    {selectedLabelSources.length} nguồn được áp dụng
                  </Button>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình in yêu cầu (order) theo nguồn */}
        <FormField
          control={form.control}
          name='sources_print'
          render={({ field: _field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cấu hình in yêu cầu (order) theo nguồn</FormLabel>
                </div>
                <FormControl>
                  <Button
                    type='button'
                    variant='outline'
                    disabled={isLoading}
                    onClick={handleOpenOrderSourceModal}
                    className='flex-1 justify-start border-blue-600 text-blue-600 hover:bg-blue-50'
                  >
                    {selectedOrderSources.length} nguồn được áp dụng
                  </Button>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép sử dụng máy in phiếu yêu cầu để in hóa đơn theo vị trí/khu vực cho PDA */}
        <FormField
          control={form.control}
          name='print_bill_order_area'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>
                    Cho phép sử dụng máy in phiếu yêu cầu để in hóa đơn theo vị trí/khu vực cho PDA
                  </FormLabel>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 flex-shrink-0 text-gray-400 hover:text-gray-600' />
                    </TooltipTrigger>
                    <TooltipContent className='max-w-xs'>
                      <p>
                        Các máy in được gán vị trí tại của hàng được cấu hình theo khu vực thỏa mãn điều kiện sẽ được in
                        ra.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tách các món trong combo khi in */}
        <FormField
          control={form.control}
          name='split_combo'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Tách các món trong combo khi in</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Bỏ note combo với các món bị tách */}
        <FormField
          control={form.control}
          name='ignore_combo_note'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Bỏ note combo với các món bị tách</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không thay thế món cha Od */}
        <FormField
          control={form.control}
          name='show_item_price_zero'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Không thay thế món cha 0đ</FormLabel>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 flex-shrink-0 text-gray-400 hover:text-gray-600' />
                    </TooltipTrigger>
                    <TooltipContent className='max-w-xs'>
                      <p>Nếu món cha 0đ thì món topping đầu tiên (không có customization) sẽ thay thế vị trí món cha</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sử dụng phiếu hủy đổ */}
        <FormField
          control={form.control}
          name='enable_delete_order_bill'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Sử dụng phiếu hủy đổ</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phiếu chuyển bàn in thêm danh sách món */}
        <FormField
          control={form.control}
          name='print_item_switch_table'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Phiếu chuyển bàn in thêm danh sách món</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Label Source Selection Modal */}
      <OrderSourceSelectionModal
        open={isLabelSourceModalOpen}
        onOpenChange={setIsLabelSourceModalOpen}
        onSourcesChange={handleLabelSourcesChange}
        appliedSources={selectedLabelSources}
      />

      {/* Order Source Selection Modal */}
      <OrderSourceSelectionModal
        open={isOrderSourceModalOpen}
        onOpenChange={setIsOrderSourceModalOpen}
        onSourcesChange={handleOrderSourcesChange}
        appliedSources={selectedOrderSources}
      />
    </div>
  )
}
