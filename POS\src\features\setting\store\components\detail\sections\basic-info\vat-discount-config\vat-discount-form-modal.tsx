import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogT<PERSON>le,
  DialogFooter,
  Button,
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Input
} from '@/components/ui'

import { vatDiscountConfigSchema, type VatDiscountConfigFormData } from './vat-discount-schema'
import type { VatDiscountConfig } from './vat-discount-types'

interface VatDiscountFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: VatDiscountConfig) => void
  editingConfig?: VatDiscountConfig | null
}

export function VatDiscountFormModal({ isOpen, onClose, onSave, editingConfig }: VatDiscountFormModalProps) {
  const form = useForm<VatDiscountConfigFormData>({
    resolver: zodResolver(vatDiscountConfigSchema),
    defaultValues: {
      discountPercentage: editingConfig?.discountPercentage || 0,
      vatPercentage: editingConfig?.vatPercentage || 0,
      programName: editingConfig?.programName || '',
      startDate: editingConfig?.startDate || new Date().toISOString().split('T')[0],
      endDate: editingConfig?.endDate || new Date().toISOString().split('T')[0]
    }
  })

  const handleSave = (data: VatDiscountConfigFormData) => {
    const configData: VatDiscountConfig = {
      id: editingConfig?.id || Date.now().toString(),
      ...data
    }
    onSave(configData)
    form.reset()
    onClose()
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-h-[80vh] w-[60vw] !max-w-[95vw]'>
        <DialogHeader>
          <DialogTitle>Cấu hình Giảm giá VAT theo nghị định</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSave)} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='startDate'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormLabel className='w-[200px] flex-shrink-0'>Ngày bắt đầu</FormLabel>
                      <FormControl className='flex-1'>
                        <Input type='date' {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='endDate'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormLabel className='w-[200px] flex-shrink-0'>Ngày kết thúc</FormLabel>
                      <FormControl className='flex-1'>
                        <Input type='date' {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='discountPercentage'
              render={({ field }) => (
                <FormItem>
                  <div className='flex items-center gap-4'>
                    <FormLabel className='w-[200px] flex-shrink-0'>
                      Mức giảm (%) <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl className='flex-1'>
                      <Input
                        type='number'
                        placeholder='0'
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='vatPercentage'
              render={({ field }) => (
                <FormItem>
                  <div className='flex items-center gap-4'>
                    <FormLabel className='w-[200px] flex-shrink-0'>
                      VAT (%) <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl className='flex-1'>
                      <Input
                        type='number'
                        placeholder='0'
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='programName'
              render={({ field }) => (
                <FormItem>
                  <div className='flex items-center gap-4'>
                    <FormLabel className='w-[200px] flex-shrink-0'>
                      Tên chương trình chiến dịch <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl className='flex-1'>
                      <Input placeholder='Nhập tên chương trình' {...field} />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type='button' variant='outline' onClick={handleClose}>
                Hủy
              </Button>
              <Button type='submit'>Lưu</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
