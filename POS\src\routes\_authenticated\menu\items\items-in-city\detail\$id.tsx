import { createFileRoute } from '@tanstack/react-router'
import { useParams } from '@tanstack/react-router'

import { ItemDetailForm } from '@/features/menu/items/items-in-city/detail'
import { useItemInCityDetail } from '@/features/menu/items/items-in-city/hooks'
import { ItemInCity } from '@/features/menu/items/items-in-city/hooks/items-in-city-types'

export const Route = createFileRoute('/_authenticated/menu/items/items-in-city/detail/$id')({
  component: ItemDetailPage
})

function ItemDetailPage() {
  const { id } = useParams({ strict: false })

  const { data: itemDetail, isLoading } = useItemInCityDetail(id, !!id)

  if (isLoading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center'>
          <div className='text-lg'><PERSON>ang tải...</div>
        </div>
      </div>
    )
  }

  if (!itemDetail?.data) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center'>
          <div className='text-lg'>Không tìm thấy món ăn</div>
        </div>
      </div>
    )
  }

  return <ItemDetailForm currentRow={itemDetail.data as ItemInCity} />
}
