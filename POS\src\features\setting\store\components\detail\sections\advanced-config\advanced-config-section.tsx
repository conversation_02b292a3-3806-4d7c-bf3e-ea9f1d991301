import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { ChevronDown, ChevronUp } from 'lucide-react'

import { Button } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'
import { BuffetConfigSection } from './buffet-config-section'
import { BusinessHoursSection } from './business-hours-section'
import { InvoiceNumberConfigSection } from './invoice-number-config-section'
import { InvoiceSection } from './invoice-section'
import { LabelOrderManagementSection } from './label-order-management-section'
import { MallConfigSection } from './mall-config-section'
import { MenuEditSection } from './menu-edit-section'
import { OperationModelSection } from './operation-model-section'
import { OrderReportSection } from './order-report-section'
import { PinCodeSection } from './pin-code-section'
import { PosAdvancedConfigSection } from './pos-advanced-config-section'
import { PosTrackingSection } from './pos-tracking-section'
import { PrintConfigSection } from './print-config-section'
import { SelfOrderConfigSection } from './self-order-config-section'
import { ShiftManagementSection } from './shift-management-section'
import { VoucherMemberSection } from './voucher-member-section'

interface AdvancedConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
  storeUid?: string
}

export function AdvancedConfigSection({ form, isLoading = false, storeUid }: AdvancedConfigSectionProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className='space-y-6'>
      <div>
        <Button
          type='button'
          variant='ghost'
          onClick={() => setIsExpanded(!isExpanded)}
          className='flex h-auto items-center gap-2 p-0 text-xl font-bold text-blue-600 hover:bg-transparent hover:text-blue-700'
        >
          <span>Cấu hình nâng cao</span>
          {isExpanded ? <ChevronUp className='h-5 w-5' /> : <ChevronDown className='h-5 w-5' />}
        </Button>
      </div>

      {isExpanded && (
        <div className='space-y-6 pt-6'>
          {/* Section Mô hình hoạt động */}
          <div className='rounded-lg border bg-white p-6'>
            <OperationModelSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình Voucher và Hội viên */}
          <div className='rounded-lg border bg-white p-6'>
            <VoucherMemberSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình nhà hàng buffet */}
          <div className='rounded-lg border bg-white p-6'>
            <BuffetConfigSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Quản lý ca */}
          <div className='rounded-lg border bg-white p-6'>
            <ShiftManagementSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Invoice */}
          <div className='rounded-lg border bg-white p-6'>
            <InvoiceSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình nâng cao POS */}
          <div className='rounded-lg border bg-white p-6'>
            <PosAdvancedConfigSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Mã PIN */}
          <div className='rounded-lg border bg-white p-6'>
            <PinCodeSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Giờ bán hàng */}
          <div className='rounded-lg border bg-white p-6'>
            <BusinessHoursSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Theo dõi bán hàng dưới POS */}
          <div className='rounded-lg border bg-white p-6'>
            <PosTrackingSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Báo cáo lượt order */}
          <div className='rounded-lg border bg-white p-6'>
            <OrderReportSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình sửa thực đơn tại cửa hàng */}
          <div className='rounded-lg border bg-white p-6'>
            <MenuEditSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình số hóa đơn */}
          <div className='rounded-lg border bg-white p-6'>
            <InvoiceNumberConfigSection form={form} isLoading={isLoading} storeUid={storeUid} />
          </div>

          {/* Section Quản lý in tem nhãn và yêu cầu (order) */}
          <div className='rounded-lg border bg-white p-6'>
            <LabelOrderManagementSection form={form} isLoading={isLoading} storeUid={storeUid} />
          </div>

          {/* Section Cấu hình in ấn */}
          <div className='rounded-lg border bg-white p-6'>
            <PrintConfigSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình thiết bị Self Order */}
          <div className='rounded-lg border bg-white p-6'>
            <SelfOrderConfigSection form={form} isLoading={isLoading} />
          </div>

          {/* Section Cấu hình trung tâm thương mại */}
          <div className='rounded-lg border bg-white p-6'>
            <MallConfigSection form={form} isLoading={isLoading} />
          </div>

          <div className='flex items-center justify-center'>
            <Button
              type='button'
              variant='ghost'
              onClick={() => setIsExpanded(!isExpanded)}
              className='text-md flex h-auto items-center justify-center gap-2 p-0 font-bold text-blue-600 hover:bg-transparent hover:text-blue-700'
            >
              <span>Ẩn bớt</span>
              {isExpanded ? <ChevronUp className='h-5 w-5' /> : <ChevronDown className='h-5 w-5' />}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
