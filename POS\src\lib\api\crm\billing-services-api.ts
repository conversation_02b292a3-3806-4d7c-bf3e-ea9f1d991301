import type { CrmServicesResponse, CrmServicesParams } from '@/types/api/crm'

import { crmApi } from './crm-api'

export const getBillingServices = async (params: CrmServicesParams = {}): Promise<CrmServicesResponse> => {
  try {
    const response = await crmApi.get('billing/get-list-service', {
      params: {
        pos_parent: 'BRAND-953H',
        limit: 1000,
        ...params
      }
    })
    return response.data
  } catch (error) {
    throw new Error('Failed to fetch billing services')
  }
}

export const billingServicesApiClient = crmApi
