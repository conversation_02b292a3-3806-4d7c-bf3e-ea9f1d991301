import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { areasApi, type Area } from '@/lib/api'

import { QUERY_KEYS } from '@/constants/query-keys'

/**
 * Hook to update area sort orders
 */
export const useUpdateAreaSort = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (areas: Area[]) => {
      const updatePromises = areas.map(area => areasApi.updateArea(area))
      return await Promise.all(updatePromises)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AREAS_LIST]
      })
      toast.success('Cập nhật thứ tự khu vực thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thứ tự khu vực'
      toast.error(errorMessage)
    }
  })

  return {
    updateAreaSort: mutate,
    isUpdating: isPending
  }
}
