import type { GetPackagesParams, PackagesApiResponse, UpdatePackagesSortRequest } from '@/types/package-types'

import { apiClient } from '@/lib/api/pos/pos-api'

export const packagesApi = {
  getPackages: async (params: GetPackagesParams): Promise<PackagesApiResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      list_store_uid: params.list_store_uid.join(','),
      ...(params.skip_limit && { skip_limit: 'true' })
    })

    const response = await apiClient.get<PackagesApiResponse>(`/mdata/v1/packages?${queryParams}`)
    return response.data
  },

  updatePackagesSort: async (data: UpdatePackagesSortRequest): Promise<void> => {
    await apiClient.post('/mdata/v1/package/sort', data)
  }
}
