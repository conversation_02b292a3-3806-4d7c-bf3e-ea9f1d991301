import { apiClient } from './api/pos/pos-api'

export interface ComboPromotion {
  promotion_uid: string
  active: number
  created_at: number
  store_uid: string
  store_name: string
}

export interface ComboPromotionGroup {
  promotion_id: string
  promotion_name: string
  partner_auto_gen: number
  array_agg: number[]
  ids_same_promotion: string[]
  promotions: ComboPromotion[]
}

export interface ComboPromotionsListParams {
  company_uid: string
  brand_uid: string
  skip_limit?: boolean
  aggregate?: boolean
  store_uid?: string
}

export interface ComboPromotionsListResponse {
  data: ComboPromotionGroup[]
  track_id: string
}

export interface ComboPackageDetail {
  LstItem_Options: Array<{
    id: string
    Name: string
    LstItem: Array<{
      item_id: string
      ta_price: number
      item_name: string
      ots_price: number
      state_change_price: number
      discount_combo_item: number
    }>
    Max_Permitted: number
    Min_Permitted: number
  }>
}

export interface Combo {
  package_id: string
  package_name: string
  use_same_data: number
  ots_value: number
  from_date: number
  to_date: number
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: string | null
  deleted_by: string | null
  ta_value: number
  time_sale_hour_day: number | null
  time_sale_date_week: number | null
  description: string
  active: number
  extra_data: {
    price_by_source: any[]
  }
  package_detail: ComboPackageDetail
  revision: string | null
  unit_uid: string | null
  promotion_uid: string | null
  brand_uid: string
  company_uid: string
  sort: number
  image_path: string | null
  image_path_thumb: string | null
  package_color: string | null
  is_fabi: number
  store_uid: string
  vat_tax_rate: number
  item_type_uid: string | null
  ps_store_uid: string
  row: number
  stores: number
  list_package_uid: string[]
  list_store_uid: string[]
}

export interface CombosListParams {
  company_uid: string
  brand_uid: string
  page?: number
  list_store_uid?: string | string[]
  status?: 'unexpired' | 'expired' | 'all'
  search?: string
  promotion_id?: string
  skip_limit?: boolean
}

export interface CombosListResponse {
  data: Combo[]
  track_id: string
}

export interface DeleteCombosParams {
  packageUids: string[]
}

export interface GetComboDetailsParams {
  packageUids: string[]
}

export interface CreateCombosParams {
  combos: Omit<Combo, 'id' | 'created_at' | 'updated_at'>[]
}

// Types for single combo creation (based on curl data)
export interface CreateComboItem {
  item_id: string
  item_name: string
  ta_price: number
  ots_price: number
  discount_combo_item: number
  state_change_price: number
}

export interface CreateComboGroup {
  LstItem: CreateComboItem[]
  Min_Permitted: number
  Max_Permitted: number
  Name: string
  id: string
}

export interface CreateComboPackageDetail {
  LstItem_Options: CreateComboGroup[]
}

export interface CreateComboPriceBySource {
  source_id: string
  price: number
  price_times: unknown[]
  is_source_exist_in_city: boolean
}

export interface CreateComboExtraData {
  price_by_source: CreateComboPriceBySource[]
}

export interface CreateComboRequest {
  package_detail: CreateComboPackageDetail
  deleted: boolean
  extra_data: CreateComboExtraData
  vat_tax_rate: number
  item_type_uid: string
  from_date: number
  to_date: number
  promotion_id: string
  time_sale_date_week: number
  time_sale_hour_day: number
  sort: number
  package_name: string
  ots_value: number
  description: string
  company_uid: string
  brand_uid: string
  package_id: string
  ta_value: number
  store_uid: string
  promotion_uid: string
  use_same_data: number
}

export interface CreateComboResponse {
  data: unknown
  track_id: string
}

// Types for combo detail (for editing)
export interface ComboDetailItem {
  item_id: string
  item_name: string
  ta_price: number
  ots_price: number
  state_change_price: number
  discount_combo_item: number
}

export interface ComboDetailGroup {
  id: string
  Name: string
  LstItem: ComboDetailItem[]
  Max_Permitted: number
  Min_Permitted: number
}

export interface ComboDetailPackageDetail {
  LstItem_Options: ComboDetailGroup[]
}

export interface ComboDetailPriceBySource {
  price: number
  source_id: string
  price_times: unknown[]
  is_source_exist_in_city: boolean
}

export interface ComboDetailExtraData {
  price_by_source: ComboDetailPriceBySource[]
}

export interface ComboDetail {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  package_id: string
  package_name: string
  ta_value: number
  ots_value: number
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string
  active: number
  extra_data: ComboDetailExtraData
  package_detail: ComboDetailPackageDetail
  revision: unknown | null
  unit_uid: unknown | null
  promotion_uid: string | null
  brand_uid: string
  company_uid: string
  sort: number
  image_path: string | null
  image_path_thumb: string | null
  use_same_data: number
  package_color: string | null
  is_fabi: number
  store_uid: string
  vat_tax_rate: number
  item_type_uid: string
  promotion_id: string | null
  row: unknown | null
}

export interface GetComboDetailResponse {
  data: ComboDetail[]
  track_id: string
}

/**
 * Combos API functions
 */
export const combosApi = {
  /**
   * Get combo promotions list
   */
  getComboPromotionsList: async (params: ComboPromotionsListParams): Promise<ComboPromotionsListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      skip_limit: (params.skip_limit ?? true).toString(),
      aggregate: (params.aggregate ?? true).toString()
    })

    if (params.store_uid) {
      queryParams.append('store_uid', params.store_uid)
    }

    const response = await apiClient.get(`/mdata/v1/promotions?${queryParams}`)
    return response.data
  },

  /**
   * Get combos list
   */
  getCombosList: async (params: CombosListParams): Promise<CombosListResponse> => {
    // Build query string manually to avoid encoding commas
    const queryParts: string[] = [`company_uid=${params.company_uid}`, `brand_uid=${params.brand_uid}`]

    // Add skip_limit if specified
    if (params.skip_limit) {
      queryParts.push('skip_limit=true')
    } else {
      queryParts.push(`page=${params.page || 1}`)
    }

    if (params.list_store_uid) {
      const storeUids = Array.isArray(params.list_store_uid) ? params.list_store_uid.join(',') : params.list_store_uid
      queryParts.push(`list_store_uid=${storeUids}`)
    }

    if (params.status && params.status !== 'all') {
      queryParts.push(`status=${params.status}`)
    }

    if (params.search) {
      queryParts.push(`search=${encodeURIComponent(params.search)}`)
    }

    if (params.promotion_id) {
      queryParts.push(`promotion_id=${params.promotion_id}`)
    }

    const queryString = queryParts.join('&')
    const response = await apiClient.get(`/mdata/v1/packages?${queryString}`)
    return response.data
  },

  /**
   * Delete combos
   */
  deleteCombos: async (params: DeleteCombosParams): Promise<void> => {
    const queryParams = new URLSearchParams({
      list_package_uid: params.packageUids.join(',')
    })

    await apiClient.delete(`/mdata/v1/packages?${queryParams}`)
  },

  /**
   * Update combos (bulk update)
   */
  updateCombos: async (combos: any[]): Promise<any> => {
    const response = await apiClient.put(`/mdata/v1/packages`, combos)
    return response.data
  },

  /**
   * Create combos from import (bulk create)
   */
  createCombosFromImport: async (combos: any[]): Promise<any> => {
    const response = await apiClient.post(`/mdata/v1/packages`, combos)
    return response.data
  },

  /**
   * Get combo details for copying
   */
  getComboDetails: async (params: GetComboDetailsParams): Promise<{ data: Combo[] }> => {
    const queryParams = new URLSearchParams({
      list_package_uid: params.packageUids.join(',')
    })

    const response = await apiClient.get(`/mdata/v1/package?${queryParams}`)
    return response.data
  },

  /**
   * Create new combos (for copying)
   */
  createCombos: async (params: CreateCombosParams): Promise<void> => {
    await apiClient.post('/mdata/v1/packages', params.combos)
  },

  /**
   * Create a single combo (based on form data)
   */
  createCombo: async (data: CreateComboRequest): Promise<CreateComboResponse> => {
    try {
      const response = await apiClient.post('/mdata/v1/packages', [data], {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Dữ liệu không hợp lệ.')
      }
      throw error
    }
  },

  /**
   * Get combo detail by package UID
   */
  getComboDetail: async (packageUid: string): Promise<GetComboDetailResponse> => {
    try {
      const response = await apiClient.get(`/mdata/v1/package?list_package_uid=${packageUid}`, {
        headers: {
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Không tìm thấy combo.')
      }
      throw error
    }
  },

  /**
   * Update a combo (based on form data)
   */
  updateCombo: async (data: ComboDetail): Promise<CreateComboResponse> => {
    try {
      const response = await apiClient.put('/mdata/v1/packages', [data], {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Dữ liệu không hợp lệ.')
      }
      throw error
    }
  }
}

/**
 * Transform form data to full combo object for update
 * Merges formData changes with originalCombo, keeping unchanged fields intact
 */
export const transformFormDataToUpdateRequest = (formData: any, originalCombo: any): any => {
  // This is a placeholder implementation
  // You'll need to implement the actual transformation logic based on combo requirements
  return {
    ...originalCombo,
    ...formData
  }
}
