import { useTableLayoutState, useTableLayoutData, useTableLayoutHandlers, useTableLayoutEffects } from './'

export const useTableLayoutPage = () => {
  const state = useTableLayoutState()

  const data = useTableLayoutData({
    selectedStoreId: state.selectedStoreId,
    selectedAreaId: state.selectedAreaId
  })

  const handlers = useTableLayoutHandlers({
    selectedStoreId: state.selectedStoreId,
    selectedAreaId: state.selectedAreaId,
    localTables: state.localTables,
    importedTableData: state.importedTableData,
    setSelectedAreaId: state.setSelectedAreaId,
    setSelectedStoreId: state.setSelectedStoreId,
    setSelectedTable: state.setSelectedTable,
    setLocalTables: state.setLocalTables,
    setIsSortModalOpen: state.setIsSortModalOpen,
    setIsImportModalOpen: state.setIsImportModalOpen,
    setIsPreviewModalOpen: state.setIsPreviewModalOpen,
    setIsEditTableModalOpen: state.setIsEditTableModalOpen,
    setIsConfigureTableModalOpen: state.setIsConfigureTableModalOpen,
    setImportedTableData: state.setImportedTableData
  })

  useTableLayoutEffects({
    allStores: data.allStores,
    selectedStoreId: state.selectedStoreId,
    setSelectedStoreId: state.setSelectedStoreId,
    areas: data.areas,
    selectedAreaId: state.selectedAreaId,
    setSelectedAreaId: state.setSelectedAreaId,
    tables: data.tables,
    setLocalTables: state.setLocalTables,
    lastTablesRef: state.lastTablesRef
  })

  return {
    selectedAreaId: state.selectedAreaId,
    selectedStoreId: state.selectedStoreId,
    selectedTable: state.selectedTable,
    localTables: state.localTables,
    isSortModalOpen: state.isSortModalOpen,
    isImportModalOpen: state.isImportModalOpen,
    isPreviewModalOpen: state.isPreviewModalOpen,
    isEditTableModalOpen: state.isEditTableModalOpen,
    isConfigureTableModalOpen: state.isConfigureTableModalOpen,
    importedTableData: state.importedTableData,
    allStores: data.allStores,
    isLoadingStores: data.isLoadingStores,
    areas: data.areas,
    areasLoading: data.areasLoading,
    tables: data.tables,
    tablesLoading: data.tablesLoading,
    ...handlers
  }
}
