import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/crm/customer-profile')({
  component: CustomerProfilePage,
})

function CustomerProfilePage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Bức tranh khách hàng</h1>
        <p className="text-gray-600 mt-2">
          Phân tích chi tiết hành vi và đặc điểm của khách hàng
        </p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Bức tranh khách hàng
          </h3>
          <p className="text-gray-500">
            Nội dung phân tích sẽ được phát triển ở giai đoạn tiếp theo
          </p>
        </div>
      </div>
    </div>
  )
}
