import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox,
  Input,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface InvoiceSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function InvoiceSection({ form, isLoading = false }: InvoiceSectionProps) {
  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'>Invoice</h2>

      <div className='space-y-6'>
        {/* Ẩn tiền Chiết khấu trên HĐDT */}
        <FormField
          control={form.control}
          name='discount_reverse_on_price'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Ẩn tiền Chiết khấu trên HĐDT</FormLabel>
                </div>
                <FormControl>
                  <div className='flex items-center gap-4'>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                    <span className='text-sm text-gray-600'>Giảm giá khấu trừ vào giá món</span>
                  </div>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ẩn món 0đ trên HĐDT */}
        <FormField
          control={form.control}
          name='inv_skip_item_no_price'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Ẩn món 0đ trên HĐDT</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cho phép auto xuất VAT khi chốt ca */}
        <FormField
          control={form.control}
          name='auto_export_vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Cho phép auto xuất VAT khi chốt ca</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình thời gian xuất VAT ở POS (phút) */}
        <FormField
          control={form.control}
          name='export_time_vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed'>Cấu hình thời gian xuất VAT ở POS (phút)</FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className='h-6 w-6 cursor-help text-gray-400 hover:text-gray-600' />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className='max-w-xs'>
                          Cấu hình thời gian cho phép xuất VAT của 1 hóa đơn tính từ lúc in thanh toán tại POS
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <Input placeholder='Không giới hạn thời gian' disabled={isLoading} className='flex-1' {...field} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Bắt buộc nhập thông tin VAT khi thanh toán */}
        <FormField
          control={form.control}
          name='require_vat_info'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>
                    Bắt buộc nhập thông tin VAT khi thanh toán
                  </FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Mặc định phương thức thanh toán TM/CK */}
        <FormField
          control={form.control}
          name='pm_export_vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed font-medium'>Mặc định phương thức thanh toán TM/CK</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Auto xuất vat khi in hoá đơn */}
        <FormField
          control={form.control}
          name='bill_auto_export_vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Auto xuất vat khi in hoá đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sắp xếp món theo thứ tự trên hoá đơn */}
        <FormField
          control={form.control}
          name='sorted_by_print'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='leading-relaxed'>Sắp xếp món theo thứ tự trên hoá đơn</FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className='h-6 w-6 cursor-help text-gray-400 hover:text-gray-600' />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className='max-w-xs'>Chỉ áp dụng đối với hoá đơn xuất lẻ</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
