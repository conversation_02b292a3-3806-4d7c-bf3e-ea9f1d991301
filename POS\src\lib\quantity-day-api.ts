import { api } from './api/pos/pos-api'

// Types for Quantity Day API
export interface QuantityDayData {
  store_uid: string
  brand_uid: string
  company_uid: string
  from_date: number
  to_date: number
  quantity_per_day: number
  time_sale_date_week: number
  item_list: string[]
  deleted: boolean
  require_update: string
  created_by: string
  created_at: string
  updated_at: string
  __v: number
  id: string
}

export interface QuantityDayApiResponse {
  data: QuantityDayData[]
  track_id: string
}

export interface GetQuantityDaysParams {
  company_uid: string
  brand_uid: string
  store_uid?: string
  list_item_id?: string
  page?: number
  limit?: number
  from_date?: number
  to_date?: number
  active?: number
}

export interface CreateQuantityDayRequest {
  require_update: string
  store_uid: string
  time_sale_date_week: number
  quantity_per_day: string | number
  item_list: string[]
  from_date: number
  to_date: number
  company_uid: string
  brand_uid: string
}

export interface UpdateQuantityDayRequest {
  require_update: string
  store_uid: string
  time_sale_date_week: number
  quantity_per_day: string | number
  item_list: string[]
  from_date: number
  to_date: number
  brand_uid: string
  company_uid: string
  deleted: boolean
  created_by: string
  created_at: string
  updated_at: string
  __v: number
  id: string
}

// Cache for quantity days requests
const quantityDaysCache = new Map<string, { data: QuantityDayApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<QuantityDayApiResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Quantity Day API Service
export const quantityDayApi = {
  /**
   * Get quantity days with request deduplication and caching
   */
  getQuantityDays: async (params: GetQuantityDaysParams): Promise<QuantityDayApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.store_uid || 'all'}-${params.list_item_id || 'all'}-${params.page || 1}-${params.limit || 50}-${params.from_date || ''}-${params.to_date || ''}-${params.active ?? 1}`

    // Check cache first
    const cached = quantityDaysCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.company_uid,
          brand_uid: params.brand_uid,
          page: (params.page || 1).toString()
        })

        if (params.list_item_id) {
          queryParams.set('list_item_id', params.list_item_id)
        }

        if (params.store_uid) {
          queryParams.set('store_uid', params.store_uid)
        }

        if (params.limit !== undefined) {
          queryParams.set('limit', params.limit.toString())
        }

        if (params.from_date !== undefined) {
          queryParams.set('from_date', params.from_date.toString())
        }

        if (params.to_date !== undefined) {
          queryParams.set('to_date', params.to_date.toString())
        }

        if (params.active !== undefined) {
          queryParams.set('active', params.active.toString())
        }

        const response = await api.get(`/mdata/v1/quantity-days?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000' // GMT+7 timezone offset in milliseconds
          },
          timeout: 30000 // 30 seconds timeout
        })

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from quantity days API')
        }

        const result = response.data as QuantityDayApiResponse

        // Cache the result
        quantityDaysCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
          throw new Error('Request timeout - server is taking too long to respond. Please try again.')
        }

        if (error.response?.status === 504) {
          throw new Error('Gateway timeout (504) - server is overloaded. Please try again later.')
        }

        if (error.response?.status === 503) {
          throw new Error('Service unavailable (503) - server is temporarily down. Please try again later.')
        }

        if (error.response?.status >= 500) {
          throw new Error(`Server error (${error.response.status}) - please try again later.`)
        }

        if (error.response?.status === 429) {
          throw new Error('Too many requests - please wait a moment before trying again.')
        }

        if (error.response?.status === 401) {
          throw new Error('Unauthorized - please check your authentication.')
        }

        if (error.response?.status === 403) {
          throw new Error('Forbidden - you do not have permission to access this resource.')
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Create a new quantity day configuration
   */
  createQuantityDay: async (data: CreateQuantityDayRequest): Promise<unknown> => {
    try {
      // Convert data to array format and fix require_update
      const arrayData = [{
        ...data,
        require_update: data.require_update === 'true' ? null : data.require_update,
        // Timestamps are already in milliseconds from getTime()
        from_date: data.from_date,
        to_date: data.to_date
      }]

      const response = await api.post('/mdata/v1/quantity-days', arrayData, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        }
      })

      // Clear cache after successful creation
      quantityDaysCache.clear()

      return response.data.data || response.data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Update an existing quantity day configuration
   */
  updateQuantityDay: async (data: UpdateQuantityDayRequest): Promise<unknown> => {
    try {
      const response = await api.put(`/mdata/v1/quantity-days`, data, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        }
      })

      // Clear cache after successful update
      quantityDaysCache.clear()

      return response.data.data || response.data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Quantity day configuration not found.')
      }
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Delete quantity day configurations (single or bulk)
   */
  deleteQuantityDay: async (data: {
    company_uid?: string
    brand_uid?: string
    id?: string
    list_id?: string[]
  }): Promise<void> => {
    try {
      await api.delete(`/mdata/v1/quantity-days`, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        data,
        timeout: 30000
      })

      // Clear cache after successful deletion
      quantityDaysCache.clear()
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Quantity day configuration not found.')
      }
      throw error
    }
  },

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache: (): void => {
    quantityDaysCache.clear()
    pendingRequests.clear()
  }
}

export default quantityDayApi
