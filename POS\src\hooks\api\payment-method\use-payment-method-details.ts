import { useQuery } from '@tanstack/react-query'

import type { PaymentMethodDetailsParams, PaymentMethodDetailsResponse } from '@/types/api'

import { getPaymentMethodDetails } from '@/lib/api/pos/payment-method-revenue-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UsePaymentMethodDetailsOptions {
  company_uid: string
  brand_uid: string
  list_store_uid?: string
  start_date?: number
  end_date?: number
  store_open_at?: number
  by_days?: number
  enabled?: boolean
}

export const usePaymentMethodDetails = ({
  company_uid,
  brand_uid,
  list_store_uid,
  start_date,
  end_date,
  store_open_at = 0,
  by_days = 1,
  enabled = true
}: UsePaymentMethodDetailsOptions) => {
  const queryKey = [
    QUERY_KEYS.REPORTS_PAYMENT_METHOD_DETAILS,
    company_uid,
    brand_uid,
    list_store_uid,
    start_date,
    end_date,
    store_open_at,
    by_days
  ]

  const queryFn = async (): Promise<PaymentMethodDetailsResponse> => {
    const params: PaymentMethodDetailsParams = {
      company_uid,
      brand_uid
    }

    // Add optional parameters only if provided
    if (list_store_uid) {
      params.list_store_uid = list_store_uid
    }
    if (start_date !== undefined) {
      params.start_date = start_date
    }
    if (end_date !== undefined) {
      params.end_date = end_date
    }
    if (store_open_at !== undefined) {
      params.store_open_at = store_open_at
    }
    if (by_days !== undefined) {
      params.by_days = by_days
    }

    return getPaymentMethodDetails(params)
  }

  return useQuery({
    queryKey,
    queryFn,
    enabled: enabled && !!(company_uid && brand_uid),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: false // Don't auto-refetch
  })
}
