import { useQuery } from '@tanstack/react-query'

import { convertApiCustomizationToCustomization } from '@/types/customizations'

import { getCustomizationById } from '@/lib/customizations-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export function useCustomizationById(customizationId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.CUSTOMIZATIONS_DETAIL, customizationId],
    queryFn: async () => {
      const apiData = await getCustomizationById(customizationId)
      return convertApiCustomizationToCustomization(apiData)
    },
    enabled: enabled && !!customizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
