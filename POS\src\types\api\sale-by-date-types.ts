// Types for Sale by Date Report API based on actual response structure

export interface SaleByDateTopping {
  hour: number
  note: string
  price: number
  amount: number
  is_vat: number
  minute: number
  item_id: string
  tran_id: string
  unit_id: string
  discount: number
  quantity: number
  item_name: string
  price_org: number
  package_id: string
  start_date: number
  is_eat_with: number
  sub_topping: unknown[]
  check_active: number
  discount_vat: number
  id_group_cus: string
  item_type_id: string
  promotion_id: string
  raw_quantity: number
  time_cooking: number
  vat_tax_rate: number
  discount_name: string
  item_class_id: string
  quantity_done: number
  id_sale_detail: string
  item_type_name: string
  split_quantity: number
  vat_tax_amount: number
  voucher_amount: number
  discount_amount: number
  item_class_name: string
  item_id_mapping: string
  ship_fee_amount: number
  commission_amount: number
  deduct_tax_amount: number
  amount_all_topping: number
  not_update_quantop: number
  quantity_secondary: number
  discount_vat_amount: number
  quantity_print_temp: number
  discount_extra_amount: number
  service_charge_amount: number
  discount_values_amount: number
  vat_tax_reverse_amount: number
  amount_discount_on_price: number
  partner_marketing_amount: number
}

export interface SaleByDateDetail {
  item_name: string
  quantity: number
  tran_id: string
  price: number
  tran_date: number
  amount_all_topping: number
  amount: number
  toppings: SaleByDateTopping[]
  discount_amount: number
  unit_id: string
}

export interface SaleByDatePaymentMethod {
  payment_method_id: string
  payment_method_name: string
  trace_no: string
  tran_id: string
  tran_date: number
  tran_id_of_partner: string
  amount: number
}

export interface SaleByDateStore {
  id: string | null
  store_name: string
}

export interface SaleByDateExtraData {
  Point: number
  mkt_max: number
  comm_max: number
  del_edit: number
  ship_fee: number
  ship_lat: number
  ship_lng: number
  peo_count: number
  tip_amount: number
  vat_option: number
  excess_cash: number
  payment_pda: number
  voucher_log: string
  process_time: number
  customer_name: string
  is_print_bill: number
  split_two_vat: number
  customer_email: string
  customer_phone: string
  deposit_amount: number
  money_received: number
  partner_config: number
  tran_no_partner: string
  customer_address: string
  print_price_temp: number
  customer_birthday: string
  Membership_Type_Id: string
  message_merge_table: string
  Membership_Type_Name: string
  call_voucher_partner: number
  message_modify_table: string
  not_show_partner_bill: number
  number_print_check_list: number
}

export interface SaleByDateData {
  store_uid: string
  shift_id: string
  tran_id: string
  tran_no: string
  start_date: number
  tran_date: number
  sale_type: string
  table_name: string
  state_action_bill: number
  origin_tran_id: string
  amount_discount_detail: number
  amount_discount_price: number
  service_charge_amount: number
  vat_amount: number
  voucher_amount: number
  discount_extra_amount: number
  commission_amount: number
  ship_fee_amount: number
  sale_note: string
  total_amount: number
  voucher_name: string
  extra_data: SaleByDateExtraData
  voucher_code: string
  voucher_extra: number
  discount_extra: number
  vat_invoice_number: string | null
  amount_origin: number
  store: SaleByDateStore
  sale_detail: SaleByDateDetail[]
  sale_payment_method: SaleByDatePaymentMethod[]
}

export interface SaleByDateResponse {
  data: SaleByDateData[]
  track_id: string
}

export interface GetSaleByDateParams {
  company_uid: string
  brand_uid: string
  list_store_uid: string
  start_date: number
  end_date: number
  csv?: number
  store_open_at?: number
  results_per_page?: number
  page?: number
}
