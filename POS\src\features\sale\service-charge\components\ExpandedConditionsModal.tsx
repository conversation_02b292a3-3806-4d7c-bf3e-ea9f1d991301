import type { ServiceCharge } from '@/types/service-charge'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useAreaNames, useSourceNames } from '@/hooks/api/use-service-charge'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { PosModal } from '@/components/pos/modal'

interface ExpandedConditionsModalProps {
  serviceCharge: ServiceCharge
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExpandedConditionsModal({ serviceCharge, open, onOpenChange }: ExpandedConditionsModalProps) {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  // Prepare area IDs for API call
  const areaIds = serviceCharge.extra_data?.area_ids?.join(',') || ''
  const sourceIds = serviceCharge.extra_data?.source_ids?.join(',') || ''

  // Check what conditions exist
  const hasAreaCondition = serviceCharge.extra_data?.is_area === 1 && areaIds
  const hasSourceCondition = serviceCharge.extra_data?.is_source === 1 && sourceIds

  // Fetch area names
  const { data: areaData } = useAreaNames(
    {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || '',
      store_uid: serviceCharge.store_uid,
      list_area_id: areaIds
    },
    open && !!hasAreaCondition
  )

  // Fetch source names
  const { data: sourceData } = useSourceNames(
    {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || '',
      store_uid: serviceCharge.store_uid,
      list_source_id: sourceIds
    },
    open && !!hasSourceCondition
  )

  const areas = areaData?.data || []
  const sources = sourceData?.data || []

  const getConditionDescription = () => {
    const conditions = []

    // Amount condition
    if (serviceCharge.from_amount > 0 || serviceCharge.to_amount > 0) {
      if (serviceCharge.from_amount === serviceCharge.to_amount && serviceCharge.from_amount === 0) {
        conditions.push('tất cả đơn hàng')
      } else {
        conditions.push(
          `hoá đơn có giá trị từ ${serviceCharge.from_amount.toLocaleString('vi-VN')} ₫ đến ${serviceCharge.to_amount.toLocaleString('vi-VN')} ₫`
        )
      }
    }

    return conditions.join(' và ')
  }

  return (
    <PosModal
      title='Điều kiện mở rộng'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={() => onOpenChange(false)}
      onConfirm={() => onOpenChange(false)}
      hideButtons={true}
      maxWidth='sm:max-w-[600px]'
    >
      <div className='space-y-4'>
        <div className='text-sm text-gray-600'>
          Phí dịch vụ được áp dụng khi {getConditionDescription()} và thoả mãn điều kiện mở rộng
        </div>

        {/* Areas Table */}
        {hasAreaCondition && (
          <div className='space-y-2'>
            <h4 className='text-sm font-medium'>Khu vực</h4>
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Khu vực</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {areas.length > 0 ? (
                    areas.map(area => (
                      <TableRow key={area.area_id}>
                        <TableCell>{area.area_name}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell className='py-4 text-center text-gray-500'>Không có Khu vực được áp dụng</TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {/* Sources Table */}
        {hasSourceCondition && (
          <div className='space-y-2'>
            <h4 className='text-sm font-medium'>Nguồn</h4>
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nguồn</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sources.length > 0 ? (
                    sources.map(source => (
                      <TableRow key={source.source_id}>
                        <TableCell>{source.source_name}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell className='py-4 text-center text-gray-500'>Không có Nguồn được áp dụng</TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {/* Show message if no conditions */}
        {!hasAreaCondition && !hasSourceCondition && (
          <div className='py-4 text-center text-gray-500'>Không có điều kiện mở rộng nào được áp dụng</div>
        )}
      </div>
    </PosModal>
  )
}
