import { apiClient } from '@/lib/api/pos/pos-api'

/**
 * License API functions
 */

export interface SyncStoreExpiryTimeParams {
  company_uid: string
  brand_uid: string
  id: string
}

export interface SyncStoreExpiryTimeResponse {
  expiry_time?: string
  [key: string]: unknown
}

/**
 * Sync store expiry time proactive
 */
export const syncStoreExpiryTimeProactive = async (
  params: SyncStoreExpiryTimeParams
): Promise<SyncStoreExpiryTimeResponse> => {
  const response = await apiClient.post('/mdata/v1/store/sync-expiry-time-proactive', params)
  return response.data
}
