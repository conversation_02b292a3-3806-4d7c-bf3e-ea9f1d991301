@import 'tailwindcss';
@import 'tw-animate-css';
@import 'leaflet/dist/leaflet.css';

@custom-variant dark (&:is(.dark *));

/* Custom styles for MultiSelect checkboxes */
.bg-primary .lucide-check {
  color: white !important;
}

/* Alternative selector for MultiSelect component */
[data-slot='command-item'] .bg-primary svg {
  color: white !important;
}

:root {
  --radius: 0.5rem;

  /* Navy Blue Light Theme */
  --background: #ffffff;
  --foreground: #264462;
  --card: #ffffff;
  --card-foreground: #002347;
  --popover: #ffffff;
  --popover-foreground: #002347;
  --primary: #005baa;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #002347;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #002347;
  --destructive: #ef4444;
  --destructive-foreground: #fef2f2;
  --success: #10b981;
  --success-foreground: #ecfdf5;
  --warning: #f59e0b;
  --warning-foreground: #fffbeb;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #005baa;

  /* Chart colors with navy blue theme */
  --chart-1: #005baa;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #64748b;

  /* Sidebar colors */
  --sidebar: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: #005baa;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #002347;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #005baa;
}

.dark {
  /* Navy Blue Dark Theme */
  --background: #002347;
  --foreground: #f8fafc;
  --card: #0f172a;
  --card-foreground: #f8fafc;
  --popover: #0f172a;
  --popover-foreground: #f8fafc;
  --primary: #36adff;
  --primary-foreground: #002347;
  --secondary: #1e293b;
  --secondary-foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #1e293b;
  --accent-foreground: #f8fafc;
  --destructive: #f87171;
  --destructive-foreground: #450a0a;
  --success: #34d399;
  --success-foreground: #022c22;
  --warning: #fbbf24;
  --warning-foreground: #451a03;
  --border: #1e293b;
  --input: #1e293b;
  --ring: #36adff;

  /* Chart colors for dark theme */
  --chart-1: #36adff;
  --chart-2: #34d399;
  --chart-3: #fbbf24;
  --chart-4: #f87171;
  --chart-5: #94a3b8;
}

@theme inline {
  --font-inter: 'Inter', 'sans-serif';
  --font-manrope: 'Manrope', 'sans-serif';

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
  html {
    @apply overflow-x-hidden;
  }
  body {
    @apply bg-background text-foreground min-h-svh w-full;
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
}

@utility no-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@utility faded-bottom {
  @apply after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_var(--background)_70%)] md:after:block;
}

/* styles.css */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
