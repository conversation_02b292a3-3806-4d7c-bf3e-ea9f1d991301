import { useQuery } from '@tanstack/react-query'

import { billTemplateApi } from '@/lib/bill-template-api'
import type { GetBillTemplateRequest } from '@/lib/bill-template-api'

interface UseGetBillTemplateOptions {
  enabled?: boolean
}

export const useGetBillTemplate = (params: GetBillTemplateRequest, options?: UseGetBillTemplateOptions) => {
  return useQuery({
    queryKey: ['bill-template', params],
    queryFn: () => billTemplateApi.getBillTemplate(params),
    enabled: options?.enabled ?? true,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
