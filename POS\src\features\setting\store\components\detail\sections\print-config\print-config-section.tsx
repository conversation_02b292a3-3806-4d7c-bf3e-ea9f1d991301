import type { UseFormReturn } from 'react-hook-form'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  Label
} from '@/components/ui'

import { PRINT_TYPE_OPTIONS, type StoreFormValues } from '../../../../data'

interface PrintConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function PrintConfigSection({ form, isLoading = false }: PrintConfigSectionProps) {
  const printType = form.watch('print_type')
  const showPrintLimit = printType === 'PROVISIONAL_INVOICE' || printType === 'CHECK_LIST_AND_PROVISIONAL_INVOICE'

  return (
    <div className='space-y-6'>
      <h2 className='mb-6 text-xl font-semibold'><PERSON><PERSON><PERSON> hình in tạm tính, chố<PERSON> đồ</h2>

      <div className='space-y-6'>
        {/* <PERSON><PERSON>u in */}
        <FormField
          control={form.control}
          name='print_type'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-start gap-4'>
                <FormLabel className='w-[200px] pt-2'>Kiểu in</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onValueChange={field.onChange}
                    className='grid flex-1 grid-cols-2 gap-4'
                    disabled={isLoading}
                  >
                    {PRINT_TYPE_OPTIONS.map(option => (
                      <div key={option.value} className='flex items-center space-x-2'>
                        <RadioGroupItem value={option.value} id={option.value} />
                        <Label htmlFor={option.value} className='cursor-pointer text-sm'>
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Giới hạn số lần in tạm tính - Only show when TEMP_CALC or BOTH is selected */}
        {showPrintLimit && (
          <FormField
            control={form.control}
            name='print_limit'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-[200px]'>Giới hạn số lần in tạm tính</FormLabel>
                  <FormControl>
                    <Input placeholder='Không giới hạn' disabled={isLoading} className='flex-1' {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </div>
    </div>
  )
}
