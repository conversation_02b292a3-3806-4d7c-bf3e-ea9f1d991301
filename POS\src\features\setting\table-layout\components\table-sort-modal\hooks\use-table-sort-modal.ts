import { useState, useEffect, useMemo } from 'react'

import { toast } from 'sonner'

import { apiClient, type Area } from '@/lib/api'
import type { Table } from '@/lib/tables-api'

import { useAreasData, useTablesData } from '@/hooks/api'

export const useTableSortModal = (open: boolean) => {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [sortedAreas, setSortedAreas] = useState<Area[]>([])
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [selectedAreaId, setSelectedAreaId] = useState<string>('')
  const [draggedTableIndex, setDraggedTableIndex] = useState<number | null>(null)
  const [customSortedTables, setCustomSortedTables] = useState<{ [areaId: string]: Table[] }>({})
  const [isSaving, setIsSaving] = useState<boolean>(false)

  const { data: areasData, isLoading: areasLoading } = useAreasData({
    storeUid: selectedStoreId
  })

  const { data: tablesData, isLoading: tablesLoading } = useTablesData({
    storeUid: selectedStoreId,
    enabled: !!selectedStoreId
  })

  useEffect(() => {
    if (!selectedStoreId) {
      setSortedAreas([])
      return
    }

    if (areasData && areasData.length > 0) {
      const sorted = [...areasData].sort((a, b) => a.sort - b.sort)
      setSortedAreas(sorted)
    } else if (areasData) {
      setSortedAreas([])
    }
  }, [selectedStoreId, areasData?.length, areasData?.map(a => a.id).join(',')])

  useEffect(() => {
    if (!open) {
      setSelectedStoreId('')
      setSortedAreas([])
      setDraggedIndex(null)
      setSelectedAreaId('')
      setDraggedTableIndex(null)
      setCustomSortedTables({})
    }
  }, [open])

  const filteredTables = useMemo(() => {
    if (!selectedAreaId || !tablesData) return []

    if (customSortedTables[selectedAreaId]) {
      return customSortedTables[selectedAreaId]
    }

    return tablesData.filter(table => table.area_uid === selectedAreaId).sort((a, b) => a.sort - b.sort)
  }, [selectedAreaId, tablesData, customSortedTables])

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('')
  }

  const handleAreaClick = (areaId: string) => {
    setSelectedAreaId(areaId)
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      return
    }

    const newAreas = [...sortedAreas]
    const draggedArea = newAreas[draggedIndex]

    newAreas.splice(draggedIndex, 1)
    newAreas.splice(dropIndex, 0, draggedArea)

    const updatedAreas = newAreas.map((area, index) => ({
      ...area,
      sort: index + 1
    }))

    setSortedAreas(updatedAreas)
    setDraggedIndex(null)
  }

  const handleTableDragStart = (e: React.DragEvent, index: number) => {
    setDraggedTableIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }

  const handleTableDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleTableDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedTableIndex === null || draggedTableIndex === dropIndex || !selectedAreaId) {
      setDraggedTableIndex(null)
      return
    }

    const currentTables = [...filteredTables]
    const draggedTable = currentTables[draggedTableIndex]

    currentTables.splice(draggedTableIndex, 1)
    currentTables.splice(dropIndex, 0, draggedTable)

    const updatedTables = currentTables.map((table, index) => ({
      ...table,
      sort: index + 1
    }))

    setCustomSortedTables(prev => ({
      ...prev,
      [selectedAreaId]: updatedTables
    }))

    setDraggedTableIndex(null)
  }

  const handleSave = async (onSave: (storeId: string, sortedAreas: Area[]) => void, onOpenChange: (open: boolean) => void) => {
    if (!selectedStoreId || sortedAreas.length === 0) {
      return
    }

    setIsSaving(true)

    try {
      const areasToUpdate = sortedAreas.map((area, index) => ({
        id: area.id,
        sort: index,
        company_uid: area.company_uid,
        brand_uid: area.brand_uid,
        store_uid: area.store_uid
      }))

      const tablesToUpdate: Array<{
        id: string
        sort: number
        company_uid: string
        brand_uid: string
        store_uid: string
      }> = []

      Object.values(customSortedTables).forEach(tables => {
        tables.forEach((table, index) => {
          tablesToUpdate.push({
            id: table.id,
            sort: index,
            company_uid: table.company_uid,
            brand_uid: table.brand_uid,
            store_uid: table.store_uid
          })
        })
      })

      const promises = []
      promises.push(apiClient.post('/pos/v1/area', areasToUpdate))

      if (tablesToUpdate.length > 0) {
        promises.push(apiClient.post('/pos/v1/table', tablesToUpdate))
      }

      await Promise.all(promises)

      toast.success('Đã cập nhật thứ tự bàn và khu vực thành công')
      onSave(selectedStoreId, sortedAreas)
      onOpenChange(false)
    } catch (error) {
      toast.error('Có lỗi xảy ra khi cập nhật thứ tự. Vui lòng thử lại.')
    } finally {
      setIsSaving(false)
    }
  }

  return {
    selectedStoreId,
    sortedAreas,
    draggedIndex,
    selectedAreaId,
    draggedTableIndex,
    filteredTables,
    isSaving,
    areasLoading,
    tablesLoading,
    handleStoreChange,
    handleAreaClick,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleTableDragStart,
    handleTableDragOver,
    handleTableDrop,
    handleSave
  }
}
