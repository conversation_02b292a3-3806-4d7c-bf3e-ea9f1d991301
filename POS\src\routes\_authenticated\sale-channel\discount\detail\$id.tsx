import { z } from 'zod'

import { createFileRoute } from '@tanstack/react-router'

import { DiscountForm } from '@/features/sale-channel/discount/components'

const editDiscountSearchSchema = z.object({
  store_uid: z.string().optional()
})

export const Route = createFileRoute('/_authenticated/sale-channel/discount/detail/$id')({
  component: EditDiscountPage,
  validateSearch: editDiscountSearchSchema
})

function EditDiscountPage() {
  const { id } = Route.useParams()
  const { store_uid: storeUid } = Route.useSearch()

  return <DiscountForm discountId={id} storeUid={storeUid} />
}
