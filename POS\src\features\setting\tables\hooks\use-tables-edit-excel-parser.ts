import { toast } from 'sonner'
import type { Table } from '@/lib/tables-api'
import type { Source } from '@/types/sources'

interface ParsedTableEditData {
  id: string
  table_name: string
  source_name?: string
  pre_order_items?: string
  description?: string
}

export function useTablesEditExcelParser() {
  const parseExcelFile = async (file: File): Promise<ParsedTableEditData[]> => {
    try {
      // Dynamic import for xlsx
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      const parsedTables: ParsedTableEditData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]

        if (!row || row.length === 0 || !row[0]) continue

        const table: ParsedTableEditData = {
          id: String(row[0] || '').trim(),
          table_name: String(row[1] || '').trim(),
          source_name: row[2] ? String(row[2]).trim() : undefined,
          pre_order_items: row[3] ? String(row[3]).trim() : undefined,
          description: row[4] ? String(row[4]).trim() : undefined
        }

        if (!table.id) {
          toast.error(`Dòng ${i + 1}: ID không được để trống`)
          continue
        }

        if (!table.table_name) {
          toast.error(`Dòng ${i + 1}: Tên bàn không được để trống`)
          continue
        }

        parsedTables.push(table)
      }

      if (parsedTables.length === 0) {
        toast.error('Không tìm thấy dữ liệu hợp lệ trong file')
        throw new Error('No valid data found')
      }

      return parsedTables
    } catch (error) {
      if (error instanceof Error && error.message !== 'No valid data found') {
        toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      }
      throw error
    }
  }

  const downloadTablesData = async (
    storeUid: string, 
    tables: Table[], 
    salesChannels: Source[]
  ) => {
    try {
      const XLSX = await import('xlsx')

      const exportData = [
        ['id', 'Tên bàn', 'Nguồn', 'Món đặt trước', 'Mô tả'],
        ...tables.map(table => {
          const source = salesChannels.find(s => s.sourceId === table.source_id)
          const preOrderItems = table.extra_data?.order_list?.length
            ? table.extra_data.order_list.map(item => `${item.item_id}(${item.quantity})`).join(', ')
            : ''

          return [
            table.id,
            table.table_name,
            source?.sourceName || '',
            preOrderItems,
            table.description || ''
          ]
        })
      ]

      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(exportData)

      const colWidths = [
        { wch: 20 },
        { wch: 20 },
        { wch: 15 },
        { wch: 30 },
        { wch: 30 }
      ]
      worksheet['!cols'] = colWidths

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Tables Data')

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      const filename = `tables_data_${storeUid}_${timestamp}.xlsx`

      XLSX.writeFile(workbook, filename)
      toast.success('Đã tải file dữ liệu bàn thành công!')
    } catch (error) {
      toast.error('Lỗi khi tải file dữ liệu bàn')
      throw error
    }
  }

  return {
    parseExcelFile,
    downloadTablesData
  }
}
