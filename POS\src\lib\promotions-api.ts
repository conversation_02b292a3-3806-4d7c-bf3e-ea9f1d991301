import { api } from './api/pos/pos-api'

// Types for Promotions API
export interface PromotionData {
  promotion_id: string
  promotion_name: string
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  revenue_net: number
  list_data: unknown[]
}

export interface PromotionsResponse {
  data: PromotionData[]
  message: string
  track_id: string
}

export interface VoucherSummary {
  storeUid: string
  storeName: string
  totalTransactions: number
  totalPrice: number
  totalAmount: number
  // Promotion data from promotions API
  revenueGross?: number
  discountAmount?: number
  voucherPercentage?: number // (discountAmount / revenueGross) * 100
  promotionData?: PromotionData
}

// Member voucher promotion IDs
export const MEMBER_VOUCHER_PROMOTION_IDS = ['259440', '239125', '65518', '239127', '135898']

// Cache for promotions requests
const promotionsCache = new Map<string, { data: PromotionsResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<PromotionsResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Promotions API Service
export const promotionsApi = {
  /**
   * Get promotions data for a store
   */
  getPromotionsData: async (params: {
    companyUid: string
    brandUid: string
    startDate: number
    endDate: number
    storeUid: string
  }): Promise<PromotionsResponse> => {
    const requestKey = `promotions-${params.companyUid}-${params.brandUid}-${params.startDate}-${params.endDate}-${params.storeUid}`

    const cached = promotionsCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          list_store_uid: params.storeUid,
          store_open_at: '0',
          by_days: '0'
        })

        const response = await api.get(`/v1/reports/sale-summary/promotions?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000'
          },
          timeout: 30000
        })

        const promotionsData = response.data as PromotionsResponse

        // Cache the result
        promotionsCache.set(requestKey, {
          data: promotionsData,
          timestamp: Date.now()
        })

        return promotionsData
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Filter promotions by specific member voucher promotion IDs
   * IDs: 259440, 239125, 65518, 239127
   */
  getMemberVoucherPromotions: async (params: {
    companyUid: string
    brandUid: string
    startDate: number
    endDate: number
    storeUid: string
  }): Promise<PromotionData[]> => {
    const promotionsResponse = await promotionsApi.getPromotionsData(params)

    // Filter for specific member voucher promotion IDs
    const memberPromotions = promotionsResponse.data.filter(promotion =>
      MEMBER_VOUCHER_PROMOTION_IDS.includes(promotion.promotion_id)
    )

    return memberPromotions
  },

  /**
   * Get member voucher promotion IDs
   */
  getMemberVoucherPromotionIds: (): string[] => {
    return [...MEMBER_VOUCHER_PROMOTION_IDS]
  },

  /**
   * Check if a promotion ID is a member voucher
   */
  isMemberVoucherPromotion: (promotionId: string): boolean => {
    return MEMBER_VOUCHER_PROMOTION_IDS.includes(promotionId)
  },

  /**
   * Clear cache
   */
  clearCache: () => {
    promotionsCache.clear()
    pendingRequests.clear()
  }
}

export default promotionsApi
