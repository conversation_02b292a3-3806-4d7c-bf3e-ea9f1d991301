import axios from 'axios'

import { CRM_HEADERS } from '@/constants'

export const crmApi = axios.create({
  baseURL: 'https://crm-ipos-proxy.lethimcook.workers.dev',
  timeout: 30000,
  headers: CRM_HEADERS
})

crmApi.interceptors.request.use(config => {
  const crmToken = localStorage.getItem('crm_token')
  if (crmToken) {
    config.headers.Authorization = crmToken
  } else {
    console.warn('No CRM token found in localStorage')
  }
  return config
})
