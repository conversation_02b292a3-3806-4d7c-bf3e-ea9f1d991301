// Reports Sources API Types

export interface ReportsSourcesDataItem {
  date: string
  tran_date: number
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  peo_count: number
  revenue_net: number
  deduct_tax_amount: number
}

export interface ReportsSourcesData {
  source_id: string
  source_name: string
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  revenue_net: number
  total_cost: number
  total_cost_rate: number
  peo_count: number
  deduct_tax_amount: number
  list_data: ReportsSourcesDataItem[]
}

export interface ReportsSourcesResponse {
  data: ReportsSourcesData[]
  message: string
  track_id: string
}

export interface GetReportsSourcesParams {
  brand_uid: string
  company_uid: string
  list_store_uid: string
  start_date: number
  end_date: number
  store_open_at?: number
  limit?: number
}
