import { useQuery } from '@tanstack/react-query'

import { QUERY_KEYS } from '@/constants/query-keys'

import { orderLogsApi, type GetOrderLogsParams } from '@/lib/order-logs-api'

interface UseOrderLogsDataOptions extends Partial<GetOrderLogsParams> {
  enabled?: boolean
}

export const useOrderLogsData = (options: UseOrderLogsDataOptions = {}) => {
  const { enabled = true, ...apiParams } = options

  // Default date range (last 30 days)
  const defaultEndDate = Date.now()
  const defaultStartDate = defaultEndDate - (30 * 24 * 60 * 60 * 1000) // 30 days ago

  const finalParams: GetOrderLogsParams = {
    company_uid: '595e8cb4-674c-49f7-adec-826b211a7ce3',
    brand_uid: 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e',
    store_uid: 'ba6e0a44-080d-4ae4-aba0-c29b79e95ab3',
    device_code: '', // Will be provided by component
    start_date: defaultStartDate,
    end_date: defaultEndDate,
    ...apiParams
  }

  return useQuery({
    queryKey: [QUERY_KEYS.ORDER_LOGS, finalParams],
    queryFn: async () => {
      if (!finalParams.device_code) {
        return { data: [] }
      }
      
      const response = await orderLogsApi.getOrderLogs(finalParams)
      return response
    },
    enabled: enabled && !!finalParams.device_code,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    select: data => data.data || [] // Extract the data array from the response
  })
}
