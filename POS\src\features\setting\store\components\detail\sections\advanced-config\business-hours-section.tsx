import type { UseFormReturn } from 'react-hook-form'

import { FormField, FormItem, FormLabel, FormControl, FormMessage, Input } from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface BusinessHoursSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function BusinessHoursSection({ form, isLoading = false }: BusinessHoursSectionProps) {
  const businessStartHour = form.watch('open_at')

  const generateBusinessDayRange = (startHour: number) => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    }

    const formatTime = (hour: number) => {
      return hour.toString().padStart(2, '0') + ':00'
    }

    const startTime = formatTime(startHour)
    const endHour = startHour === 0 ? 23 : startHour - 1
    const endTime = formatTime(endHour) === '00:00' ? '23:59' : formatTime(endHour) + ':59'

    if (startHour === 0) {
      return `${formatDate(today)} 00:00 - ${formatDate(today)} 23:59`
    } else {
      return `${formatDate(today)} ${startTime} - ${formatDate(tomorrow)} ${endTime}`
    }
  }

  const startHourNumber = businessStartHour ? parseInt(businessStartHour.toString(), 10) : 0
  const isValidHour = !isNaN(startHourNumber) && startHourNumber >= 0 && startHourNumber <= 23

  const reportRange = isValidHour ? generateBusinessDayRange(startHourNumber) : '04/08/2025 00:00 - 04/08/2025 23:59'

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Giờ bán hàng</h2>
        <div className='space-y-2 text-sm text-gray-600'>
          <p>Chi cái đặt khi cửa hàng của bạn bán qua 12h đêm</p>
          <p>
            Các báo cáo có chú thích: Báo cáo được tính từ {reportRange} . Đảm bảo các hóa đơn qua đêm sẽ được tổng hợp
            lại theo 1 ngày bán hàng.
          </p>
        </div>
      </div>

      <div className='space-y-4'>
        {/* Giờ bắt đầu bán */}
        <FormField
          control={form.control}
          name='open_at'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Giờ bắt đầu bán</FormLabel>
                </div>
                <FormControl>
                  <Input
                    {...field}
                    type='number'
                    min='0'
                    max='23'
                    placeholder='0'
                    disabled={isLoading}
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
