import { useEffect, useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

import { Combobox } from '@/components/pos'

import type { StoreFormValues } from '../../../../data'

interface PrintConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

const invoiceTemplateOptions = [
  { value: '0', label: 'Mặc định' },
  { value: '1', label: 'Mẫu hóa đơn 1' },
  { value: '2', label: 'Mẫu hóa đơn 2' },
  { value: '3', label: 'Mẫu hóa đơn 3' },
  { value: '4', label: 'Mẫu hóa đơn 4' },
  { value: '5', label: 'Mẫu tùy chỉnh' }
]

const comboSeparationOptions = [
  { value: '0', label: 'Không tách món combo' },
  { value: '1', label: 'Tách món trong combo và in' },
  { value: '2', label: 'Tách món trong combo và không in' }
]

const posPaymentScreenOptions = [
  { value: 0, label: 'Mặc định' },
  { value: 1, label: 'Xác nhận và in hóa đơn' },
  { value: 2, label: 'Thanh toán và không in hóa đơn' }
]

const posShiftCloseOptions = [
  { value: 0, label: 'Mặc định' },
  { value: 4, label: 'Hiển thị đóng ca' },
  { value: 8, label: 'Hiển thị đóng ca và in phiếu đóng ca' }
]

export function PrintConfigSection({ form, isLoading }: PrintConfigSectionProps) {
  const [paymentSelection, setPaymentSelection] = useState<number>(0)
  const [shiftSelection, setShiftSelection] = useState<number>(0)

  const combinedCode = form.watch('disable_print_button_in_payment_screen') || 0

  useEffect(() => {
    const payment = (combinedCode & 1) === 1 ? 1 : (combinedCode & 2) === 2 ? 2 : 0
    const shift = (combinedCode & 4) === 4 ? 4 : (combinedCode & 8) === 8 ? 8 : 0
    setPaymentSelection(payment)
    setShiftSelection(shift)
  }, [combinedCode])

  const updateCombinedCode = (nextPayment: number, nextShift: number) => {
    const nextCode = (nextPayment | nextShift) >>> 0
    form.setValue('disable_print_button_in_payment_screen', nextCode, { shouldDirty: true, shouldTouch: true })
  }

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mb-2 text-xl font-semibold'>Cấu hình in ấn</h2>
      </div>

      <div className='space-y-4'>
        {/* Gộp món khi in chốt đơn, tạm tính, in hóa đơn */}
        <FormField
          control={form.control}
          name='group_item'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Gộp món khi in chốt đơn, tạm tính, in hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Mẫu hóa đơn */}
        <FormField
          control={form.control}
          name='bill_template'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Mẫu hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    options={invoiceTemplateOptions}
                    value={field.value?.toString() || '0'}
                    onValueChange={val => field.onChange(val === '' ? undefined : Number(val))}
                    disabled={isLoading}
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không cho thu ngân sửa máy in */}
        <FormField
          control={form.control}
          name='prevent_cashier_edit_printer'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Không cho thu ngân sửa máy in</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình màn thanh toán tại POS */}
        <FormField
          control={form.control}
          name='disable_print_button_in_payment_screen'
          render={() => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cấu hình màn thanh toán tại POS</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    options={posPaymentScreenOptions}
                    value={paymentSelection}
                    onValueChange={val => {
                      const v = val === '' ? 0 : Number(val)
                      setPaymentSelection(v)
                      updateCombinedCode(v, shiftSelection)
                    }}
                    disabled={isLoading}
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cấu hình nút đóng ca tại POS */}
        <FormField
          control={form.control}
          name='disable_print_button_in_payment_screen'
          render={() => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Cấu hình nút đóng ca tại POS</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    options={posShiftCloseOptions}
                    value={shiftSelection}
                    onValueChange={val => {
                      const v = val === '' ? 0 : Number(val)
                      setShiftSelection(v)
                      updateCombinedCode(paymentSelection, v)
                    }}
                    disabled={isLoading}
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hiển thị logo trên phiếu tạm tính */}
        <FormField
          control={form.control}
          name='is_show_logo_in_provisional_invoice'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Hiển thị logo trên phiếu tạm tính</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tách món combo khi in chốt ca */}
        <FormField
          control={form.control}
          name='report_item_combo_split'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Tách món combo khi in chốt ca</FormLabel>
                </div>
                <FormControl>
                  <Combobox
                    options={comboSeparationOptions}
                    value={field.value?.toString() || '0'}
                    onValueChange={val => field.onChange(val === '' ? undefined : val)}
                    disabled={isLoading}
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phiếu tạm tính in giống hóa đơn */}
        <FormField
          control={form.control}
          name='tem_invoice_fake_bill'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Phiếu tạm tính in giống hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ẩn món có giá 0đ khi in hóa đơn */}
        <FormField
          control={form.control}
          name='hideItemPriceAfterPrintBill'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Ẩn món có giá 0đ khi in hóa đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ẩn món có giá 0đ khi in chốt đơn */}
        <FormField
          control={form.control}
          name='hideItemPriceAfterPrintChecklist'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Ẩn món có giá 0đ khi in chốt đơn</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* In lại hóa đơn yêu cầu nhập mã pin */}
        <FormField
          control={form.control}
          name='require_pin_reprint'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>In lại hóa đơn yêu cầu nhập mã pin</FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không in hóa đơn đối với các giao dịch thanh toán bằng MoMo hoặc ví điện tử có sự đồng xác nhận thanh toán */}
        <FormField
          control={form.control}
          name='prevent_print_order_transfer'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>
                    Không in hóa đơn đối với các giao dịch thanh toán bằng MoMo hoặc ví điện tử có sự đồng xác nhận
                    thanh toán
                  </FormLabel>
                </div>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isLoading} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
