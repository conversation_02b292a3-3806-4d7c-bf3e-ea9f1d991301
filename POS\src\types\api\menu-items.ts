export interface MenuItem {
  Id: number
  Pos_Id: number
  Item_Name: string
  Item_Id: string
  Item_Type_Id: string
  Item_Master_Id: number
  Item_Type_Master_Id: number
  Item_Image_Path?: string
  Item_Image_Path_Thumb?: string
  Last_Updated: string
  Description?: string
  Description_Fb: string
  Ots_Price: number
  Ta_Price: number
  Point: number
  Is_Gift: number
  Allow_Take_Away: number
  Show_On_Web: number
  Special_Type: number
  Show_Price_On_Web: number
  Active: number
  Is_Eat_With: number
  Require_Eat_With: number
  Item_Id_Eat_With: string
  Sort: number
  Is_Featured: number
  Is_Parent: number
  Is_Sub: number
  Time_Sale_Date_Week: number
  Time_Sale_Hour_Day: number
  Customizations?: string
  Allow_Self_Order: number
}

export interface MenuItemsResponse {
  count: number
  totalPage: number
  list_item: MenuItem[]
}

export interface UpdateMenuItemRequest {
  data: {
    Item_Id: string
    Ots_Price: number
    Ta_Price: number
    Item_Name: string
    Description: string
    Sort: number
    Item_Type_Id: string
    Customizations: string
    Is_Parent: number
    Is_Sub: number
    Is_Eat_With: number
    Item_Id_Eat_With: string
    Allow_Take_Away: number
    Allow_Self_Order: number
    Item_Image_Path: string
    Item_Image_Path_Thumb: string
  }
  id_value: string
  table_name: string
  list_pos: string
  pos_parent: string
  sync_fields: string
}

export interface MenuItemFormData {
  item_id: string
  item_name: string
  description: string
  ots_price: number
  ta_price: number
  sort: number
  item_type_id: string
  customizations: string
  allow_take_away: boolean
  allow_self_order: boolean
  is_eat_with: boolean
  item_image_path: string
}
