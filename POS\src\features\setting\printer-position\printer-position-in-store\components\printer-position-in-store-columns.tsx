'use client'

import type { ColumnDef } from '@tanstack/react-table'

import { useAreasData } from '@/hooks/api/use-areas'

import { DataTableColumnHeader } from '@/components/data-table'

import { PrinterPositionInStore } from '../data'

function AreaNamesCell({ areaIds, storeUid }: { areaIds: string; storeUid: string }) {
  const { data: areas = [], isLoading } = useAreasData({ storeUid })

  if (!areaIds) return <div className='text-muted-foreground text-sm'>—</div>
  const codes = areaIds.split(',').filter(Boolean)

  if (isLoading) {
    return (
      <div className='text-muted-foreground max-w-[280px] truncate text-sm' title={codes.join(', ')}>
        {codes.join(', ')}
      </div>
    )
  }

  const codeToName = new Map<string, string>(areas.map(a => [a.area_id, a.area_name]))
  const names = codes.map(code => codeToName.get(code) || code)
  const display = names.join(', ')

  return (
    <div className='text-muted-foreground max-w-[280px] truncate text-sm' title={display}>
      {display}
    </div>
  )
}

export const columns: ColumnDef<PrinterPositionInStore>[] = [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'printerPositionName',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tên vị trí máy in' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('printerPositionName')}</div>,
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'areaIds',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Khu vực' />,
    cell: ({ row }) => {
      const original = row.original as PrinterPositionInStore
      return <AreaNamesCell areaIds={original.areaIds || ''} storeUid={original.storeUid || ''} />
    },
    enableSorting: false
  }
]
