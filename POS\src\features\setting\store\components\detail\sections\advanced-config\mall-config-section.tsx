import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle } from 'lucide-react'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Input,
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui'

import type { StoreFormValues } from '../../../../data'

interface MallConfigSectionProps {
  form: UseFormReturn<StoreFormValues>
  isLoading?: boolean
}

export function MallConfigSection({ form, isLoading = false }: MallConfigSectionProps) {
  return (
    <div className='space-y-6'>
      {/* Section Header with Toggle */}
      <div className='flex items-center justify-between'>
        <h2 className='text-lg font-semibold'>Cấu hình trung tâm thương mại</h2>
      </div>

      {/* Section Content */}
      <div className='space-y-4'>
        {/* Mã định danh của hàng */}
        <FormField
          control={form.control}
          name='counter_code'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Mã định danh của hàng</FormLabel>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 text-gray-400' />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Mã định danh duy nhất của cửa hàng trong hệ thống trung tâm thương mại</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <FormControl>
                  <Input {...field} disabled={isLoading} placeholder='Nhập mã định danh của hàng' className='flex-1' />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email nhận file báo cáo */}
        <FormField
          control={form.control}
          name='counter_mails'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-4'>
                <div className='flex w-[240px] items-center gap-2'>
                  <FormLabel className='font-medium'>Email nhận file báo cáo</FormLabel>
                </div>
                <FormControl>
                  <Input
                    value={Array.isArray(field.value) ? field.value.join(', ') : ''}
                    onChange={e => {
                      const raw = e.target.value
                      const arr = raw
                        .split(',')
                        .map(s => s.trim())
                        .filter(Boolean)
                      field.onChange(arr)
                    }}
                    disabled={isLoading}
                    placeholder='Nhập nhiều email, phân tách bằng dấu phẩy'
                    type='text'
                    className='flex-1'
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
