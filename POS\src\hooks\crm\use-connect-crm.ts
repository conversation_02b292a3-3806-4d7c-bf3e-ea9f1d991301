import { useMutation } from '@tanstack/react-query'

import type { ConnectCrmRequest, ConnectCrmResponse } from '@/types/api/crm'

import { connectCrmApi } from '@/lib/api/crm/connect-crm-api'

export function useConnectCrm() {
  return useMutation<ConnectCrmResponse, Error, ConnectCrmRequest>({
    mutationFn: async (payload: ConnectCrmRequest) => {
      const res = await connectCrmApi(payload)
      return res.data
    }
  })
}
