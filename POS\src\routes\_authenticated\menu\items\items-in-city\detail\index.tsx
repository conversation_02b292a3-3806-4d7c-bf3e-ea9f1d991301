import { useEffect, useState } from 'react'

import { createFileRoute, useSearch } from '@tanstack/react-router'

import { ItemDetailForm } from '@/features/menu/items/items-in-city/detail'
import { ItemInCity, useItemInCityDetail } from '@/features/menu/items/items-in-city/hooks'

export const Route = createFileRoute('/_authenticated/menu/items/items-in-city/detail/')({
  component: ItemCreatePage
})

function ItemCreatePage() {
  const search = useSearch({ from: '/_authenticated/menu/items/items-in-city/detail' })
  const [currentRow, setCurrentRow] = useState<ItemInCity | null>(null)

  const id = (search as any)?.id
  const { data: itemDetail, isLoading } = useItemInCityDetail(id, !!id)

  if (!id) {
    return <ItemDetailForm />
  }

  useEffect(() => {
    if (itemDetail) {
      setCurrentRow(itemDetail.data as ItemInCity)
    }
  }, [itemDetail])

  if (isLoading || !currentRow) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center'>
          <div className='text-lg'>Đang tải...</div>
        </div>
      </div>
    )
  }

  return <ItemDetailForm currentRow={{ ...currentRow, item_id: '', item_id_barcode: '' }} isCopyMode={!!currentRow} />
}
