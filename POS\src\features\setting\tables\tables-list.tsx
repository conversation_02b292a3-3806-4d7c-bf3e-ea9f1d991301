import { useState, useMemo, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Upload, Settings, Edit, ArrowUpDown, ChevronDownIcon } from 'lucide-react'

import { useAreasData, useTablesData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import {
  TablesDataTable,
  createTablesColumns,
  ImportTablesModal,
  EditTablesModal,
  ArrangeTablesModal,
  ConfigureTablesModal
} from './'

interface Store {
  id: string
  store_name: string
  active: number
}

export function TablesList() {
  const navigate = useNavigate()

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData) ? storesData.filter((store: Store) => store.active === 1) : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [selectedAreaId, setSelectedAreaId] = useState<string>('all')
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isArrangeModalOpen, setIsArrangeModalOpen] = useState(false)
  const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false)

  useEffect(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores.length, selectedStoreId])

  useEffect(() => {
    if (!selectedStoreId) {
      setSelectedAreaId('all')
    }
  }, [selectedStoreId])

  const { data: areas = [], isLoading: isLoadingAreas } = useAreasData({
    storeUid: selectedStoreId || undefined,
    page: 1,
    results_per_page: 15000
  })

  const {
    data: tables = [],
    isLoading,
    error
  } = useTablesData({
    storeUid: selectedStoreId,
    skip_limit: true,
    enabled: !!selectedStoreId
  })

  const columns = useMemo(() => createTablesColumns(selectedStoreId), [selectedStoreId])

  const filteredTables = useMemo(() => {
    if (selectedAreaId === 'all') {
      return tables
    }
    return tables.filter(table => table.area_uid === selectedAreaId)
  }, [tables, selectedAreaId])

  const handleCreateNew = () => {
    navigate({ to: '/setting/table/detail' })
  }

  const handleImportFromFile = () => {
    if (!selectedStoreId) {
      return
    }
    setIsImportModalOpen(true)
  }

  const handleCloseImportModal = () => {
    setIsImportModalOpen(false)
  }

  const handleImportSuccess = () => {
    setIsImportModalOpen(false)
  }

  const handleEditTableInfo = () => {
    if (!selectedStoreId) {
      return
    }
    setIsEditModalOpen(true)
  }

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false)
  }

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
  }

  const handleArrangeTable = () => {
    if (!selectedStoreId) {
      return
    }
    setIsArrangeModalOpen(true)
  }

  const handleCloseArrangeModal = () => {
    setIsArrangeModalOpen(false)
  }

  const handleArrangeSuccess = () => {
    setIsArrangeModalOpen(false)
  }

  const handleConfigureTable = () => {
    if (!selectedStoreId) {
      return
    }
    setIsConfigureModalOpen(true)
  }

  const handleCloseConfigureModal = () => {
    setIsConfigureModalOpen(false)
  }

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('all')
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto space-y-6 py-6'>
          <div className='flex items-center gap-4'>
            <h1 className='text-2xl font-bold tracking-tight'>Danh sách bàn</h1>

            <div className='flex flex-1 gap-4'>
              <Select value={selectedStoreId} onValueChange={handleStoreChange}>
                <SelectTrigger className='w-[300px]'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {stores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedAreaId}
                onValueChange={setSelectedAreaId}
                disabled={!selectedStoreId || isLoadingAreas}
              >
                <SelectTrigger className='w-[300px]'>
                  <SelectValue
                    placeholder={
                      !selectedStoreId ? 'Chọn cửa hàng trước' : isLoadingAreas ? 'Đang tải...' : 'Chọn khu vực'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {selectedStoreId && (
                    <>
                      <SelectItem value='all'>Tất cả khu vực</SelectItem>
                      {areas.map(area => (
                        <SelectItem key={area.id} value={area.id}>
                          {area.area_name}
                        </SelectItem>
                      ))}
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className='flex gap-2'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='outline'>
                    Tiện ích
                    <ChevronDownIcon className='ml-2 h-4 w-4' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='w-56'>
                  <DropdownMenuItem onClick={handleImportFromFile}>
                    <Upload className='mr-2 h-4 w-4' />
                    Thêm bàn từ file
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEditTableInfo}>
                    <Edit className='mr-2 h-4 w-4' />
                    Sửa thông tin bàn
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleArrangeTable}>
                    <ArrowUpDown className='mr-2 h-4 w-4' />
                    Sắp xếp bàn
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleConfigureTable}>
                    <Settings className='mr-2 h-4 w-4' />
                    Cấu hình bàn
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button onClick={handleCreateNew}>Tạo bàn mới</Button>
            </div>
          </div>

          {/* Tables Table */}
          {isLoading && (
            <div className='rounded-md border p-8 text-center'>
              <p className='text-muted-foreground'>Đang tải...</p>
            </div>
          )}
          {error && (
            <div className='rounded-md border p-8 text-center'>
              <p className='text-muted-foreground'>Có lỗi xảy ra khi tải dữ liệu</p>
            </div>
          )}
          {!isLoading && !error && (
            <TablesDataTable columns={columns} data={filteredTables} storeUid={selectedStoreId} />
          )}

          {!selectedStoreId && stores.length > 0 && (
            <div className='bg-card rounded-lg border p-8 text-center'>
              <p className='text-muted-foreground'>Vui lòng chọn cửa hàng để xem danh sách bàn</p>
            </div>
          )}

          {stores.length === 0 && (
            <div className='bg-card rounded-lg border p-8 text-center'>
              <p className='text-muted-foreground'>Không có cửa hàng nào khả dụng</p>
            </div>
          )}

          {/* Import Tables Modal */}
          <ImportTablesModal
            open={isImportModalOpen}
            onOpenChange={setIsImportModalOpen}
            onCancel={handleCloseImportModal}
            onSuccess={handleImportSuccess}
          />

          {/* Edit Tables Modal */}
          <EditTablesModal
            open={isEditModalOpen}
            onOpenChange={setIsEditModalOpen}
            onCancel={handleCloseEditModal}
            onSuccess={handleEditSuccess}
          />

          {/* Arrange Tables Modal */}
          <ArrangeTablesModal
            open={isArrangeModalOpen}
            onOpenChange={setIsArrangeModalOpen}
            onCancel={handleCloseArrangeModal}
            onSuccess={handleArrangeSuccess}
          />

          {/* Configure Tables Modal */}
          <ConfigureTablesModal
            open={isConfigureModalOpen}
            onOpenChange={setIsConfigureModalOpen}
            onCancel={handleCloseConfigureModal}
          />
        </div>
      </Main>
    </>
  )
}
