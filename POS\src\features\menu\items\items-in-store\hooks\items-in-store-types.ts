/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ItemExtraData {
  formula_qrcode: string
  is_buffet_item: number
  up_size_buffet: unknown[]
  is_item_service: number
  is_virtual_item: number
  enable_edit_price: number
  exclude_items_buffet: string[]
  no_update_quantity_toping: number
  cross_price: {
    quantity: number
    price: number
  }[]
  price_by_source: {
    price: number
    source_id: string
    price_times: number[]
    is_source_exist_in_city: boolean
  }[]
}

export interface ItemStore {
  id: string
  city_id: string
  fb_city_id: string
  city_name: string
  image_path: string | null
  description: string
  active: number
  extra_data: unknown | null
  revision: number
  sort: number
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  items_cities: {
    item_uid: string
    city_uid: string
  }
}

export interface ItemInStore {
  id: string
  item_id: string
  item_name: string
  description: string
  ots_price: number
  ots_tax: number
  ta_price: number
  ta_tax: number
  time_sale_hour_day: number
  time_sale_date_week: number
  allow_take_away: number
  is_eat_with: number
  image_path: string
  image_path_thumb: string
  item_color: string
  list_order: number
  is_service: number
  is_material: number
  active: number
  user_id: string
  is_foreign: number
  quantity_default: number
  price_change: number
  currency_type_id: string
  point: number
  is_gift: number
  is_fc: number
  show_on_web: number
  show_price_on_web: number
  cost_price: number
  is_print_label: number
  quantity_limit: number
  is_kit: number
  time_cooking: number
  item_id_barcode: string
  process_index: number
  is_allow_discount: number
  quantity_per_day: number
  item_id_eat_with: string
  is_parent: number
  is_sub: number
  item_id_mapping: string
  effective_date: number
  expire_date: number
  sort: number
  sort_online: number
  extra_data: ItemExtraData
  revision: number
  unit_uid: string
  unit_secondary_uid: string | null
  item_type_uid: string
  item_class_uid: string | null
  source_uid: string | null
  brand_uid: string
  city_uid: string
  store_uid: string
  company_uid: string
  customization_uid: string
  is_fabi: number
  deleted: boolean
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  apply_with_store: number
  Stores?: any[]
  item_old?: Record<string, unknown>
}

export interface ItemsInStoreApiResponse {
  data: ItemInStore[]
  total_item: number
  track_id: string
}

export interface GetItemsInStoreParams {
  company_uid: string
  brand_uid: string
  page?: number
  store_uid?: string
  item_type_uid?: string
  time_sale_date_week?: string
  active?: number
  reverse?: number
  search?: string
  limit?: number
  skip_limit?: boolean
  apply_with_store?: number
}

export interface DeleteItemInStoreParams {
  company_uid: string
  brand_uid: string
  id: string
}

export interface DeleteMultipleItemsInStoreParams {
  company_uid: string
  brand_uid: string
  list_item_uid: string[]
}

export interface CreateItemInStoreRequest
  extends Omit<
    ItemInStore,
    'id' | 'revision' | 'created_at' | 'updated_at' | 'deleted_at' | 'created_by' | 'updated_by' | 'deleted_by'
  > {
  company_uid: string
  brand_uid: string
}

export interface UpdateItemInStoreRequest extends ItemInStore {
  company_uid: string
  brand_uid: string
}

export interface GetItemByListIdParams {
  company_uid: string
  brand_uid: string
  list_item_id: string
}

export interface GetItemByIdParams {
  id: string
  company_uid?: string
  brand_uid?: string
}

export interface UpdateItemStatusRequest {
  id: string
  active: number
  company_uid: string
  brand_uid: string
}

export interface DownloadTemplateParams {
  company_uid: string
  brand_uid: string
  city_uid?: string
  item_type_uid?: string
  active?: string
}

export interface ImportItemsRequest {
  company_uid: string
  brand_uid: string
  items: unknown[]
}

export interface ApiError {
  response?: {
    status?: number
    data?: {
      message?: string
    }
  }
}

export interface TimeFrameConfig {
  id: string
  price: number
  startDate: Date
  endDate: Date
  selectedDays: number[]
  selectedHours: number[]
}

export interface PriceTime {
  price: number
  from_date: number
  to_date: number
  time_sale_date_week: number
  time_sale_hour_day: number
}

export interface BulkUpdateItemInStoreRequest {
  items: ItemInStore[]
}

export interface CloneMenuRequest {
  company_uid: string
  brand_uid: string
  store_uid_root: string
  store_uid_target: string
  list_item_id: string[]
  menu_type: 'store' | 'brand' | 'city'
}

export interface BulkCreateItemInStoreRequest {
  store_uid: string
  apply_with_store: number
  company_uid: string
  brand_uid: string
  city_uid?: string
  item_id: string
  unit_uid: string
  ots_price: number
  ta_price: number
  ots_tax: number
  ta_tax: number
  item_name: string
  item_id_barcode?: string
  is_eat_with: number
  item_type_uid: string
  item_class_uid: string | null
  description?: string
  item_id_mapping?: string
  time_cooking: number
  time_sale_date_week: number
  time_sale_hour_day: number
  sort: number
  image_path_thumb?: string
  image_path?: string
  extra_data: {
    no_update_quantity_toping: number
    enable_edit_price: number
    is_virtual_item: number
    is_item_service: number
    is_buffet_item: number
  }
}
