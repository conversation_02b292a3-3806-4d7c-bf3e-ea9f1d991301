import { crmApi } from './crm-api'
import type {
  CustomerReportParams,
  CustomerRegistrationReportResponse,
  MembershipTypeChangeStatsResponse,
  OrderByMembershipTypeResponse
} from '@/types/api/crm/customer-report'

/**
 * Customer Report API Service
 */
export const customerReportApi = {
  /**
   * Get customer registration report
   * Endpoint: /general/report-customer
   */
  getCustomerRegistrationReport: async (
    params: CustomerReportParams
  ): Promise<CustomerRegistrationReportResponse> => {
    try {
      const queryParams = new URLSearchParams({
        start_date: params.start_date,
        end_date: params.end_date,
        pos_parent: params.pos_parent || 'BRAND-953H'
      })

      const response = await crmApi.get(`/general/report-customer?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from customer registration report API')
      }

      return response.data as CustomerRegistrationReportResponse
    } catch (error) {
      console.error('Error fetching customer registration report:', error)
      throw new Error('Failed to fetch customer registration report')
    }
  },

  /**
   * Get membership type change statistics
   * Endpoint: /general/membership-type-change-stat
   */
  getMembershipTypeChangeStats: async (
    params: CustomerReportParams
  ): Promise<MembershipTypeChangeStatsResponse> => {
    try {
      const queryParams = new URLSearchParams({
        start_date: params.start_date,
        end_date: params.end_date,
        pos_parent: params.pos_parent || 'BRAND-953H'
      })

      const response = await crmApi.get(`/general/membership-type-change-stat?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from membership type change stats API')
      }

      return response.data as MembershipTypeChangeStatsResponse
    } catch (error) {
      console.error('Error fetching membership type change stats:', error)
      throw new Error('Failed to fetch membership type change statistics')
    }
  },

  /**
   * Get orders by membership type
   * Endpoint: /general/order-by-membership-type
   */
  getOrderByMembershipType: async (
    params: CustomerReportParams
  ): Promise<OrderByMembershipTypeResponse> => {
    try {
      const queryParams = new URLSearchParams({
        start_date: params.start_date,
        end_date: params.end_date,
        pos_parent: params.pos_parent || 'BRAND-953H'
      })

      const response = await crmApi.get(`/general/order-by-membership-type?${queryParams.toString()}`)

      // Validate response structure
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from order by membership type API')
      }

      return response.data as OrderByMembershipTypeResponse
    } catch (error) {
      console.error('Error fetching order by membership type:', error)
      throw new Error('Failed to fetch order by membership type data')
    }
  }
}
